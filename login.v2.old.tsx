import React, { useState, useEffect, useMemo } from 'react';
import { View, TouchableOpacity, StyleSheet, TextStyle, KeyboardAvoidingView, ScrollView, TextInput, Platform, Image, I18nManager, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { NewRTLView as RTLView, NewRTLText as RTLText, NewRTLTextInput as RTLTextInput, useRTLContext, NewRTLText } from '@/components/rtl/new-index';
import { Feather, Ionicons, MaterialIcons } from '@expo/vector-icons';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/lib/supabase';
import type { Country, CountryPickerItem } from '@/types/countries';


interface CountryPickerProps {
  show: boolean;
  pickerButtonOnPress: (item: CountryPickerItem) => void;
  style: {
    modal: {
      height: number;
      backgroundColor: string;
    };
    textInput: {
      color: string;
      backgroundColor: string;
    };
    countryButtonStyles: {
      backgroundColor: string;
    };
    countryName: {
      color: string;
    };
    dialCode: {
      color: string;
    };
  };
  onBackdropPress: () => void;
  initialState: CountryPickerItem;
}

const CountryPicker: React.FC<CountryPickerProps> = ({ show, pickerButtonOnPress, style, onBackdropPress, initialState }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [countries, setCountries] = useState<CountryPickerItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { isRTL } = useRTLContext();

  useEffect(() => {
    async function fetchCountries() {
      try {
        const { data, error } = await supabase
          .from('countries')
          .select('code, name, dial_code, flag_emoji')
          .order('name');

        if (error) {
          console.error('Error fetching countries:', error);
          return;
        }

        setCountries(data);
      } catch (error) {
        console.error('Error in fetchCountries:', error);
      } finally {
        setIsLoading(false);
      }
    }

    if (show) {
      fetchCountries();
    }
  }, [show]);

  const filteredCountries = useMemo(() => {
    return countries.filter(country => 
      country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      country.dial_code.includes(searchQuery)
    );
  }, [searchQuery, countries]);

  if (!show) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: isDarkMode ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.5)' ,
    }}>
      <View style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        height: '90%',
        padding: 16,
      }}>
        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 20 }}> 
          <TouchableOpacity onPress={onBackdropPress} style={{ padding: 8 }}>
            <MaterialIcons name="close" size={24} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
          </TouchableOpacity>
          <RTLText style={{ 
            flex: 1, 
            fontSize: 18, 
            fontWeight: '600',
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
            textAlign: 'center',
            marginRight: 40
          }}>
            {t('auth.countryPicker.title', 'Select a Country')}
          </RTLText>
        </View>

        <View style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          alignItems: 'center',
          backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
          borderRadius: 8,
          paddingHorizontal: 12,
          marginBottom: 16
        }}>
          <Feather name="search" size={20} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
          <RTLTextInput
            style={{
              flex: 1,
              height: 40,
              marginLeft: 8,
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              textAlign: isRTL ? 'right' : 'left'
            }}
            placeholder={t('auth.countryPicker.searchPlaceholder', 'Search for a country')}
            placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <View style={{ marginBottom: 16 }}>
          <RTLText style={{ 
            fontSize: 16, 
            color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
            marginBottom: 8
          }}>
            {t('auth.countryPicker.currentLocation', 'Current location')}
          </RTLText>
          <TouchableOpacity 
            style={{ 
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              padding: 12,
              backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light,
              borderRadius: 8
            }}
            onPress={() => pickerButtonOnPress(initialState)}
          >
            <RTLText style={{ fontSize: 24, marginRight: 12 }}>{initialState.flag_emoji}</RTLText>
            <RTLText style={{ 
              flex: 1,
              fontSize: 16,
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light
            }}>
              {initialState.name}
            </RTLText>
            {initialState.code === 'SA' && (
              <Feather name="check" size={20} color={colors.brandColors.primary[500]} />
            )}
          </TouchableOpacity>
        </View>

        <ScrollView>
          {isLoading ? (
            <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
          ) : (
            filteredCountries.map((country, index) => (
              <TouchableOpacity
                key={country.code}
                style={{
                  flexDirection: isRTL ? 'row-reverse' : 'row',
                  alignItems: 'center',
                  padding: 12,
                  borderBottomWidth: index < filteredCountries.length - 1 ? 1 : 0,
                  borderBottomColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light
                }}
                onPress={() => pickerButtonOnPress(country)}
              >
                <RTLText style={{ fontSize: 24, marginRight: 12 }}>{country.flag_emoji}</RTLText>
                <RTLText style={{ 
                  flex: 1,
                  fontSize: 16,
                  color: isDarkMode ? colors.textColors.dark : colors.textColors.light
                }}>
                  {country.name}
                </RTLText>
                {country.code === initialState.code && (
                  <Feather name="check" size={20} color={colors.brandColors.primary[500]} />
                )}
              </TouchableOpacity>
            ))
          )}
        </ScrollView>
      </View>
    </View>
  );
};

export default function LoginScreenV2() {
  const router = useRouter();
  const { t, i18n } = useTranslation();
  const { signInWithPhone, signInWithEmail } = useAuth();
  const { isRTL } = useRTLContext();
  const { isDarkMode, initializeTheme } = useTheme();
  
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loginMethod, setLoginMethod] = useState('phone'); // 'email' or 'phone'
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showCountryPicker, setShowCountryPicker] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<CountryPickerItem>({ 
    dial_code: '+966', 
    code: 'SA', 
    name: 'Saudi Arabia',
    flag_emoji: '🇸🇦'
  });
  const [activeTab, setActiveTab] = useState('phone');

  // Initialize language and RTL settings
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // No need to manually initialize language here anymore
        // The RTLContext now handles this properly on mount
        // This prevents duplicate initialization and potential race conditions
      } catch (error) {
        console.error('Failed to initialize language:', error);
      }
    };

    initializeLanguage();
  }, []);

  // Initialize theme on mount
  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeTheme();
      } catch (error) {
        console.error('Failed to initialize theme:', error);
      }
    };
    initialize();
  }, []);

  // Force RTL on mount
  useEffect(() => {
    const forceRTL = async () => {
      if (isRTL) {
        I18nManager.allowRTL(true);
        I18nManager.forceRTL(true);
      }
    };
    forceRTL();
  }, [isRTL]);

  // Add debug logs for country picker state
  useEffect(() => {
    console.log('showCountryPicker state changed:', showCountryPicker);
  }, [showCountryPicker]);

  useEffect(() => {
    console.log('selectedCountry changed:', selectedCountry);
  }, [selectedCountry]);

  const handleLogin = async () => {
    if (activeTab === 'phone') {
      if (!phone) {
        setError(t('auth.login.errors.phoneRequired', 'Please enter your phone number'));
        return;
      }

      setError('');
      setIsLoading(true);

      try {
        await signInWithPhone(selectedCountry.dial_code + phone);
        // Re-initialize theme after login to get user preferences
        await initializeTheme();
        router.push('/');
      } catch (err) {
        setError(t('auth.login.errors.failed', 'Login failed. Please try again.'));
      } finally {
        setIsLoading(false);
      }
    } else {
      if (!email || !password) {
        setError(t('auth.login.errors.emailPasswordRequired', 'Please enter both email and password'));
        return;
      }

      setError('');
      setIsLoading(true);

      try {
        await signInWithEmail(email, password);
        // Re-initialize theme after login to get user preferences
        await initializeTheme();
        router.push('/');
      } catch (err) {
        setError(t('auth.login.errors.failed', 'Login failed. Please try again.'));
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleGoogleLogin = async () => {
    // For now, just show an error that this feature is coming soon
    setError(t('auth.login.errors.googleSignInNotAvailable', 'Google sign-in will be available soon'));
  };

  const handleSignUp = () => {
    router.push('/register');
  };

  const tabTextStyle = (isActive: boolean): TextStyle => ({
    color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
    ...(isActive && { color: colors.brandColors.secondary[600] })
  });

  

  return (
    <SafeAreaView style={[
      styles.container, 
      { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }
    ]}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          
          <RTLView style={styles.header}>
            <Image source={isRTL ? require('@/assets/logoSVG-ar.png') : require('@/assets/logoSVG-en.png')} style={styles.logoImage} />
            <RTLText style={[styles.title, { color: isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500] }]}>
              {activeTab === 'phone' 
                ? t('auth.login.title') + ' ' + t('auth.login.withPhone', 'with phone number')
                : t('auth.login.title') + ' ' + t('auth.login.withEmail', 'with email')
              }
            </RTLText>
            <RTLText style={[styles.subtitle, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {activeTab === 'phone' 
                ? t('auth.login.phoneDescription')
                : t('auth.login.emailDescription') 
              }
            </RTLText>
          </RTLView>

          <RTLView style={[styles.tabContainer, { flexDirection: isRTL ? 'row-reverse' as const : 'row' as const }, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
          <TouchableOpacity 
              style={[
                styles.tab, 
                activeTab === 'phone' && styles.activeTab,
              ]}
              onPress={() => setActiveTab('phone')}
            >
              <RTLText style={[
                styles.tabText,
                tabTextStyle(activeTab === 'phone')
              ]}>
                {t('auth.login.phoneMethod')}
              </RTLText>
            </TouchableOpacity>
          <TouchableOpacity 
              style={[
                styles.tab, 
                activeTab === 'email' && styles.activeTab,
              ]}
              onPress={() => setActiveTab('email')}
            >
              <RTLText style={[
                styles.tabText, 
                tabTextStyle(activeTab === 'email')
              ]}>
                {t('auth.login.emailMethod')}
              </RTLText>
            </TouchableOpacity>
            
            
          </RTLView>

          {activeTab === 'phone' ? (
            <View style={styles.inputContainer}>
              <View style={[styles.phoneInputContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <TouchableOpacity 
                  style={[
                    styles.countryCode,
                    { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }
                  ]}
                  onPress={() => {
                    console.log('Country code button pressed');
                    setShowCountryPicker(true);
                  }}
                >
                  <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
                    <RTLText style={{ 
                      fontSize: 16, 
                      color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                      marginRight: 8,
                      writingDirection: 'ltr'
                    }}>
                      {selectedCountry.dial_code}
                    </RTLText>
                    <Feather name="chevron-down" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
                  </View>
                </TouchableOpacity>
                <RTLTextInput
                  style={[
                    styles.phoneInput,
                    { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }
                  ]}
                  placeholder={t('auth.login.phoneNumber')}
                  placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                  textAlign={isRTL ? 'right' : 'left'}
                />
              </View>
            </View>
          ) : (
            <View style={styles.inputContainer}>
              <RTLTextInput
                style={[styles.input, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}
                placeholder={t('auth.login.emailPlaceholder')}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              
              <View style={[styles.passwordContainer, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
                <RTLTextInput
                  style={[styles.passwordInput, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }, { paddingRight: isRTL ? 0 : 40, paddingLeft: isRTL ? 40 : 0 }]}
                  placeholder={t('auth.login.passwordPlaceholder')}
                  placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity 
                  style={[styles.passwordToggle, { right:isRTL ? undefined : 8, left:isRTL ? 8 : undefined }]}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Feather 
                    name={showPassword ? "eye-off" : "eye"} 
                    size={20} 
                    color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}

          {error ? (
            <RTLText style={styles.errorText}>
              {error}
            </RTLText>
          ) : null}

          <TouchableOpacity
            style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
            onPress={handleLogin}
            disabled={isLoading}
          >
            <RTLText style={[styles.buttonText, { color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600] }]}>
              {t('common.continue')}
            </RTLText>
          </TouchableOpacity>

          <View style={styles.termsContainer}>
            <RTLText style={styles.termsText}>
              {t('auth.terms.description')} {' '}
              <RTLText style={styles.termsLink}>{t('auth.terms.terms')}</RTLText>
              {' '}{t('auth.terms.and')}{' '}
              <RTLText style={styles.termsLink}>{t('auth.terms.privacy')}</RTLText>
            </RTLText>
          </View>

          <View style={styles.dividerContainer}>
            <View style={[styles.divider, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]} />
            <RTLText style={[styles.dividerText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>{t('auth.login.orContinueWith')}</RTLText>
            <View style={[styles.divider, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]} />
          </View>

          <TouchableOpacity
            style={[styles.socialButton, { borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,  }]}
            onPress={handleGoogleLogin}
          >
            <Image 
              source={require('@/assets/google-logo.png')} 
              style={styles.socialIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>

          <RTLView style={[styles.footer, { flexDirection: isRTL ? 'row-reverse' as const : 'row' as const }]}>   
            <RTLText style={[styles.footerText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {t('auth.login.noAccount')}
            </RTLText>
            <TouchableOpacity onPress={handleSignUp}>
              <RTLText style={styles.footerLink}>
                {t('auth.login.createAccount')}
              </RTLText>
            </TouchableOpacity>
          </RTLView>
        </ScrollView>
      </KeyboardAvoidingView>
      
      <CountryPicker
        show={showCountryPicker}
        pickerButtonOnPress={(item: CountryPickerItem) => {
          setSelectedCountry(item);
          setShowCountryPicker(false);
        }}
        style={{
          modal: {
            height: 500,
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
          },
          textInput: {
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
          },
          countryButtonStyles: {
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
          },
          countryName: {
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light
          },
          dialCode: {
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light
          }
        }}
        onBackdropPress={() => setShowCountryPicker(false)}
        initialState={selectedCountry}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  tabContainer: {
    
    marginBottom: 24,
    borderRadius: 12,
    
    padding: 4,
  },
  header: {
    
    flexDirection: 'column',
    marginTop: 40,
    marginBottom: 40,
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  logoImage: {
    height: 32,
    resizeMode: 'contain',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
  },
  activeTab: {
    backgroundColor: colors.brandColors.primary[500],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    
  },
  inputContainer: {
    marginBottom: 24,
  },
  phoneInputContainer: {
    
    alignItems: 'center',
    gap: 12,
  },
  countryCode: {
    height: 50,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
  },
  phoneInput: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  input: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  button: {
    backgroundColor: colors.brandColors.primary[500],
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    color: colors.brandColors.danger[500],
    marginBottom: 16,
    fontSize: 14,
  },
  termsContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    color: 'gray',
    lineHeight: 18,
  },
  termsLink: {
    color: colors.brandColors.primary[500],
    fontWeight: '500',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 30,
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 10,
    fontSize: 12,
  },
  socialButton: {
    backgroundColor: 'transparent',
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.3,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },
  footer: {
    justifyContent: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    marginRight: 5,
  },
  footerLink: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.brandColors.primary[500],
  },
  passwordContainer: {
    flexDirection: 'row', 
    alignItems: 'center',
    height: 50,
    borderRadius: 8,
    marginTop: 16,
    paddingHorizontal: 16,
  },
  passwordInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  passwordToggle: {
    padding: 8,
    position: 'absolute',
    
  },
}); 