-- Step 1: Create new features table
CREATE TABLE public.features (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    name_en text NOT NULL,
    name_ar text,
    icon_name text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Step 2: Create junction table for dumpster-feature many-to-many relationship
CREATE TABLE public.dumpster_feature_links (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    dumpster_id uuid NOT NULL REFERENCES public.dumpsters(id) ON DELETE CASCADE,
    feature_id uuid NOT NULL REFERENCES public.features(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT now(),
    UNIQUE(dumpster_id, feature_id)
);

-- Step 3: Migrate existing feature data
-- First, insert unique features into the new features table
INSERT INTO public.features (name_en)
SELECT DISTINCT feature 
FROM public.dumpster_features;

-- Then link dumpsters to these features
INSERT INTO public.dumpster_feature_links (dumpster_id, feature_id)
SELECT df.dumpster_id, f.id
FROM public.dumpster_features df
JOIN public.features f ON f.name_en = df.feature;

-- Step 4: Modify dumpster_sizes table to remove dumpster_type_id
ALTER TABLE public.dumpster_sizes
DROP CONSTRAINT IF EXISTS dumpster_sizes_dumpster_type_id_fkey,
DROP COLUMN dumpster_type_id;

-- Step 5: Add direct size_id to dumpsters table
ALTER TABLE public.dumpsters
ADD COLUMN size_id uuid REFERENCES public.dumpster_sizes(id);

-- Step 6: Migrate existing size data (keep first size for each dumpster)
UPDATE public.dumpsters d
SET size_id = (
    SELECT dumpster_size_id 
    FROM public.dumpster_size_options 
    WHERE dumpster_id = d.id 
    LIMIT 1
);

-- Step 7: Remove dumpster_type_id from dumpsters
ALTER TABLE public.dumpsters
DROP CONSTRAINT IF EXISTS dumpsters_dumpster_type_id_fkey,
DROP COLUMN dumpster_type_id;

-- Step 8: Drop the junction table that's no longer needed
DROP TABLE IF EXISTS public.dumpster_size_options;

-- Step 9: Create appropriate indexes
CREATE INDEX idx_dumpsters_size_id ON public.dumpsters(size_id);
CREATE INDEX idx_dumpster_feature_links_dumpster_id ON public.dumpster_feature_links(dumpster_id);
CREATE INDEX idx_dumpster_feature_links_feature_id ON public.dumpster_feature_links(feature_id);

-- Step 10: Add RLS policies for the new tables
ALTER TABLE public.features ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow public read access for features" ON public.features FOR SELECT USING (true);
CREATE POLICY "Allow admin access to all features" ON public.features USING (public.authorize('admin'::text));

ALTER TABLE public.dumpster_feature_links ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow public read access for dumpster_feature_links" ON public.dumpster_feature_links FOR SELECT USING (true);
CREATE POLICY "Allow admin access to all dumpster_feature_links" ON public.dumpster_feature_links USING (public.authorize('admin'::text));
