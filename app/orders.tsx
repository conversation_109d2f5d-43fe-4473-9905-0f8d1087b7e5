import React, { useEffect, useState, useCallback } from 'react';
import { Text, View, SafeAreaView, TouchableOpacity, Image, Alert } from 'react-native';
import { router } from 'expo-router';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ActivityIndicator } from 'react-native-paper';
import { Feather } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { NewRTLView, NewRTLText } from '@/components/rtl/new-index';
import { TouchableOpacity as StyledTouchableOpacity, ScrollView as StyledScrollView } from '@/components/styled';
import { useTheme } from '@/context/ThemeContext';
import { useRTL } from '@/hooks/useRTL';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/utils/dateUtils';
import * as colors from '@/theme/colors';
import { useOrders } from '@/hooks/useOrders';
import { styled } from 'nativewind';
import { syncProfileAuthIds, refreshUserProfile } from '@/utils/user';
import { useAuth } from '@/context/AuthContext';
import { useOrderRealtime } from '@/hooks/useOrderRealtime';
import { RealtimePostgresChangesPayload, RealtimeChannel } from '@supabase/supabase-js';

interface Partner {
  id: string;
  company_name: string;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
}

interface Order {
  id: string;
  created_at: string;
  status: 'pending' | 'confirmed' | 'delivered' | 'in_use' | 'pickup_scheduled' | 'completed' | 'cancelled';
  delivery_address: string;
  delivery_date: string;
  total_amount: number;
  partner_id: string | null;
  partner?: Partner[] | null;
  customer_id?: string;
  user_id?: string;
  payment_status: 'pending' | 'partial' | 'paid' | 'refunded';
  payment_method: 'credit_card' | 'cash' | 'bank_transfer' | null;
}

interface DriverAssignment {
  id: string;
  order_id: string;
  driver_id: string | null;
  status: string;
}

const StyledView = styled(View);
const StyledText = styled(Text);

export default function OrdersScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTL();
  const { session } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();

  const handleBack = () => router.back();
  const handleOrderPress = (orderId: string) => router.push(`/orders/${orderId}`);

  useEffect(() => {
    // Subscribe directly to all tables to ensure we're getting events
    console.log('[DEBUG] Setting up direct real-time listener');
    
    const channel = supabase.channel('direct-debug-channel', {
      config: { broadcast: { self: true } }
    });
    
    channel
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'orders' 
      }, (payload) => {
        console.log('[DEBUG-DIRECT] Received order change:', payload);
        // Force refetch immediately
        queryClient.invalidateQueries({ queryKey: ['orders'] });
        queryClient.refetchQueries({ queryKey: ['orders'], type: 'active' });
      })
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'order_status_history' 
      }, (payload) => {
        console.log('[DEBUG-DIRECT] Received order status history change:', payload);
        // Force refetch immediately 
        queryClient.invalidateQueries({ queryKey: ['orders'] });
        queryClient.refetchQueries({ queryKey: ['orders'], type: 'active' });
      })
      .subscribe((status) => {
        console.log(`[DEBUG-DIRECT] Direct subscription status: ${status}`);
      });
    
    // Add an auto-refresh to ensure up-to-date data
    const intervalId = setInterval(() => {
      console.log('[DEBUG] Auto-refreshing orders data');
      queryClient.refetchQueries({ queryKey: ['orders'], type: 'active' });
    }, 15000); // Refresh every 15 seconds
    
    return () => {
      console.log('[DEBUG] Cleaning up direct real-time listener');
      supabase.removeChannel(channel);
      clearInterval(intervalId);
    };
  }, [queryClient]);

  const syncProfileData = async () => {
    try {
      setIsRefreshing(true);
      await syncProfileAuthIds(false);
      await refreshUserProfile(false);
      await new Promise(r => setTimeout(r, 500));
      await refetch();
    } catch (error) {
      console.error('Error syncing profile data:', error);
      Alert.alert(
        t('common.error', 'Error'), 
        t('orders.errorRefreshing', 'There was a problem refreshing your orders. Please try again.')
      );
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await refetch();
    } catch (error) {
      console.error('Error refreshing orders:', error);
      Alert.alert(
        t('common.error', 'Error'), 
        t('orders.errorRefreshing', 'There was a problem refreshing your orders. Please try again.')
      );
    } finally {
      setIsRefreshing(false);
    }
  };

  const { data: orders, isLoading, error, refetch } = useOrders();

  const handleRealtimeOrderUpdate = useCallback((payload: RealtimePostgresChangesPayload<Order>) => {
    console.log('[DEBUG] Received order update in main handler:', payload);
    
    // Always refetch regardless of payload content
    queryClient.invalidateQueries({ queryKey: ['orders'] });
    
    // Use more forceful refetch options
    queryClient.refetchQueries({ 
      queryKey: ['orders'],
      type: 'active',
      exact: false,
      fetchOptions: { cancelRefetch: false }
    });
    
    if (payload.new && 'id' in payload.new && payload.new.id) {
      queryClient.invalidateQueries({ queryKey: ['order', payload.new.id] });
      queryClient.refetchQueries({ 
        queryKey: ['order', payload.new.id],
        type: 'active'
      });
    }
  }, [queryClient]);

  const handleRealtimeAssignmentUpdate = useCallback((payload: RealtimePostgresChangesPayload<DriverAssignment>) => {
    console.log('[DEBUG] Received assignment update in main handler:', payload);
    
    // Always refetch orders regardless of payload content
    queryClient.invalidateQueries({ queryKey: ['orders'] });
    queryClient.refetchQueries({ 
      queryKey: ['orders'],
      type: 'active',
      exact: false,
      fetchOptions: { cancelRefetch: false }
    });
    
    if (payload.new && 'order_id' in payload.new && payload.new.order_id) {
      queryClient.invalidateQueries({ queryKey: ['order', payload.new.order_id] });
      queryClient.refetchQueries({ 
        queryKey: ['order', payload.new.order_id],
        type: 'active'
      });
    }
  }, [queryClient]);

  const { error: realtimeError } = useOrderRealtime(
    handleRealtimeOrderUpdate,
    handleRealtimeAssignmentUpdate
  );
  if (realtimeError) {
    console.error('🔍 REALTIME ERROR:', realtimeError);
  }
  

  const sortedOrders = React.useMemo(() => {
    if (!orders) return [];
    return [...orders].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }, [orders]);

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return isDarkMode ? colors.brandColors.warning[400] : colors.brandColors.warning[500];
      case 'confirmed':
        return isDarkMode ? colors.brandColors.info[400] : colors.brandColors.info[500];
      case 'delivered':
        return isDarkMode ? colors.brandColors.success[400] : colors.brandColors.success[500];
      case 'completed':
        return isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500];
      case 'cancelled':
        return isDarkMode ? colors.brandColors.danger[400] : colors.brandColors.danger[500];
      default:
        return isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500];
    }
  };

  const dynaoimcStyles = React.useMemo(() => ({
    container: {
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
    },
  }), [isRTL, isDarkMode]);

  const renderOrderCard = React.useCallback((order: Order) => (
    <StyledTouchableOpacity
      key={order.id}
      style={{
        marginBottom: 16,
        borderRadius: 12,
        padding: 16,
        backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
        borderWidth: 1,
        borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
      }}
      onPress={() => handleOrderPress(order.id)}
    >
      <NewRTLView style={{
        marginBottom: 8,
        justifyContent: 'space-between'
      }}>
        <NewRTLView style={{ alignItems: 'center', gap: 4 }}>
          <NewRTLText
            style={{
              fontSize: 18,
              fontWeight: '600',
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light
            }}
          >
            {t('orders.orderNumber')}

          </NewRTLText>
          <NewRTLText
            style={{
              fontSize: 18,
              fontWeight: '600',
              color: isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]
            }}
          >
            {"#" + order.id.slice(0, 4)}
          </NewRTLText>
        </NewRTLView>


        <NewRTLView
          style={{
            paddingHorizontal: 12,
            paddingVertical: 4,
            borderRadius: 16,
            backgroundColor: getStatusColor(order.status)
          }}
        >
          <NewRTLText style={{ color: colors.textColors.light, fontWeight: '500' }}>
            {t(`orders.status.${order.status}`)}
          </NewRTLText>
        </NewRTLView>
      </NewRTLView>

      <NewRTLText
        style={{
          marginBottom: 8,
          fontSize: 14,
          color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light
        }}
      >
        {order.delivery_address}
      </NewRTLText>

      <NewRTLView style={{ justifyContent: 'space-between', alignItems: 'center' }}>
        <NewRTLText
          style={{
            fontSize: 14,
            color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light
          }}
        >
          {formatDate(order.delivery_date)}
        </NewRTLText>
        <NewRTLView style={{ alignItems: 'center', flexDirection: isRTL ? 'row' : 'row-reverse' }}>

          <NewRTLText
            style={{
              fontWeight: '700',
              fontSize: 20,
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light
            }}
          >
            {'' + order.total_amount}
          </NewRTLText>
          {isDarkMode ? (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                  style={{ width: 20, height: 20, marginRight: 2 }}
                  resizeMode="contain"
                />) : (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                    style={{ width: 20, height: 20, marginRight: 2 }}
                  resizeMode="contain"
                />)}
        </NewRTLView>

      </NewRTLView>
    </StyledTouchableOpacity>
  ), [isDarkMode, t, router, isRTL]);

  return (
    <SafeAreaView style={{
      flex: 1,
      backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
    }}>
      <StyledView className="flex-row items-center px-5 py-4 border-b"
        style={{
          borderBottomColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          flexDirection: isRTL ? 'row' : 'row-reverse'
        }}>
        <StyledTouchableOpacity
          className="w-10 h-10 justify-center items-center"
          onPress={() => router.back()}
        >
          <Feather
            name={isRTL ? "arrow-left" : "arrow-right"}
            size={24}
            color={isDarkMode ? colors.textColors.dark : colors.textColors.light}
          />
        </StyledTouchableOpacity>
        <StyledText
          className="flex-1 text-xl font-semibold "
          style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'left' : 'right' }}
        >
          {t('orders.title')}
        </StyledText>
      </StyledView>

      {realtimeError && (
         <StyledView style={{ padding: 10, backgroundColor: colors.brandColors.danger[500] }}>
             <StyledText style={{ color: 'white', textAlign: 'center' }}>
                 {t('common.realtimeError', 'Realtime connection issue: {{msg}}', { msg: realtimeError })}
             </StyledText>
         </StyledView>
      )}

      {isLoading || isRefreshing ? (
        <NewRTLView style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <ActivityIndicator
            size="large"
            color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]}
          />
          <NewRTLText
            style={{
              marginTop: 16,
              fontSize: 16,
              color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light
            }}
          >
            {t('orders.loadingState.description')}
          </NewRTLText>
        </NewRTLView>
      ) : error ? (
        <NewRTLView style={{ flex: 1, alignItems: 'center', flexDirection:'column', justifyContent: 'center', paddingHorizontal: 20 }}>
          <Feather
            name="alert-circle"
            size={48}
            color={isDarkMode ? colors.brandColors.danger[400] : colors.brandColors.danger[500]}
          />
          <NewRTLText
            style={{
              marginTop: 16,
              fontSize: 18,
              fontWeight: '500',
              textAlign: 'center',
              color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light
            }}
          >
            {t('orders.errorState.title')}
          </NewRTLText>
          <NewRTLText
            style={{
              marginTop: 8,
              fontSize: 14,
              textAlign: 'center',
              color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light
            }}
          >
            {t('orders.errorState.description')}
          </NewRTLText>
          <TouchableOpacity
            style={{
              marginTop: 24,
              paddingVertical: 12,
              paddingHorizontal: 24,
              backgroundColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[500],
              borderRadius: 8
            }}
            onPress={syncProfileData}
          >
            <NewRTLText style={{ color: 'white', fontWeight: '600' }}>
              {t('common.syncAndTryAgain', 'Sync profile and try again')}
            </NewRTLText>
          </TouchableOpacity>
        </NewRTLView>
      ) : orders && orders.length > 0 ? (
        <StyledScrollView 
          style={{ flex: 1, padding: 20 }}
        >
          {sortedOrders.map(renderOrderCard)}

          <TouchableOpacity
            style={{
              marginTop: 20,
              marginBottom: 20,
              alignSelf: 'center',
              paddingVertical: 12,
              paddingHorizontal: 24,
              borderRadius: 8,
              backgroundColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[500],
              opacity: isRefreshing ? 0.5 : 1,
            }}
            onPress={handleRefresh}
            disabled={isRefreshing}
          >
            <NewRTLView style={{ alignItems: 'center', gap: 8 }}>
              {isRefreshing ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Feather name="refresh-cw" size={16} color="white" />
              )}
              <NewRTLText style={{ color: 'white', fontWeight: '600' }}>
                {isRefreshing ? t('common.reloading', 'Reloading...') : t('common.reloadOrders', 'Reload orders')}
              </NewRTLText>
            </NewRTLView>
          </TouchableOpacity>
        </StyledScrollView>
      ) : (
        <NewRTLView style={{ flex: 1, flexDirection: 'column', alignItems: 'center', justifyContent: 'center', paddingHorizontal: 20 }}>
          <Feather
            name="shopping-bag"
            size={48}
            color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
          />
          <NewRTLText
            style={{
              marginTop: 16,
              fontSize: 18,
              fontWeight: '500',
              textAlign: 'center',
              color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light
            }}
          >
            {t('orders.emptyState.title')}
          </NewRTLText>
          <NewRTLText
            style={{
              marginTop: 8,
              fontSize: 14,
              textAlign: 'center',
              color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light
            }}
          >
            {t('orders.emptyState.description')}
          </NewRTLText>
          <TouchableOpacity
            style={{
              marginTop: 24,
              paddingVertical: 12,
              paddingHorizontal: 24,
              backgroundColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[500],
              borderRadius: 8
            }}
            onPress={() => router.push('/dumpster-selection')}
          >
            <NewRTLText style={{ color: 'white', fontWeight: '600' }}>
              {t('orders.emptyState.actionButton')}
            </NewRTLText>
          </TouchableOpacity>
        </NewRTLView>
      )}
    </SafeAreaView>
  );
} 
