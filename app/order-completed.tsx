import React, { useEffect } from 'react';
import { View, Text, StatusBar } from 'react-native';
import { styled } from 'nativewind';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Button, Divider } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import { shadowStyles } from '@/utils/shadowStyles';
import { useTranslation } from 'react-i18next';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/lib/supabase';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledSafeAreaView = styled(SafeAreaView);
const StyledLinearGradient = styled(LinearGradient);
const StyledDivider = styled(Divider);

export default function OrderCompletedScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { isDarkMode } = useTheme();
  const params = useLocalSearchParams();
  const [order, setOrder] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        // Debug logs
        console.log('Fetching order with ID:', params.orderId);
        
        if (!params.orderId) {
          throw new Error('No order ID provided');
        }

        const { data, error } = await supabase
          .from('orders')
          .select(`
            *,
            dumpster:dumpster_id(*)
          `)
          .eq('id', params.orderId)
          .single();

        console.log('Supabase response:', { data, error });

        if (error) throw error;
        if (!data) throw new Error('Order not found');

        setOrder(data);
      } catch (err: any) {
        console.error('Error fetching order:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [params.orderId]);

  // Play haptic feedback when screen loads
  useEffect(() => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }, []);

  const handleGoHome = () => {
    router.replace('/');
  };

  const handleViewOrders = () => {
    router.replace('/orders');
  };

  if (loading) {
    return (
      <StyledLinearGradient
        className="flex-1"
        colors={isDarkMode ? ['#1a202c', '#1a202c'] : ['#f5f7fa', '#e8f0fe']}
      >
        <StyledView className="flex-1 items-center justify-center">
          <StyledText className="text-lg">
            {t('orders.loadingDetails')}
          </StyledText>
        </StyledView>
      </StyledLinearGradient>
    );
  }

  if (error || !order) {
    return (
      <StyledLinearGradient
        className="flex-1"
        colors={isDarkMode ? ['#1a202c', '#1a202c'] : ['#f5f7fa', '#e8f0fe']}
      >
        <StyledView className="flex-1 items-center justify-center px-6">
          <MaterialIcons 
            name="error-outline" 
            size={48} 
            color={isDarkMode ? colors.brandColors.danger[300] : colors.brandColors.danger[500]} 
          />
          <StyledText 
            className="text-xl font-bold mt-4 mb-2"
            style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
          >
            {t('orders.errorLoadingOrder')}
          </StyledText>
          <Button
            mode="contained"
            onPress={handleGoHome}
            style={{ marginTop: 16 }}
          >
            {t('common.goBackToHome')}
          </Button>
        </StyledView>
      </StyledLinearGradient>
    );
  }

  // Format the delivery date
  const deliveryDate = new Date(order.delivery_date);
  const formattedDeliveryDate = deliveryDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <StyledLinearGradient
      className="flex-1"
      colors={isDarkMode ? ['#1a202c', '#1a202c', '#1a202c'] : ['#f5f7fa', '#e8f0fe', '#e1eafc']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <StyledView className="flex-1 items-center justify-center px-6">
        <StyledView 
          className="w-full p-8 rounded-2xl items-center"
          style={[
            shadowStyles.md, 
            { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }
          ]}
        >
          <StyledView className="w-28 h-28 mb-6 items-center justify-center rounded-full"
            style={{ backgroundColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[50] }}
          >
            <MaterialIcons 
              name="check-circle" 
              size={80} 
              color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[500]} 
            />
          </StyledView>
          
          <StyledText 
            className="text-2xl font-bold mb-3 text-center"
            style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
          >
            {t('Order Completed!')}
          </StyledText>
          
          <StyledText 
            className="text-base text-center mb-6"
            style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}
          >
            {t('Your dumpster has been successfully ordered. We will deliver it on your selected date.')}
          </StyledText>
          
          <StyledDivider className="w-full mb-6" />
          
          <StyledView className="w-full mb-6">
            <StyledText 
              className="text-lg font-semibold mb-4"
              style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
            >
              {t('Order Summary')}
            </StyledText>
            
            <StyledView className="flex-row justify-between mb-2">
              <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                {t('Order ID')}
              </StyledText>
              <StyledText 
                className="font-medium"
                style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
              >
                #{order.id.slice(0, 8)}
              </StyledText>
            </StyledView>
            
            <StyledView className="flex-row justify-between mb-2">
              <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                {t('Delivery Date')}
              </StyledText>
              <StyledText 
                className="font-medium"
                style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
              >
                {formattedDeliveryDate}
              </StyledText>
            </StyledView>
            
            <StyledView className="flex-row justify-between">
              <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                {t('Total Amount')}
              </StyledText>
              <StyledView className="flex-row items-center">
                <StyledText 
                  className="font-bold"
                  style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
                >
                  SAR {order.total_amount || order.base_price}
                </StyledText>
              </StyledView>
            </StyledView>
          </StyledView>
          
          <StyledView className="w-full gap-3">
            <Button
              mode="contained"
              onPress={handleViewOrders}
              contentStyle={{ paddingVertical: 8 }}
              labelStyle={{ fontSize: 16, fontWeight: 'bold' }}
              style={{ 
                backgroundColor: isDarkMode 
                  ? colors.brandColors.primary[400] 
                  : colors.brandColors.primary[500]
              }}
            >
              {t('View My Orders')}
            </Button>
            
            <Button
              mode="outlined"
              onPress={handleGoHome}
              contentStyle={{ paddingVertical: 8 }}
              labelStyle={{ fontSize: 16 }}
              style={{ 
                borderColor: isDarkMode 
                  ? colors.brandColors.primary[400] 
                  : colors.brandColors.primary[500]
              }}
              textColor={isDarkMode 
                ? colors.brandColors.primary[400] 
                : colors.brandColors.primary[500]
              }
            >
              {t('Go to Home')}
            </Button>
          </StyledView>
        </StyledView>
      </StyledView>
      
      <StyledSafeAreaView edges={['bottom']} />
    </StyledLinearGradient>
  );
} 
