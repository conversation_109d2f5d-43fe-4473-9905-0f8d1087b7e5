import React, { useState } from "react";
import { View, Text, Image } from "react-native";
import { styled } from 'nativewind';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { brandColors, textColors } from '@/theme/colors';
import { ActivityIndicator } from 'react-native';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledImage = styled(Image);

interface OnboardingContentProps {
  title: string;
  image?: string;
}

export const OnboardingContent: React.FC<OnboardingContentProps> = ({
  title,
  image,
}) => {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [isLoading, setIsLoading] = useState(true);
  return (
    <StyledView className="flex-1 justify-center items-center px-6">
      {image && (
        <StyledView className="mb-8">
          <StyledImage
            source={{ uri: image }}
            className="w-64 h-64"
            accessible={true}
            accessibilityRole="image"
            accessibilityLabel="Onboarding illustration"
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
          />
          {isLoading && (
            <StyledView className="absolute inset-0 justify-center rounded-full items-center">
              <ActivityIndicator 
                size="large" 
                color={isDarkMode ? brandColors.primary[400] : brandColors.primary[500]} 
              />
            </StyledView>
          )}
        </StyledView>
      )}
      <StyledText
        style={{
          color: isDarkMode ? textColors.dark : textColors.light,
          textAlign: isRTL ? 'right' : 'left',
        }}
        className="text-center text-xl font-semibold mb-6"
        accessible={true}
        accessibilityRole="header"
      >
        {title}
      </StyledText>
    </StyledView>
  );
};

export default OnboardingContent;
