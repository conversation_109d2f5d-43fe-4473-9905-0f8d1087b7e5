import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { styled } from 'nativewind';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { brandColors, textColors } from '@/theme/colors';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledText = styled(Text);

interface NavigationButtonsProps {
  onSkip: () => void;
  onNext: () => void;
}

export const NavigationButtons: React.FC<NavigationButtonsProps> = ({
  onSkip,
  onNext,
}) => {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  return (
    <StyledView className="flex-row justify-between items-center px-6 py-4">
      <StyledTouchableOpacity
        onPress={onSkip}
        style={{
          padding: 10,
        }}
      >
        <StyledText
          style={{
            color: isDarkMode ? textColors.dark : textColors.light,
            textAlign: isRTL ? 'right' : 'left',
          }}
          className="text-base"
        >
          Skip
        </StyledText>
      </StyledTouchableOpacity>

      <StyledTouchableOpacity
        onPress={onNext}
        style={{
          backgroundColor: brandColors.primary[500],
          paddingHorizontal: 20,
          paddingVertical: 10,
          borderRadius: 20,
        }}
      >
        <StyledText
          style={{
            color: textColors.dark,
            textAlign: isRTL ? 'right' : 'left',
          }}
          className="text-base font-semibold"
        >
          Next
        </StyledText>
      </StyledTouchableOpacity>
    </StyledView>
  );
};

export default NavigationButtons;
