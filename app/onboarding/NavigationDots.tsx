import React from "react";
import { View } from "react-native";
import { styled } from 'nativewind';
import { useTheme } from '@/context/ThemeContext';
import { brandColors } from '@/theme/colors';

const StyledView = styled(View);

interface NavigationDotsProps {
  totalDots: number;
  activeDotIndex: number;
}

export const NavigationDots: React.FC<NavigationDotsProps> = ({
  totalDots,
  activeDotIndex,
}) => {
  const { isDarkMode } = useTheme();

  return (
    <StyledView
      className="flex-row justify-center items-center space-x-2"
      accessible={true}
      accessibilityRole="tablist"
    >
      {Array.from({ length: totalDots }).map((_, index) => (
        <StyledView
          key={index}
          style={{
            width: 8,
            height: 8,
            borderRadius: 4,
            backgroundColor: index === activeDotIndex
              ? brandColors.primary[500]
              : isDarkMode
                ? brandColors.grayScale[700]
                : brandColors.grayScale[300],
          }}
        />
      ))}
    </StyledView>
  );
};

export default NavigationDots;
