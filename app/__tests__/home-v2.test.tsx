import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import HomeScreenV2 from '../home-v2';

// Mock the hooks
jest.mock('@/context/AuthContext', () => ({
  useAuth: () => ({
    session: { user: { id: 'test-user-id' } },
  }),
  GlobalUserIdentifier: { profileId: 'test-profile-id' },
}));

jest.mock('@/hooks/useProfile', () => ({
  useProfile: () => ({
    profile: { full_name: 'Test User' },
  }),
}));

jest.mock('@/hooks/v2/useDumpsters', () => ({
  useDumpsters: () => ({
    data: [
      {
        id: '1',
        nameEn: 'Test Dumpster',
        nameAr: 'اختبار حاوية',
        isAvailable: true,
        rating: 4.5,
        reviewCount: 10,
        length: 10,
        width: 8,
        height: 6,
        imageUrl: 'https://example.com/dumpster1.jpg'
      },
      {
        id: '2',
        nameEn: 'Another Dumpster',
        nameAr: 'حاوية أخرى',
        isAvailable: true,
        rating: 4.0,
        reviewCount: 5,
        length: 12,
        width: 8,
        height: 6,
        imageUrl: 'https://example.com/dumpster2.jpg'
      },
    ],
    isLoading: false,
    isError: false,
  }),
  useWasteTypes: () => ({
    data: [
      { id: '1', nameEn: 'Construction', nameAr: 'بناء' },
      { id: '2', nameEn: 'Household', nameAr: 'منزلي' },
    ],
  }),
}));

jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
  NewRTLView: 'View',
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { language: 'en' }
  }),
}));

jest.mock('@/utils/user', () => ({
  getUserName: jest.fn().mockResolvedValue('Test User'),
  refreshUserProfile: jest.fn().mockResolvedValue({ full_name: 'Test User' }),
  syncProfileAuthIds: jest.fn(),
  getUserOrders: jest.fn().mockResolvedValue([{ id: '1' }]),
}));

jest.mock('@/utils/notifications', () => ({
  registerForPushNotificationsAsync: jest.fn().mockResolvedValue('test-push-token'),
  savePushToken: jest.fn(),
}));

describe('HomeScreenV2', () => {
  it('renders the greeting and username', async () => {
    const { getByText } = render(<HomeScreenV2 />);

    await waitFor(() => {
      // Check for any of the possible greetings
      const greetingRegex = /(home\.greeting\.morning|home\.greeting\.afternoon|home\.greeting\.evening)/;
      expect(getByText(greetingRegex)).toBeTruthy();
    });
  });

  it('renders the marketing carousel', () => {
    const { getByTestId } = render(<HomeScreenV2 />);
    expect(getByTestId('marketing-carousel')).toBeTruthy();
  });

  it('renders dumpster cards when data is loaded', async () => {
    const { getAllByTestId } = render(<HomeScreenV2 />);

    await waitFor(() => {
      expect(getAllByTestId('dumpster-card').length).toBe(2);
    });
  });

  it('navigates to orders screen when orders button is pressed', () => {
    const { getByText } = render(<HomeScreenV2 />);
    const router = require('expo-router').useRouter();

    fireEvent.press(getByText('home.viewAll'));
    expect(router.push).toHaveBeenCalledWith('/all-dumpsters');
  });

  it('navigates to dumpster selection when the FAB button is pressed', () => {
    const { getByTestId } = render(<HomeScreenV2 />);
    const router = require('expo-router').useRouter();

    fireEvent.press(getByTestId('tap-to-type-button'));
    expect(router.push).toHaveBeenCalledWith('/dumpster-selection');
  });
});
