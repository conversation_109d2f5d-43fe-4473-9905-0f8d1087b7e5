import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import HomeV4Screen from '../home-v4';

// Mock the hooks
jest.mock('@/context/AuthContext', () => ({
  useAuth: () => ({
    session: { user: { id: 'test-user-id' } },
  }),
  GlobalUserIdentifier: { profileId: 'test-profile-id' },
}));

jest.mock('@/hooks/useProfile', () => ({
  useProfile: () => ({
    profile: {
      id: 'test-profile-id',
      first_name: 'Test',
      last_name: 'User',
    },
  }),
}));

jest.mock('@/hooks/v2/useDumpsters', () => ({
  useDumpsters: () => ({
    data: [
      {
        id: '1',
        nameEn: 'Test Dumpster',
        nameAr: 'اختبار حاوية',
        isAvailable: true,
        rating: 4.5,
        reviewCount: 10,
        length: 10,
        width: 8,
        height: 6,
        imageUrl: 'https://example.com/dumpster1.jpg',
        pricePerLoad: 250,
        capacity: 10,
        maxWeight: 2000,
        standingArea: 80,
        sizeId: 'size-1',
      },
      {
        id: '2',
        nameEn: 'Another Dumpster',
        nameAr: 'حاوية أخرى',
        isAvailable: true,
        rating: 4.0,
        reviewCount: 5,
        length: 12,
        width: 8,
        height: 6,
        imageUrl: 'https://example.com/dumpster2.jpg',
        pricePerLoad: 300,
        capacity: 12,
        maxWeight: 2500,
        standingArea: 96,
        sizeId: 'size-2',
      },
    ],
    isLoading: false,
    isError: false,
  }),
  useWasteTypes: () => ({
    data: [
      { id: '1', nameEn: 'Construction', nameAr: 'بناء' },
      { id: '2', nameEn: 'Household', nameAr: 'منزلي' },
    ],
  }),
}));

jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
  NewRTLView: 'View',
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('react-native-reanimated', () => {
  const Reanimated = jest.requireActual('react-native-reanimated/mock');

  // Add useAnimatedStyle mock
  Reanimated.useAnimatedStyle = jest.fn(() => ({}));

  // Add interpolate mock
  Reanimated.interpolate = jest.fn((value, inputRange, outputRange) => {
    // Simulate interpolation based on scroll position
    if (value < inputRange[0]) return outputRange[0];
    if (value > inputRange[1]) return outputRange[1];
    return outputRange[0] + ((value - inputRange[0]) / (inputRange[1] - inputRange[0])) * (outputRange[1] - outputRange[0]);
  });

  // Add useSharedValue mock with listener support
  Reanimated.useSharedValue = jest.fn((initialValue) => ({
    value: initialValue,
    addListener: jest.fn((callback) => {
      // Return a mock unsubscribe function
      return 'mock-unsubscribe-id';
    }),
    removeListener: jest.fn(),
  }));

  // Add useAnimatedScrollHandler mock
  Reanimated.useAnimatedScrollHandler = jest.fn(({ onScroll }) => {
    return jest.fn((event) => {
      if (onScroll) {
        onScroll(event);
      }
    });
  });

  // Add Extrapolation mock
  Reanimated.Extrapolation = {
    CLAMP: 'clamp',
  };

  // Add createAnimatedComponent mock
  Reanimated.createAnimatedComponent = (component) => component;

  return Reanimated;
});

describe('HomeV4Screen', () => {
  it('renders the greeting and username', async () => {
    const { getByText } = render(<HomeV4Screen />);

    await waitFor(() => {
      // Check for greeting text
      expect(getByText(/home\.greeting\.(morning|afternoon|evening)/)).toBeTruthy();
      // Check for username
      expect(getByText('Test User')).toBeTruthy();
    });
  });

  it('renders the hero card', () => {
    const { getByText, getByTestId } = render(<HomeV4Screen />);

    expect(getByTestId('hero-card')).toBeTruthy();
    expect(getByText('Premium Dumpsters')).toBeTruthy();
    expect(getByText('Find the perfect dumpster for your project')).toBeTruthy();
  });

  it('renders the sticky search header', () => {
    const { getByTestId, getByPlaceholderText } = render(<HomeV4Screen />);

    expect(getByTestId('sticky-search-header')).toBeTruthy();
    expect(getByPlaceholderText('Search dumpsters...')).toBeTruthy();
  });

  it('tests sticky behavior when scrolling past threshold', () => {
    const { getByTestId } = render(<HomeV4Screen />);
    const header = getByTestId('sticky-search-header');

    // Get the useSharedValue mock
    const useSharedValueMock = require('react-native-reanimated').useSharedValue;
    const scrollYMock = useSharedValueMock.mock.results[0].value;

    // Initially not sticky (below threshold)
    scrollYMock.value = 100;
    // Trigger the listener callback
    const listenerCallback = scrollYMock.addListener.mock.calls[0][0];
    listenerCallback(100);

    // Now scroll past threshold
    scrollYMock.value = 250;
    listenerCallback(250);

    // Verify that the header is now sticky
    expect(header.props.scrollY.value).toBe(250);

    // Scroll back before threshold
    scrollYMock.value = 150;
    listenerCallback(150);

    // Verify that the header is no longer sticky
    expect(header.props.scrollY.value).toBe(150);
  });

  it('renders dumpster tiles when data is loaded', async () => {
    const { getAllByTestId } = render(<HomeV4Screen />);

    await waitFor(() => {
      expect(getAllByTestId('dumpster-tile').length).toBe(2);
    });
  });
});
