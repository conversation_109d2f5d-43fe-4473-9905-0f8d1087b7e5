import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import DiscoverV3Screen from '../discover-v3';
import { useDumpsters, useWasteTypes } from '@/hooks/v2/useDumpsters';

// Mock the hooks
jest.mock('@/hooks/v2/useDumpsters');
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));
jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
}));
jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    reload: jest.fn(),
  }),
  Stack: {
    Screen: () => null,
  },
}));

// Mock react-native-raw-bottom-sheet
jest.mock('react-native-raw-bottom-sheet', () => {
  return {
    __esModule: true,
    default: ({ children, ref }) => {
      // Store the ref methods
      if (ref) {
        ref.current = {
          open: jest.fn(),
          close: jest.fn(),
        };
      }
      return <>{children}</>;
    },
  };
});

describe('DiscoverV3Screen', () => {
  beforeEach(() => {
    (useDumpsters as jest.Mock).mockReturnValue({
      data: [
        {
          id: '1',
          nameEn: '10 Yard Dumpster',
          nameAr: 'حاوية 10 ياردة',
          imageUrl: 'https://example.com/dumpster.jpg',
          length: 10,
          width: 8,
          height: 4,
          pricePerLoad: 250,
          rating: 4.5,
          reviewCount: 12,
          isAvailable: true,
          sizeId: 'size-1',
        },
        {
          id: '2',
          nameEn: '20 Yard Dumpster',
          nameAr: 'حاوية 20 ياردة',
          imageUrl: 'https://example.com/dumpster2.jpg',
          length: 20,
          width: 8,
          height: 4,
          pricePerLoad: 350,
          rating: 4.2,
          reviewCount: 8,
          isAvailable: true,
          sizeId: 'size-2',
        },
      ],
      isLoading: false,
      isError: false,
    });
    
    (useWasteTypes as jest.Mock).mockReturnValue({
      data: [
        { id: 'wt1', nameEn: 'Construction', nameAr: 'بناء' },
        { id: 'wt2', nameEn: 'Household', nameAr: 'منزلي' },
      ],
    });
  });

  it('renders the dumpster grid correctly', () => {
    const { getByTestId } = render(<DiscoverV3Screen />);
    
    expect(getByTestId('dumpster-grid')).toBeTruthy();
  });

  it('shows loading state when dumpsters are loading', () => {
    (useDumpsters as jest.Mock).mockReturnValue({
      data: [],
      isLoading: true,
      isError: false,
    });
    
    const { getByText } = render(<DiscoverV3Screen />);
    
    expect(getByText('loading_dumpsters')).toBeTruthy();
  });

  it('shows error state when dumpster loading fails', () => {
    (useDumpsters as jest.Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: true,
    });
    
    const { getByText } = render(<DiscoverV3Screen />);
    
    expect(getByText('error_loading_dumpsters')).toBeTruthy();
  });
});
