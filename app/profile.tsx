import React, { useMemo, useState, useEffect } from 'react';
import { View, TouchableOpacity, ScrollView, Alert, TextInput, Modal, ActivityIndicator, Image, I18nManager } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useProfile } from '@/hooks/useProfile';
import { GlobalUserIdentifier, useAuth } from '@/context/AuthContext';
import { supabase } from '@/services/supabase';
import { useQueryClient } from '@tanstack/react-query';
import * as ImagePicker from 'expo-image-picker';
import { decode } from 'base64-arraybuffer';
import { useAddresses } from '@/hooks/useAddresses';
import { useTranslation } from 'react-i18next';
import * as colors from '@/theme/colors';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText as NewRTLTextComponent } from '@/components/rtl/NewRTLText';
import { getUserProfile, refreshUserProfile, syncProfileAuthIds, getUserAddresses, UserProfile } from '@/utils/user';
import { Profile } from '@/types/profile';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { useTheme } from '@/context/ThemeContext';

export default function ProfileScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { session } = useAuth();
  const { profile: hookProfile, isLoading: isLoadingHookProfile, error: hookError, updateProfile } = useProfile(session?.user?.id || '');
  const queryClient = useQueryClient();
  const [isPhoneModalVisible, setPhoneModalVisible] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isAddressModalVisible, setAddressModalVisible] = useState(false);
  const [addressInput, setAddressInput] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [isNameModalVisible, setNameModalVisible] = useState(false);
  const [fullName, setFullName] = useState('');
  const { addresses: hookAddresses, isLoading: isLoadingAddresses } = useAddresses();
  const { isRTL } = useRTLContext();
  const { isDarkMode } = useTheme();
  
  // New state to store profile from our enhanced utility
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [addresses, setAddresses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Manual profile sync function that will be called by a button instead of on mount
  const syncProfile = async () => {
    try {
      if (session?.user?.id) {
        await syncProfileAuthIds(false).catch(console.error);
        await refreshUserProfile(false);
        // Invalidate queries to refetch data
        queryClient.invalidateQueries({ queryKey: ['profile'] });
      }
    } catch (error) {
      console.error('Error syncing profile:', error);
    }
  };

  // Don't sync auth IDs on component mount - only load existing data
  useEffect(() => {
    // Removed profile sync to avoid read-only transaction errors
  }, [session?.user?.id]);

  // Convert hookProfile to UserProfile type
  const convertProfileToUserProfile = (hookProfile: Profile): UserProfile => {
    return {
      id: hookProfile.id,
      email: hookProfile.email || undefined,
      phone: hookProfile.phone || undefined,
      full_name: hookProfile.full_name || undefined,
      avatar_url: hookProfile.avatar_url || undefined,
      user_type: hookProfile.user_type,
      created_at: hookProfile.created_at?.toString() || undefined,
      updated_at: hookProfile.updated_at?.toString() || undefined,
      // Add any custom fields here
      address: undefined // This will be loaded separately
    };
  };

  // Load profile data using our enhanced utilities
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        setIsLoading(true);
        
        // Try to get profile from local storage only to avoid database writes
        const profileJson = await AsyncStorage.getItem('@app:user_profile');
        if (profileJson) {
          const userProfile = JSON.parse(profileJson);
          setProfile(userProfile);
          setPhoneNumber(userProfile.phone || '');
          setFullName(userProfile.full_name || '');
          
          // Get address from metadata
          try {
            const address = await AsyncStorage.getItem('@app:user_address');
            setAddressInput(address || '');
          } catch (e) {
            console.error('Error loading address:', e);
          }
        } else if (hookProfile) {
          // Fallback to hook data if our utility fails
          setProfile(convertProfileToUserProfile(hookProfile));
          setPhoneNumber(hookProfile.phone || '');
          setFullName(hookProfile.full_name || '');
        } else {
          // Just use empty state instead of trying to fetch from database
          console.log('No profile data available in local storage or hook');
        }
        
        // Get addresses from the hook directly
        if (hookAddresses && hookAddresses.length > 0) {
          setAddresses(hookAddresses);
        } else {
          setAddresses([]);
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('Error loading profile data:', err);
        setError(err instanceof Error ? err : new Error(String(err)));
        setIsLoading(false);
      }
    };
    
    loadProfileData();
  }, [hookProfile, hookAddresses]);

  const ADDRESS_TYPES = [
    { value: 'home', label: 'Home', icon: 'home' },
    { value: 'office', label: 'Office', icon: 'briefcase' },
    { value: 'other', label: 'Other', icon: 'map-marker' },
  ] as const;
  
  // Create RTL-aware dynamic styles
  const dynamicStyles = useMemo(() => ({
    container: {
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
    },
  }), [isRTL]);

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets && result.assets[0].base64) {
        setUploadingImage(true);
        await uploadProfilePicture(result.assets[0].base64, `image/${result.assets[0].uri.split('.').pop()}`);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    } finally {
      setUploadingImage(false);
    }
  };

  const invalidateProfile = () => {
    queryClient.invalidateQueries({ queryKey: ['profile', session?.user?.id] });
    // Also refresh profile data in AsyncStorage
    refreshUserProfile(false).catch(console.error);
  };

  const handleNameUpdate = async () => {
    if (!fullName.trim()) {
      Alert.alert('Error', 'Please enter a valid name');
      return;
    }

    setIsUpdating(true);
    try {
      await updateProfile({
        full_name: fullName.trim()
      });
      setNameModalVisible(false);
      invalidateProfile();
      
      // Update local state
      if (profile) {
        setProfile({
          ...profile,
          full_name: fullName.trim()
        });
      }
      
      Alert.alert('Success', 'Name updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update name');
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePhoneUpdate = async () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    setIsUpdating(true);
    try {
      await updateProfile({
        phone: phoneNumber.trim()
      });
      setPhoneModalVisible(false);
      invalidateProfile();
      
      // Update local state
      if (profile) {
        setProfile({
          ...profile,
          phone: phoneNumber.trim()
        });
      }
      
      Alert.alert('Success', 'Phone number updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update phone number');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAddressUpdate = async () => {
    setIsUpdating(true);
    try {
      // Handle address update - we can store this in user_metadata
      // since it's not in the Profile type
      if (session?.user?.id) {
        // Store address in user metadata
        await supabase.auth.updateUser({
          data: { address: addressInput }
        });
        
        // Update local state
        if (profile) {
          setProfile({
            ...profile,
            address: addressInput
          });
        }
        
        // Also store in AsyncStorage for immediate access
        const userProfile = await getUserProfile();
        if (userProfile) {
          await AsyncStorage.setItem('@app:user_profile', JSON.stringify({
            ...userProfile,
            address: addressInput
          }));
          await AsyncStorage.setItem('@app:user_address', addressInput);
        }
        
        setAddressModalVisible(false);
        Alert.alert('Success', 'Address updated successfully');
      } else {
        throw new Error('No authenticated user');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update address');
    } finally {
      setIsUpdating(false);
    }
  };

  const uploadProfilePicture = async (base64Image: string, mimeType: string) => {
    try {
      if (!session?.user?.id) {
        throw new Error('User not authenticated');
      }

      // Upload image to storage
      const fileName = `avatar-${GlobalUserIdentifier.profileId}-${Date.now()}`;
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, decode(base64Image), {
          contentType: mimeType,
          upsert: true,
        });

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = await supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      if (!urlData?.publicUrl) {
        throw new Error('Failed to get public URL');
      }

      // Update profile with avatar URL
      await updateProfile({
        avatar_url: urlData.publicUrl,
      });

      // Update local state
      if (profile) {
        setProfile({
          ...profile,
          avatar_url: urlData.publicUrl
        });
      }

      // Refresh profile data in AsyncStorage
      await refreshUserProfile(false);
      
      invalidateProfile();
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      Alert.alert('Error', 'Failed to upload profile picture');
    }
  };

  // Handle settings
  const handleSettings = () => {
    // Navigate to settings screen
    router.push('/settings');
  };

  // Handle payment method
  const handleAddPayment = () => {
    Alert.alert(
      'Add Payment Method',
      'This feature will be available soon',
      [{ text: 'OK' }]
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <NewRTLText style={{ fontSize: 16, color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: 'center', marginBottom: 20 }}>
          Failed to load profile
        </NewRTLText>
        <TouchableOpacity
          onPress={() => router.back()}
          style={{
            backgroundColor: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500],
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8
          }}
        >
          <NewRTLText style={{ color: isDarkMode ? colors.textColors.light : colors.textColors.dark, fontSize: 16 }}>Go Back</NewRTLText>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }}>
      <View style={{
        flexDirection: dynamicStyles.container.flexDirection,
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingTop: 10,
        backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
        borderBottomWidth: 1,
        borderBottomColor: isDarkMode ? colors.backgroundColors.outline.dark : colors.backgroundColors.outline.light,
        paddingVertical: 16,
      }}>
        <TouchableOpacity onPress={() => router.back()}>
          <Feather name={isRTL ? "arrow-right" : "arrow-left"} size={24} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </TouchableOpacity>
        <NewRTLText style={{
          flex: 1,
          fontSize: 20,
          fontWeight: '600',
          color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
          marginLeft: 15
        }}>
          {t('profile.title', 'Profile')}
        </NewRTLText>
        <TouchableOpacity onPress={handleSettings}>
          <Feather name="settings" size={24} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Profile Header */}
        <View style={{ padding: 20, alignItems: 'center' }}>
          <TouchableOpacity
            onPress={pickImage}
            disabled={uploadingImage}
            style={{
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: '#f5f5f5',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 15
            }}
          >
            {uploadingImage ? (
              <ActivityIndicator color="#2196f3" />
            ) : profile?.avatar_url ? (
              <Image
                source={{ uri: profile.avatar_url }}
                style={{ width: '100%', height: '100%', borderRadius: 40 }}
              />
            ) : (
              <View style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: '#f5f5f5',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Feather name="user" size={40} color="#666" />
              </View>
            )}
            <View style={{
              position: 'absolute',
              right: isRTL ? 55 : -5,
              bottom: -5,
              backgroundColor: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500],
              width: 26,
              height: 26,
              borderRadius: 13,
              justifyContent: 'center',
              alignItems: 'center'
            }}>
              <Feather name="camera" size={14} color={isDarkMode ? colors.brandColors.secondary[700] : colors.textColors.dark} />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => setNameModalVisible(true)}
            style={{ alignItems: 'center' }}
          >
            <NewRTLText style={{ fontSize: 24, fontWeight: '600', color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
              {profile?.full_name || 'Your Name'}
            </NewRTLText>
            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginTop: 5 }}>
              <Feather name="edit-2" size={14} color={isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500]} style={{ marginRight: isRTL ? 0 : 6, marginLeft: isRTL ? 6 : 0 }} />
              <NewRTLText style={{ fontSize: 14, color: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500] }}>
                {t('profile.editName', 'Edit Name')}
              </NewRTLText>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              marginTop: 15,
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
              paddingVertical: 12,
              paddingHorizontal: 18,
              borderRadius: 100
            }}
          >
            <Feather name="phone" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} style={{ marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0, transform: isRTL ? [{ scaleX: -1 }] : [] }} />
            <NewRTLText style={{ fontSize: 16, color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
              {profile?.phone || '05XXXXXXXX'}
            </NewRTLText>
          </TouchableOpacity>
        </View>

        {/* My Address */}
        <View style={{ padding: 20 }}>
          <View style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 8,
            paddingVertical: 12
          }}>
            <NewRTLText style={{ fontSize: 20, fontWeight: '600', color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
              {t('profile.addresses.title', 'My addresses')}
            </NewRTLText>
            <TouchableOpacity
              onPress={() => router.push('/addresses')}
              style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}
            >
              <NewRTLText style={{ color: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500], fontSize: 14 }}>
                {addresses && addresses.length > 0 
                  ? t('profile.addresses.viewAll', 'View All') 
                  : t('profile.addresses.addNew', 'Add New')}
              </NewRTLText>
              {/* <Feather name={addresses && addresses.length > 0 ? "chevron-right" : "plus"} size={20} color={isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500]} style={{ marginRight: isRTL ? 5 : 0, marginLeft: isRTL ? 0 : 5, transform: [{ scaleX: isRTL ? -1 : 1 }] }} /> */}
              
            </TouchableOpacity>
          </View>

          {isLoadingAddresses ? (
            <ActivityIndicator color="#2196f3" style={{ marginVertical: 20 }} />
          ) : addresses && addresses.length > 0 ? (
            (() => {
              // Find default address or get the most recent one
              const defaultAddress = addresses.find(addr => addr.is_default) || addresses[0];
              
              return (
                <TouchableOpacity
                  key={defaultAddress.id}
                  onPress={() => router.push('/addresses')}
                  style={{
                    flexDirection: isRTL ? 'row-reverse' : 'row',
                    alignItems: 'center',
                    backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
                    padding: 15,
                    borderRadius: 12,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.05,
                    shadowRadius: 3,
                    elevation: 2,
                  }}
                >
                  <View style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: isRTL ? 0 : 15,
                    marginLeft: isRTL ? 15 : 0
                  }}>
                    <Feather 
                      name={[ADDRESS_TYPES.find(type => type.value === defaultAddress.type)?.icon || 'map-marker'] as any} 
                      size={20} 
                      color={isDarkMode ? colors.textColors.dark : colors.textColors.light} 
                    />
                  </View>
                  <View style={{ flex: 1 }}>
                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
                      <NewRTLText style={{ 
                        color: isDarkMode ? colors.textColors.dark : colors.textColors.light, 
                        fontSize: 16, 
                        marginBottom: 4,
                        marginRight: isRTL ? 0 : 8,
                        marginLeft: isRTL ? 8 : 0
                      }}>
                        {defaultAddress.name || defaultAddress.street_address}
                      </NewRTLText>
                      {defaultAddress.is_default && (
                        <View style={{
                          backgroundColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[100],
                          paddingHorizontal: 8,
                          paddingVertical: 3,
                          borderRadius: 12,
                          marginBottom: 4,
                        }}>
                          <NewRTLText style={{
                            fontSize: 12,
                            color: isDarkMode ? colors.brandColors.primary[200] : colors.brandColors.primary[700]
                          }}>
                            {t('profile.addresses.default', 'Default')}
                          </NewRTLText>
                        </View>
                      )}
                    </View>
                    <NewRTLText style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, fontSize: 14 }}>
                      {defaultAddress.street_address}, {defaultAddress.city}, {defaultAddress.state} {defaultAddress.zip_code}
                    </NewRTLText>
                  </View>
                  <Feather 
                    name={isRTL ? 'chevron-left' : 'chevron-right'} 
                    size={20} 
                    color={isDarkMode ? colors.textColors.dark : colors.textColors.light} 
                  />
                </TouchableOpacity>
              );
            })()
          ) : (
            <TouchableOpacity
              onPress={() => router.push('/addresses')}
              style={{
                flexDirection: 'column',
                alignItems: 'center',
                backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
                borderWidth: 1,
                borderColor: isDarkMode ? colors.backgroundColors.outline.dark : colors.backgroundColors.outline.light,
                padding: 15,
                borderRadius: 12,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.05,
                shadowRadius: 3,
                elevation: 2
              }}
            >
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: isDarkMode ? colors.surfaceColors.onPrimaryContainer.dark : colors.surfaceColors.onPrimaryContainer.light,
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 10
              }}>
                <Feather name="map-pin" size={20} color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[500]} />
              </View>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <NewRTLText style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                  {t('profile.addresses.addFirst', 'Add your first address')}
                </NewRTLText>
                <NewRTLText style={{ color: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500], marginTop: 5, fontSize: 14 }}>
                  {t('profile.addresses.tapAdd', 'Tap to add address')}
                </NewRTLText>
              </View>
            </TouchableOpacity>
          )}
        </View>

        {/* Payment Methods */}
        <View style={{ padding: 20 }}>
          <NewRTLText style={{ fontSize: 20, fontWeight: '600', color: isDarkMode ? colors.textColors.dark : colors.textColors.light, marginBottom: 15 }}>
            {t('profile.payment.title', 'My payment methods')}
          </NewRTLText>
          <View style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            marginBottom: 15
          }}>
            <Feather name="shield" size={20} color={isDarkMode ? colors.brandColors.success[300] : colors.brandColors.success[500]} />
            <NewRTLText style={{ color: isDarkMode ? colors.brandColors.success[300] : colors.brandColors.success[500], marginLeft: 10 }}>
              {t('profile.payment.protected', 'Payment information is protected')}
            </NewRTLText>
          </View>
          <TouchableOpacity
            onPress={handleAddPayment}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
              padding: 15,
              borderRadius: 12,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.05,
              shadowRadius: 3,
              elevation: 2
            }}
          >
            <View style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 15
            }}>
              <Feather name="credit-card" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
            </View>
            <NewRTLText style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, flex: 1 }}>
              {t('profile.payment.addCard', 'Add new credit card')}
            </NewRTLText>
          </TouchableOpacity>
        </View>

        {/* Help Section */}
        <View style={{ padding: 20 }}>
          <NewRTLText style={{ fontSize: 20, fontWeight: '600', color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light, marginBottom: 20 }}>
            {t('profile.help.title', 'Need help?')}
          </NewRTLText>
          <NewRTLText style={{ fontSize: 24, fontWeight: '600', color: isDarkMode ? colors.textColors.dark : colors.textColors.light, marginBottom: 15 }}>
            {t('profile.help.contact', 'Contact Support')}
          </NewRTLText>
          <TouchableOpacity
            style={{
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
              padding: 20,
              borderRadius: 12,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.05,
              shadowRadius: 3,
              elevation: 2,
              marginBottom: 20
            }}
          >
            <NewRTLText style={{ fontSize: 18, color: isDarkMode ? colors.textColors.dark : colors.textColors.light, marginBottom: 5 }}>
              {t('profile.help.rethink', 'Rethink simplicity')}
            </NewRTLText>
            <NewRTLText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
              {t('profile.help.tired', 'Tired of too many different delivery and pickup services?')}
            </NewRTLText>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Name Update Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={isNameModalVisible}
        onRequestClose={() => setNameModalVisible(false)}
      >
        <View style={{ flex: 1, justifyContent: 'center', backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <View style={{
            margin: 20,
            backgroundColor: 'white',
            borderRadius: 12,
            padding: 20,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5
          }}>
            <NewRTLText style={{ fontSize: 18, fontWeight: '600', marginBottom: 15 }}>
              {t('profile.updateName', 'Update Name')}
            </NewRTLText>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: '#ddd',
                borderRadius: 8,
                padding: 12,
                marginBottom: 20,
              }}
              placeholder="Enter your name"
              value={fullName}
              onChangeText={setFullName}
            />
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity
                onPress={() => setNameModalVisible(false)}
                style={{ marginRight: 15 }}
              >
                <NewRTLText style={{ color: '#666', fontSize: 16 }}>{t('profile.cancel', 'Cancel')}</NewRTLText>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleNameUpdate}
                disabled={isUpdating}
              >
                <NewRTLText style={{ color: '#2196f3', fontSize: 16, fontWeight: '600' }}>
                  {isUpdating ? t('profile.updating', 'Updating...') : t('profile.update', 'Update')}
                </NewRTLText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Phone Number Modal */}
      <Modal
        visible={isPhoneModalVisible}
        animationType="slide"
        transparent={true}
      >
        <View style={{
          flex: 1,
          justifyContent: 'center',
          backgroundColor: 'rgba(0,0,0,0.5)',
          padding: 20
        }}>
          <View style={{
            backgroundColor: '#fff',
            borderRadius: 12,
            padding: 20
          }}>
            <NewRTLText style={{ fontSize: 18, fontWeight: '600', marginBottom: 15 }}>
              {t('profile.updatePhone', 'Update Phone Number')}
            </NewRTLText>
            <TextInput
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              placeholder="Enter phone number"
              keyboardType="phone-pad"
              style={{
                borderWidth: 1,
                borderColor: '#ddd',
                borderRadius: 8,
                padding: 10,
                marginBottom: 15,
              }}
            />
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity
                onPress={() => setPhoneModalVisible(false)}
                style={{ marginRight: 15 }}
              >
                <NewRTLText style={{ color: '#666' }}>{t('profile.cancel', 'Cancel')}</NewRTLText>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handlePhoneUpdate}
                disabled={isUpdating}
              >
                <NewRTLText style={{ color: '#2196f3', fontWeight: '600' }}>
                  {isUpdating ? t('profile.updating', 'Updating...') : t('profile.update', 'Update')}
                </NewRTLText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Address Modal */}
      <Modal
        visible={isAddressModalVisible}
        animationType="slide"
        transparent={true}
      >
        <View style={{
          flex: 1,
          justifyContent: 'center',
          backgroundColor: 'rgba(0,0,0,0.5)',
          padding: 20
        }}>
          <View style={{
            backgroundColor: '#fff',
            borderRadius: 12,
            padding: 20
          }}>
            <NewRTLText style={{ fontSize: 18, fontWeight: '600', marginBottom: 15 }}>
              {t('profile.updateAddress', 'Update Address')}
            </NewRTLText>
            <TextInput
              value={addressInput}
              onChangeText={setAddressInput}
              placeholder="Enter your address"
              multiline
              numberOfLines={3}
              style={{
                borderWidth: 1,
                borderColor: '#ddd',
                borderRadius: 8,
                padding: 10,
                marginBottom: 15,
                height: 80,
              }}
            />
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity
                onPress={() => setAddressModalVisible(false)}
                style={{ marginRight: 15 }}
              >
                <NewRTLText style={{ color: '#666' }}>{t('profile.cancel', 'Cancel')}</NewRTLText>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleAddressUpdate}
                disabled={isUpdating}
              >
                <NewRTLText style={{ color: '#2196f3', fontWeight: '600' }}>
                  {isUpdating ? t('profile.updating', 'Updating...') : t('profile.update', 'Update')}
                </NewRTLText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
} 