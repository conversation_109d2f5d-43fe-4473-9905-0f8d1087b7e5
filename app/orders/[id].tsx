import React, { useEffect, useLayoutEffect } from 'react';
import { View, Text, ScrollView, ActivityIndicator, StatusBar, Image, Alert, TouchableOpacity, LogBox, Linking } from 'react-native';
import { useTranslation } from 'react-i18next';
import { styled } from 'nativewind';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Button, Divider, Card } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons, FontAwesome5, Feather } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { shadowStyles } from '@/utils/shadowStyles';
import { useQuery } from '@tanstack/react-query';
import * as colors from '@/theme/colors';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/lib/supabase';
import { getSafeImageUrl } from '@/utils/dumpsterAdapter';
import { usePartner } from '@/hooks/usePartner';
import { fetchOrderById, useOrder } from '@/hooks/useOrders';
import { useOrderDriver } from '@/hooks/useOrderDriver';
import { useOrderRealtime } from '@/hooks/useOrderRealtime';
import { Order } from '@/types/supabase';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
// No need to import assignDriverToOrder as we're not using it


interface Dumpster {
  id: string;
  name_en: string;
  name_ar: string;
  image_url: string;
}

interface Partner {
  id: string;
  company_name: string;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
}

// Updated to match database schema
// interface Order {
//   id: string;
//   created_at: string;
//   status: 'pending' | 'confirmed' | 'delivered' | 'in_use' | 'pickup_scheduled' | 'completed' | 'cancelled';
//   delivery_address: string;
//   delivery_date: string;
//   total_amount: number;
//   partner_id: string | null;
//   partner?: Partner[] | null; // Supabase returns this as an array
//   customer_id: string;
//   payment_status: 'pending' | 'partial' | 'paid' | 'refunded';
//   payment_method: 'credit_card' | 'cash' | 'bank_transfer' | null;
// }

// Styled components
const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledSafeAreaView = styled(SafeAreaView);
const StyledLinearGradient = styled(LinearGradient);
const StyledDivider = styled(Divider);
const StyledCard = styled(Card);
const StyledTouchableOpacity = styled(TouchableOpacity);

export default function OrderDetailsScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { id: orderId } = useLocalSearchParams<{ id: string }>();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  LogBox.ignoreAllLogs(true);
  const { data: orderDetails, isLoading: orderLoading, error: orderError, refetch } = useOrder(orderId as string); //useOrder(orderId as string);
  console.log('🔍 ORDER DETAILS:', orderDetails);


  // Fetch partner details using the custom hook
  const { data: partner, isLoading: partnerLoading } = usePartner(orderDetails?.partner_id ?? null);
  console.log('🔍 PARTNER DETAILS:', partner);

  // Fetch driver details directly using the custom hook
  const { data: driver, isLoading: driverLoading } = useOrderDriver(orderId as string);
  console.log('🔍 DRIVER DETAILS:', driver);

  const getPaymentIcon = (method: Order['payment_method']) => {
    switch (method) {
      case 'credit_card':
        return <FontAwesome5 name="credit-card" size={18} color={colors.textColors.light} style={{ marginRight: isRTL ? 0 : 5, marginLeft: isRTL ? 5 : 0 }} />;
      case 'cash':
        return <FontAwesome5 name="money-bill-alt" size={18} color={colors.textColors.light} style={{ marginRight: isRTL ? 0 : 5, marginLeft: isRTL ? 5 : 0 }} />;
      case 'bank_transfer':
        return <FontAwesome5 name="university" size={18} color={colors.textColors.light} style={{ marginRight: isRTL ? 0 : 5, marginLeft: isRTL ? 5 : 0 }} />;
      default:
        return null;
    }
  };

  const getPaymentStatusColor = (status: Order['payment_status']) => {
    switch (status) {
      case 'pending':
        return isDarkMode ? colors.brandColors.warning[400] : colors.brandColors.warning[500];
      case 'partial':
        return isDarkMode ? colors.brandColors.info[400] : colors.brandColors.info[500];
      case 'paid':
        return isDarkMode ? colors.brandColors.success[400] : colors.brandColors.success[500];
      case 'refunded':
        return isDarkMode ? colors.brandColors.danger[400] : colors.brandColors.danger[500];
      default:
        return isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500];
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return isDarkMode ? colors.brandColors.warning[400] : colors.brandColors.warning[500];
      case 'confirmed':
        return isDarkMode ? colors.brandColors.info[400] : colors.brandColors.info[500];
      case 'delivered':
        return isDarkMode ? colors.brandColors.success[400] : colors.brandColors.success[500];
      case 'completed':
        return isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500];
      case 'cancelled':
        return isDarkMode ? colors.brandColors.danger[400] : colors.brandColors.danger[500];
      default:
        return isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500];
    }
  };
  const isLoading = orderLoading || partnerLoading || driverLoading;


  // Add real-time update handling
  const handleRealtimeOrderUpdate = (payload: RealtimePostgresChangesPayload<Order>) => {
    if (payload.new && 'id' in payload.new && payload.new.id === orderId) {
      console.log('Realtime: Order details updated, refetching. ID:', payload.new.id);
      refetch();
    } else {
      console.warn('Received order update payload without new data or order_id:', payload);
    }
  };

  const handleRealtimeAssignmentUpdate = (payload: any) => {
    if (payload.new && 'order_id' in payload.new && payload.new.order_id === orderId) {
      console.log('Realtime: Driver assignment updated for order, refetching. Order ID:', payload.new.order_id);
      refetch();
    } else {
      console.warn('Received assignment update payload without new data or order_id:', payload);
    }
  };

  const { error: realtimeError  } = useOrderRealtime(
    handleRealtimeOrderUpdate,
    handleRealtimeAssignmentUpdate
  );
  if (realtimeError) {
    console.error('🔍 REALTIME ERROR:', realtimeError);
  }





  if (isLoading) {
    return (
      <StyledView className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
        <StyledText className="mt-4 text-center text-gray-600">
          {t('Loading order details...')}
        </StyledText>
      </StyledView>
    );
  }

  if (orderError || !orderDetails) {
    return (
      <StyledView className="flex-1 justify-center items-center p-4">
        <MaterialIcons name="error-outline" size={48} color={colors.brandColors.danger[500]} />
        <StyledText className="mt-4 text-lg text-center font-medium">
          {t('Order not found')}
        </StyledText>
        <StyledText className="mt-2 text-center text-gray-600">
          {orderError?.message || t('The requested order could not be found')}
        </StyledText>
        <Button mode="contained" onPress={() => router.back()} style={{ marginTop: 16 }}>
          {t('Go Back')}
        </Button>
      </StyledView>
    );
  }

  // Since dumpster is directly on orderDetails from the Supabase query
  const dumpster = orderDetails.dumpster as unknown as Dumpster;
  const dumpsterImageUrl = getSafeImageUrl(dumpster?.image_url);
  const dumpsterName = isRTL
    ? (dumpster?.name_ar || dumpster?.name_en || 'Unnamed Dumpster')
    : (dumpster?.name_en || 'Unnamed Dumpster');

  return (
    <StyledSafeAreaView style={{
      flex: 1,
      backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
    }}>
      <StyledView className="flex-row items-center px-5 py-4 border-b"
        style={{
          borderBottomColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          flexDirection: isRTL ? 'row-reverse' : 'row'
        }}>
        <StyledTouchableOpacity
          className="w-10 h-10 justify-center items-center"
          onPress={() => router.back()}
        >
          <Feather
            name={isRTL ? "arrow-right" : "arrow-left"}
            size={24}
            color={isDarkMode ? colors.textColors.dark : colors.textColors.light}
          />
        </StyledTouchableOpacity>
        <StyledText
          className="flex-1 text-xl font-semibold "
          style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }}
        >
          {t('orders.details.title')}
        </StyledText>
      </StyledView>

      <StyledScrollView className="flex-1 px-4 py-6">
        {/* Order Status Card */}
        <StyledCard
          mode="contained"
          className="mb-6"
          style={{
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
            //borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light
          }}
        >
          <Card.Content>
            <StyledView className="flex-row justify-between items-center" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
              <StyledText className="text-lg font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                {t('orders.details.OrderStatus')}
              </StyledText>
              <StyledView className="px-3 py-1 rounded-full" style={{
                backgroundColor: getStatusColor(orderDetails.status)
              }}>
                <StyledText style={{
                  color: colors.textColors.light,
                  fontWeight: '500'
                }}>
                  {t(`orders.status.${orderDetails.status}`)}
                </StyledText>
              </StyledView>
            </StyledView>
          </Card.Content>
        </StyledCard>

        {/* Dumpster Details */}
        <StyledCard
          mode="contained"
          className="mb-6"
          style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}
        >
          <Card.Content>
            <StyledText className="text-lg font-bold mb-4" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }}>
              {t('orders.details.DumpsterDetails')}
            </StyledText>

            <StyledView className="flex-row" style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
              <Image
                source={{ uri: dumpsterImageUrl }}
                style={{ width: 80, height: 80, borderRadius: 8 }}
                resizeMode="cover"
              />
              <StyledView className="ml-4 flex-1" style={{ alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
                <StyledText className="font-bold text-lg" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                  {dumpsterName}
                </StyledText>
                <StyledView className="flex-row items-center mt-1">
                  {isDarkMode ? (<Image
                    source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                    style={{ width: 16, height: 16, marginRight: 2 }}
                    resizeMode="contain"
                  />) : (<Image
                    source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                    style={{ width: 16, height: 16, marginRight: 2 }}
                    resizeMode="contain"
                  />)}
                  <StyledText style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                    {orderDetails.total_amount} / {t('Load')}
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>
          </Card.Content>
        </StyledCard>

        {/* Delivery Details */}
        <StyledCard
          mode="contained"
          className="mb-6"
          style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}
        >
          <Card.Content>
            <StyledText className="text-lg font-bold mb-4" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }}>
              {t('orders.details.DeliveryDetails')}
            </StyledText>

            <StyledView className="mb-4" style={{ alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
              <StyledText className="font-semibold mb-1" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                {t('orders.details.Address')}
              </StyledText>
              <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                {orderDetails.delivery_address}
              </StyledText>
            </StyledView>

            <StyledView className="mb-4" style={{ alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
              <StyledText className="font-semibold mb-1" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                {t('orders.details.DeliveryDate')}
              </StyledText>
              <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                {new Date(orderDetails.delivery_date).toDateString()}
              </StyledText>
            </StyledView>

            {orderDetails.delivery_instructions && (
              <StyledView style={{ alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
                <StyledText className="font-semibold mb-1" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                  {t('orders.details.Instructions')}
                </StyledText>
                <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  {orderDetails.delivery_instructions}
                </StyledText>
              </StyledView>
            )}
          </Card.Content>
        </StyledCard>

        {/* Partner Details Card */}
        {partner && (
          <StyledCard
            mode="contained"
            className="mb-6"
            style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}
          >
            <Card.Content>
              <StyledText className="text-lg font-bold mb-4"
                style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }}>
                {t('orders.details.PartnerDetails')}
              </StyledText>

              <StyledView className="flex-row items-center" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                {partner.profile?.avatar_url && (
                  <Image
                    source={{ uri: getSafeImageUrl(partner.profile.avatar_url) }}
                    style={{ width: 50, height: 50, borderRadius: 25, marginRight: 12 }}
                  />
                )}

                <StyledView className="flex-1" style={{ alignItems: isRTL ? 'flex-end' : 'flex-start', marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }}>
                  <StyledText className="font-bold"
                    style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                    {partner.company_name}
                  </StyledText>
                  <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                    {partner.profile?.full_name}
                  </StyledText>
                  <StyledTouchableOpacity onPress={() => Linking.openURL(`tel:${partner.profile?.phone}`)}>
                    <StyledText style={{ color: colors.brandColors.primary[500] }}>
                      {partner.profile?.phone}
                    </StyledText>
                  </StyledTouchableOpacity>
                </StyledView>
              </StyledView>
            </Card.Content>
          </StyledCard>
        )}

        {/* Driver Details Card */}
        <StyledCard
          mode="contained"
          className="mb-6"
          style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}
        >
          <Card.Content>
            <StyledText className="text-lg font-bold mb-4"
              style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }}>
              {t('orders.details.DriverDetails')}
            </StyledText>

            {driver ? (
              <StyledView className="flex-row items-center" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                {driver.profile?.avatar_url && (
                  <Image
                    source={{ uri: getSafeImageUrl(driver.profile.avatar_url) }}
                    style={{ width: 50, height: 50, borderRadius: 25, marginRight: 12 }}
                  />
                )}

                <StyledView className="flex-1" style={{ alignItems: isRTL ? 'flex-end' : 'flex-start', marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }}>
                  <StyledText className="font-bold"
                    style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                    {driver.profile?.full_name || 'Unknown Driver'}
                  </StyledText>
                  {driver.profile?.phone && (
                    <StyledTouchableOpacity onPress={() => Linking.openURL(`tel:${driver.profile?.phone}`)}>
                      <StyledText style={{ color: colors.brandColors.primary[500] }}>
                        {driver.profile?.phone}
                      </StyledText>
                    </StyledTouchableOpacity>
                  )}

                  {/* License Information */}
                  {driver.license_number && (
                    <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                      License: {driver.license_number}
                    </StyledText>
                  )}

                  {/* Vehicle Information */}
                  {driver.vehicle && (
                    <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                      Vehicle: {driver.vehicle.license_plate || 'Unknown'}
                    </StyledText>
                  )}

                  {/* Assignment Status */}
                  <StyledView className="mt-2 px-3 py-1 rounded-full" style={{
                    backgroundColor: isDarkMode ? colors.brandColors.info[400] : colors.brandColors.info[500]
                  }}>
                    <StyledText style={{ color: colors.textColors.light }}>
                      Assigned
                    </StyledText>
                  </StyledView>
                </StyledView>
              </StyledView>
            ) : driverLoading ? (
              <StyledView>
                <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  Loading driver information...
                </StyledText>
              </StyledView>
            ) : (
              <StyledView>
                <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  No driver has been assigned to this order yet.
                </StyledText>
              </StyledView>
            )}
          </Card.Content>
        </StyledCard>

        {/* Payment Details */}
        <StyledCard
          mode="contained"
          className="mb-6"
          style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}
        >
          <Card.Content>
            <StyledText className="text-lg font-bold mb-4" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }}>
              {t('orders.details.PaymentDetails')}
            </StyledText>

            <StyledView className="flex-row justify-between mb-2" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
              <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                {t('orders.details.BasePrice')}
              </StyledText>
              <StyledView className="flex-row items-center">
                {isDarkMode ? (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />) : (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />)}
                <StyledText style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                  {orderDetails.base_price}
                </StyledText>
              </StyledView>
            </StyledView>

            {(orderDetails as any).discount_amount > 0 && (
              <StyledView className="flex-row justify-between mb-2" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                <StyledText style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  {t('orders.details.Discount')}
                </StyledText>
                <StyledView className="flex-row items-center">
                  {isDarkMode ? (<Image
                    source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                    style={{ width: 16, height: 16, marginRight: 2 }}
                    resizeMode="contain"
                  />) : (<Image
                    source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                    style={{ width: 16, height: 16, marginRight: 2 }}
                    resizeMode="contain"
                  />)}
                  <StyledText style={{ color: colors.brandColors.success[500] }}>
                    -{(orderDetails as any).discount_amount}
                  </StyledText>
                </StyledView>
              </StyledView>
            )}

            <StyledDivider className="my-2" />

            <StyledView className="flex-row justify-between" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
              <StyledText className="font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                {t('orders.details.Total')}
              </StyledText>
              <StyledView className="flex-row items-center">
                {isDarkMode ? (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />) : (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />)}
                <StyledText className="font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                  {orderDetails.total_amount}
                </StyledText>
              </StyledView>
            </StyledView>

            <StyledView className="mt-4 p-5 rounded-lg" style={{ backgroundColor: getPaymentStatusColor(orderDetails.payment_status), alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
              <StyledText className="text-center mb-2" style={{ color: colors.textColors.light }}>
                {t('orders.details.PaymentStatus')}: {t(`orders.details.${orderDetails.payment_status}`)}
              </StyledText>
              <StyledText className="text-center mb-2" style={{ color: colors.textColors.light }}>
                {t('orders.details.PaymentMethod')}
              </StyledText>
              <StyledView className="flex-row items-center" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                {getPaymentIcon(orderDetails.payment_method)}
                <StyledText className="text-center" style={{ color: colors.textColors.light }}>
                  {t(`orders.details.${orderDetails.payment_method}`)}
                </StyledText>
              </StyledView>

            </StyledView>
          </Card.Content>
        </StyledCard>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
