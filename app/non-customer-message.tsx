import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { supabase } from '@/services/supabase/client';
import { NewRTLView as RTLView, NewRTLText as RTLText, useRTLContext } from '@/components/rtl/new-index';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { Feather } from '@expo/vector-icons';
import * as colors from '@/theme/colors';

export default function NonCustomerMessage() {
  const router = useRouter();
  const { t } = useTranslation();
  const { signOut, userRole } = useAuth();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login.v2');
  };

  return (
    <View style={[
      styles.container, 
      { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }
    ]}>
      <View style={styles.content}>
        <Image 
          source={require('@/assets/emblem-only.png')} 
          style={styles.logo} 
          resizeMode="contain"
        />
        
        <RTLText style={[
          styles.title, 
          { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }
        ]}>
          {t('auth.accessDenied', 'Access Denied')}
        </RTLText>
        
        <RTLText style={[
          styles.message, 
          { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }
        ]}>
          {t('auth.nonCustomerMessage', 'Your account is registered as a {{role}} account. This app is for customers only.', 
            { role: userRole || 'non-customer' })}
        </RTLText>
        
        <RTLText style={[
          styles.instructions, 
          { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }
        ]}>
          {t('auth.useAnotherAccount', 'Please use a different phone number or email to access the customer app, or sign out and create a new account.')}
        </RTLText>
        
        <View style={styles.iconContainer}>
          <Feather 
            name="alert-circle" 
            size={60} 
            color={colors.brandColors.danger[500]} 
          />
        </View>
      </View>
      
      <TouchableOpacity
        style={[styles.button, { backgroundColor: colors.brandColors.primary[500] }]}
        onPress={handleSignOut}
      >
        <RTLText style={styles.buttonText}>
          {t('common.signOut', 'Sign Out')}
        </RTLText>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 24,
  },
  instructions: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 20,
  },
  iconContainer: {
    marginBottom: 32,
  },
  button: {
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 