import React, { useState, useEffect } from 'react';
import { 
  View, 
  StyleSheet, 
  TouchableOpacity, 
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { GlobalUserIdentifier, useAuth } from '@/context/AuthContext';
import { NewRTLView as RTLView, NewRTLText as RTLText, NewRTLTextInput as RTLTextInput, useRTLContext } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/services/supabase/client';

export default function CreateProfile() {
  const router = useRouter();
  const { t } = useTranslation();
  const { session, refreshSession } = useAuth();
  const { isRTL } = useRTLContext();
  const { isDarkMode } = useTheme();

  const [fullName, setFullName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Load existing profile data if available
  useEffect(() => {
    const loadProfile = async () => {
      if (!session?.user?.id) return;

      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('profiles')
          .select('phone, full_name')
          .eq('id', GlobalUserIdentifier.profileId)
          .single();

        if (error) throw error;

        if (data) {
          setPhoneNumber(data.phone || '');
          setFullName(data.full_name || '');
        }
      } catch (err) {
        console.error('Error loading profile:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [session]);

  const handleSaveProfile = async () => {
    if (!fullName.trim()) {
      setError(t('profile.errors.nameRequired', 'Please enter your full name'));
      return;
    }

    if (!session?.user?.id) {
      setError(t('profile.errors.notLoggedIn', 'You must be logged in to save your profile'));
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Update the profile
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: fullName,
          updated_at: new Date().toISOString(),
        })
        .eq('id', GlobalUserIdentifier.profileId);

      if (error) throw error;

      // Show success message
      Alert.alert(
        t('profile.success.title', 'Profile Updated'),
        t('profile.success.message', 'Your profile has been updated successfully.'),
        [
          {
            text: t('common.continue', 'Continue'),
            onPress: () => {
              // Navigate to home screen
              router.replace('/');
            },
          },
        ]
      );
    } catch (err) {
      console.error('Error saving profile:', err);
      setError(t('profile.errors.saveFailed', 'Failed to save profile. Please try again.'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }
    ]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <RTLView style={styles.header}>
            <RTLText style={[
              styles.title, 
              { color: isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500] }
            ]}>
              {t('profile.create.title', 'Complete Your Profile')}
            </RTLText>
            <RTLText style={[
              styles.subtitle, 
              { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }
            ]}>
              {t('profile.create.subtitle', 'Please provide the following information to complete your profile')}
            </RTLText>
          </RTLView>

          <RTLView style={styles.form}>
            <RTLView style={styles.inputContainer}>
              <RTLText style={[
                styles.label, 
                { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }
              ]}>
                {t('profile.fields.fullName', 'Full Name')}
              </RTLText>
              <RTLTextInput
                style={[
                  styles.input,
                  { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light },
                  { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }
                ]}
                placeholder={t('profile.placeholders.fullName', 'Enter your full name')}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                value={fullName}
                onChangeText={setFullName}
              />
            </RTLView>

            <RTLView style={styles.inputContainer}>
              <RTLText style={[
                styles.label, 
                { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }
              ]}>
                {t('profile.fields.phone', 'Phone Number')}
              </RTLText>
              <RTLTextInput
                style={[
                  styles.input,
                  { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light },
                  { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }
                ]}
                placeholder={t('profile.placeholders.phone', 'Enter your phone number')}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                value={phoneNumber}
                editable={false} // Phone number is read-only
              />
              <RTLText style={[
                styles.helperText, 
                { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }
              ]}>
                {t('profile.hints.phoneReadOnly', 'Phone number cannot be changed')}
              </RTLText>
            </RTLView>

            {error ? (
              <RTLText style={styles.errorText}>
                {error}
              </RTLText>
            ) : null}

            <TouchableOpacity
              style={[
                styles.saveButton,
                { opacity: isLoading ? 0.7 : 1 }
              ]}
              onPress={handleSaveProfile}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600]} />
              ) : (
                <RTLText style={[
                  styles.buttonText,
                  { color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600] }
                ]}>
                  {t('profile.actions.save', 'Save Profile')}
                </RTLText>
              )}
            </TouchableOpacity>
          </RTLView>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    flexDirection: 'column',
    marginTop: 40,
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  form: {
    flexDirection: 'column',
    width: '100%',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
  },
  errorText: {
    color: colors.brandColors.danger[500],
    marginBottom: 16,
    fontSize: 14,
  },
  saveButton: {
    backgroundColor: colors.brandColors.primary[500],
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
}); 