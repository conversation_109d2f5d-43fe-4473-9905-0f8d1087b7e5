import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import Button from '@/components/ui/Button';
import { PhoneInput } from '@/components/ui/PhoneInput';
import { useAuth } from '@/context/AuthContext';
import { NewRTLView, NewRTLText, NewRTLTextInput, useRTLContext } from '@/components/rtl/new-index';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function LoginScreenV2() {
  const router = useRouter();
  const { t, i18n } = useTranslation();
  const { signInWithPhone, signInWithEmail } = useAuth();
  const { setLanguage, isRTL } = useRTLContext();
  
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loginMethod, setLoginMethod] = useState('email'); // 'email' or 'phone'
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Initialize language and RTL settings
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem('@app:language');
        if (savedLanguage && savedLanguage !== i18n.language) {
          await setLanguage(savedLanguage);
        }
      } catch (error) {
        console.error('Failed to initialize language:', error);
      }
    };

    initializeLanguage();
  }, []);

  const handleLogin = async () => {
    if (loginMethod === 'phone') {
      if (!phone) {
        setError(t('auth.login.errors.phoneRequired', 'Please enter your phone number'));
        return;
      }

      setError('');
      setIsLoading(true);

      try {
        await signInWithPhone(phone);
        router.push('/');
      } catch (err) {
        setError(t('auth.login.errors.failed', 'Login failed. Please try again.'));
      } finally {
        setIsLoading(false);
      }
    } else {
      if (!email || !password) {
        setError(t('auth.login.errors.emailPasswordRequired', 'Please enter both email and password'));
        return;
      }

      setError('');
      setIsLoading(true);

      try {
        await signInWithEmail(email, password);
        router.push('/');
      } catch (err) {
        setError(t('auth.login.errors.failed', 'Login failed. Please try again.'));
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleSignUp = () => {
    router.push('/register');
  };

  return (
    <SafeAreaView style={styles.container}>
      <NewRTLView style={styles.content} direction="column">
        {/* Welcome Text */}
        <NewRTLView style={styles.welcomeSection} direction="column">
          <NewRTLText style={styles.title}>
            {t('auth.login.title')}
          </NewRTLText>
          <NewRTLText style={styles.description}>
            {loginMethod === 'phone' 
              ? t('auth.login.phoneDescription', 'Enter your phone number to continue')
              : t('auth.login.emailDescription', 'Enter your email and password to continue')}
          </NewRTLText>
        </NewRTLView>

        {/* Login Method Toggle */}
        <NewRTLView style={styles.toggleContainer}>
          <TouchableOpacity 
            style={[
              styles.toggleButton,
              { backgroundColor: loginMethod === 'phone' ? '#fff' : 'transparent' }
            ]}
            onPress={() => {
              setLoginMethod('phone');
              setError('');
            }}
          >
            <NewRTLText style={[
              styles.toggleText,
              { color: loginMethod === 'phone' ? '#2196F3' : '#666' }
            ]}>
              {t('auth.login.phoneMethod', 'Phone')}
            </NewRTLText>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[
              styles.toggleButton,
              { backgroundColor: loginMethod === 'email' ? '#fff' : 'transparent' }
            ]}
            onPress={() => {
              setLoginMethod('email');
              setError('');
            }}
          >
            <NewRTLText style={[
              styles.toggleText,
              { color: loginMethod === 'email' ? '#2196F3' : '#666' }
            ]}>
              {t('auth.login.emailMethod', 'Email')}
            </NewRTLText>
          </TouchableOpacity>
        </NewRTLView>

        {/* Login Form */}
        <NewRTLView style={styles.formContainer} direction="column">
          {loginMethod === 'phone' ? (
            <PhoneInput
              value={phone}
              onChangeText={setPhone}
              error={error}
              placeholder={t('auth.login.phoneNumber')}
            />
          ) : (
            <NewRTLView direction="column">
              <NewRTLTextInput
                style={[styles.input, { borderColor: error ? '#ff4444' : '#ddd' }]}
                value={email}
                onChangeText={setEmail}
                placeholder={t('auth.login.emailPlaceholder', 'Your email')}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              <NewRTLTextInput
                style={[styles.input, { borderColor: error ? '#ff4444' : '#ddd' }]}
                value={password}
                onChangeText={setPassword}
                placeholder={t('auth.login.passwordPlaceholder', 'Your password')}
                secureTextEntry
              />
            </NewRTLView>
          )}

          {error ? (
            <NewRTLText style={styles.errorText}>
              {error}
            </NewRTLText>
          ) : null}
        </NewRTLView>

        {/* Login Button */}
        <Button
          onPress={handleLogin}
          disabled={isLoading}
          style={styles.loginButton}
        >
          {t('auth.login.signIn')}
        </Button>

        {/* Sign Up Button */}
        <TouchableOpacity
          onPress={handleSignUp}
          style={styles.signUpButton}
        >
          <NewRTLText style={styles.signUpText}>
            {t('auth.login.noAccount')} <NewRTLText style={styles.signUpLink}>{t('auth.login.createAccount')}</NewRTLText>
          </NewRTLText>
        </TouchableOpacity>

        {/* Terms */}
        <NewRTLView style={styles.termsContainer} direction="column">
          <NewRTLText style={styles.termsText}>
            {t('auth.terms.description', 'By continuing, you agree to our')}
          </NewRTLText>
          <NewRTLView style={styles.termsLinks}>
            <TouchableOpacity onPress={() => router.push('/terms')}>
              <NewRTLText style={styles.linkText}>
                {t('auth.terms.terms', 'Terms of Service')}
              </NewRTLText>
            </TouchableOpacity>
            <NewRTLText style={styles.termsText}> {t('auth.terms.and', 'and')} </NewRTLText>
            <TouchableOpacity onPress={() => router.push('/privacy')}>
              <NewRTLText style={styles.linkText}>
                {t('auth.terms.privacy', 'Privacy Policy')}
              </NewRTLText>
            </TouchableOpacity>
          </NewRTLView>
        </NewRTLView>
      </NewRTLView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  welcomeSection: {
    marginBottom: 40,
    width: '100%',
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#333',
    marginBottom: 10,
    width: '100%',
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    width: '100%',
  },
  formContainer: {
    width: '100%',
  },
  toggleContainer: {
    marginBottom: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 4,
  },
  toggleButton: {
    flex: 1,
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  toggleText: {
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    fontSize: 16,
  },
  errorText: {
    color: '#ff4444',
    marginTop: 8,
    marginBottom: 8,
  },
  loginButton: {
    marginTop: 20,
  },
  termsContainer: {
    marginTop: 40,
    alignItems: 'center',
  },
  termsText: {
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
  },
  termsLinks: {
    justifyContent: 'center',
    marginTop: 5,
    gap: 5,
  },
  linkText: {
    color: '#2196F3',
  },
  signUpButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  signUpText: {
    fontSize: 14,
    color: '#666',
  },
  signUpLink: {
    color: '#2196F3',
    fontWeight: '600',
  },
}); 