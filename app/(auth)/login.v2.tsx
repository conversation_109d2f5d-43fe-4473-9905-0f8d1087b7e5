import React, { useState, useEffect, useMemo, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, TextStyle, KeyboardAvoidingView, ScrollView, Platform, Image, ActivityIndicator, Alert, LogBox, Keyboard, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAuth, GlobalUserIdentifier } from '@/context/AuthContext';
import { NewRTLView as RTLView, NewRTLText as RTLText, NewRTLTextInput as RTLTextInput, useRTLContext } from '@/components/rtl/new-index';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/services/supabase/client';
import type { CountryPickerItem } from '@/types/countries';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CountryPickerSheet, { CountryPickerSheetRef } from '@/components/CountryPickerSheet';
import OtpVerificationSheet from '@/components/auth/OtpVerificationSheet';

// Get screen height
const screenHeight = Dimensions.get('window').height;

export default function LoginScreenV2() {
  LogBox.ignoreAllLogs(true);
  const router = useRouter();
  const { t } = useTranslation();
  const { signInWithPhone, signInWithEmail } = useAuth();
  const { isRTL } = useRTLContext();
  const { isDarkMode, initializeTheme } = useTheme();

  // --- Ref for the Bottom Sheet ---
  const countryPickerSheetRef = useRef<CountryPickerSheetRef>(null);

  // Form state
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Country picker state - selectedCountry remains here
  // Remove showCountryPicker state
  const [selectedCountry, setSelectedCountry] = useState<CountryPickerItem>({
    dial_code: '+966',
    code: 'SA',
    name_en: 'Saudi Arabia',
    name_ar: 'المملكة العربية السعودية',
    flag_emoji: '🇸🇦'
  });

  // Login method tabs
  const [activeTab, setActiveTab] = useState('phone');

  // OTP verification state
  const [showOtpBottomSheet, setShowOtpBottomSheet] = useState(false);
  const [phoneForVerification, setPhoneForVerification] = useState<string | null>(null);

  // Reset state
  const resetTapCount = React.useRef(0);
  const resetTimer = React.useRef<NodeJS.Timeout>();

  // Handle phone login
  const handlePhoneLogin = async () => {
    if (!phone) {
      setError(t('auth.login.errors.phoneRequired', 'Please enter your phone number'));
      return;
    }

    setError('');
    setIsLoading(true);
    Keyboard.dismiss();

    try {
      // Step 1: Format the phone number
      console.log('[Login.v2] Step 1: Formatting phone number');
      const fullPhoneNumber = selectedCountry.dial_code + phone;
      const phoneNumberWithoutPlus = fullPhoneNumber.replace('+', '');
      console.log('[Login.v2] Looking up profile for phone:', phoneNumberWithoutPlus);

      // Step 2: Check if this phone number exists in profiles
      console.log('[Login.v2] Step 2: Checking if profile exists');
      const { data: existingProfile, error: profileError } = await supabase
        .from('profiles')
        .select('id, user_type, full_name')
        .eq('phone', phoneNumberWithoutPlus)
        .single();

      console.log('[Login.v2] Profile check result:', { existingProfile, profileError });

      if (profileError) {
        if (profileError.code === 'PGRST116') {
          // No profile found - new user
          setIsLoading(false);
          Alert.alert(
            t('auth.login.newUser.title', 'New User'),
            t('auth.login.newUser.message', 'This phone number is not registered. Please create an account first.'),
            [
              { text: t('common.cancel', 'Cancel'), style: 'cancel' },
              { text: t('auth.login.newUser.signUp', 'Sign Up'), onPress: () => router.push('/register') }
            ]
          );
          return;
        }
        throw profileError;
      }

      // Step 3: Check user type
      console.log('[Login.v2] Step 3: Checking user type');
      if (existingProfile && existingProfile.user_type !== 'customer') {
        setIsLoading(false);
        Alert.alert(
          t('auth.login.wrongRole.title', 'Access Denied'),
          t('auth.login.wrongRole.message', 
            'This phone number is linked to a {{role}} account under the name {{name}}. This app is for customers only.', 
            { role: existingProfile.user_type, name: existingProfile.full_name || 'unknown' }
          ),
          [{ text: t('common.ok', 'OK') }]
        );
        return;
      }

      // Step 4: Store profile info for verification
      console.log('[Login.v2] Step 4: Storing profile info for verification');
      await AsyncStorage.setItem('@app:temp_user_type', 'customer');
      await AsyncStorage.setItem('@app:profile_id', existingProfile.id);
      GlobalUserIdentifier.updateProfileId(existingProfile.id);

      // Step 5: Clean up existing verification state
      console.log('[Login.v2] Step 5: Cleaning up verification state');
      await AsyncStorage.removeItem('@app:verifying_otp_in_progress');
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');
      await AsyncStorage.removeItem('@app:otp_sending_in_progress');
      
      // Step 7: Request OTP
      console.log('[Login.v2] Step 7: Requesting OTP');
      const { error: otpError } = await supabase.auth.signInWithOtp({
        phone: fullPhoneNumber,
      });

      if (otpError) {
        throw otpError;
      }

      // Step 8: Show verification screen AND START TIMER
      console.log('[Login.v2] Step 8: Showing verification screen and starting timer');
      setPhoneForVerification(fullPhoneNumber);
      setResendCountdown(120); // Start 2-minute countdown immediately
      setCanResend(false);     // Disable resend initially
      setShowOtpBottomSheet(true);
      setIsLoading(false);
    } catch (err: any) {
      console.error('Phone login error:', err);
      
      // Clean up any stored verification data
      await AsyncStorage.removeItem('@app:temp_user_type');
      await AsyncStorage.removeItem('@app:verifying_otp_in_progress');
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');
      
      setIsLoading(false);
      if (err?.message?.includes('rate limit')) {
        setError(t('auth.login.errors.rateLimited', 'Too many attempts. Please try again later.'));
      } else {
        setError(err.message || t('auth.login.errors.failed', 'Login failed. Please try again.'));
      }
    }
  };

  // Email login - RESTRUCTURED
  const handleEmailLogin = async () => {
    if (!email) {
      setError(t('auth.login.errors.emailRequired', 'Please enter your email'));
      return;
    }
    if (!password) {
      setError(t('auth.login.errors.passwordRequired', 'Please enter your password'));
      return;
    }

    setError('');
    setIsLoading(true);
    let profileData: any = null; // Variable to hold profile data

    try {
      console.log('[Login.v2] Attempting email login for:', email);

      // --- Step 1: Find Profile (with Timeout) ---
      console.log('[Login.v2] Step 1: Finding profile by email with 15s timeout...');
      const profileQuery = supabase
        .from('profiles')
        .select('id, user_type, email, email_auth_id, phone_auth_id, full_name, avatar_url, phone') // Select fields needed initially
        .eq('email', email.toLowerCase().trim())
        .single();

      const profileTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Profile lookup timed out after 15 seconds')), 15000)
      );

      let profileResult: { data: any; error: any; };
      try {
         profileResult = await Promise.race([profileQuery, profileTimeout]) as { data: any; error: any; };
         console.log('[Login.v2] Profile lookup completed or timeout handled.');
      } catch (timeoutError: any) {
         console.warn('[Login.v2] Profile lookup timed out:', timeoutError.message);
         setError(t('auth.login.errors.profileCheckTimeout', 'Failed to find profile information in time. Please try again.'));
         throw timeoutError; // Stop execution
      }

      if (profileResult.error) {
        console.error('[Login.v2] Profile lookup error:', profileResult.error);
        if (profileResult.error.code === 'PGRST116') {
          setError(t('auth.login.errors.notRegistered', 'Email not registered. Please sign up first.'));
        } else {
          setError(t('auth.login.errors.profileCheck', 'Error checking profile. Please try again.'));
        }
        throw profileResult.error; // Stop execution
      }

      profileData = profileResult.data; // Store profile data
      console.log('[Login.v2] Profile found:', profileData.id);

      // --- Step 2: Check User Type ---
      console.log('[Login.v2] Step 2: Checking user type');
      if (profileData.user_type !== 'customer') {
        setError(t('auth.login.errors.nonCustomerAccount', 'This email is registered as a non-customer account.'));
        throw new Error('Non-customer account'); // Stop execution
      }

      // --- Step 3: Authenticate with Supabase Auth ---
      console.log('[Login.v2] Step 3: Authenticating with email and password...');
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) {
        console.error('[Login.v2] Authentication error:', authError);
        if (authError.message.includes('Invalid login credentials')) {
          setError(t('auth.login.errors.invalidCredentials', 'Invalid email or password.'));
        } else {
          setError(authError.message || t('auth.login.errors.authFailed', 'Authentication failed. Please try again.'));
        }
        throw authError; // Stop execution
      }

      // Check if auth was successful and we have a user ID
      if (!authData?.user?.id) {
         console.error('[Login.v2] Authentication successful but no user ID received.');
         setError(t('auth.login.errors.authFailedNoUser', 'Authentication failed unexpectedly. Please try again.'));
         throw new Error('Auth succeeded but no user ID');
      }
      const authUserId = authData.user.id;
      console.log('[Login.v2] Authentication successful. Auth User ID:', authUserId);

      // --- Step 4: Post-Authentication Updates (State, Storage, DB) ---
      console.log('[Login.v2] Step 4: Performing post-authentication updates...');

      // 4.1: Update Global State & Core AsyncStorage
      console.log('[Login.v2] 4.1: Updating GlobalUserIdentifier and core AsyncStorage');
      GlobalUserIdentifier.updateIds(authUserId, profileData.id);
      await AsyncStorage.setItem('@app:profile_id', profileData.id);
      await AsyncStorage.setItem('@app:user_role', profileData.user_type || 'customer');

      // 4.2: Fetch Full Profile & Store Extended Data in AsyncStorage (if needed)
      // Run this asynchronously in the background - no need to await here
      // Use an IIFE (Immediately Invoked Function Expression) to run async code without awaiting
      (async () => {
          console.log('[Login.v2] 4.2 (Background): Storing/fetching full profile data');
          let userProfileToStore = { ...profileData }; // Start with initially fetched data
          if (!profileData.full_name || !profileData.avatar_url || !profileData.phone) {
            try {
              console.log('[Login.v2] (Background) Fetching full profile details...');
              const { data: fullProfileData, error: fullProfileError } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', profileData.id)
                .single();
              if (fullProfileError) {
                console.warn('[Login.v2] (Background) Warning: Error fetching full profile details:', fullProfileError);
              } else if (fullProfileData) {
                console.log('[Login.v2] (Background) Full profile data retrieved, merging.');
                userProfileToStore = { ...userProfileToStore, ...fullProfileData }; // Merge
              }
            } catch (fetchErr) {
                console.warn('[Login.v2] (Background) Warning: Exception fetching full profile details:', fetchErr);
            }
          }
          try {
            await AsyncStorage.setItem('@app:user_profile', JSON.stringify(userProfileToStore));
            if (userProfileToStore.full_name) await AsyncStorage.setItem('@app:user_name', userProfileToStore.full_name);
            if (userProfileToStore.avatar_url) await AsyncStorage.setItem('@app:user_avatar', userProfileToStore.avatar_url);
            if (userProfileToStore.phone) await AsyncStorage.setItem('@app:user_phone', userProfileToStore.phone);
            if (userProfileToStore.email) await AsyncStorage.setItem('@app:user_email', userProfileToStore.email);
            console.log('[Login.v2] (Background) Extended profile data stored in AsyncStorage.');
          } catch (storageErr) {
             console.warn('[Login.v2] (Background) Warning: Failed to store extended profile data:', storageErr);
          }
      })(); // End of background IIFE for profile fetching/storage

      // 4.3: Link Auth IDs in Database (Email and potentially Phone)
      // Run this asynchronously in the background
       (async () => {
          console.log('[Login.v2] 4.3 (Background): Linking Auth IDs in database');
          const profileUpdates: { email_auth_id: string; phone_auth_id?: string; } = {
            email_auth_id: authUserId
          };
          // Use profileData here as userProfileToStore might not be updated yet
          if (profileData.phone && !profileData.phone_auth_id) {
            console.log('[Login.v2] (Background) Linking phone_auth_id for compatibility');
            profileUpdates.phone_auth_id = authUserId;
          }
          if (Object.keys(profileUpdates).length > 0) {
              try {
                  const { error: updateError } = await supabase
                    .from('profiles')
                    .update(profileUpdates)
                    .eq('id', profileData.id);
                  if (updateError) {
                    console.warn('[Login.v2] (Background) Warning: Failed to link auth IDs in profile:', updateError);
                  } else {
                    console.log('[Login.v2] (Background) Successfully linked auth IDs.');
                  }
              } catch (linkErr) {
                  console.warn('[Login.v2] (Background) Warning: Exception linking auth IDs:', linkErr);
              }
          }
       })(); // End of background IIFE for DB linking

      // --- Step 4.4: Update Supabase Auth User Metadata (Await this!) ---
      console.log('[Login.v2] 4.4: Updating Supabase user metadata...');
       try {
           await supabase.auth.updateUser({
             data: {
               role: profileData.user_type || 'customer',
               user_type: profileData.user_type || 'customer',
               profile_id: profileData.id
             }
           });
            console.log('[Login.v2] Successfully updated user metadata.');
       } catch (metaErr) {
            // If metadata update fails, log warning but proceed, RLS might fail later
            console.warn('[Login.v2] Warning: Exception updating user metadata:', metaErr);
       }

      // --- Step 5: Finalize and Navigate ---
      console.log('[Login.v2] Step 5: Reinitializing theme and navigating home');
      // Theme can initialize in background
      initializeTheme().catch(themeErr => console.warn("[Login.v2] Theme initialization failed:", themeErr));

      // Set flag for successful login AFTER essential updates
      await AsyncStorage.setItem('@app:login_success', 'true');

      // Navigate home with delay
      setTimeout(() => {
        console.log('[Login.v2] Executing delayed navigation to home');
        router.replace('/');
      }, 100); // Keep this delay

      // Set loading false here, before potential unmount
      setIsLoading(false);
      console.log('[Login.v2] Email login primary flow completed (background tasks may continue).');

    } catch (err: any) {
      console.error('[Login.v2] Error during email login process:', err);
      // Clean up critical AsyncStorage items on error
       try {
            await AsyncStorage.removeItem('@app:profile_id');
            await AsyncStorage.removeItem('@app:user_role');
            GlobalUserIdentifier.clear(); // Clear global state on error
       } catch (cleanupError) {
            console.error('[Login.v2] Error during error cleanup:', cleanupError);
       }
      // Error message is already set by the specific step that failed
      // Ensure loading is false in the finally block
    } finally {
       // Ensure loading state is always reset
       console.log('[Login.v2] Email login finally block. Setting isLoading to false.');
       setIsLoading(false);
    }
  };

  // Handle OTP verification - adapt to use the component's callback
  const handleVerifyOtp = async (otpFromSheet: string) => {
    if (!phoneForVerification) {
      setError(t('auth.verify.errors.phoneRequired', 'Phone number is missing'));
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      console.log('[Login.v2] Step 1: Verifying OTP for phone:', phoneForVerification);
      
      // First clean up any existing verification flags
      await AsyncStorage.removeItem('@app:verifying_otp_in_progress');
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');

      // Step 2: Sign out to clear any existing session state
      console.log('[Login.v2] Step 2: Signing out to reset auth state');
      //await supabase.auth.signOut({ scope: 'local' });
      
      // Small delay to ensure signOut is processed
      //await new Promise(resolve => setTimeout(resolve, 300));

      // Step 3: Verify the OTP
      console.log('[Login.v2] Step 3: Verifying OTP token');
      const { data, error } = await supabase.auth.verifyOtp({
        phone: phoneForVerification,
        token: otpFromSheet,
        type: 'sms',
      });

      if (error) {
        console.error('OTP verification error:', error);
        setIsLoading(false);
        
        if (error.message.includes('Token has expired')) {
          setError(t('auth.verify.errors.expired', 'Verification code has expired. Please request a new one.'));
        } else if (error.message.includes('Invalid')) {
          setError(t('auth.verify.errors.invalid', 'Invalid verification code. Please check and try again.'));
        } else {
          setError(error.message || t('auth.verify.errors.failed', 'Verification failed. Please try again.'));
        }
        return;
      }

      // Step 4: Check if we have a valid user in the verification data
      console.log('[Login.v2] Step 4: Checking verification data');
      if (!data?.user?.id) {
        console.error('No user ID in verification data');
        setIsLoading(false);
        setError(t('auth.verify.errors.noUserId', 'Verification failed: No user ID received'));
        await supabase.auth.signOut();
        return;
      }

      // Step 5: Get the profile by phone number
      console.log('[Login.v2] Step 5: Finding profile by phone');
      const cleanPhoneNumber = phoneForVerification.replace('+', '');
      
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, user_type, phone')
        .eq('phone', cleanPhoneNumber)
        .single();
        
      if (profileError || !profileData) {
        console.error('Profile query error:', profileError);
        setIsLoading(false);
        setError(t('auth.verify.errors.profileLookupFailed', 'Could not find your profile. Please try again.'));
        await supabase.auth.signOut();
        return;
      }
      
      console.log('[Login.v2] Found profile by phone:', profileData);
      
      // Step 6: Check if user is a customer
      console.log('[Login.v2] Step 6: Checking user type');
      if (profileData.user_type !== 'customer') {
        console.error('Non-customer account tried to log in:', profileData.user_type);
        setIsLoading(false);
        setError(t('auth.verify.errors.nonCustomerAccount', 'This account is registered as a non-customer account. Please use a different phone number.'));
        await supabase.auth.signOut();
        return;
      }
      
      // Step 7: Link the phone auth ID to the profile
      console.log('[Login.v2] Step 7: Linking phone auth ID to profile');
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ phone_auth_id: data.user.id })
        .eq('id', profileData.id);
        
      if (updateError) {
        console.error('Error updating phone_auth_id:', updateError);
        // Not critical, continue
      } else {
        console.log('[Login.v2] Successfully updated phone_auth_id');
      }
      
      // Step 8: Store the profile ID and user role
      console.log('[Login.v2] Step 8: Storing user data in AsyncStorage');
      await AsyncStorage.setItem('@app:profile_id', profileData.id);
      await AsyncStorage.setItem('@app:user_role', profileData.user_type || 'customer');
      GlobalUserIdentifier.updateIds(data.user.id, profileData.id);

      // NEW: Store full profile data for access across the app
      console.log('[Login.v2] Step 8.1: Storing full profile data');
      // Get complete profile data with all fields
      const { data: fullProfileData, error: fullProfileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profileData.id)
        .single();
        
      const userProfileToStore = fullProfileData || { id: profileData.id, user_type: profileData.user_type, phone: profileData.phone };

      if (fullProfileError) {
        console.error('Error fetching full profile data:', fullProfileError);
      } else {
        console.log('[Login.v2] Full profile data retrieved/merged:', userProfileToStore);
      }

      // Store the potentially merged profile data
      await AsyncStorage.setItem('@app:user_profile', JSON.stringify(userProfileToStore));
      if (userProfileToStore.full_name) {
        await AsyncStorage.setItem('@app:user_name', userProfileToStore.full_name);
      }
      if (userProfileToStore.avatar_url) {
        await AsyncStorage.setItem('@app:user_avatar', userProfileToStore.avatar_url);
      }
      if (userProfileToStore.email) {
         await AsyncStorage.setItem('@app:user_email', userProfileToStore.email);
      }
      
      // CRITICAL FIX: Make sure both phone_auth_id and the main profile ID are updated
      // This ensures all orders and addresses linked to the profile ID can be found
      // regardless of which auth method was used
      if (fullProfileData) { // Only if we got the full profile
          const profileUpdates: { 
            phone_auth_id: string; 
            email_auth_id?: string;
          } = {
            phone_auth_id: data.user.id,
          };

          // If we have both email and phone in the profile but no email_auth_id,
          // we should also update that to point to the current auth record
          if (fullProfileData.email && !fullProfileData.email_auth_id) {
            console.log('[Login.v2] Setting email_auth_id for future email login compatibility');
            profileUpdates.email_auth_id = data.user.id;
          }

          // Update the profile with both auth IDs pointing to the same record
          console.log('[Login.v2] Updating profile with auth IDs for cross-auth compatibility');
          const { error: updateCrossAuthError } = await supabase
            .from('profiles')
            .update(profileUpdates)
            .eq('id', profileData.id);

          if (updateCrossAuthError) {
            console.error('Error updating profile cross-auth IDs:', updateCrossAuthError);
          } else {
            console.log('[Login.v2] Successfully updated profile auth IDs for multi-auth support');
          }
       }

      // Step 9: Update the user's metadata
      console.log('[Login.v2] Step 9: Updating user metadata with profile ID for data consistency');
      await supabase.auth.updateUser({
        data: { 
          role: profileData.user_type || 'customer',
          user_type: profileData.user_type || 'customer',
          profile_id: profileData.id
        }
      });

      // Step 10: Initialize theme and navigate
      console.log('[Login.v2] Step 10: Finalizing and navigating home');
      await initializeTheme();
      
      // Close OTP sheet and reset loading state BEFORE navigation
      setShowOtpBottomSheet(false);
      setIsLoading(false);
      
      // Set a success flag
      await AsyncStorage.setItem('@app:login_success', 'true');
      
      // CRITICAL FIX - Force navigation with delay
      console.log('[Login.v2] Phone verification successful, forcing navigation to home');
      setTimeout(() => {
        console.log('[Login.v2] Executing delayed navigation to home');
        router.replace('/');
      }, 100);
      
    } catch (err: any) {
      console.error('Unexpected error during OTP verification:', err);
      
      // Clean up on error
      await AsyncStorage.removeItem('@app:verifying_otp_in_progress');
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');
      
      setIsLoading(false);
      setError(err.message || t('auth.verify.errors.failed', 'Verification failed. Please try again.'));
    }
  };

  // Handle login based on active tab
  const handleLogin = async () => {
    if (activeTab === 'phone') {
      await handlePhoneLogin();
    } else {
      await handleEmailLogin();
    }
  };

  // Handle resend code
  const [canResend, setCanResend] = useState(true);
  const [resendCountdown, setResendCountdown] = useState(0);

  // Function to handle countdown for resend button
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (resendCountdown > 0) {
      timer = setTimeout(() => {
        setResendCountdown(resendCountdown - 1);
      }, 1000);
    } else if (resendCountdown === 0) {
      setCanResend(true);
    }
    
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [resendCountdown]);

  // Handle resend code
  const handleResendCode = async () => {
    if (!phoneForVerification || !canResend) {
      return;
    }

    setError('');
    setIsLoading(true);
    setCanResend(false);
    const RESEND_TIMEOUT = 120; // Changed to 120 seconds (2 minutes)
    
    try {
      // Step 1: Check for rate limiting on client side
      console.log('[Login.v2] Step 1: Checking rate limiting');
      const lastResendTime = await AsyncStorage.getItem('@app:last_resend_timestamp');
      if (lastResendTime) {
        const elapsed = Date.now() - parseInt(lastResendTime, 10);
        // Use RESEND_TIMEOUT * 1000 for milliseconds comparison
        if (elapsed < RESEND_TIMEOUT * 1000) { 
          const secondsLeft = Math.ceil((RESEND_TIMEOUT * 1000 - elapsed) / 1000);
          setError(t('auth.verify.errors.tooSoon', { seconds: secondsLeft }));
          setCanResend(true);
          setIsLoading(false);
          return;
        }
      }
      
      // Step 2: Set resend timestamp
      console.log('[Login.v2] Step 2: Setting resend timestamp');
      await AsyncStorage.setItem('@app:last_resend_timestamp', Date.now().toString());
      
      console.log('[Login.v2] Step 3: Resending OTP to:', phoneForVerification);
      
      // Step 3: Clear any existing session before requesting a new OTP
      console.log('[Login.v2] Step 4: Signing out to reset auth state');
      await supabase.auth.signOut({ scope: 'local' });
      
      // Small delay to ensure signOut is processed
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Step 5: Request new OTP
      console.log('[Login.v2] Step 5: Requesting new OTP');
      const { error } = await supabase.auth.signInWithOtp({
        phone: phoneForVerification,
      });

      if (error) {
        console.error('Error resending OTP:', error);
        
        if (error.message.includes('rate limit') || error.message.includes('too many requests')) {
          throw new Error(t('auth.verify.errors.rateLimited', 'Too many attempts. Please try again later.'));
        } else {
          throw error;
        }
      }

      // Step 6: Start countdown and show success message
      console.log('[Login.v2] Step 6: Starting countdown and showing success message');
      setResendCountdown(RESEND_TIMEOUT);
      
      Alert.alert(
        t('auth.verify.resendSuccess.title', 'Code Sent'),
        t('auth.verify.resendSuccess.message', 'A new verification code has been sent to your phone.')
      );
    } catch (err: any) {
      console.error('Unexpected error resending OTP:', err);
      if (err?.message?.includes('rate limit') || err?.message?.includes('too many requests')) {
        setError(t('auth.verify.errors.rateLimited', 'Too many attempts. Please try again later.'));
      } else {
        setError(err.message || t('auth.verify.errors.resendFailed', 'Failed to resend code. Please try again.'));
      }
      setCanResend(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    // For now, just show an error that this feature is coming soon
    setError(t('auth.login.errors.googleSignInNotAvailable', 'Google sign-in will be available soon'));
  };

  const handleSignUp = () => {
    try {
      console.log('[Login.v2] Navigating to register screen');
      // Clean up any verification state before navigating
      AsyncStorage.removeItem('@app:otp_verification_in_progress')
        .then(() => AsyncStorage.removeItem('@app:verifying_otp_in_progress'))
        .then(() => AsyncStorage.removeItem('@app:verifying_phone'))
        .catch(err => console.error('Error clearing verification state:', err));
      
      // Navigate to register screen
      router.push('/register');
    } catch (err) {
      console.error('Error navigating to register:', err);
      // Fallback if navigation fails
      Alert.alert(
        t('common.error', 'Error'),
        t('auth.login.errors.navigationFailed', 'There was a problem navigating to the registration screen. Please try again.'),
        [{ text: t('common.ok', 'OK') }]
      );
    }
  };

  const tabTextStyle = (isActive: boolean): TextStyle => ({
    color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
    ...(isActive && { color: colors.brandColors.secondary[600] })
  });

  // Add a function to reset auth state
  const resetAuthState = async () => {
    try {
      // Clear all authentication-related flags from AsyncStorage
      await AsyncStorage.removeItem('@app:verifying_otp_in_progress');
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');
      await AsyncStorage.removeItem('@app:otp_sending_in_progress');
      await AsyncStorage.removeItem('@app:last_otp_timestamp');
      await AsyncStorage.removeItem('@app:last_resend_timestamp');
      await AsyncStorage.removeItem('@app:is_new_user');
      await AsyncStorage.removeItem('@app:profile_id');
      await AsyncStorage.removeItem('@app:user_role');
      await AsyncStorage.removeItem('@app:user_profile');
      await AsyncStorage.removeItem('@app:user_name');
      await AsyncStorage.removeItem('@app:user_avatar');
      await AsyncStorage.removeItem('@app:user_email');
      await AsyncStorage.removeItem('@app:user_phone');
      await AsyncStorage.removeItem('@app:temp_user_type');
      GlobalUserIdentifier.clear(); // Clear global state

      // Also sign out from Supabase to ensure a clean slate
      await supabase.auth.signOut({ scope: 'local' });

      // Notify the user
      setError('');
      Alert.alert(
        t('auth.reset.success', 'Authentication Reset'),
        t('auth.reset.message', 'Authentication state has been reset successfully.'),
        [{ text: t('common.ok', 'OK') }]
      );
    } catch (err) {
      console.error('Error resetting auth state:', err);
      setError(t('auth.reset.error', 'Failed to reset authentication state'));
    }
  };

  // Add a function to check for conflicting profiles
  const checkForConflictingProfiles = async (phoneNumber: string): Promise<boolean> => {
    try {
      // Check if this phone number is used by multiple profiles with different roles
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('id, user_type')
        .eq('phone', phoneNumber);
      
      if (error) {
        console.error('Error checking profiles:', error);
        return false;
      }

      // If we found multiple profiles
      if (profiles && profiles.length > 1) {
        console.log('[Login.v2] Found multiple profiles using this phone number:', profiles);
        
        // Check if there are both customer and non-customer roles
        const hasCustomer = profiles.some(p => p.user_type === 'customer');
        const hasNonCustomer = profiles.some(p => p.user_type !== 'customer');
        
        if (hasCustomer && hasNonCustomer) {
          // This is a genuine conflict - the phone number is used for multiple role types
          setError(t('auth.login.errors.conflictingAccounts', 
            'This phone number is linked to multiple accounts with different roles. Please contact support for assistance.'));
          return true;
        }
      }
      
      return false;
    } catch (err) {
      console.error('Error in conflict check:', err);
      return false;
    }
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }
    ]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <RTLView style={styles.header}>
            <Image source={isRTL ? require('@/assets/logoSVG-ar.png') : require('@/assets/logoSVG-en.png')} style={styles.logoImage} />
            <TouchableOpacity 
              onPress={() => {
                // Track tap count to reveal the reset option
                resetTapCount.current = (resetTapCount.current || 0) + 1;
                
                // After 5 quick taps, show reset option
                if (resetTapCount.current >= 5) {
                  resetTapCount.current = 0;
                  Alert.alert(
                    t('auth.reset.title', 'Reset Authentication'),
                    t('auth.reset.confirmMessage', 'This will reset all authentication state. Use this only if you\'re experiencing login issues.'),
                    [
                      { text: t('common.cancel', 'Cancel'), style: 'cancel' },
                      { text: t('common.reset', 'Reset'), onPress: resetAuthState }
                    ]
                  );
                }
                
                // Reset tap counter after 3 seconds of inactivity
                clearTimeout(resetTimer.current);
                resetTimer.current = setTimeout(() => {
                  resetTapCount.current = 0;
                }, 3000);
              }}
              style={{ padding: 10 }}
            >
              <RTLText style={[styles.title, { color: isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500] }]}>
                {activeTab === 'phone'
                  ? t('auth.login.title') + ' ' + t('auth.login.withPhone', 'with phone number')
                  : t('auth.login.title') + ' ' + t('auth.login.withEmail', 'with email')
                }
              </RTLText>
            </TouchableOpacity>
            <RTLText style={[styles.subtitle, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {activeTab === 'phone'
                ? t('auth.login.phoneDescription')
                : t('auth.login.emailDescription')
              }
            </RTLText>
          </RTLView>

          <RTLView style={[styles.tabContainer, { flexDirection: isRTL ? 'row-reverse' as const : 'row' as const }, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
              <TouchableOpacity
                style={[
                  styles.tab,
                  activeTab === 'phone' && styles.activeTab,
                ]}
                onPress={() => setActiveTab('phone')}
              >
                <RTLText style={[
                  styles.tabText,
                  tabTextStyle(activeTab === 'phone')
                ]}>
                  {t('auth.login.phoneMethod')}
                </RTLText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.tab,
                  activeTab === 'email' && styles.activeTab,
                ]}
                onPress={() => setActiveTab('email')}
              >
                <RTLText style={[
                  styles.tabText,
                  tabTextStyle(activeTab === 'email')
                ]}>
                  {t('auth.login.emailMethod')}
                </RTLText>
              </TouchableOpacity>
            </RTLView>
          
          

          {/* Phone Input UI */}
          {activeTab === 'phone' ? (
            <View style={styles.inputContainer}>
              <View style={[styles.phoneInputContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <TouchableOpacity
                  style={[
                    styles.countryCode,
                    { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }
                  ]}
                  onPress={() => countryPickerSheetRef.current?.open()}
                >
                  <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
                    <RTLText style={{ fontSize: 24, marginHorizontal: 4 }}>{selectedCountry.flag_emoji}</RTLText>
                    {/* <RTLText style={{
                      fontSize: 16,
                      color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                      marginHorizontal: 8, // Use horizontal margin
                      writingDirection: 'ltr'
                    }}>
                      {selectedCountry.dial_code}
                    </RTLText> */}

                    <Feather name="chevron-down" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
                  </View>
                </TouchableOpacity>
                <RTLTextInput
                  style={[
                    styles.phoneInput,
                    { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
                      color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                     }
                  ]}
                  placeholder={t('auth.login.phoneNumber')}
                  placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                  textAlign={isRTL ? 'right' : 'left'}
                />
              </View>
            </View>
          ) : (
            <View style={styles.inputContainer}>
              <RTLTextInput
                style={[styles.input, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}
                placeholder={t('auth.login.emailPlaceholder')}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />

              <View style={[styles.passwordContainer, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
                <RTLTextInput
                  style={[styles.passwordInput, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }, { paddingRight: isRTL ? 0 : 40, paddingLeft: isRTL ? 40 : 0 }]}
                  placeholder={t('auth.login.passwordPlaceholder')}
                  placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  style={[styles.passwordToggle, { right:isRTL ? undefined : 8, left:isRTL ? 8 : undefined }]}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Feather
                    name={showPassword ? "eye-off" : "eye"}
                    size={20}
                    color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}

          {error ? (
            <RTLText style={styles.errorText}>
              {error}
            </RTLText>
          ) : null}

          <TouchableOpacity
            style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
            onPress={handleLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color={isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600]} />
            ) : (
              <RTLText style={[styles.buttonText, { color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600] }]}>
                {t('common.continue')}
              </RTLText>
            )}
          </TouchableOpacity>

            
              <View style={styles.termsContainer}>
                <RTLText style={styles.termsText}>
                  {t('auth.terms.description')} {' '}
                  <RTLText style={styles.termsLink}>{t('auth.terms.terms')}</RTLText>
                  {' '}{t('auth.terms.and')}{' '}
                  <RTLText style={styles.termsLink}>{t('auth.terms.privacy')}</RTLText>
                </RTLText>
              </View>

              <View style={styles.dividerContainer}>
                <View style={[styles.divider, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]} />
                {/* <RTLText style={[styles.dividerText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>{t('auth.login.orContinueWith')}</RTLText> */}
                {/* <View style={[styles.divider, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]} /> */}
              </View>

              {/* <TouchableOpacity
                style={[styles.socialButton, { borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,  }]}
                onPress={handleGoogleLogin}
              >
                <Image
                  source={require('@/assets/google-logo.png')}
                  style={styles.socialIcon}
                  resizeMode="contain"
                />
              </TouchableOpacity> */}

              <RTLView style={[styles.footer, { flexDirection: isRTL ? 'row-reverse' as const : 'row' as const }]}>
                <RTLText style={[styles.footerText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
                  {t('auth.login.noAccount')}
                </RTLText>
                <TouchableOpacity onPress={handleSignUp}>
                  <RTLText style={styles.footerLink}>
                    {t('auth.login.createAccount')}
                  </RTLText>
                </TouchableOpacity>
              </RTLView>
            
          
        </ScrollView>
      </KeyboardAvoidingView>

      {/* --- Render the Country Picker Bottom Sheet --- */}
      <CountryPickerSheet
        ref={countryPickerSheetRef}
        initialState={selectedCountry}
        onSelectCountry={(item: CountryPickerItem) => {
          setSelectedCountry(item);
        }}
      />

      {/* --- Render the OTP Verification Bottom Sheet Component --- */}
      <OtpVerificationSheet
        isVisible={showOtpBottomSheet}
        phoneForVerification={phoneForVerification}
        isLoading={isLoading}
        error={error}
        canResend={canResend}
        resendCountdown={resendCountdown}
        onVerify={handleVerifyOtp}
        onResend={handleResendCode}
        onClose={() => {
          // Clear error when closing manually
          setError(''); 
          // Clear OTP verification flag when closing the bottom sheet
          AsyncStorage.removeItem('@app:otp_verification_in_progress');
          setShowOtpBottomSheet(false);
          setPhoneForVerification(null);
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  tabContainer: {
    marginBottom: 24,
    borderRadius: 12,
    padding: 4,
  },
  header: {
    flexDirection: 'column',
    marginTop: 40,
    marginBottom: 40,
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  logoImage: {
    height: 32,
    resizeMode: 'contain',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
  },
  activeTab: {
    backgroundColor: colors.brandColors.primary[500],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  inputContainer: {
    marginBottom: 24,
  },
  phoneInputContainer: {
    alignItems: 'center',
    gap: 12,
  },
  countryCode: {
    height: 50,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
  },
  phoneInput: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  input: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  button: {
    backgroundColor: colors.brandColors.primary[500],
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    color: colors.brandColors.danger[500],
    marginBottom: 16,
    fontSize: 14,
  },
  termsContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    color: 'gray',
    lineHeight: 18,
  },
  termsLink: {
    color: colors.brandColors.primary[500],
    fontWeight: '500',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 30,
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 10,
    fontSize: 12,
  },
  socialButton: {
    backgroundColor: 'transparent',
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.3,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },
  footer: {
    justifyContent: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    marginRight: 5,
  },
  footerLink: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.brandColors.primary[500],
  },
  resendButton: {
    marginTop: 24,
    alignItems: 'center',
  },
  resendText: {
    fontSize: 16,
    fontWeight: '500',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    borderRadius: 8,
    marginTop: 16,
    paddingHorizontal: 16,
    // Add background color based on theme if needed, or keep it simple
    // backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light
  },
  passwordInput: {
    flex: 1,
    height: '100%', // Ensure it takes full height
    fontSize: 16,
    // Add text color based on theme if needed
    // color: isDarkMode ? colors.textColors.dark : colors.textColors.light
  },
  passwordToggle: {
    padding: 8,
    position: 'absolute', // Keep absolute positioning
    // Adjust right/left based on RTL if needed, or set globally
    right: 8, // Default to right, adjust based on isRTL inline if necessary
  },
});
