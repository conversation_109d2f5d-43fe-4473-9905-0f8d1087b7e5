import { Stack } from 'expo-router';
import { useRTLContext } from '@/components/rtl/new-index';

export default function AuthLayout() {
  const { isRTL } = useRTLContext();

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        animation: 'fade',
        animationDuration: 200,
      }}
    >
      <Stack.Screen
        name="language-select"
        options={{
          title: 'Select Language',
        }}
      />
      <Stack.Screen
        name="login"
        options={{
          title: 'Sign In',
        }}
      />
      <Stack.Screen
        name="login.v2"
        options={{
          title: 'Sign In',
        }}
      />
      <Stack.Screen
        name="register"
        options={{
          title: 'Create Account',
        }}
      />
      <Stack.Screen
        name="forgot-password"
        options={{
          title: 'Reset Password',
        }}
      />
      <Stack.Screen
        name="phone-input"
        options={{
          title: 'Enter Phone',
        }}
      />
      <Stack.Screen
        name="verify-otp"
        options={{
          title: 'Verify Code',
        }}
      />
    </Stack>
  );
} 