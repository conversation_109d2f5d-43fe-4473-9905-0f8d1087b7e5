import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, TextStyle, KeyboardAvoidingView, ScrollView, TextInput, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { NewRTLView as RTLView, NewRTLText as RTLText, NewRTLTextInput as RTLTextInput, useRTLContext, NewRTLText } from '@/components/rtl/new-index';
import { Feather, Ionicons } from '@expo/vector-icons';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';

interface CountryPickerItem {
  dial_code: string;
  code: string;
  name: string;
}

interface CountryPickerProps {
  show: boolean;
  pickerButtonOnPress: (item: CountryPickerItem) => void;
  style: {
    modal: {
      height: number;
      backgroundColor: string;
    };
    textInput: {
      color: string;
      backgroundColor: string;
    };
    countryButtonStyles: {
      backgroundColor: string;
    };
    countryName: {
      color: string;
    };
    dialCode: {
      color: string;
    };
  };
  onBackdropPress: () => void;
  initialState: CountryPickerItem;
}

const CountryPicker: React.FC<CountryPickerProps> = ({ show, pickerButtonOnPress, style, onBackdropPress, initialState }) => {
  // Mock implementation
  return null;
};

export default function RegisterScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { signUpWithPhone, signUpWithEmail } = useAuth();
  const { isDarkMode, initializeTheme } = useTheme();
  const { isRTL } = useRTLContext();

  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showCountryPicker, setShowCountryPicker] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<CountryPickerItem>({ dial_code: '+1', code: 'US', name: 'United States' });
  const [activeTab, setActiveTab] = useState('phone');
  const [error, setError] = useState('');

  // Initialize theme on mount
  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeTheme();
      } catch (error) {
        console.error('Failed to initialize theme:', error);
      }
    };
    initialize();
  }, []);

  const handleSignUp = async () => {
    if (isLoading) return;
    setIsLoading(true);
    setError('');

    try {
      if (activeTab === 'phone') {
        if (!fullName || !phone) {
          setError(t('auth.register.errors.fieldsRequired', 'All fields are required'));
          setIsLoading(false);
          return;
        }
        await signUpWithPhone(selectedCountry.dial_code + phone);
      } else {
        if (!fullName || !email || !password || !confirmPassword) {
          setError(t('auth.register.errors.fieldsRequired', 'All fields are required'));
          setIsLoading(false);
          return;
        }
        
        if (password !== confirmPassword) {
          setError(t('auth.register.errors.passwordMatch'));
          setIsLoading(false);
          return;
        }
        
        if (password.length < 6) {
          setError(t('auth.register.errors.passwordLength'));
          setIsLoading(false);
          return;
        }
        
        await signUpWithEmail(email, password);
      }
      
      // Re-initialize theme after registration to get user preferences
      await initializeTheme();
      router.push('/');
    } catch (error) {
      console.error('Registration error:', error);
      setError(t('auth.register.errors.failed', 'Registration failed. Please try again.'));
    } finally {
      setIsLoading(false);
    }
  };

  const getTabTextStyle = (isActive: boolean): TextStyle => ({
    color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
    ...(isActive && { color: colors.brandColors.primary[500] })
  });

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }]}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Back button */}
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
          </TouchableOpacity>

          <View style={styles.header}>
            <NewRTLText style={[styles.title, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>
              {t('auth.register.title')}
            </NewRTLText>
            <NewRTLText style={[styles.subtitle, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {activeTab === 'phone' ? t('auth.register.phoneDescription') : t('auth.register.emailDescription')}
            </NewRTLText>
          </View>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder={t('auth.register.name')}
              placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
              value={fullName}
              onChangeText={setFullName}
            />
          </View>

          <View style={styles.tabContainer}>
            <TouchableOpacity 
              style={[
                styles.tab, 
                activeTab === 'phone' && styles.activeTab,
              ]}
              onPress={() => setActiveTab('phone')}
            >
              <NewRTLText style={[styles.tabText, getTabTextStyle(activeTab === 'phone')]}>
                {t('auth.register.phoneMethod')}
              </NewRTLText>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[
                styles.tab, 
                activeTab === 'email' && styles.activeTab,
              ]}
              onPress={() => setActiveTab('email')}
            >
              <NewRTLText style={[styles.tabText, getTabTextStyle(activeTab === 'email')]}>
                {t('auth.register.emailMethod')}
              </NewRTLText>
            </TouchableOpacity>
          </View>

          {activeTab === 'phone' ? (
            <View style={styles.inputContainer}>
              <View style={styles.phoneInputContainer}>
                <TouchableOpacity 
                  style={styles.countryCode}
                  onPress={() => setShowCountryPicker(true)}
                >
                  <NewRTLText style={styles.countryCodeText}>
                    {selectedCountry.dial_code}
                  </NewRTLText>
                  <Feather name="chevron-down" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
                </TouchableOpacity>
                <TextInput
                  style={styles.phoneInput}
                  placeholder={t('auth.register.phoneNumber')}
                  placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                />
              </View>
            </View>
          ) : (
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder={t('auth.register.emailPlaceholder')}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              <TextInput
                style={[styles.input, { marginTop: 16 }]}
                placeholder={t('auth.register.passwordPlaceholder')}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                value={password}
                onChangeText={setPassword}
                secureTextEntry
              />
              <TextInput
                style={[styles.input, { marginTop: 16 }]}
                placeholder={t('auth.register.confirmPasswordPlaceholder')}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
              />
            </View>
          )}

          {error ? (
            <NewRTLText style={styles.errorText}>
              {error}
            </NewRTLText>
          ) : null}

          <TouchableOpacity
            style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
            onPress={handleSignUp}
            disabled={isLoading}
          >
            <NewRTLText style={styles.buttonText}>
              {t('auth.register.createAccount')}
            </NewRTLText>
          </TouchableOpacity>

          <View style={styles.termsContainer}>
            <NewRTLText style={styles.termsText}>
              {t('auth.terms.description')} {' '}
              <NewRTLText style={styles.termsLink}>{t('auth.terms.terms')}</NewRTLText>
              {' '}{t('auth.terms.and')}{' '}
              <NewRTLText style={styles.termsLink}>{t('auth.terms.privacy')}</NewRTLText>
            </NewRTLText>
          </View>

          <View style={styles.footer}>
            <NewRTLText style={styles.footerText}>
              {t('auth.register.haveAccount')}
            </NewRTLText>
            <TouchableOpacity onPress={() => router.push('/login')}>
              <NewRTLText style={styles.footerLink}>
                {t('auth.register.signIn')}
              </NewRTLText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      
      <CountryPicker
        show={showCountryPicker}
        pickerButtonOnPress={(item) => {
          setSelectedCountry(item);
          setShowCountryPicker(false);
        }}
        style={{
          modal: {
            height: 500,
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
          },
          textInput: {
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
          },
          countryButtonStyles: {
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light
          },
          countryName: {
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light
          },
          dialCode: {
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light
          }
        }}
        onBackdropPress={() => setShowCountryPicker(false)}
        initialState={selectedCountry}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginBottom: 20,
  },
  header: {
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTab: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  inputContainer: {
    marginBottom: 24,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryCode: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginRight: 12,
  },
  countryCodeText: {
    fontSize: 16,
    marginRight: 8,
    fontWeight: '500',
  },
  phoneInput: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  input: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  button: {
    backgroundColor: colors.brandColors.primary[500],
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    color: colors.brandColors.danger[500],
    marginBottom: 16,
    fontSize: 14,
  },
  termsContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    color: 'gray',
    lineHeight: 18,
  },
  termsLink: {
    color: colors.brandColors.primary[500],
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    color: 'gray',
    marginRight: 5,
  },
  footerLink: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.brandColors.primary[500],
  },
}); 