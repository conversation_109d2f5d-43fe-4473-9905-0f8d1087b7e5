import React from 'react';
import { View, StyleSheet, TouchableOpacity, useColorScheme, Alert, Image } from 'react-native';
import { Text, Provider as PaperProvider, MD3DarkTheme, MD3LightTheme } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { changeLanguage } from '@/i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRTLContext } from '@/components/rtl/new-index';
import { useTranslation } from 'react-i18next';
import { I18nManager } from 'react-native';
import * as colors from '@/theme/colors';
import { shadowStyles } from '@/utils/shadowStyles';
import { useTheme } from '@/context/ThemeContext';


const LANGUAGE_KEY = '@app:language';

export default function LanguageSelectScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { setLanguage } = useRTLContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const { isDarkMode } = useTheme();

  // Create theme with proper dark mode support
  const paperTheme = isDarkMode ? { ...MD3DarkTheme } : { ...MD3LightTheme };

  const selectLanguage = async (language: string) => {
    setIsLoading(true);
    try {
      // Update RTL context and state
      setLanguage(language);
      const isRTL = language === 'ar';
      I18nManager.allowRTL(isRTL);
      I18nManager.forceRTL(isRTL);

      // Apply language change
      await changeLanguage(language);

      // Navigate to onboarding
      router.replace('/(auth)/login.v2');
    } catch (error) {
      console.log('[Language-select] Error changing language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PaperProvider theme={paperTheme}>
      <View style={[styles.container, { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }]}>
        <View style={styles.logoContainer}>
          <Image source={require('@/assets/emblem-only.png')} style={styles.logoImage} />
          <Text style={[styles.logoText, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>DUMPSTER</Text>
        </View>
        
        <Text style={[styles.title, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>Welcome to Dumpster</Text>
        <Text style={[styles.subtitle, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>Please select your preferred language</Text>
        
        <View style={styles.languageContainer}>
          <TouchableOpacity
            style={[styles.languageButton, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }, shadowStyles.sm]}
            onPress={() => selectLanguage('en')}
            disabled={isLoading}
          >
            <Text style={[styles.languageName, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>English</Text>
            <Text style={[styles.languageNative, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>English</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.languageButton, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }, shadowStyles.sm]}
            onPress={() => selectLanguage('ar')}
            disabled={isLoading}
          >
            <Text style={[styles.languageName, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>Arabic</Text>
            <Text style={[styles.languageNative, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>العربية</Text>
          </TouchableOpacity>
        </View>
      </View>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  logoContainer: {
    marginBottom: 40,
    alignItems: 'center',
  },
  logoImage: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
  },
  logoText: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
  },
  languageContainer: {
    width: '100%',
    gap: 16,
  },
  languageButton: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  languageName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  languageNative: {
    fontSize: 16,
  },
}); 