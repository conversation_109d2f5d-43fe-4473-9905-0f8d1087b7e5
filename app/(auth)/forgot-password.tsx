import React, { useState, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import Button from '@/components/ui/Button';
import { NewRTLView, NewRTLText, NewRTLTextInput, useRTLContext } from '@/components/rtl/new-index';
import { backgroundColors, textColors } from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';

export default function ForgotPasswordScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isRTL } = useRTLContext();
  const { isDarkMode } = useTheme();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Create RTL-aware dynamic styles
  const dynamicStyles = useMemo(() => ({
    header: {
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
      alignItems: 'center' as ViewStyle['alignItems'],
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
    },
    headerTitle: {
      flex: 1,
      fontSize: 20,
      fontWeight: '600' as const,
      marginHorizontal: 15,
    },
  }), [isRTL]);

  const handleResetPassword = async () => {
    if (!email) {
      setError(t('auth.forgotPassword.errors.emailRequired', 'Please enter your email'));
      return;
    }

    try {
      setLoading(true);
      setError('');
      // TODO: Implement password reset
      console.log('Reset password for:', email);
    } catch (error) {
      console.error('Password reset error:', error);
      setError(t('auth.forgotPassword.errors.failed', 'Failed to send reset link. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[
      styles.container, 
      { backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light }
    ]}>
      {/* Header */}
      <NewRTLView style={[
        dynamicStyles.header,
        { 
          borderBottomColor: isDarkMode ? backgroundColors.outline.dark : backgroundColors.outline.light,
          backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light 
        }
      ]}>
        <NewRTLText style={[
          dynamicStyles.headerTitle,
          { color: isDarkMode ? textColors.dark : textColors.light }
        ]}>
          {t('auth.forgotPassword.title', 'Reset Password')}
        </NewRTLText>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Feather 
            name={isRTL ? "arrow-right" : "arrow-left"} 
            size={24} 
            color={isDarkMode ? textColors.dark : textColors.light} 
          />
        </TouchableOpacity>
      </NewRTLView>

      <View style={styles.content}>
        {/* Welcome Text */}
        <View style={styles.header}>
          <NewRTLText style={[
            styles.title,
            { color: isDarkMode ? textColors.dark : textColors.light }
          ]}>
            {t('auth.forgotPassword.title', 'Reset Password')}
          </NewRTLText>
          <NewRTLText style={[
            styles.description,
            { color: isDarkMode ? textColors.secondary.dark : textColors.secondary.light }
          ]}>
            {t('auth.forgotPassword.description', 'Enter your email address and we\'ll send you instructions to reset your password')}
          </NewRTLText>
        </View>

        {/* Form */}
        <View>
          <NewRTLTextInput
            style={[styles.input, error ? styles.inputError : {}]}
            value={email}
            onChangeText={(text) => {
              setEmail(text);
              setError('');
            }}
            placeholder={t('auth.forgotPassword.emailPlaceholder', 'Your email')}
            autoCapitalize="none"
            keyboardType="email-address"
          />

          {error ? (
            <NewRTLText style={styles.errorText}>
              {error}
            </NewRTLText>
          ) : null}

          <TouchableOpacity
            style={[styles.button, loading ? styles.buttonDisabled : {}]}
            onPress={handleResetPassword}
            disabled={loading}
          >
            <NewRTLText style={styles.buttonText}>
              {t('auth.forgotPassword.sendLink', 'Send Reset Link')}
            </NewRTLText>
          </TouchableOpacity>

          {/* Back to Login */}
          <TouchableOpacity
            onPress={() => router.push('/login')}
            style={styles.textButton}
          >
            <NewRTLText style={styles.textButtonText}>
              {t('auth.forgotPassword.backToLogin', 'Back to Login')}
            </NewRTLText>
          </TouchableOpacity>
        </View>

        {/* Terms */}
        <View style={styles.termsContainer}>
          <NewRTLText style={styles.termsText}>
            {t('auth.terms.description', 'By continuing, you agree to our')}
          </NewRTLText>
          <NewRTLView style={styles.termsLinks}>
            <TouchableOpacity onPress={() => router.push('/terms')}>
              <NewRTLText style={styles.linkText}>
                {t('auth.terms.terms', 'Terms of Service')}
              </NewRTLText>
            </TouchableOpacity>
            <NewRTLText style={styles.termsText}> {t('auth.terms.and', 'and')} </NewRTLText>
            <TouchableOpacity onPress={() => router.push('/privacy')}>
              <NewRTLText style={styles.linkText}>
                {t('auth.terms.privacy', 'Privacy Policy')}
              </NewRTLText>
            </TouchableOpacity>
          </NewRTLView>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#333',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: '#ff4444',
  },
  button: {
    backgroundColor: '#2196F3',
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  textButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  textButtonText: {
    fontSize: 14,
    color: '#666',
  },
  termsContainer: {
    marginTop: 40,
  },
  termsText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
  },
  termsLinks: {
    justifyContent: 'center',
    marginTop: 5,
    gap: 5,
  },
  linkText: {
    color: '#2196F3',
    fontWeight: '600',
  },
  errorText: {
    color: '#ff4444',
    marginTop: 10,
  },
}); 