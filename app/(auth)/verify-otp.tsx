import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, KeyboardAvoidingView, ScrollView, Platform, Image, I18nManager, ActivityIndicator, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { NewRTLView as RTLView, NewRTLText as RTLText, NewRTLTextInput as RTLTextInput, useRTLContext } from '@/components/rtl/new-index';
import { Feather } from '@expo/vector-icons';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/services/supabase/client';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Helper function to execute RPC with timeout
const executeRpcWithTimeout = async (functionName, params, timeoutMs = 8000) => {
  // Create a promise that resolves with the result of the RPC call
  const rpcPromise = supabase.rpc(functionName, params);
  
  // Create a promise that rejects after the timeout
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`RPC call to ${functionName} timed out after ${timeoutMs}ms`)), timeoutMs);
  });
  
  // Race the RPC call against the timeout
  try {
    const result = await Promise.race([rpcPromise, timeoutPromise]);
    return result;
  } catch (error) {
    console.error(`Error or timeout in RPC call to ${functionName}:`, error);
    return { data: null, error };
  }
};

export default function VerifyOTPScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isRTL } = useRTLContext();
  const { isDarkMode, initializeTheme } = useTheme();
  const params = useLocalSearchParams();

  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationTimer, setVerificationTimer] = useState(null);

  // Add logging to debug phone number parameter
  useEffect(() => {
    console.log('Verify OTP screen mounted with phone param:', params.phone);
    console.log('Raw params:', params);

    // Set the phone number from params
    if (params.phone) {
      setPhoneNumber(params.phone as string);
    }
  }, [params]);

  useEffect(() => {
    console.log('Phone number state updated:', phoneNumber);
  }, [phoneNumber]);

  useEffect(() => {
    if (!phoneNumber && !params.phone) {
      console.error('No phone number available');
      Alert.alert(
        t('auth.verify.missingPhone.title', 'Error'),
        t('auth.verify.missingPhone.message', 'Phone number is missing. Please go back and try again.'),
        [
          {
            text: t('common.goBack', 'Go Back'),
            onPress: () => router.replace('/(auth)/login.v2'),
          },
        ]
      );
    }

    // Clean up timeout if component unmounts
    return () => {
      if (verificationTimer) {
        clearTimeout(verificationTimer);
      }
    };
  }, [phoneNumber, params.phone, router, t, verificationTimer]);

  const handleVerify = async () => {
    if (!otp) {
      setError(t('auth.verify.errors.otpRequired', 'Please enter the verification code'));
      return;
    }

    if (!phoneNumber) {
      setError(t('auth.verify.errors.phoneRequired', 'Phone number is missing'));
      return;
    }

    setError('');
    setIsLoading(true);

    // Set a timeout to prevent indefinite loading state
    const timer = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
        setError(t('auth.verify.errors.timeout', 'Verification timed out. Please try again.'));
        console.error('Verification process timed out');
      }
    }, 15000);
    setVerificationTimer(timer);

    try {
      console.log('Verifying OTP for phone:', phoneNumber);

      // Clear any existing verification flags
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');
      await AsyncStorage.removeItem('@app:user_role');

      // First check if a profile exists with this phone number
      const cleanPhoneNumber = phoneNumber.replace('+', '');
      console.log('Checking profile for phone:', cleanPhoneNumber);
      
      // Store the phone number for later use by layout
      await AsyncStorage.setItem('@app:verifying_phone', cleanPhoneNumber);
      
      // Try RPC function first with timeout
      const rpcFindProfile = async () => {
        console.log('Attempting to find profile by phone using RPC function');
        const { data, error } = await executeRpcWithTimeout('find_profile_by_phone', {
          phone_in: cleanPhoneNumber
        });
        
        if (error) {
          console.error('Error using find_profile_by_phone RPC:', error);
          return null;
        }
        
        if (Array.isArray(data) && data.length > 0) {
          console.log('Found profile using RPC function:', data[0]);
          return data[0];
        }
        return null;
      };
      
      // Try direct DB query as fallback
      const directProfileQuery = async () => {
        console.log('Attempting direct DB query for profile');
        const { data, error } = await supabase
          .from('profiles')
          .select('id, user_type, email, email_auth_id, phone_auth_id')
          .eq('phone', cleanPhoneNumber);
          
        if (error) {
          console.error('Profile check error (direct query):', error);
          return null;
        }
        
        if (Array.isArray(data) && data.length > 0) {
          console.log('Found profile via direct DB query:', data[0]);
          return data[0];
        }
        return null;
      };
      
      // Try both methods to find the profile
      let existingProfile = await rpcFindProfile();
      if (!existingProfile) {
        existingProfile = await directProfileQuery();
      }

      if (!existingProfile) {
        console.error('No profile found for phone number:', cleanPhoneNumber);
        clearTimeout(timer);
        setIsLoading(false);
        Alert.alert(
          t('auth.login.newUser.title', 'New User'),
          t('auth.login.newUser.message', 'This phone number is not registered. Please create an account first.'),
          [
            { text: t('common.cancel', 'Cancel'), style: 'cancel' },
            { text: t('auth.login.newUser.signUp', 'Sign Up'), onPress: () => router.push('/register') }
          ]
        );
        return;
      }

      // Check if this is a customer account
      if (existingProfile.user_type !== 'customer') {
        clearTimeout(timer);
        setError(t('auth.verify.errors.nonCustomerAccount', 'This phone number is registered as a non-customer account.'));
        setIsLoading(false);
        return;
      }

      console.log('Found profile:', existingProfile);
      
      // CRITICAL: Store profile ID immediately
      await AsyncStorage.setItem('@app:profile_id', existingProfile.id);
      await AsyncStorage.setItem('@app:user_role', 'customer');
      
      // Set verification in progress flag
      await AsyncStorage.setItem('@app:otp_verification_in_progress', 'true');
      await AsyncStorage.setItem('@app:verification_timestamp', Date.now().toString());

      // First attempt to sign the user out to clear any problematic session state
      await supabase.auth.signOut({ scope: 'local' });
      
      // Short delay to ensure signOut is processed
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verify the OTP
      console.log('Verifying OTP token');
      const { data: verifyData, error: verifyError } = await supabase.auth.verifyOtp({
        phone: phoneNumber,
        token: otp,
        type: 'sms',
      });

      if (verifyError) {
        console.error('OTP verification error:', verifyError);
        clearTimeout(timer);
        await AsyncStorage.removeItem('@app:otp_verification_in_progress');
        setError(verifyError.message || t('auth.verify.errors.failed', 'Verification failed. Please try again.'));
        setIsLoading(false);
        return;
      }

      console.log('OTP verification successful:', verifyData);

      if (!verifyData?.user?.id) {
        console.error('No user ID in verification data');
        clearTimeout(timer);
        await AsyncStorage.removeItem('@app:otp_verification_in_progress');
        setError(t('auth.verify.errors.noUserId', 'Verification failed: No user ID received'));
        setIsLoading(false);
        return;
      }

      // Link the phone auth ID to the profile - use both methods for maximum reliability
      console.log('Linking phone auth ID:', verifyData.user.id, 'to profile:', existingProfile.id);
      
      // Method 1: DIRECT DB UPDATE to ensure it works
      const { error: updatePhoneAuthIdError } = await supabase
        .from('profiles')
        .update({ phone_auth_id: verifyData.user.id })
        .eq('id', existingProfile.id);
        
      if (updatePhoneAuthIdError) {
        console.error('Error updating phone_auth_id directly:', updatePhoneAuthIdError);
      } else {
        console.log('Successfully updated phone_auth_id directly');
      }
      
      // Method 2: Try the RPC function as backup (with timeout)
      const linkAuthId = async () => {
        try {
          const { data, error } = await executeRpcWithTimeout('link_auth_id', {
            profile_id: existingProfile.id,
            new_auth_id: verifyData.user.id,
            auth_type: 'phone'
          });
          
          if (error) {
            console.error('Error linking phone auth ID via RPC:', error);
            return false;
          }
          
          console.log('Successfully linked phone auth ID via RPC:', data);
          return true;
        } catch (err) {
          console.error('Exception in link_auth_id RPC:', err);
          return false;
        }
      };
      
      // Try the RPC but don't block on it
      linkAuthId().catch(err => console.error('Unhandled error in linkAuthId:', err));

      // Update user metadata
      console.log('Updating user metadata');
      const { error: updateError } = await supabase.auth.updateUser({
        data: { 
          role: 'customer',
          user_type: 'customer',
          profile_id: existingProfile.id // Store profile ID in metadata
        }
      });

      if (updateError) {
        console.error('Error updating user metadata:', updateError);
        // Continue anyway
      } else {
        console.log('User metadata updated successfully');
      }

      // Add a small delay to ensure all updates are processed
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Clear verification flag
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');
      
      // Re-initialize theme after login
      await initializeTheme();
      
      console.log('Verification complete, navigating to home screen');
      
      // Clear the timeout since we're done
      clearTimeout(timer);
      
      // Navigate to home screen
      router.replace('/');
    } catch (err: any) {
      console.error('Unexpected error during OTP verification:', err);
      clearTimeout(timer);
      await AsyncStorage.removeItem('@app:otp_verification_in_progress');
      await AsyncStorage.removeItem('@app:user_role');
      await AsyncStorage.removeItem('@app:profile_id');
      setError(err.message || t('auth.verify.errors.failed', 'Verification failed. Please try again.'));
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!phoneNumber) {
      setError(t('auth.verify.errors.phoneRequired', 'Phone number is missing'));
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        phone: phoneNumber,
      });

      if (error) {
        console.error('Error resending OTP:', error);
        setError(error.message || t('auth.verify.errors.resendFailed', 'Failed to resend code. Please try again.'));
        return;
      }

      Alert.alert(
        t('auth.verify.resendSuccess.title', 'Code Sent'),
        t('auth.verify.resendSuccess.message', 'A new verification code has been sent to your phone.')
      );
    } catch (err: any) {
      console.error('Unexpected error resending OTP:', err);
      setError(err.message || t('auth.verify.errors.resendFailed', 'Failed to resend code. Please try again.'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }
    ]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <RTLView style={styles.header}>
            <Image source={isRTL ? require('@/assets/logoSVG-ar.png') : require('@/assets/logoSVG-en.png')} style={styles.logoImage} />
            <RTLText style={[styles.title, { color: isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500] }]}>
              {t('auth.verify.title', 'Verify Your Phone')}
            </RTLText>
            <RTLText style={[styles.subtitle, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {t('auth.verify.description', 'Enter the verification code sent to your phone')}
            </RTLText>
          </RTLView>

          <View style={styles.inputContainer}>
            <RTLTextInput
              style={[
                styles.input,
                { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light },
                { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }
              ]}
              placeholder={t('auth.verify.otpPlaceholder', 'Verification Code')}
              placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
              value={otp}
              onChangeText={setOtp}
              keyboardType="number-pad"
              maxLength={6}
            />
          </View>

          {error ? (
            <RTLText style={styles.errorText}>
              {error}
            </RTLText>
          ) : null}

          <TouchableOpacity
            style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
            onPress={handleVerify}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color={isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600]} />
            ) : (
              <RTLText style={[styles.buttonText, { color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600] }]}>
                {t('auth.verify.verifyButton', 'Verify')}
              </RTLText>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.resendButton}
            onPress={handleResendCode}
            disabled={isLoading}
          >
            <RTLText style={[styles.resendText, { color: colors.brandColors.primary[500] }]}>
              {t('auth.verify.resendCode', 'Resend Code')}
            </RTLText>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    flexDirection: 'column',
    marginTop: 40,
    marginBottom: 40,
    alignItems: 'center',
  },
  logoImage: {
    height: 32,
    resizeMode: 'contain',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 24,
  },
  input: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    textAlign: 'center',
    letterSpacing: 8,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: colors.brandColors.primary[500],
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    color: colors.brandColors.danger[500],
    marginBottom: 16,
    fontSize: 14,
  },
  resendButton: {
    marginTop: 24,
    alignItems: 'center',
  },
  resendText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
