import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/context/ThemeContext';
import OrderCompleteScreen from '../order-complete';

// Mock the required hooks and modules
jest.mock('expo-router', () => ({
  useRouter: jest.fn(),
  useLocalSearchParams: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(),
}));

jest.mock('@/context/ThemeContext', () => ({
  useTheme: jest.fn(),
}));

jest.mock('@expo/vector-icons', () => ({
  MaterialIcons: 'MaterialIcons',
}));

describe('OrderCompleteScreen', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
  };

  const mockT = jest.fn((key) => key);

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useLocalSearchParams as jest.Mock).mockReturnValue({ orderId: '123' });
    (useTranslation as jest.Mock).mockReturnValue({ t: mockT });
    (useTheme as jest.Mock).mockReturnValue({ isDarkMode: false });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with light theme', () => {
    const { getByText, getByTestId } = render(<OrderCompleteScreen />);

    expect(getByText('Order Placed Successfully!')).toBeTruthy();
    expect(getByText('Thank you for your order. We\'ll send you a confirmation email shortly.')).toBeTruthy();
    expect(getByTestId('view-order-button')).toBeTruthy();
    expect(getByTestId('back-home-button')).toBeTruthy();
  });

  it('renders correctly with dark theme', () => {
    (useTheme as jest.Mock).mockReturnValue({ isDarkMode: true });
    const { getByText, getByTestId } = render(<OrderCompleteScreen />);

    expect(getByText('Order Placed Successfully!')).toBeTruthy();
    expect(getByText('Thank you for your order. We\'ll send you a confirmation email shortly.')).toBeTruthy();
    expect(getByTestId('view-order-button')).toBeTruthy();
    expect(getByTestId('back-home-button')).toBeTruthy();
  });

  it('navigates to order details when view order button is pressed', () => {
    const { getByTestId } = render(<OrderCompleteScreen />);
    fireEvent.press(getByTestId('view-order-button'));

    expect(mockRouter.push).toHaveBeenCalledWith('/orders/123');
  });

  it('navigates to home when back to home button is pressed', () => {
    const { getByTestId } = render(<OrderCompleteScreen />);
    fireEvent.press(getByTestId('back-home-button'));

    expect(mockRouter.replace).toHaveBeenCalledWith('/');
  });

  it('translates text correctly', () => {
    const mockTranslate = jest.fn((key) => `translated_${key}`);
    (useTranslation as jest.Mock).mockReturnValue({ t: mockTranslate });
    
    render(<OrderCompleteScreen />);

    expect(mockTranslate).toHaveBeenCalledWith('Order Placed Successfully!');
    expect(mockTranslate).toHaveBeenCalledWith('Thank you for your order. We\'ll send you a confirmation email shortly.');
    expect(mockTranslate).toHaveBeenCalledWith('View Order Details');
    expect(mockTranslate).toHaveBeenCalledWith('Back to Home');
  });
}); 