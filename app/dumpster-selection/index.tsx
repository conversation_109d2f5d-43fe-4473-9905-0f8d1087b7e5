import React, { useState, useEffect, useRef } from 'react';
import { View, Text, ScrollView, ActivityIndicator, Platform, SafeAreaView, TextInput, Keyboard, TouchableOpacity, Image, FlatList, LogBox, KeyboardAvoidingView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { styled } from 'nativewind';
import { Stack, useRouter } from 'expo-router';
import { Button, Divider, FAB, IconButton, useTheme as usePaperTheme } from 'react-native-paper';
import { useWasteTypes } from '@/hooks/useDumpsters';
import { useDumpsters, getDumpsters } from '@/hooks/v2/useDumpsters';
import { useAIRecommendations } from '@/hooks/useAIRecommendations';
import { useUserLocation } from '@/hooks/useUserLocation';
import { useTheme } from '@/context/ThemeContext';
import { NewRTLTextInput, useRTLContext } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { useAuth } from '@/context/AuthContext';
import { useProfile } from '@/hooks/useProfile';

import DumpsterCardV2 from '../components/v2/DumpsterCard';
//import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, FontAwesome5 } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { shadowStyles } from '@/utils/shadowStyles';
import { Dumpster as NewDumpster, WasteType } from '@/types/new/dumpster';
import { Dumpster as V2Dumpster } from '@/types/v2/dumpster';
import { chatWithAI } from '@/services/ai/openai';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledDivider = styled(Divider);
const StyledButton = styled(Button);
const StyledFAB = styled(FAB);
const StyledSafeAreaView = styled(SafeAreaView);
const StyledTextInput = styled(TextInput);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledLinearGradient = styled(LinearGradient);

// Define ProcessedRecommendation type
type ProcessedRecommendation = {
  dumpster: V2Dumpster;
  score: number;
  reasons: string[];
  aiResponse: string;
};

// Import error boundary component or create a simpler one for this file
function SafeRender({
  children,
  fallback
}: {
  children: React.ReactNode,
  fallback: React.ReactNode
}) {
  try {
    return <>{children}</>;
  } catch (error) {
    console.error("Render error:", error);
    return <>{fallback}</>;
  }
}

export default function DumpsterSelectionScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const paperTheme = usePaperTheme();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const { session } = useAuth();
  const { profile } = useProfile(session?.user?.id || '');
  LogBox.ignoreAllLogs(true);

  // State
  const [inputText, setInputText] = useState('');
  const [finalQuery, setFinalQuery] = useState('');
  const [hasGivenRecommendation, setHasGivenRecommendation] = useState(false);
  const [threadId, setThreadId] = useState<string | undefined>(undefined);
  const [showResetButton, setShowResetButton] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  // Modified setQuery handler
  const handleInputChange = (text: string) => {
    setInputText(text);
  };

  const handleInputSubmit = () => {
    if (!inputText.trim()) return;
    setFinalQuery(inputText.trim());
    handleSubmit(inputText.trim());
  };

  const [isRecording, setIsRecording] = useState(false);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [recommendations, setRecommendations] = useState<ProcessedRecommendation[]>([]);
  const [isLoadingDumpsters, setIsLoadingDumpsters] = useState(false);
  const [convertedDumpsters, setConvertedDumpsters] = useState<NewDumpster[]>([]);
  const [chatHistory, setChatHistory] = useState<Array<{
    type: 'user' | 'ai';
    message: string;
    timestamp: number;
  }>>([]);

  // Queries
  const { data: wasteTypes } = useWasteTypes();
  const { data: dumpsters, isLoading: isLoadingDumpstersData } = useDumpsters();
  const { mutate: getRecommendations, isPending: isLoadingRecommendations } = useAIRecommendations();
  const { data: userLocation, isLoading: isLoadingLocation } = useUserLocation();

  // Convert dumpsters from the old format to the new format
  useEffect(() => {
    if (dumpsters && dumpsters.length > 0) {
      // Since we're now directly using V2 dumpsters, there's no need to convert
      console.log(`Loaded ${dumpsters.length} dumpsters from V2 API`);
      // setConvertedDumpsters is for legacy code, we won't use it anymore
    }
  }, [dumpsters]);

  // Handle keyboard visibility
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setShowKeyboard(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setShowKeyboard(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Handle dumpster selection
  const handleDumpsterPress = (dumpsterId: string) => {
    // Navigate using the ID - the details page will resolve old/new IDs
    router.push(`/dumpster-selection/${dumpsterId}`);
  };

  // Handle voice input
  const handleVoicePress = () => {
    //Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsRecording(true);
    // In a real implementation, we would start voice recording here
  };

  const handleVoiceRelease = () => {
    if (isRecording) {
      setIsRecording(false);
      // In a real implementation, we would stop recording and process the audio
    }
  };

  // Handle AI recommendations from query
  const handleSubmit = async (submittedQuery: string) => {
    if (!submittedQuery.trim()) return;

    setIsLoadingDumpsters(true);
    Keyboard.dismiss();

    try {
      // Reset thread if previous recommendation was given
      if (hasGivenRecommendation) {
        setThreadId(undefined);
        setHasGivenRecommendation(false);
      }

      // Add message to chat history
      const newMessage = {
        type: 'user' as const,
        message: submittedQuery,
        timestamp: Date.now()
      };

      setChatHistory(prev => [...prev, newMessage]);

      // Convert chat history to AI messages format
      const aiMessages = chatHistory.map(chat => ({
        role: chat.type === 'user' ? 'user' as const : 'assistant' as const,
        content: chat.message
      }));

      // Add current message
      aiMessages.push({
        role: 'user',
        content: submittedQuery
      });

      const aiResponse = await chatWithAI(
        aiMessages,
        profile?.full_name?.split(' ')[0],
        hasGivenRecommendation ? undefined : threadId // Only pass threadId if no recommendation yet
      );

      // Save new threadId if received
      if (aiResponse.threadId) {
        setThreadId(aiResponse.threadId);
      }

      setChatHistory(prev => [...prev, {
        type: 'ai',
        message: aiResponse.reply,
        timestamp: Date.now()
      }]);

      // Process recommendations if present
      if (aiResponse.recommendation) {
        setHasGivenRecommendation(true);
        try {
          const { size, wasteTypes } = aiResponse.recommendation;
          console.log('----------AI Response:', aiResponse);
          
          // Fetch dumpsters using V2 API directly
          const v2Dumpsters = await getDumpsters();
          
          // Filter matching dumpsters based on size requirements
          const matchingDumpsters = v2Dumpsters?.filter((dumpster: V2Dumpster) => {
            const dumpsterCapacity = dumpster.capacity || 0;
            return dumpsterCapacity >= size.min && dumpsterCapacity <= size.max;
          }) || [];

          // If no matching dumpsters found, use fallback recommendations
          if (matchingDumpsters.length === 0) {
            const fallbackRecs = createV2FallbackRecommendations(v2Dumpsters || []);
            const recommendationsWithAiResponse = fallbackRecs.map(rec => ({ 
              ...rec, 
              aiResponse: aiResponse.reply 
            }));
            
            setRecommendations(recommendationsWithAiResponse);
          } else {
            // Process matching dumpsters into recommendations
            const processedRecommendations = matchingDumpsters
              .slice(0, 3)
              .map((dumpster: V2Dumpster, index: number) => {
                return {
                  dumpster: dumpster,
                  score: 95 - (index * 5),
                  reasons: [
                    'Matches your project requirements',
                    `${dumpster.capacity || 10} cubic yards capacity`,
                    ...(wasteTypes || []).map((type: any) => `Suitable for ${type.name_en || type.nameEn}`),
                    'Available for immediate rental'
                  ],
                  aiResponse: aiResponse.reply
                };
              });

            setRecommendations(processedRecommendations);
          }
        } catch (error) {
          console.error('Error processing recommendations:', error);
          setRecommendations([]);
        }
      }

      setInputText('');
      setFinalQuery('');
    } catch (error) {
      console.error('Error getting recommendations:', error);
      setChatHistory(prev => [...prev, {
        type: 'ai',
        message: "I'm having trouble processing your request. Please try again.",
        timestamp: Date.now()
      }]);
    } finally {
      setIsLoadingDumpsters(false);
    }
  };

  // Create fallback recommendations from V2 dumpsters
  const createV2FallbackRecommendations = (dumpsters: V2Dumpster[]) => {
    if (!dumpsters?.length) return [];

    return dumpsters
      .slice(0, 3)
      .map((dumpster, index) => {
        return {
          dumpster: dumpster,
          score: 95 - (index * 5),
          reasons: [
            'General purpose dumpster',
            `${dumpster.capacity || 10} cubic yards capacity`,
            'Available for immediate rental'
          ]
        };
      });
  };

  // Handle recommendation click to navigate to dumpster detail
  const handleRecommendationPress = (dumpster: V2Dumpster) => {
    if (!dumpster?.id) return;

    //Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push(`/dumpster-selection/${dumpster.id}`);
  };

  // Handle example click - Modified to immediately submit
  const handleExamplePress = async (example: string) => {
    //Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setFinalQuery(example);
    // Wrap in setTimeout to ensure state is updated before submission
    setTimeout(() => {
      handleSubmit(example);
    }, 100);
  };

  // Example queries to help users get started
  const exampleQueries = [
    t('dumpsterSelection.initialPrompt.examples.construction'),
    t('dumpsterSelection.initialPrompt.examples.yard'),
    t('dumpsterSelection.initialPrompt.examples.moving'),
    t('dumpsterSelection.initialPrompt.examples.renovation'),
  ];

  // Add this near the top of the component to debug available dumpsters
  useEffect(() => {
    console.log('-------Available dumpsters:', dumpsters);
  }, [dumpsters]);

  // Reset states when component unmounts or user starts new conversation
  useEffect(() => {
    return () => {
      setThreadId(undefined);
      setHasGivenRecommendation(false);
    };
  }, []);

  // Reset chat state function
  const handleResetChat = () => {
    //Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium); // Optional haptics
    setInputText('');
    setFinalQuery('');
    setHasGivenRecommendation(false);
    setThreadId(undefined);
    setRecommendations([]);
    setChatHistory([]);
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  };

  // Effect to scroll to bottom when chat history changes
  useEffect(() => {
    if (chatHistory.length > 0 && scrollViewRef.current) {
      // Use setTimeout to allow the UI to render the new message first
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100); // Short delay
    }
  }, [chatHistory]); // Dependency array includes chatHistory

  // Update reset button visibility based on chat state
  useEffect(() => {
    setShowResetButton(chatHistory.length > 0 || recommendations.length > 0);
  }, [chatHistory, recommendations]);

  // Render function for FlatList items
  const renderRecommendationItem = ({ item }: { item: ProcessedRecommendation }) => {
    if (!item || typeof item !== 'object' || !item.dumpster || typeof item.dumpster !== 'object') {
      return null;
    }

    return (
      <StyledView className="mb-4 mr-4 w-72">
        <DumpsterCardV2
          dumpster={item.dumpster}
          onPress={() => handleRecommendationPress(item.dumpster)}
          isRecommended={true}
          recommendationScore={item.score}
        />
        <StyledView className="rounded-lg p-3 mt-2" style={{
          backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
        }}>
          <StyledText className="font-medium mb-1" style={{
            color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
            textAlign: isRTL ? 'right' : 'left',
          }}>
            {t('dumpsterSelection.whyThisWorks')}
          </StyledText>
          <StyledText style={{
            color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
            textAlign: isRTL ? 'right' : 'left',
          }}>
            {Array.isArray(item.reasons) ? item.reasons.join('. ') : ''}
          </StyledText>
        </StyledView>
      </StyledView>
    );
  };

  // UPDATE this function to navigate
  const handleShowAllPress = () => {
    //setIsBottomSheetVisible(true);
    //bottomSheetModalRef.current?.present();
    // Instead, navigate to the new modal screen
    router.push('/all-dumpsters'); // Route name TBD
    // Add Haptics if desired
    // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  return (
    <StyledLinearGradient
      className="flex-1"
      colors={isDarkMode
        ? [colors.brandColors.primary[900], colors.brandColors.secondary[900]]
        : [colors.brandColors.primary[100], colors.brandColors.secondary[100]]
      }
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1.3 }}
    >
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <Stack.Screen
        options={{
          headerShown: false
        }}
      />

      <StyledSafeAreaView className="flex-1">
        {/* Header */}
        <StyledView className="flex-row items-center justify-between px-4 pt-2 pb-2" style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
        }}>
          {/* Left Group (Back Button + Title) */}
          <StyledView className="flex-row items-center" style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
            <StyledTouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 justify-center items-center"
            >
              <Ionicons
                name={isRTL ? "arrow-forward" : "arrow-back"}
                size={24}
                color={isDarkMode ? colors.textColors.dark : colors.textColors.light}
              />
            </StyledTouchableOpacity>

            <StyledText className="text-xl font-semibold" style={{
              marginLeft: isRTL ? 0 : 8,
              marginRight: isRTL ? 8 : 0,
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
            }}>
              {t('dumpsterSelection.title')}
            </StyledText>
          </StyledView>

          {/* Reset Button (conditionally rendered) */}
          {showResetButton && (
            <IconButton
              icon="refresh"
              size={24}
              iconColor={isDarkMode ? colors.textColors.dark : colors.textColors.light}
              onPress={handleResetChat}
              style={{ margin: 0, padding: 0 }} // Adjust styling as needed
            />
          )}
        </StyledView>

        <StyledDivider style={{
          backgroundColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
        }} />

        {/* Chat-like interface */}
        <StyledScrollView
          ref={scrollViewRef}
          className="flex-1 pt-4 px-6"
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ paddingBottom: showKeyboard ? 80 : 40 }}
        >
          {/* Initial prompt */}
          {!chatHistory.length && !isLoadingDumpsters && (
            <StyledView className="mb-8">
              <StyledText className="text-lg font-medium mb-2" style={{
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                textAlign: isRTL ? 'right' : 'left',
              }}>
                {t('dumpsterSelection.initialPrompt.title')}
              </StyledText>
              <StyledText className="mb-6" style={{
                color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
                textAlign: isRTL ? 'right' : 'left',
              }}>
                {t('dumpsterSelection.initialPrompt.description')}
              </StyledText>

              {/* Example queries */}
              <StyledText className="mb-2 font-medium" style={{
                color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
                textAlign: isRTL ? 'right' : 'left',
              }}>
                {t('dumpsterSelection.initialPrompt.examplesTitle')}
              </StyledText>

              {exampleQueries.map((example, index) => (
                <StyledTouchableOpacity
                  key={index}
                  onPress={() => handleExamplePress(example)}
                  className="rounded-lg p-4 mb-3"
                  style={{
                    borderBottomLeftRadius: isRTL ? 16 : 4,
                    borderBottomRightRadius: isRTL ? 4 : 16,
                    backgroundColor: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
                    alignSelf: isRTL ? 'flex-end' : 'flex-start',
                    ...shadowStyles.md
                  }}
                >
                  <StyledText style={{
                    color: isDarkMode ? colors.textColors.light : colors.textColors.dark,
                    textAlign: isRTL ? 'right' : 'left',
                  }}>
                    {example}
                  </StyledText>
                </StyledTouchableOpacity>
              ))}
            </StyledView>
          )}

          {/* Chat History */}
          {Array.isArray(chatHistory) && chatHistory.length > 0 && chatHistory.map((chat, index) => {
            if (!chat || typeof chat !== 'object') return null;
            
            return (
              <StyledView key={`chat-${index}`} className="mb-4">
                {/* User Message */}
                {chat.type === 'user' && chat.message && (
                  <StyledView
                    className="self-end rounded-lg px-4 py-5 mb-4 max-w-[85%]"
                    style={{
                      borderBottomLeftRadius: isRTL ? 16 : 4,
                      borderBottomRightRadius: isRTL ? 4 : 16,
                      backgroundColor: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
                      alignSelf: isRTL ? 'flex-end' : 'flex-start',
                      ...shadowStyles.md
                    }}
                  >
                    <StyledText style={{
                      color: isDarkMode ? colors.textColors.light : colors.textColors.dark,
                      textAlign: isRTL ? 'right' : 'left',
                    }}>
                      {String(chat.message)}
                    </StyledText>
                  </StyledView>
                )}

                {/* AI Response */}
                {chat.type === 'ai' && chat.message && (
                  <StyledView
                    className="self-start rounded-lg px-4 py-5 mb-4 max-w-[85%]"
                    style={{
                      borderTopLeftRadius: isRTL ? 4 : 16,
                      borderTopRightRadius: isRTL ? 16 : 4,
                      backgroundColor: isDarkMode ? colors.brandColors.secondary[300] : colors.brandColors.secondary[600],
                      alignSelf: isRTL ? 'flex-start' : 'flex-end',
                      ...shadowStyles.lg
                    }}
                  >
                    <StyledText style={{
                      color: isDarkMode ? colors.textColors.light : colors.textColors.dark,
                      textAlign: isRTL ? 'right' : 'left',
                    }}>
                      {String(chat.message)}
                    </StyledText>
                  </StyledView>
                )}
              </StyledView>
            );
          })}

          {/* Loading indicator */}
          {isLoadingDumpsters && (
            <StyledView className="self-start rounded-2xl px-4 py-3 mb-4">
              <ActivityIndicator color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
            </StyledView>
          )}

          {/* Recommendations Carousel */}
          {Array.isArray(recommendations) && recommendations.length > 0 && !isLoadingDumpsters && (
            <StyledView className="mt-4 mb-4">
              <FlatList
                data={recommendations}
                renderItem={renderRecommendationItem}
                keyExtractor={(item) => `recommendation-${item.dumpster.id}`}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingLeft: 0, paddingRight: 0 }}
              />
            </StyledView>
          )}
        </StyledScrollView>

        {/* Floating "Show All Dumpsters" Button (keep this) */}
        {!chatHistory.length && !isLoadingDumpsters && (
        <StyledTouchableOpacity
          onPress={handleShowAllPress}
          className="absolute bottom-32 flex-row items-center justify-center rounded-full px-6 py-3 shadow-lg z-10"
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            backgroundColor: isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500],
            alignSelf: 'center'
          }}
        >
          <StyledText style={{ color: colors.textColors.light }}>{t('dumpsterSelection.showAll')}</StyledText>
          <Ionicons name="open-outline" size={24} color={colors.textColors.light} style={{ transform: [{ scaleX: isRTL ? -1 : 1 }], marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }} />
        </StyledTouchableOpacity>
        )}

        {/* Input area */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <StyledView className="px-4 pb-4">
            <StyledView className="flex-row items-center rounded-full border px-4 py-2" style={{
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
              borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
              flexDirection: isRTL ? 'row-reverse' : 'row',
              ...shadowStyles.sm
            }}>
              <TextInput
                placeholder={t('dumpsterSelection.input.placeholder')}
                value={inputText}
                onChangeText={handleInputChange}
                onSubmitEditing={() => handleInputSubmit()}
                multiline={false}
                returnKeyType="send"
                style={{
                  flex: 1,
                  paddingVertical: 12,
                  color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  textAlign: isRTL ? 'right' : 'left',
                }}
                placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                autoCorrect={false}
                spellCheck={false}
                keyboardType="default"
                autoCapitalize="none"
                textContentType="none"
                contextMenuHidden={true}
                enablesReturnKeyAutomatically={true}
                allowFontScaling={false}
                maxFontSizeMultiplier={1}
              />

              {inputText.trim() ? (
                <StyledTouchableOpacity
                  onPress={handleInputSubmit}
                  className="w-10 h-10 justify-center items-center"
                >
                  <Ionicons
                    name="send"
                    size={20}
                    color={colors.brandColors.primary[500]}
                    style={{ transform: [{ scaleX: isRTL ? -1 : 1 }] }}
                  />
                </StyledTouchableOpacity>
              ) : (
                <StyledTouchableOpacity
                  onPressIn={handleVoicePress}
                  onPressOut={handleVoiceRelease}
                  className="w-10 h-10 justify-center items-center"
                >
                  {/* <FontAwesome5
                    name="microphone"
                    size={20}
                    color={isRecording ? colors.brandColors.danger[500] : colors.brandColors.primary[500]}
                  /> */}
                </StyledTouchableOpacity>
              )}
            </StyledView>
          </StyledView>
        </KeyboardAvoidingView>

      </StyledSafeAreaView>
    </StyledLinearGradient>
  );
} 
