import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Image, StyleSheet, ActivityIndicator, Dimensions, TouchableOpacity, StatusBar as RNStatusBar, LogBox } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { But<PERSON>, Divider, IconButton } from 'react-native-paper';
// Use V2 Hooks and Types
import { useDumpster, useWasteTypes } from '@/hooks/v2/useDumpsters';
import { Dumpster, DumpsterSize, WasteType, DumpsterFeature } from '@/types/v2/dumpster';
import { AntDesign, MaterialCommunityIcons, FontAwesome5, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { useRTLContext } from '@/components/rtl/RTLContext';
import NewRTLText from '@/components/rtl/NewRTLText';

LogBox.ignoreAllLogs(true);

const { width } = Dimensions.get('window');

// --- Error Boundary Component (copied from original) ---
function ErrorBoundary({ children, fallback = null }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  // Note: Removed componentDidCatch for functional component, 
  // relying on try/catch for render errors.

  if (hasError) {
    return (
      <View style={styles.errorContainer}>
        <NewRTLText style={styles.errorText}>
          Something went wrong {fallback}
        </NewRTLText>
      </View>
    );
  }

  try {
    // Wrap children in a fragment or View to ensure a single root
    return <>{children}</>; 
  } catch (error) {
    console.error('Render error caught by ErrorBoundary:', error);
    // Instead of using state directly in catch, let the next render handle it
    // Or use a different mechanism if immediate fallback is needed.
    // For simplicity, we'll assume the component state handles this.
    // setHasError(true); // This might cause issues depending on React version
    return (
      <View style={styles.errorContainer}>
        <NewRTLText style={styles.errorText}>
          Error rendering content | {fallback}
        </NewRTLText>
      </View>
    );
  }
}
// --- End Error Boundary ---

export default function DumpsterDetailScreenV2() {
  const { t } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const dumpsterId = id as string;
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  // State
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [renderError, setRenderError] = useState<string | null>(null); // For handling general render errors

  // Fetch V2 dumpster data
  // Note: The V2 useDumpster hook should ideally populate size, features, wasteTypes directly
  const { data: dumpster, isLoading, error: fetchError } = useDumpster(dumpsterId);

  // Handle image load errors
  const handleImageError = () => {
    console.error('Error loading image for dumpster', dumpster?.imageUrl);
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  // Handle Add to Cart
  const handleAddToCart = () => {
    if (!dumpster) return; // Should not happen if button is enabled
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    router.push({
      pathname: '/checkout',
      params: {
        id: dumpster.id,
        partnerId: dumpster.partnerId || '' // Ensure partnerId is passed
      }
    });
  };

  // Render loading state
  if (isLoading) {
    return (
      <LinearGradient
        style={styles.fullScreenCenter}
        colors={isDarkMode ? [colors.backgroundColors.main.dark, colors.backgroundColors.main.dark] : ['#f5f7fa', '#e1eafc']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <ActivityIndicator size="large" color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[500]} />
        <NewRTLText style={[styles.loadingText, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>
          {t('Loading dumpster details...')}
        </NewRTLText>
      </LinearGradient>
    );
  }

  // Handle fetch error
  if (fetchError) {
    console.error("Error fetching dumpster:", fetchError);
    return (
      <View style={styles.errorScreen}>
        <NewRTLText style={styles.errorTitle}>
          {t('Error loading dumpster')}
        </NewRTLText>
        <TouchableOpacity
          style={styles.errorButton}
          onPress={() => router.back()}
        >
          <NewRTLText style={styles.errorButtonText}>
            {t('Go Back')}
          </NewRTLText>
        </TouchableOpacity>
      </View>
    );
  }

  // Handle case where dumpster is not found
  if (!dumpster) {
    return (
      <View style={styles.errorScreen}>
        <NewRTLText style={styles.errorTitle}>
          {t('Dumpster not found')}
        </NewRTLText>
        <TouchableOpacity
          style={styles.errorButton}
          onPress={() => router.back()}
        >
          <NewRTLText style={styles.errorButtonText}>
            {t('Go Back')}
          </NewRTLText>
        </TouchableOpacity>
      </View>
    );
  }

  // Render error state if something went wrong during rendering (caught by ErrorBoundary)
  if (renderError) {
      return (
        <View style={styles.errorScreen}>
          <NewRTLText style={styles.errorTitle}>{renderError}</NewRTLText>
           <TouchableOpacity
            style={styles.errorButton}
            onPress={() => router.back()}
          >
            <NewRTLText style={styles.errorButtonText}>{t('Go Back')}</NewRTLText>
          </TouchableOpacity>
        </View>
      );
  }

  // --- Prepare display data --- 
  const name = isRTL ? dumpster.nameAr : dumpster.nameEn;
  const description = dumpster.description ? (isRTL ? dumpster.description.ar : dumpster.description.en) : '';
  const imageUrl = dumpster.imageUrl; // V2 should have a direct URL
  const displayPrice = dumpster.pricePerLoad || 0;
  const displayRating = dumpster.rating || 4.0;
  const displayReviewCount = dumpster.reviewCount || 0;
  const displayIsAvailable = dumpster.isAvailable;
  const displayNextAvailableDate = dumpster.nextAvailableDate;
  const selectedSize = dumpster.size; // V2 model has size directly
  const compatibleWasteTypes = dumpster.wasteTypes || []; // Assume hook populates this
  const displayFeatures = dumpster.features || []; // Assume hook populates this

  return (
    <LinearGradient
      style={styles.container}
      colors={[isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light, isDarkMode ? colors.brandColors.secondary[800] : colors.brandColors.secondary[300]]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <RNStatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />

      <Stack.Screen
        options={{
          headerShown: true,
          headerTransparent: true,
          headerTitle: '',
          headerLeft: () => (
            <IconButton
              icon="arrow-left" // MaterialCommunityIcons name
              size={24}
              onPress={() => router.back()}
              style={[styles.headerIcon, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}
              iconColor={isDarkMode ? colors.textColors.dark : colors.textColors.light}
            />
          ),
          headerRight: () => (
            <IconButton
              icon="share-variant" // MaterialCommunityIcons name
              size={24}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                // Add sharing logic here
              }}
              style={[styles.headerIcon, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}
               iconColor={isDarkMode ? colors.textColors.dark : colors.textColors.light}
            />
          ),
        }}
      />

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
      >
        {/* --- Hero Image Section --- */}
        <View style={[styles.heroImageContainer]}>
           {imageLoading && (
            <View style={styles.imageLoadingContainer}>
              <ActivityIndicator size="large" color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
            </View>
          )}

          <Image
            source={{ uri: imageUrl }}
            style={styles.heroImage}
            resizeMode="cover"
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={handleImageLoad}
            onError={handleImageError}
          />

          {imageError && (
            <View style={styles.imageErrorContainer}>
              <MaterialCommunityIcons name="image-off" size={48} color={isDarkMode ? colors.brandColors.danger[400] : colors.brandColors.danger[500]} />
              <NewRTLText style={styles.imageErrorText}>
                {t('Image not available')}
              </NewRTLText>
            </View>
          )}

          {/* Availability Badge (Top Right) */}
          {!displayIsAvailable && (
            <View style={[styles.availabilityBadge, { backgroundColor: colors.brandColors.danger[500] }]}>
              <NewRTLText style={styles.availabilityText}>
                {displayNextAvailableDate
                  ? t('Available from {{date}}', { date: new Date(displayNextAvailableDate).toLocaleDateString() })
                  : t('Not Available')}
              </NewRTLText>
            </View>
          )}
          
          {/* Rating Badge (Bottom Left) */}
          {displayRating > 0 && (
            <View style={[styles.ratingBadge, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
              <MaterialIcons name="star" size={16} color={colors.brandColors.warning[500]} />
              <NewRTLText style={[styles.ratingText, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>
                {`${Number(displayRating).toFixed(1)}`}
              </NewRTLText>
              <NewRTLText style={[styles.reviewCountText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
                {` (${displayReviewCount})`}
              </NewRTLText>
            </View>
          )}
        </View>

        {/* --- Content Section --- */}
        <ErrorBoundary fallback={
          <View style={styles.errorContainer}>
            <NewRTLText style={styles.errorText}>
              {t('Error displaying dumpster details')}
            </NewRTLText>
          </View>
        }>
          <View style={styles.contentContainer}>
             {/* Header: Title, Price, Description */}
             <View style={[
                styles.headerSection, 
                { borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light } // Dynamic border color
             ]}>
                <NewRTLText style={[styles.dumpsterTitle, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>{name}</NewRTLText>
                <View style={styles.priceContainer}>
                  <View style={styles.priceTextContainer}>
                     {isDarkMode ? (<Image
                      source={require('../../../assets/images/currency/Saudi_Riyal_Symbol_primary.png')} // Adjusted path
                      style={styles.currencySymbol}
                      resizeMode="contain"
                    />) : (<Image
                      source={require('../../../assets/images/currency/Saudi_Riyal_Symbol.png')} // Adjusted path
                      style={styles.currencySymbol}
                      resizeMode="contain"
                    />)}
                    <NewRTLText style={[styles.priceText, {color: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[600] }]}>
                      {`${displayPrice}`}
                    </NewRTLText>
                     <NewRTLText style={[styles.priceUnitText, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
                      {` / ${t('Load')}`}
                    </NewRTLText>
                  </View>
                  {/* Optional: Add Partner Logo Here */}
                  {/* {dumpster.partnerLogo && <Image source={{uri: dumpster.partnerLogo }} style={styles.partnerLogo} />} */}
                </View>
                {description && (
                  <NewRTLText style={[styles.descriptionText, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                    {description}
                  </NewRTLText>
                )}
             </View>

             {/* Specifications Section (using selectedSize) */}
             {selectedSize && (
               <View style={[styles.specificationsContainer, {backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light}]}>
                 <NewRTLText style={[styles.sectionTitle, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                   {t('Specifications')}
                 </NewRTLText>
                 <View style={styles.specificationsGrid}>
                   {/* Volume */}
                   <View style={styles.specificationItem}>
                     <MaterialCommunityIcons name="cube-outline" size={24} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                     <NewRTLText style={[styles.specificationLabel, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                       {t('Volume')}
                     </NewRTLText>
                     <NewRTLText style={[styles.specificationValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                       {selectedSize.capacity} {t('yd³')}
                     </NewRTLText>
                   </View>
                   {/* Max Weight */}
                   <View style={styles.specificationItem}>
                     <MaterialCommunityIcons name="weight" size={24} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                     <NewRTLText style={[styles.specificationLabel, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                       {t('Max Weight')}
                     </NewRTLText>
                     <NewRTLText style={[styles.specificationValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                       {selectedSize.maxWeight} {t('lbs')}
                     </NewRTLText>
                   </View>
                   {/* Dimensions */}
                   <View style={styles.specificationItem}>
                     <MaterialCommunityIcons name="ruler" size={24} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                     <NewRTLText style={[styles.specificationLabel, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                       {t('Dimensions')}
                     </NewRTLText>
                     <NewRTLText style={[styles.specificationValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                       {`${selectedSize.length} × ${selectedSize.width} × ${selectedSize.height} m`}
                     </NewRTLText>
                   </View>
                 </View>
                 {/* Size Description */}
                 {selectedSize.description && (isRTL ? selectedSize.description.ar : selectedSize.description.en) && (
                   <NewRTLText style={[styles.sizeDescription, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                     {isRTL ? selectedSize.description.ar : selectedSize.description.en}
                   </NewRTLText>
                 )}
               </View>
             )}

             {/* Compatible Waste Types Section */}
            <View style={styles.section}>
              <NewRTLText style={[styles.sectionTitle, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                {t('Compatible Waste Types')}
              </NewRTLText>
              <View style={[styles.tagsContainer, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
                {compatibleWasteTypes.length > 0 ? (
                  compatibleWasteTypes.map((wasteType) => (
                    <View 
                      key={wasteType.id} 
                      style={[
                        styles.tag, 
                        { flexDirection: isRTL ? 'row-reverse' : 'row' }, 
                        { marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }, // Spacing adjustment
                        { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }
                      ]}
                    >
                      <MaterialCommunityIcons name="check-circle-outline" size={16} color={isDarkMode ? colors.brandColors.success[300] : colors.brandColors.success[500]} />
                      <NewRTLText 
                        style={[
                          styles.tagText, 
                          { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }, 
                          { marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0 } // Text spacing adjustment
                        ]}
                      >
                        {isRTL ? wasteType.nameAr : wasteType.nameEn}
                      </NewRTLText>
                    </View>
                  ))
                ) : (
                  <NewRTLText style={[styles.noContentText, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                    {t('Suitable for general waste')} { /* Changed message */}
                  </NewRTLText>
                )}
              </View>
            </View>

            {/* Features Section */}
            {displayFeatures.length > 0 && (
              <View style={styles.section}>
                 <NewRTLText style={[styles.sectionTitle, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>{t('Features')}</NewRTLText>
                 <View style={[styles.featuresContainer, {alignItems: isRTL ? 'flex-end' : 'flex-start'},{backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light}]}>
                   {displayFeatures.map((feature) => (
                     <View key={feature.id} style={[styles.featureItem, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
                       <MaterialCommunityIcons 
                         name={feature.iconName as any || "check-circle"} // Use specific icon if available
                         size={18} 
                         color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} 
                       />
                       <NewRTLText style={[styles.featureText, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                         {isRTL ? feature.nameAr : feature.nameEn}
                       </NewRTLText>
                     </View>
                   ))}
                 </View>
              </View>
            )}
          </View>
        </ErrorBoundary>
      </ScrollView>

      {/* --- Bottom Action Bar --- */}
      <SafeAreaView edges={['bottom']} style={[
        styles.bottomBarSafeArea, 
        { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light },
        { borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light } // Dynamic border color
      ]}>
        <View style={[styles.bottomBar, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
          {/* Price Display */}
          <View style={styles.bottomPriceContainer}>
            <NewRTLText style={[styles.totalPriceLabel, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>{t('Total Price')}</NewRTLText>
            <View style={styles.totalPriceValueContainer}>
              {isDarkMode ? (<Image
                source={require('../../../assets/images/currency/Saudi_Riyal_Symbol_white.png')} // Adjusted path
                style={styles.currencySymbolLarge}
                resizeMode="contain"
              />) : (<Image
                source={require('../../../assets/images/currency/Saudi_Riyal_Symbol.png')} // Adjusted path
                style={styles.currencySymbolLarge}
                resizeMode="contain"
              />)}
              <NewRTLText style={[styles.totalPriceValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                  {`${displayPrice}`}
              </NewRTLText>
              <NewRTLText style={[styles.totalPriceUnit, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                  {`/ ${t('Load')}`}
              </NewRTLText>
            </View>
          </View>
          {/* Action Button */}
          <TouchableOpacity
            style={[
              styles.actionButton,
              { 
                backgroundColor: !displayIsAvailable 
                  ? (isDarkMode ? colors.brandColors.grayScale[700] : colors.brandColors.grayScale[300]) // Dynamic disabled background
                  : (isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[600]) // Dynamic normal background
              },
              // Removed styles.actionButtonDisabled reference
            ]}
            onPress={handleAddToCart}
            disabled={!displayIsAvailable} // Disable interaction
          >
            <NewRTLText style={[styles.actionButtonText, { color: !displayIsAvailable ? (isDarkMode ? colors.brandColors.grayScale[400] : colors.brandColors.grayScale[500]) : colors.textColors.dark }]}>
              {displayIsAvailable ? t('Proceed to Checkout') : t('Not Available')}
            </NewRTLText>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
      {/* --- End Bottom Bar --- */}      

     </LinearGradient>
   );
}

// --- Styles (Adapted from original, with V2 considerations) ---
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
     paddingBottom: 100, // Ensure content doesn't hide behind bottom bar
  },
  fullScreenCenter: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorScreen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.backgroundColors.main.light, // Use theme colors
  },
  errorTitle: {
    fontSize: 20,
    textAlign: 'center',
    color: colors.textColors.light, // Use theme colors
    marginBottom: 16,
  },
  errorButton: {
    marginTop: 16,
    backgroundColor: colors.brandColors.primary[500], // Use theme colors
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  errorButtonText: {
    color: colors.textColors.dark,
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: colors.brandColors.danger[100], // Use theme colors
    borderRadius: 8,
    margin: 16, // Add margin for visibility
  },
  errorText: {
    color: colors.brandColors.danger[600], // Use theme colors
    fontWeight: 'bold',
  },
  headerIcon: {
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroImageContainer: {
    position: 'relative',
    width: '100%',
    height: width * 0.75, // Adjusted aspect ratio
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden', // Clip image to rounded corners
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  imageLoadingContainer: {
    ...StyleSheet.absoluteFillObject, // Cover the container
    backgroundColor: 'rgba(0,0,0,0.1)', // Slight overlay
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  imageErrorContainer: {
     ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.surfaceColors.container.light, // Use theme color
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageErrorText: {
    marginTop: 8,
    color: colors.textColors.secondary.light, // Use theme color
  },
  availabilityBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 6,
  },
  availabilityText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  ratingBadge: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20, // Make it pill-shaped
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 4,
    fontWeight: 'bold',
    fontSize: 14,
  },
   reviewCountText: {
    marginLeft: 4,
    fontSize: 14,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  headerSection: {
    marginBottom: 24,
    borderBottomWidth: 1,
    paddingBottom: 16,
    // borderColor removed, applied inline dynamically
  },
  dumpsterTitle: {
    fontSize: 26, // Slightly larger title
    fontWeight: 'bold',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline', // Align text baseline
    marginBottom: 12,
  },
  priceTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceText: {
    fontSize: 24, // Larger price
    fontWeight: 'bold',
    marginRight: 4,
  },
  priceUnitText: {
    fontSize: 16,
  },
  currencySymbol: {
    width: 18, // Adjusted size
    height: 18,
    marginRight: 6, // Spacing
  },
  partnerLogo: {
      width: 30,
      height: 30,
      borderRadius: 15,
      marginLeft: 'auto', // Push to the right
  },
  descriptionText: {
    fontSize: 15, // Slightly smaller description
    lineHeight: 22,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18, // Consistent section title size
    fontWeight: '600',
    marginBottom: 12,
  },
  specificationsContainer: {
    marginBottom: 24,
    padding: 16,
    borderRadius: 12,
    // Removed shadow for a flatter design, use borders if needed
  },
  specificationsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around', // Better distribution
    marginBottom: 8,
  },
  specificationItem: {
    alignItems: 'center',
    flex: 1, // Allow items to share space
    paddingHorizontal: 4, // Add some padding
  },
  specificationLabel: {
    fontSize: 13, // Smaller label
    marginTop: 6,
    textAlign: 'center', // Center align text
  },
  specificationValue: {
    fontWeight: '600', // Slightly less bold
    textAlign: 'center', // Center align text
    marginTop: 2,
    fontSize: 15,
  },
  sizeDescription: {
    marginTop: 16, // Add space before description
    fontSize: 14,
    lineHeight: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    // Gap handled by margin on individual tags
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12, // Adjust padding
    paddingVertical: 6,
    borderRadius: 16, // Pill shape
    marginRight: 8, // Add gap between tags
    marginBottom: 8, // Add gap between rows
  },
  tagText: {
    marginLeft: 6, // Space icon and text
    fontSize: 14,
  },
  noContentText: {
    fontStyle: 'italic',
    fontSize: 14,
  },
  featuresContainer: {
    paddingVertical: 8, // Reduce padding
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10, // Adjust spacing
  },
  featureText: {
    marginLeft: 8,
    fontSize: 15,
  },
  // --- Bottom Bar Styles ---
  bottomBarSafeArea: {
     borderTopWidth: 1,
     // borderColor removed, applied inline dynamically
  },
  bottomBar: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center', // Align items vertically
    justifyContent: 'space-between',
  },
  bottomPriceContainer: {
    // Takes remaining space if needed, but let button define width primarily
  },
  totalPriceLabel: {
    fontSize: 13, // Smaller label
    marginBottom: 2,
  },
  totalPriceValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalPriceValue: {
    fontSize: 22, // Slightly smaller price
    fontWeight: 'bold',
  },
   totalPriceUnit: {
    fontSize: 14,
    marginLeft: 4,
  },
  currencySymbolLarge: {
    width: 16, // Adjusted size
    height: 16,
    marginRight: 4,
  },
  actionButton: {
    paddingHorizontal: 24,
    paddingVertical: 14, // Make button taller
    borderRadius: 12, // Less rounded corners
    flexGrow: 1, // Allow button to grow if needed, but usually fixed size
    marginLeft: 16, // Space between price and button
    alignItems: 'center', // Center text
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});
// --- End Styles --- 