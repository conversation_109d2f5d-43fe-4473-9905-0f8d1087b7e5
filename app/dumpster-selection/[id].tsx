import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';

export default function DumpsterDetailRedirect() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  useEffect(() => {
    // Redirect to the V2 implementation
    if (id) {
      router.replace(`/dumpster-selection/v2/${id}`);
    } else {
      router.replace('/dumpster-selection');
    }
  }, [id, router]);
  
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Stack.Screen options={{ headerShown: false }} />
      <ActivityIndicator size="large" color="#3b82f6" />
      <Text style={{ marginTop: 16, color: '#666' }}>Redirecting...</Text>
    </View>
  );
} 
