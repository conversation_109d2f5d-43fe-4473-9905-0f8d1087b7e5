import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Stack, useRouter } from 'expo-router';
import { IconButton } from 'react-native-paper';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';

// Function to safely render text content - this might be the source of the issue
const SafeText = ({children, style}: {children: React.ReactNode, style?: any}) => {
  if (children === null || children === undefined) {
    return null;
  }
  return <Text style={style}>{children}</Text>;
};

// Debug component focusing on SafeText
export default function DebugSafeTextScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { isDarkMode } = useTheme();
  
  const [testValue, setTestValue] = useState<string | null>("Test Value");
  const [testUndefined, setTestUndefined] = useState<string | undefined>(undefined);
  
  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Debug SafeText',
          headerLeft: () => (
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={() => router.back()}
              style={styles.headerIcon}
            />
          ),
        }}
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.contentContainer}>
          {/* Title */}
          <NewRTLText style={styles.title}>
            {t('Testing SafeText Component')}
          </NewRTLText>
          
          {/* Testing NewRTLText */}
          <View style={styles.testSection}>
            <NewRTLText style={styles.sectionTitle}>
              {t('Using NewRTLText Component')}
            </NewRTLText>
            
            <NewRTLText style={styles.regularText}>
              {t('Regular text with NewRTLText')}
            </NewRTLText>
            
            <NewRTLText style={styles.regularText}>
              {testValue}
            </NewRTLText>
            
            <NewRTLText style={styles.regularText}>
              {testUndefined}
            </NewRTLText>
          </View>
          
          {/* Testing SafeText */}
          <View style={styles.testSection}>
            <NewRTLText style={styles.sectionTitle}>
              {t('Using SafeText Component')}
            </NewRTLText>
            
            <SafeText style={styles.regularText}>
              {t('Regular text with SafeText')}
            </SafeText>
            
            <SafeText style={styles.regularText}>
              {testValue}
            </SafeText>
            
            <SafeText style={styles.regularText}>
              {testUndefined}
            </SafeText>
          </View>
          
          {/* Testing without any wrapper */}
          <View style={styles.testSection}>
            <NewRTLText style={styles.sectionTitle}>
              {t('Using Standard Text Component')}
            </NewRTLText>
            
            <Text style={styles.regularText}>
              {t('Regular text with standard Text')}
            </Text>
            
            <Text style={styles.regularText}>
              {testValue}
            </Text>
            
            {/* This might cause issues if rendered directly */}
            {testUndefined !== undefined && (
              <Text style={styles.regularText}>
                {testUndefined}
              </Text>
            )}
          </View>
          
          {/* Testing buttons to toggle values */}
          <View style={styles.buttonRow}>
            <TouchableOpacity 
              style={styles.button}
              onPress={() => setTestValue(testValue ? null : "Test Value")}
            >
              <NewRTLText style={styles.buttonText}>
                {testValue ? t('Set to null') : t('Set to value')}
              </NewRTLText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.button}
              onPress={() => setTestUndefined(testUndefined ? undefined : "Defined now")}
            >
              <NewRTLText style={styles.buttonText}>
                {testUndefined ? t('Set to undefined') : t('Define value')}
              </NewRTLText>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: 'center',
  },
  headerIcon: {
    backgroundColor: '#e5e7eb',
    borderRadius: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 24,
    textAlign: 'center',
  },
  testSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4b5563',
    marginBottom: 12,
  },
  regularText: {
    fontSize: 16,
    color: '#4b5563',
    marginBottom: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  button: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
}); 