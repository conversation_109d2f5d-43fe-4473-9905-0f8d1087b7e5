import React, { useState, useMemo, useEffect } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, Modal, FlexAlignType, ViewStyle, View, Linking, ActivityIndicator } from 'react-native';
import { shadow, Switch, useTheme as usePaperTheme } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useSettings } from '@/hooks/useSettings';
import { useTheme } from '@/context/ThemeContext';
import { useUIStore } from '@/stores';
import { useAuth } from '@/context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import * as Updates from 'expo-updates';
import { supabase } from '@/services/supabase/client';
import ThemeSelector from '@/components/ui/ThemeSelector';
import { UserSettings } from '@/types/settings';

import { backgroundColors, brandColors, surfaceColors, textColors } from '@/theme/colors';

export default function SettingsScreen() {
  const router = useRouter();
  const { t, i18n } = useTranslation();
  const { settings, loading, updateSettings } = useSettings();
  const { isDarkMode, toggleTheme } = useTheme();

  const showToast = useUIStore((state) => state.showToast);
  const { signOut, session } = useAuth();
  const { isRTL, setLanguage, resetRTLSettings } = useRTLContext();
  const [languageModalVisible, setLanguageModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Create RTL-aware dynamic styles
  const dynamicStyles = useMemo(() => ({
    settingItem: {
      padding: 15,
      borderRadius: 10,
      marginBottom: 10,
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
      alignItems: 'center' as FlexAlignType,
      justifyContent: 'space-between' as ViewStyle['justifyContent'],
    },
    settingContent: {
      flex: 1,
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
      alignItems: 'center' as FlexAlignType,
    },
    iconContainer: {
      marginRight: isRTL ? 0 : 10,
      marginLeft: isRTL ? 10 : 0,
    },
    languageOption: {
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
      justifyContent: 'space-between' as ViewStyle['justifyContent'],
      alignItems: 'center' as FlexAlignType,
      padding: 15,
      borderRadius: 5,
      marginBottom: 10,
    },
    settingTexts: {
      flex: 1,
      marginLeft: isRTL ? 0 : 10,
      marginRight: isRTL ? 10 : 0,
    },
    header: {
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
      alignItems: 'center' as FlexAlignType,
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
    },
    headerTitle: {
      flex: 1,
      fontSize: 20,
      fontWeight: '600' as const,
      marginHorizontal: 15,
    },
    backButton: {
      padding: 5,
    },
    content: {
      flex: 1,
    },
    section: {
      padding: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600' as const,
      marginBottom: 15,
    },
    settingTitle: {
      fontSize: 16,
      color: '#333',
    },
    settingDescription: {
      fontSize: 14,
      color: '#666',
      marginTop: 5,
    },
    chevronContainer: {
      padding: 5,
    },
    signOutButton: {
      margin: 20,
      padding: 15,
      backgroundColor: '#f44336',
      borderRadius: 10,
      alignItems: 'center' as FlexAlignType,
    },
    signOutText: {
      color: brandColors.danger.DEFAULT,
      fontSize: 16,
      fontWeight: '600' as const,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center' as ViewStyle['justifyContent'],
      alignItems: 'center' as FlexAlignType,
    },
    modalContent: {
      width: '80%' as const,
      backgroundColor: '#fff',
      borderRadius: 10,
      padding: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600' as const,
      marginBottom: 20,
      textAlign: 'center' as const,
    },
    selectedLanguage: {
      backgroundColor: '#e3f2fd',
    },
    languageText: {
      fontSize: 16,
    },
    cancelButton: {
      marginTop: 10,
      padding: 15,
      borderRadius: 5,
      backgroundColor: '#f5f5f5',
      alignItems: 'center' as FlexAlignType,
    },
    cancelText: {
      color: '#333',
      fontSize: 16,
    },
    switchStyle: isRTL ? { transform: [{ scaleX: -1 }] } : {},
  }), [isRTL]);

  const handleThemeChange = async () => {
    await toggleTheme();
  };

  const handleNotificationToggle = async () => {
    if (!settings?.notifications) return;

    await updateSettings({
      ...settings,
      notifications: {
        ...settings.notifications,
        push_enabled: !settings.notifications.push_enabled
      }
    });
  };

  const handleLocationSharingToggle = async () => {
    await updateSettings({
      ...settings,
      location_sharing: !settings.location_sharing
    });
  };

  const openPrivacyPolicy = () => {
    if (settings?.privacy?.policy_url) {
      Linking.openURL(settings.privacy.policy_url);
    }
  };

  const openTerms = () => {
    if (settings?.privacy?.terms_url) {
      Linking.openURL(settings.privacy.terms_url);
    }
  };

  const handleLanguageChange = async (newLanguage: 'en' | 'ar') => {
    try {
      setLanguageModalVisible(false);
      setIsLoading(true);

      // Update language in RTL context - this now handles all updates
      const needsRestart = await setLanguage(newLanguage);

      // Show success message
      showToast(t('settings.language.updated', 'Language updated successfully'), 'success');

      // Check if we need to reload the app
      if (needsRestart) {
        if (!__DEV__) {
          try {
            await Updates.reloadAsync();
          } catch (error) {
            console.error('Failed to reload app:', error);
            showToast(t('settings.language.restartError', 'Failed to restart app'), 'error');
          }
        } else {
          // In development, show a message
          alert(t('settings.language.restartRequired', 'Please restart the app to apply language changes'));
        }
      }
    } catch (error) {
      console.error('Failed to change language:', error);
      showToast(t('settings.language.updateError', 'Failed to update language'), 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    setIsLoading(true); // Start loading indicator for settings screen
    try {
      console.log('[SettingsScreen] Calling AuthContext.signOut...');
      // Call signOut and pass navigation logic as the onSuccess callback
      await signOut(() => {
        console.log('[SettingsScreen] AuthContext.signOut successful, navigating...');
        // Ensure router is available before navigating
        if (router) {
           router.replace('/(auth)/language-select');
        } else {
           console.error('[SettingsScreen] Router not available for navigation after sign out.');
        }
      });
      console.log('[SettingsScreen] AuthContext.signOut call completed (callback might run later).');
      // Note: Navigation happens in the callback, screen might unmount before this point

    } catch (error) {
      // Error is already logged in AuthContext, just show toast here
      console.error('[SettingsScreen] Error during sign out process:', error);
      showToast && showToast(t('settings.error', 'An error occurred during sign out'), 'error');
    } finally {
       // Always ensure the settings screen loading indicator is turned off
       console.log('[SettingsScreen] handleSignOut finally block. Setting isLoading to false.');
       setIsLoading(false); 
    }
  };

  const goBack = () => router.back();

  return (<SafeAreaView style={[
    styles.container,
    { backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light }
  ]}>

    {/* Header */}
    <NewRTLView style={[
      dynamicStyles.header,
      {
        borderBottomColor: isDarkMode ? backgroundColors.outline.dark : backgroundColors.outline.light,
        backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light
      }
    ]}>
      {isRTL ? (
        <>
          <TouchableOpacity onPress={goBack} style={dynamicStyles.backButton}>
            <Feather
              name="arrow-right"
              size={24}
              color={isDarkMode ? textColors.dark : textColors.light}
            />
          </TouchableOpacity>
          <NewRTLText style={[
            dynamicStyles.headerTitle,
            { color: isDarkMode ? textColors.dark : textColors.light }
          ]}>
            {t('settings.title', 'Settings')}
          </NewRTLText>
        </>
      ) : (
        <>
          <TouchableOpacity onPress={goBack} style={dynamicStyles.backButton}>
            <Feather
              name="arrow-left"
              size={24}
              color={isDarkMode ? textColors.dark : textColors.light}
            />
          </TouchableOpacity>
          <NewRTLText style={[
            dynamicStyles.headerTitle,
            { color: isDarkMode ? textColors.dark : textColors.light }
          ]}>
            {t('settings.title', 'Settings')}
          </NewRTLText>

        </>
      )}
    </NewRTLView>

    <ScrollView style={dynamicStyles.content}>
      {/* Theme */}
      <NewRTLView style={dynamicStyles.section} direction="column">
        <NewRTLText style={[
          dynamicStyles.sectionTitle,
          { color: isDarkMode ? textColors.secondary.dark : textColors.secondary.light }
        ]}>
          {t('settings.theme.title', 'Theme')}
        </NewRTLText>
        <ThemeSelector />
      </NewRTLView>

      {/* Design System */}
      <NewRTLView style={dynamicStyles.section} direction="column">
        <NewRTLText style={[
          dynamicStyles.sectionTitle,
          { color: isDarkMode ? textColors.secondary.dark : textColors.secondary.light }
        ]}>
          {t('settings.designSystem.title', 'Design System')}
        </NewRTLText>
        <TouchableOpacity
          style={[
            dynamicStyles.settingItem,
            { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
          ]}
          onPress={() => router.push('/design-system')}
        >
          <NewRTLView style={dynamicStyles.iconContainer}>
            <Feather
              name="layout"
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
          <NewRTLView style={dynamicStyles.settingTexts} direction="column">
            <NewRTLText style={[
              dynamicStyles.settingTitle,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('settings.designSystem.viewComponents', 'View Components')}
            </NewRTLText>
            <NewRTLText style={[
              dynamicStyles.settingDescription,
              { color: isDarkMode ? textColors.placeholder.dark : textColors.placeholder.light }
            ]}>
              {t('settings.designSystem.description', 'Explore our design system')}
            </NewRTLText>
          </NewRTLView>
          <NewRTLView style={dynamicStyles.chevronContainer}>
            <Feather
              name={isRTL ? "chevron-left" : "chevron-right"}
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
        </TouchableOpacity>
      </NewRTLView>

      {/* Language */}
      <NewRTLView style={dynamicStyles.section} direction="column">
        <NewRTLText style={[
          dynamicStyles.sectionTitle,
          { color: isDarkMode ? textColors.secondary.dark : textColors.secondary.light }
        ]}>
          {t('settings.language.title', 'Language')}
        </NewRTLText>
        <TouchableOpacity
          style={[
            dynamicStyles.settingItem,
            { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
          ]}
          onPress={() => setLanguageModalVisible(true)}
        >
          <NewRTLView style={dynamicStyles.iconContainer}>
            <Feather
              name="globe"
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
          <NewRTLView style={dynamicStyles.settingTexts} direction="column">
            <NewRTLText style={[
              dynamicStyles.settingTitle,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('settings.language.appLanguage', 'App Language')}
            </NewRTLText>
            <NewRTLText style={[
              dynamicStyles.settingDescription,
              { color: isDarkMode ? textColors.placeholder.dark : textColors.placeholder.light }
            ]}>
              {i18n.language === 'en' ? 'English' : 'العربية'}
            </NewRTLText>
          </NewRTLView>
          <NewRTLView style={dynamicStyles.chevronContainer}>
            <Feather
              name={isRTL ? "chevron-left" : "chevron-right"}
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
        </TouchableOpacity>
      </NewRTLView>

      {/* Notifications */}
      <NewRTLView style={dynamicStyles.section} direction="column">
        <NewRTLText style={[
          dynamicStyles.sectionTitle,
          { color: isDarkMode ? textColors.secondary.dark : textColors.secondary.light }
        ]}>
          {t('settings.notifications.title', 'Notifications')}
        </NewRTLText>
        <TouchableOpacity
          style={[
            dynamicStyles.settingItem,
            { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
          ]}
          onPress={handleNotificationToggle}
        >
          <NewRTLView style={dynamicStyles.iconContainer}>
            <Feather
              name="bell"
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
          <NewRTLView style={dynamicStyles.settingTexts} direction="column">
            <NewRTLText style={[
              dynamicStyles.settingTitle,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('settings.notifications.push', 'Push Notifications')}
            </NewRTLText>
            <NewRTLText style={[
              dynamicStyles.settingDescription,
              { color: isDarkMode ? textColors.placeholder.dark : textColors.placeholder.light }
            ]}>
              {t('settings.notifications.description', 'Get notifications about your orders')}
            </NewRTLText>
          </NewRTLView>
          <Switch
            style={dynamicStyles.switchStyle}
            value={settings?.notifications?.push_enabled}
            onValueChange={handleNotificationToggle}
            trackColor={{
              true: brandColors.primary[500],
              false: isDarkMode ? brandColors.primary[900] : brandColors.primary[50],
            }}
          />
        </TouchableOpacity>
      </NewRTLView>

      {/* Location Sharing */}
      <NewRTLView style={dynamicStyles.section} direction="column">
        <NewRTLText style={[
          dynamicStyles.sectionTitle,
          { color: isDarkMode ? textColors.secondary.dark : textColors.secondary.light }
        ]}>
          {t('settings.location.title', 'Location')}
        </NewRTLText>
        <TouchableOpacity
          style={[
            dynamicStyles.settingItem,
            { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
          ]}
          onPress={handleLocationSharingToggle}
        >
          <NewRTLView style={dynamicStyles.iconContainer}>
            <Feather
              name="map-pin"
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
          <NewRTLView style={dynamicStyles.settingTexts} direction="column">
            <NewRTLText style={[
              dynamicStyles.settingTitle,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('settings.location.sharing', 'Location Sharing')}
            </NewRTLText>
            <NewRTLText style={[
              dynamicStyles.settingDescription,
              { color: isDarkMode ? textColors.placeholder.dark : textColors.placeholder.light }
            ]}>
              {t('settings.location.description', 'Share your location for better service')}
            </NewRTLText>
          </NewRTLView>
          <Switch
            style={dynamicStyles.switchStyle}
            value={settings?.location_sharing}
            onValueChange={handleLocationSharingToggle}
            trackColor={{
              true: brandColors.primary[500],
              false: isDarkMode ? brandColors.primary[900] : brandColors.primary[50],
            }}
          />
        </TouchableOpacity>
      </NewRTLView>

      {/* Privacy */}
      <NewRTLView style={dynamicStyles.section} direction="column">
        <NewRTLText style={[
          dynamicStyles.sectionTitle,
          { color: isDarkMode ? textColors.secondary.dark : textColors.secondary.light }
        ]}>
          {t('settings.privacy.title', 'Privacy')}
        </NewRTLText>
        <TouchableOpacity
          style={[
            dynamicStyles.settingItem,
            { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
          ]}
          onPress={openPrivacyPolicy}
        >
          <NewRTLView style={dynamicStyles.iconContainer}>
            <Feather
              name="shield"
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
          <NewRTLView style={dynamicStyles.settingTexts} direction="column">
            <NewRTLText style={[
              dynamicStyles.settingTitle,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('settings.privacy.policy', 'Privacy Policy')}
            </NewRTLText>
          </NewRTLView>
          <NewRTLView style={dynamicStyles.chevronContainer}>
            <Feather
              name={isRTL ? "chevron-left" : "chevron-right"}
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            dynamicStyles.settingItem,
            { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
          ]}
          onPress={openTerms}
        >
          <NewRTLView style={dynamicStyles.iconContainer}>
            <Feather
              name="file-text"
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
          <NewRTLView style={dynamicStyles.settingTexts} direction="column">
            <NewRTLText style={[
              dynamicStyles.settingTitle,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('settings.privacy.terms', 'Terms of Service')}
            </NewRTLText>
          </NewRTLView>
          <NewRTLView style={dynamicStyles.chevronContainer}>
            <Feather
              name={isRTL ? "chevron-left" : "chevron-right"}
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
        </TouchableOpacity>

        {/* Show Onboarding */}
        <TouchableOpacity
          style={[
            dynamicStyles.settingItem,
            { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
          ]}
          onPress={() => router.push('/onboarding/OnboardingScreen')}
        >
          <NewRTLView style={dynamicStyles.iconContainer}>
            <Feather
              name="book-open"
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
          <NewRTLView style={dynamicStyles.settingTexts} direction="column">
            <NewRTLText style={[
              dynamicStyles.settingTitle,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('settings.showOnboarding', 'Show Onboarding')}
            </NewRTLText>
          </NewRTLView>
          <NewRTLView style={dynamicStyles.chevronContainer}>
            <Feather
              name={isRTL ? "chevron-left" : "chevron-right"}
              size={20}
              color={isDarkMode ? textColors.secondary.dark : textColors.secondary.light}
            />
          </NewRTLView>
        </TouchableOpacity>
      </NewRTLView>

      {/* Sign Out */}
      <TouchableOpacity
        style={[
          dynamicStyles.signOutButton,
          { backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light },
          { opacity: isLoading ? 0.7 : 1 }
        ]}
        onPress={handleSignOut}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color={brandColors.danger.DEFAULT} />
        ) : (
          <NewRTLText style={dynamicStyles.signOutText}>
            {t('settings.signOut', 'Sign Out')}
          </NewRTLText>
        )}
      </TouchableOpacity>
    </ScrollView>

    {/* Language Selection Modal */}
    <Modal
      visible={languageModalVisible}
      animationType="fade"
      transparent={true}
      onRequestClose={() => setLanguageModalVisible(false)}
    >
      <NewRTLView style={dynamicStyles.modalOverlay}>
        <NewRTLView
          style={[
            dynamicStyles.modalContent,
            { backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light }
          ]}
          direction="column"
        >
          <NewRTLText style={[
            dynamicStyles.modalTitle,
            { color: isDarkMode ? textColors.dark : textColors.light }
          ]}>
            {t('settings.language.select', 'Select Language')}
          </NewRTLText>

          <TouchableOpacity
            style={[
              dynamicStyles.languageOption,
              i18n.language === 'en' && dynamicStyles.selectedLanguage,
              { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
            ]}
            onPress={() => handleLanguageChange('en')}
          >
            <NewRTLText style={[
              dynamicStyles.languageText,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              English
            </NewRTLText>
            {i18n.language === 'en' && (
              <Feather name="check" size={20} color={brandColors.primary[500]} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              dynamicStyles.languageOption,
              i18n.language === 'ar' && dynamicStyles.selectedLanguage,
              { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
            ]}
            onPress={() => handleLanguageChange('ar')}
          >
            <NewRTLText style={[
              dynamicStyles.languageText,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              العربية
            </NewRTLText>
            {i18n.language === 'ar' && (
              <Feather name="check" size={20} color={brandColors.primary[500]} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              dynamicStyles.cancelButton,
              { backgroundColor: isDarkMode ? backgroundColors.main.dark : backgroundColors.main.light }
            ]}
            onPress={() => setLanguageModalVisible(false)}
          >
            <NewRTLText style={[
              dynamicStyles.cancelText,
              { color: isDarkMode ? textColors.dark : textColors.light }
            ]}>
              {t('common.cancel', 'Cancel')}
            </NewRTLText>
          </TouchableOpacity>
        </NewRTLView>
      </NewRTLView>
    </Modal>
  </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: '600',
    marginHorizontal: 15,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  settingTitle: {
    fontSize: 16,
    color: '#333',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  chevronContainer: {
    padding: 5,
  },
  signOutButton: {
    margin: 20,
    padding: 15,
    backgroundColor: '#f44336',
    borderRadius: 10,
    alignItems: 'center',
  },
  signOutText: {
    color: brandColors.danger.DEFAULT,
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
    textAlign: 'center',
  },
  selectedLanguage: {
    backgroundColor: '#e3f2fd',
  },
  languageText: {
    fontSize: 16,
  },
  cancelButton: {
    marginTop: 10,
    padding: 15,
    borderRadius: 5,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
  },
  cancelText: {
    color: '#333',
    fontSize: 16,
  },
}); 