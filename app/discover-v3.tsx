import React, { useState, useRef, useCallback, useEffect } from 'react';
import { View, Text, FlatList, ActivityIndicator, Dimensions, Modal, TouchableOpacity, Image } from 'react-native';
import { styled } from 'nativewind';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useRouter } from 'expo-router';
import { MaterialIcons, Feather } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn } from 'react-native-reanimated';
import RBSheet from 'react-native-raw-bottom-sheet';

// Hooks
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { useDumpsters, useWasteTypes } from '@/hooks/v2/useDumpsters';
import { usePartner } from '@/hooks/usePartner';
import { usePartnerOffers } from '@/hooks/usePartnerOffers';

// Components
import { NewRTLText } from '@/components/rtl/new-index';
import DumpsterCardV3 from '@/components/DumpsterCardV3';
import PartnerOfferList, { PartnerOffer } from '@/components/PartnerOfferList';
import DumpsterDetailsV3 from '@/screens/DumpsterDetailsV3';

// Types
import { Dumpster, DumpsterFilters } from '@/types/v2/dumpster';
import * as colors from '@/theme/colors';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledFlatList = styled(FlatList);
const StyledAnimatedView = styled(Animated.View);

// Marketing banner data
const MARKETING_BANNERS = [
  {
    id: '1',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner1.jpg',
    title: 'Spring Cleaning Special',
    subtitle: '15% off your next rental',
  },
  {
    id: '2',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner2.jpg',
    title: 'Same-Day Delivery',
    subtitle: 'For orders placed before noon',
  },
  {
    id: '3',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner3.jpg',
    title: 'Eco-Friendly Options',
    subtitle: 'Sustainable waste management',
  },
];

// Best-for tags mapping
const BEST_FOR_TAGS: Record<string, string[]> = {
  'size-1': ['Small Projects', 'Residential'],
  'size-2': ['Medium Projects', 'Renovation'],
  'size-3': ['Large Projects', 'Construction'],
  'size-4': ['Commercial', 'Heavy Debris'],
};

/**
 * DiscoverV3Screen - A Skyscanner-inspired discovery screen for dumpsters
 *
 * Features:
 * - Displays a grid of dumpster cards with size, price, and availability info
 * - Shows marketing banners in an auto-sliding carousel
 * - Opens a details sheet when a dumpster is selected
 * - Navigates to full details screen when a partner offer is selected
 * - Supports RTL layouts and dark mode
 */
export default function DiscoverV3Screen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenWidth = Dimensions.get('window').width;
  const detailsSheetRef = useRef<any>(null); // Using any type for RBSheet ref

  // State
  const [selectedDumpster, setSelectedDumpster] = useState<Dumpster | null>(null);
  const [filters, setFilters] = useState<DumpsterFilters>({
    availability: true,
  });

  // Fetch dumpsters
  const {
    data: dumpsters,
    isLoading: isLoadingDumpsters,
    isError: isErrorDumpsters
  } = useDumpsters(filters);

  // Fetch waste types
  const { data: wasteTypes } = useWasteTypes();

  // Fetch partner offers for the selected dumpster
  const {
    data: partnerOffers = [],
    isLoading: isLoadingOffers
  } = usePartnerOffers(
    selectedDumpster?.id || null,
    selectedDumpster?.pricePerLoad
  );

  // Handle dumpster selection
  const handleDumpsterPress = useCallback((dumpster: Dumpster) => {
    setSelectedDumpster(dumpster);

    // Open the details sheet
    detailsSheetRef.current?.open();
  }, []);

  // Handle partner offer selection
  const handleOfferSelect = useCallback((offer: PartnerOffer) => {
    if (!selectedDumpster) return;

    // Close the sheet
    detailsSheetRef.current?.close();

    // Navigate to the dumpster details screen
    router.push({
      pathname: `/dumpster/${selectedDumpster.id}`,
      params: {
        partnerId: offer.partnerId,
        offerId: offer.id,
      }
    });
  }, [selectedDumpster, router]);

  // Render dumpster card
  const renderDumpsterCard = useCallback(({ item }: { item: Dumpster }) => {
    // Get best-for tags based on size ID
    const bestFor = BEST_FOR_TAGS[item.sizeId] || [];

    return (
      <StyledView className="w-1/2 px-2 mb-4">
        <DumpsterCardV3
          dumpster={item}
          onPress={handleDumpsterPress}
          bestFor={bestFor}
        />
      </StyledView>
    );
  }, [handleDumpsterPress]);

  // Render empty state
  const renderEmptyState = useCallback(() => {
    if (isLoadingDumpsters) {
      return (
        <StyledView className="flex-1 justify-center items-center py-8">
          <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
          <StyledText className="text-gray-600 dark:text-gray-400 mt-4">
            {t('loading_dumpsters')}
          </StyledText>
        </StyledView>
      );
    }

    if (isErrorDumpsters) {
      return (
        <StyledView className="flex-1 justify-center items-center py-8">
          <MaterialIcons name="error" size={48} color={colors.brandColors.danger[500]} />
          <StyledText className="text-gray-800 dark:text-gray-200 text-lg font-bold mt-4">
            {t('error_loading_dumpsters')}
          </StyledText>
          <StyledText className="text-gray-600 dark:text-gray-400 text-center mt-2 px-8">
            {t('error_loading_dumpsters_description')}
          </StyledText>
          <StyledTouchableOpacity
            className="mt-4 bg-primary-500 px-4 py-2 rounded-lg"
            onPress={() => router.reload()}
          >
            <StyledText className="text-white font-medium">
              {t('try_again')}
            </StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      );
    }

    return (
      <StyledView className="flex-1 justify-center items-center py-8">
        <MaterialIcons name="search-off" size={48} color={colors.textColors.secondary.light} />
        <StyledText className="text-gray-800 dark:text-gray-200 text-lg font-bold mt-4">
          {t('no_dumpsters_found')}
        </StyledText>
        <StyledText className="text-gray-600 dark:text-gray-400 text-center mt-2 px-8">
          {t('no_dumpsters_found_description')}
        </StyledText>
      </StyledView>
    );
  }, [isLoadingDumpsters, isErrorDumpsters, t, router]);

  return (
    <SafeAreaView style={{ backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light, flex: 1 }}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <Stack.Screen
        options={{
          title: t('discover'),
          headerShown: true,
          headerStyle: {
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
          },
          headerTintColor: isDarkMode ? colors.textColors.dark : colors.textColors.light,
          headerShadowVisible: false,
        }}
      />

      <StyledView className="flex-1">
        {/* Marketing Carousel */}
        <StyledView className="h-48 mb-4">
          <FlatList
            data={MARKETING_BANNERS}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            renderItem={({ item }: { item: typeof MARKETING_BANNERS[0] }) => (
              <StyledView style={{ width: screenWidth, padding: 16 }}>
                <StyledTouchableOpacity
                  className="w-full h-full rounded-xl overflow-hidden"
                  activeOpacity={0.9}
                >
                  <StyledView className="w-full h-full bg-gray-200 dark:bg-gray-700">
                    {/* Banner Content */}
                    <StyledView className="absolute bottom-0 left-0 right-0 p-4 bg-black bg-opacity-50">
                      <StyledText className="text-white text-xl font-bold">
                        {item.title}
                      </StyledText>
                      <StyledText className="text-white text-sm mt-1">
                        {item.subtitle}
                      </StyledText>
                    </StyledView>
                  </StyledView>
                </StyledTouchableOpacity>
              </StyledView>
            )}
          />
        </StyledView>

        {/* Dumpster Grid */}
        <StyledView className="flex-1 px-2">
          <StyledFlatList
            data={dumpsters || []}
            renderItem={renderDumpsterCard as any}
            keyExtractor={(item: any) => item.id}
            numColumns={2}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
            ListEmptyComponent={renderEmptyState}
            testID="dumpster-grid"
          />
        </StyledView>
      </StyledView>

      {/* Details Bottom Sheet */}
      <RBSheet
        ref={detailsSheetRef}
        // @ts-ignore - RBSheet types are not complete
        closeOnDragDown={true}
        closeOnPressMask={true}
        height={Dimensions.get('window').height * 0.9}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0,0,0,0.5)',
          },
          container: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
          },
          draggableIcon: {
            backgroundColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
            width: 60,
          },
        }}
      >
        {selectedDumpster && (
          <DumpsterDetailsV3
            dumpster={selectedDumpster}
            isLoading={false}
            partnerOffers={partnerOffers}
            isLoadingOffers={isLoadingOffers}
            onSelectOffer={handleOfferSelect}
            onClose={() => detailsSheetRef.current?.close()}
          />
        )}
      </RBSheet>
    </SafeAreaView>
  );
}

// Jest test stub
/*
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import DiscoverV3Screen from '../discover-v3';
import { useDumpsters, useWasteTypes } from '@/hooks/v2/useDumpsters';

// Mock the hooks
jest.mock('@/hooks/v2/useDumpsters');
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));
jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
}));
jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    reload: jest.fn(),
  }),
  Stack: {
    Screen: () => null,
  },
}));
jest.mock('react-native-reanimated-carousel', () => {
  return {
    __esModule: true,
    default: ({ data, renderItem }) => {
      return (
        <View testID="mock-carousel">
          {data.map((item, index) => (
            <View key={index}>{renderItem({ item, index })}</View>
          ))}
        </View>
      );
    },
  };
});

describe('DiscoverV3Screen', () => {
  beforeEach(() => {
    (useDumpsters as jest.Mock).mockReturnValue({
      data: [
        {
          id: '1',
          nameEn: '10 Yard Dumpster',
          nameAr: 'حاوية 10 ياردة',
          imageUrl: 'https://example.com/dumpster.jpg',
          length: 10,
          width: 8,
          height: 4,
          pricePerLoad: 250,
          rating: 4.5,
          reviewCount: 12,
          isAvailable: true,
          sizeId: 'size-1',
        },
        {
          id: '2',
          nameEn: '20 Yard Dumpster',
          nameAr: 'حاوية 20 ياردة',
          imageUrl: 'https://example.com/dumpster2.jpg',
          length: 20,
          width: 8,
          height: 4,
          pricePerLoad: 350,
          rating: 4.2,
          reviewCount: 8,
          isAvailable: true,
          sizeId: 'size-2',
        },
      ],
      isLoading: false,
      isError: false,
    });

    (useWasteTypes as jest.Mock).mockReturnValue({
      data: [
        { id: 'wt1', nameEn: 'Construction', nameAr: 'بناء' },
        { id: 'wt2', nameEn: 'Household', nameAr: 'منزلي' },
      ],
    });
  });

  it('renders the dumpster grid correctly', () => {
    const { getByTestId, getAllByTestId } = render(<DiscoverV3Screen />);

    expect(getByTestId('dumpster-grid')).toBeTruthy();
    expect(getAllByTestId('dumpster-card-v3').length).toBe(2);
  });

  it('renders the marketing carousel', () => {
    const { getByTestId } = render(<DiscoverV3Screen />);

    expect(getByTestId('mock-carousel')).toBeTruthy();
  });

  it('shows loading state when dumpsters are loading', () => {
    (useDumpsters as jest.Mock).mockReturnValue({
      data: [],
      isLoading: true,
      isError: false,
    });

    const { getByText } = render(<DiscoverV3Screen />);

    expect(getByText('loading_dumpsters')).toBeTruthy();
  });

  it('shows error state when dumpster loading fails', () => {
    (useDumpsters as jest.Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: true,
    });

    const { getByText } = render(<DiscoverV3Screen />);

    expect(getByText('error_loading_dumpsters')).toBeTruthy();
  });
});
*/
