import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';
import * as colors from '@/theme/colors';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText as NewRTLTextComponent } from '@/components/rtl/NewRTLText';
import { useTheme } from '@/context/ThemeContext';
const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

interface CustomDatePickerProps {
  date: Date;
  onDateChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  placeholder?: string;
  formatDate?: (date: Date) => string;
}

export default function CustomDatePicker({
  date,
  onDateChange,
  minimumDate,
  maximumDate,
  placeholder = 'Select Date',
  formatDate = (date: Date) => date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}: CustomDatePickerProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleDateChange = (event: any, selectedDate?: Date) => {
    
    if (selectedDate) {
      onDateChange(selectedDate);
    }
    setShowDatePicker(false);
  };
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  function handleOpenDatePicker(): void {

  }

  return (
    <>
      <StyledView
        className="flex-row items-center justify-start p-3 border rounded-lg"
        style={{ backgroundColor: 'transparent', borderColor: 'transparent' }}
      //style={styles.container}
      >
        <StyledView className="flex-row items-center">
          <StyledView
            className="w-10 h-10 rounded-full items-center justify-center"
            style={{ backgroundColor: isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500] }}
          >
            <MaterialIcons name="calendar-today" size={20} color={isDarkMode ? colors.textColors.light : colors.textColors.dark} />
          </StyledView>
          {/* <StyledText className="text-base text-gray-700 font-medium">
            {formatDate(date)}

          </StyledText> */}
          <StyledView className="flex-row justify-stretch">
            <DateTimePicker
              value={date}
              mode="date"
              display="default"
              minimumDate={minimumDate}
              maximumDate={maximumDate}
              onChange={handleDateChange}
              accentColor={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]}
              textColor={isDarkMode ? colors.textColors.light : colors.textColors.dark}

            />
          </StyledView>
        </StyledView>
        {/* <MaterialIcons name="keyboard-arrow-down" size={24} color="#3b82f6" /> */}
      </StyledView>


    </>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f9fafb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  }
}); 