import React from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { useTheme as usePaperTheme } from 'react-native-paper';
import * as colors from '@/theme/colors';
import { Dumpster } from '@/types/v2/dumpster';
import { useAppTheme } from '@/hooks/useAppTheme';

interface DumpsterCardProps {
  dumpster: Dumpster;
  recommendationScore?: number;
  isRecommended?: boolean;
  showRating?: boolean;
  onPress?: (dumpster: Dumpster) => void;
}

export default function DumpsterCardV2({
  dumpster,
  recommendationScore,
  isRecommended = false,
  showRating = true,
  onPress,
}: DumpsterCardProps) {
  const { isDarkMode } = useAppTheme();
  const paperTheme = usePaperTheme();

  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const router = useRouter();

  const name = isRTL ? dumpster.nameAr : dumpster.nameEn;
  const description = isRTL ? dumpster.description?.ar : dumpster.description?.en;

  // Format currency based on locale
  const formatPrice = (amount: number): string => {
    const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
    const currencyCode = 'SAR';

    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);
    } catch (error) {
      return `${amount.toFixed(0)}`;
    }
  };

  const handlePress = () => {
    if (onPress) {
      onPress(dumpster);
    } else {
      router.push(`/dumpster-selection/${dumpster.id}`);
    }
  };

  return (
    <Pressable
      style={[styles.container, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}
      onPress={handlePress}
    >
      {isRecommended && (
        <View style={[styles.recommendedBadge, { backgroundColor: colors.brandColors.primary[500] }]}>
          <Text style={styles.recommendedText}>{t('ai_recommended')}</Text>
          {recommendationScore !== undefined && (
            <Text style={styles.scoreText}>{Math.round(recommendationScore)}%</Text>
          )}
        </View>
      )}

      <Image
        source={{ uri: dumpster.imageUrl }}
        style={styles.image}
        resizeMode="cover"
      />

      <View style={styles.content}>
        <Text style={[styles.title, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]} numberOfLines={1}>
          {name}
        </Text>

        {description && (
          <Text style={[styles.description, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]} numberOfLines={2}>
            {description}
          </Text>
        )}

        <View style={styles.detailsRow}>
          <View style={styles.dimensionBox}>
            <Text style={[styles.dimensionLabel, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {t('dimensions')}
            </Text>
            <Text style={[styles.dimensionValue, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {dumpster.length}x{dumpster.width}x{dumpster.height} m
            </Text>
          </View>

          <View style={styles.priceBox}>
            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignContent: 'center', alignItems: 'center' }}>
              <Image
                source={require('../../../assets/images/currency/Saudi_Riyal_Symbol_primary.png')} // Adjusted path
                style={styles.currencySymbol}
                resizeMode="contain"
              />
              <Text style={[styles.priceValue, { color: colors.brandColors.primary[500] }]}>
                {dumpster.pricePerLoad}
              </Text>
            </View>

            <Text style={[styles.priceLabel, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {t('load')}
            </Text>
          </View>
        </View>


        {showRating && (
          <View style={[styles.ratingRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }, { justifyContent: 'space-between' }]}>

            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignContent: 'center', alignItems: 'center' }}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={[styles.ratingText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
                {dumpster.rating?.toFixed(1) || '4.0'} ({dumpster.reviewCount || 0})
              </Text>
            </View>

            <View style={[styles.availabilityDot, { marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
              <View
                style={[
                  styles.dot,
                  { backgroundColor: dumpster.isAvailable ? '#4CAF50' : '#F44336' }
                ]}
              />
              <Text style={[styles.availabilityText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
                {dumpster.isAvailable ? t('available') : t('unavailable')}
              </Text>
            </View>
          </View>
        )}
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  image: {
    width: '100%',
    height: 150,
  },
  content: {
    padding: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  dimensionBox: {
    flex: 1,
  },
  dimensionLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  dimensionValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  priceBox: {
    alignItems: 'flex-end',
  },
  priceValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  currencySymbol: {
    width: 14, // Adjusted size
    height: 14,
    marginRight: 4, // Spacing
  },
  priceLabel: {
    fontSize: 12,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 4,
    fontSize: 14,
    marginRight: 12,
  },
  availabilityDot: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  availabilityText: {
    fontSize: 12,
  },
  recommendedBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    zIndex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  recommendedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  scoreText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 4,
  },
}); 