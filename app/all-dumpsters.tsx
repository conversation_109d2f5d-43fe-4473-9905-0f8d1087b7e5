// app/all-dumpsters.tsx
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { View, Text, TouchableOpacity, FlatList, ActivityIndicator, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { styled } from 'nativewind';
import { useTranslation } from 'react-i18next';
import { IconButton, Button as PaperButton } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { MaterialIcons } from '@expo/vector-icons';

import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { useWasteTypes } from '@/hooks/v2/useDumpsters';
import { useDumpsters } from '@/hooks/v2/useDumpsters';
import { Dumpster, WasteType, DumpsterFilters } from '@/types/v2/dumpster';
import DumpsterCardV2 from './components/v2/DumpsterCard';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledButton = styled(PaperButton);

// Define types for sorting
type SortOption = 'default' | 'rating_desc' | 'size_desc' | 'size_asc';

export default function AllDumpstersScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  // State
  const [selectedWasteTypeId, setSelectedWasteTypeId] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState<SortOption>('default');

  // Create filters object for V2 API
  const [filters, setFilters] = useState<DumpsterFilters>({});

  // Fetch dumpsters with V2 API
  const { 
    data: dumpsters, 
    isLoading: isLoadingDumpsters, 
    isError: isErrorDumpsters 
  } = useDumpsters(filters);

  const { data: wasteTypes, isLoading: isLoadingWasteTypes } = useWasteTypes();

  // Update filters when waste type selection changes
  useEffect(() => {
    const newFilters: DumpsterFilters = {};
    
    // Add waste type filter
    if (selectedWasteTypeId) {
      newFilters.wasteTypeIds = [selectedWasteTypeId];
    }
    
    setFilters(newFilters);
  }, [selectedWasteTypeId]);

  // Create sorted dumpsters based on sort option
  const sortedDumpsters = useMemo(() => {
    if (!dumpsters) return [];
    
    const sorted = [...dumpsters];
    
    // Apply Sorting
    switch (sortOption) {
      case 'rating_desc':
        sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'size_desc':
        // Sort by volume (calculate from dimensions)
        sorted.sort((a, b) => {
          const volumeA = a.capacity || 0;
          const volumeB = b.capacity || 0;
          return volumeB - volumeA;
        });
        break;
      case 'size_asc':
        // Sort by volume (calculate from dimensions)
        sorted.sort((a, b) => {
          const volumeA = a.capacity || 0;
          const volumeB = b.capacity || 0;
          return volumeA - volumeB;
        });
        break;
      case 'default':
      default:
        // No sorting needed
        break;
    }
    
    return sorted;
  }, [dumpsters, sortOption]);

  // Handle card press - navigate to details
  const handleDumpsterPress = useCallback((dumpster: Dumpster) => {
    router.back();
    setTimeout(() => {
      router.push(`/dumpster-selection/${dumpster.id}`);
    }, 300);
  }, [router]);

  // Open waste type selection
  const openWasteTypeSelector = () => {
    // Create options including "All" option
    const options = [
      { id: null, text: t('allDumpsters.filters.all', 'All Waste Types') },
      ...(wasteTypes?.map(wt => ({ 
        id: wt.id, 
        text: isRTL ? wt.nameAr : wt.nameEn 
      })) || [])
    ];
    
    Alert.alert(
      t('allDumpsters.filters.selectWasteType', 'Select Waste Type'),
      '',
      options.map(opt => ({
        text: opt.text,
        onPress: () => setSelectedWasteTypeId(opt.id)
      })),
      { cancelable: true }
    );
  };

  // Open sort selection
  const openSortSelector = () => {
    const options: { key: SortOption; text: string }[] = [
      { key: 'default', text: t('allDumpsters.sorting.default', 'Default') },
      { key: 'rating_desc', text: t('allDumpsters.sorting.ratingDesc', 'Rating (Highest)') },
      { key: 'size_desc', text: t('allDumpsters.sorting.sizeDesc', 'Size (Largest)') },
      { key: 'size_asc', text: t('allDumpsters.sorting.sizeAsc', 'Size (Smallest)') },
    ];
    
    Alert.alert(
      t('allDumpsters.sorting.selectSort', 'Sort By'),
      '',
      options.map(opt => ({
        text: opt.text,
        onPress: () => setSortOption(opt.key)
      })),
      { cancelable: true }
    );
  };

  // Get selected waste type name for display
  const selectedWasteTypeName = useMemo(() => {
    if (!selectedWasteTypeId) return t('allDumpsters.filters.all', 'All');
    
    const wasteType = wasteTypes?.find(wt => wt.id === selectedWasteTypeId);
    return wasteType ? (isRTL ? wasteType.nameAr : wasteType.nameEn) : t('allDumpsters.filters.all', 'All');
  }, [selectedWasteTypeId, wasteTypes, t, isRTL]);

  // Get sort option name for display
  const selectedSortName = useMemo(() => {
    switch (sortOption) {
      case 'rating_desc': return t('allDumpsters.sorting.ratingDesc', 'Rating (Highest)');
      case 'size_desc': return t('allDumpsters.sorting.sizeDesc', 'Size (Largest)');
      case 'size_asc': return t('allDumpsters.sorting.sizeAsc', 'Size (Smallest)');
      case 'default':
      default: return t('allDumpsters.sorting.default', 'Default');
    }
  }, [sortOption, t]);

  // Render
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light, marginTop: -56 }}>
      {/* Configure this screen as a modal */}
      <Stack.Screen
        options={{
          presentation: 'modal',
          headerShown: false, // Hide default header, we'll make our own
        }}
      />

      {/* Custom Header */}
      <StyledView
        className="flex-row items-center justify-between px-4 py-2 border-b"
        style={{
          borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          flexDirection: isRTL ? 'row-reverse' : 'row',
        }}
      >
        <StyledText
          className="text-lg font-semibold"
          style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
        >
          {t('allDumpsters.title', 'All Dumpsters')}
        </StyledText>
        
        <IconButton
          icon={() => <Ionicons name="close" size={24} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />}
          onPress={() => router.back()} // Close button
        />
      </StyledView>

      {/* Filters Section */}
      <StyledView className="p-4  flex-row justify-between items-center" style={{ borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
         {/* Waste Type Filter */}
         <StyledView className="flex-1 mr-2" style={{ marginRight: isRTL ? 0: 8, marginLeft: isRTL ? 8 : 0 }}>
             <StyledText className="text-xs mb-1" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light, textAlign: isRTL ? 'right': 'left' }}>{t('allDumpsters.filters.wasteType', 'Waste Type')}</StyledText>
             <StyledButton
                mode="contained"
                onPress={openWasteTypeSelector}
                textColor={isDarkMode ? colors.textColors.dark : colors.textColors.light}
                style={{ borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light }}
                labelStyle={{ fontSize: 14 }}
                compact // Makes button smaller
             >
                {selectedWasteTypeName}
             </StyledButton>
         </StyledView>
          {/* Sorting Section */}
       <StyledView className="flex-1" style={{ borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light, flexDirection: isRTL ? 'column-reverse' : 'column', alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
       <StyledText className="text-xs mb-1" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light, textAlign: isRTL ? 'right': 'left' }}>{t('allDumpsters.sorting.sortBy', 'Sort by:')}</StyledText>
            <StyledButton
                mode="contained"
                onPress={openSortSelector}
                textColor={isDarkMode ? colors.textColors.dark : colors.textColors.light}
                style={{ borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light, width: '100%' }}
                labelStyle={{ fontSize: 14 }}
                
             >
                  {selectedSortName}
             </StyledButton>
       </StyledView>
      </StyledView>

     

      {/* Dumpster List */}
      <FlatList
        testID="dumpsters-list"
        data={sortedDumpsters}
        keyExtractor={(item) => item.id}
        numColumns={2}
        style={{ flex: 1, marginRight: -12 }}
        renderItem={({ item }) => (
          <StyledView className="w-1/2 mr-[-12]">
            <DumpsterCardV2
              dumpster={item}
              onPress={() => handleDumpsterPress(item)}
            />
          </StyledView>
        )}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <StyledView className="p-4 items-center justify-center" style={{ minHeight: 200 }}>
            {isLoadingDumpsters ? (
              <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
            ) : (
              <>
                <MaterialIcons name="info-outline" size={36} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
                <StyledText className="text-center mt-2 text-base" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                  {t('allDumpsters.noDumpstersFound', 'No dumpsters found')}
                </StyledText>
                <StyledText className="text-center mt-1 text-sm" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  {t('allDumpsters.tryDifferentFilters', 'Try adjusting your filters')}
                </StyledText>
              </>
            )}
          </StyledView>
        }
      />
    </SafeAreaView>
  );
} 