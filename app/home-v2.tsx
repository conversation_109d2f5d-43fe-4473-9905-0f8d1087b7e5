import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, ScrollView, ActivityIndicator, Pressable } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { styled } from 'nativewind';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Feather } from '@expo/vector-icons';

// Hooks
import { useAuth, GlobalUserIdentifier } from '@/context/AuthContext';
import { useProfile } from '@/hooks/useProfile';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { useDumpsters, useWasteTypes } from '@/hooks/v2/useDumpsters';

// Components
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import MarketingCarousel, { BannerItem } from '@/components/home/<USER>';
import DumpsterCardV2 from './components/v2/DumpsterCard';

// Utils
import { safeTranslate } from '@/utils/i18nHelpers';
import { getUserName, refreshUserProfile, syncProfileAuthIds, getUserOrders } from '@/utils/user';
import * as colors from '@/theme/colors';
import { registerForPushNotificationsAsync, savePushToken } from '../src/utils/notifications';

// Types
import { DumpsterFilters } from '@/types/v2/dumpster';

const StyledView = styled(View);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledPressable = styled(Pressable);

// Mock banner data - in production, this would come from an API
const MARKETING_BANNERS: BannerItem[] = [
  {
    id: '1',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner.png',
    title: 'Spring Cleaning Special',
    subtitle: 'Get 15% off your next dumpster rental',
    actionUrl: '/promotions/spring-cleaning',
  },
  {
    id: '2',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner-en.png',
    title: 'New Eco-Friendly Options',
    subtitle: 'Sustainable waste management solutions',
    actionUrl: '/eco-friendly',
  },
  {
    id: '3',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner-ar.png',
    title: 'Same-Day Delivery',
    subtitle: 'For orders placed before noon',
    actionUrl: '/same-day-delivery',
  },
];

export default function HomeScreenV2() {
  const router = useRouter();
  const { t } = useTranslation();
  const { session } = useAuth();
  const { profile } = useProfile(GlobalUserIdentifier.profileId || '');
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  // State
  const [userName, setUserName] = useState('');
  const [orderCount, setOrderCount] = useState(0);
  const [filters, setFilters] = useState<DumpsterFilters>({
    availability: true,
  });

  // Fetch dumpsters with V2 API
  const {
    data: dumpsters,
    isLoading: isLoadingDumpsters,
    isError: isErrorDumpsters
  } = useDumpsters(filters);

  // Fetch waste types
  const { data: wasteTypes } = useWasteTypes();

  // Sync auth IDs on app start
  useEffect(() => {
    if (session?.user?.id) {
      syncProfileAuthIds(false).catch(console.error);
    }
  }, [session?.user?.id]);

  // Get user name
  useEffect(() => {
    const fetchUserName = async () => {
      const name = await getUserName();
      if (name) {
        // If we have a name in AsyncStorage, use it
        setUserName(name.split(' ')[0]);
      } else if (profile?.full_name) {
        // If not, but we have it from the profile hook, use that and refresh profile data
        setUserName(profile.full_name.split(' ')[0]);
        refreshUserProfile(false).catch(console.error);
      } else {
        // Last resort, refresh profile data from server
        const refreshedProfile = await refreshUserProfile(false);
        if (refreshedProfile?.full_name) {
          setUserName(refreshedProfile.full_name.split(' ')[0]);
        } else {
          setUserName('User');
        }
      }
    };

    fetchUserName().catch(console.error);
  }, [profile]);

  // Get order count
  useEffect(() => {
    const loadOrders = async () => {
      if (session?.user?.id) {
        const orders = await getUserOrders();
        if (orders) {
          setOrderCount(orders.length);
        }
      }
    };

    loadOrders().catch(console.error);
  }, [session?.user?.id]);

  // Register and save push token
  useEffect(() => {
    const registerAndSaveToken = async () => {
      if (session?.user?.id) {
        const token = await registerForPushNotificationsAsync();
        if (token) {
          await savePushToken(session.user.id, token);
        }
      }
    };

    registerAndSaveToken().catch(console.error);
  }, [session?.user?.id]);

  // Get time-based greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return safeTranslate(t, 'home.greeting.morning', 'Good morning');
    if (hour < 18) return safeTranslate(t, 'home.greeting.afternoon', 'Good afternoon');
    return safeTranslate(t, 'home.greeting.evening', 'Good evening');
  };

  // Navigation handlers
  const openOrders = () => router.push('/orders');
  const openProfile = () => router.push('/profile');
  const openAllDumpsters = () => router.push('/all-dumpsters');
  const openDiscoverV3 = () => router.push('/discover-v3');
  const handleDumpsterPress = (dumpsterId: string) => router.push(`/dumpster-selection/${dumpsterId}`);
  const handleBannerPress = (banner: BannerItem) => {
    if (banner.actionUrl) {
      router.push(banner.actionUrl);
    }
  };

  return (
    <SafeAreaView style={{ backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light, flex: 1 }}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      {/* Top Navigation */}
      <StyledView className="flex-row justify-between items-center px-5 py-2">
        <StyledTouchableOpacity
          onPress={openOrders}
          className="w-10 h-10 rounded-full justify-center items-center" style={{
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}
        >
          <Feather name="file-text" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </StyledTouchableOpacity>

        <StyledTouchableOpacity
          onPress={openProfile}
          className="w-10 h-10 rounded-full justify-center items-center" style={{
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}
        >
          <Feather name="user" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </StyledTouchableOpacity>
      </StyledView>

      <StyledScrollView className="flex-1 px-5">
        {/* Greeting Section */}
        <StyledView className="my-3">
          <NewRTLText
            style={{
              fontSize: 24,
              fontWeight: '600',
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              textAlign: isRTL ? 'right' : 'left',
            }}
          >
            {getGreeting()},
          </NewRTLText>
          <NewRTLText
            style={{
              fontSize: 24,
              fontWeight: '400',
              color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
              textAlign: isRTL ? 'right' : 'left',
            }}
          >
            {userName || 'User'}
          </NewRTLText>
        </StyledView>

        {/* Marketing Carousel */}
        <MarketingCarousel
          banners={MARKETING_BANNERS}
          onBannerPress={handleBannerPress}
        />

        {/* Dumpster Section Header */}
        <NewRTLView
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: 16,
            marginBottom: 8,
          }}
        >
          <StyledView className="flex-row items-center">
            <NewRTLText
              style={{
                fontSize: 18,
                fontWeight: '600',
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              }}
            >
              {safeTranslate(t, 'home.allDumpsters', 'Top Dumpsters')}
            </NewRTLText>
            <StyledTouchableOpacity
              className="ml-2 bg-primary-100 dark:bg-primary-900 px-2 py-1 rounded-full"
              onPress={openDiscoverV3}
            >
              <NewRTLText
                style={{
                  fontSize: 12,
                  color: colors.brandColors.primary[500],
                }}
              >
                Try V3
              </NewRTLText>
            </StyledTouchableOpacity>
          </StyledView>
          <StyledTouchableOpacity onPress={openAllDumpsters}>
            <NewRTLText
              style={{
                fontSize: 14,
                color: colors.brandColors.primary[500],
              }}
            >
              {safeTranslate(t, 'home.viewAll', 'View All')}
            </NewRTLText>
          </StyledTouchableOpacity>
        </NewRTLView>

        {/* Dumpster List */}
        {isLoadingDumpsters ? (
          <StyledView className="py-8 items-center">
            <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
          </StyledView>
        ) : isErrorDumpsters ? (
          <StyledView className="py-8 items-center">
            <NewRTLText
              style={{
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              }}
            >
              {safeTranslate(t, 'home.errorLoading', 'Error loading dumpsters')}
            </NewRTLText>
          </StyledView>
        ) : (
          <StyledView className="flex-row flex-wrap justify-between">
            {dumpsters?.slice(0, 4).map((dumpster) => (
              <StyledView key={dumpster.id} className="flex-row justify-center w-1/2 ">
                <DumpsterCardV2
                  dumpster={dumpster}
                  onPress={() => handleDumpsterPress(dumpster.id)}
                />
              </StyledView>
            ))}
          </StyledView>
        )}
      </StyledScrollView>

      {/* Bottom Input Button (FAB) */}
      <StyledView className="absolute bottom-10 left-5 right-5 items-center">
        <StyledPressable
          onPress={() => router.push('/dumpster-selection')}
          className="w-full bg-primary-500 py-4 px-6 rounded-full " style={{shadowColor: 'black', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.2, shadowRadius: 4, elevation: 4}}
          testID="tap-to-type-button"
        >
          <StyledView className={`flex-row items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <StyledView className="w-10 h-10 rounded-full justify-center items-center shadow-sm" style={{
              backgroundColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[500],
            }}
            >
              <Feather
                name="mic"
                size={20}
                color={isDarkMode ? colors.textColors.light : colors.textColors.dark}
              />
            </StyledView>
            <NewRTLText
              style={{
                flex: 1,
                fontSize: 16,
                color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[700],
                fontWeight: '500',
                marginLeft: isRTL ? 0 : 12,
                marginRight: isRTL ? 12 : 0,
                textAlign: isRTL ? 'right' : 'left',
              }}
            >
              {safeTranslate(t, 'home.inputPrompt', 'Tap to type')}
            </NewRTLText>
          </StyledView>
        </StyledPressable>
      </StyledView>
    </SafeAreaView>
  );
}

// Jest test stub
/*
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import HomeScreenV2 from '../home-v2';
import { useAuth } from '@/context/AuthContext';
import { useDumpsters } from '@/hooks/v2/useDumpsters';

// Mock the hooks
jest.mock('@/context/AuthContext');
jest.mock('@/hooks/v2/useDumpsters');
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

describe('HomeScreenV2', () => {
  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue({
      session: { user: { id: 'test-user-id' } },
    });

    (useDumpsters as jest.Mock).mockReturnValue({
      data: [
        { id: '1', nameEn: 'Test Dumpster', isAvailable: true },
        { id: '2', nameEn: 'Another Dumpster', isAvailable: true },
      ],
      isLoading: false,
      isError: false,
    });
  });

  it('renders the greeting and username', async () => {
    const { getByText } = render(<HomeScreenV2 />);
    await waitFor(() => {
      expect(getByText(/Good (morning|afternoon|evening)/)).toBeTruthy();
    });
  });

  it('renders the marketing carousel', () => {
    const { getByTestId } = render(<HomeScreenV2 />);
    expect(getByTestId('marketing-carousel')).toBeTruthy();
  });

  it('renders dumpster cards when data is loaded', async () => {
    const { getAllByTestId } = render(<HomeScreenV2 />);
    await waitFor(() => {
      expect(getAllByTestId('dumpster-card').length).toBeGreaterThan(0);
    });
  });
});
*/
