import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, ActivityIndicator, StatusBar, TouchableOpacity, Image, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { styled } from 'nativewind';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Button, Divider, IconButton, TextInput, Card } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons, Ionicons, FontAwesome5 } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import { shadowStyles } from '@/utils/shadowStyles';
import CustomDatePicker from './components/CustomDatePicker';
import { useAddresses } from '@/hooks/useAddresses';
import { useDumpster } from '@/hooks/useDumpsters';
import * as colors from '@/theme/colors';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import { useTheme } from '@/context/ThemeContext';
import { GlobalUserIdentifier, useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';
import { useCreateOrder } from '@/hooks/new/useOrders';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledButton = styled(Button);
const StyledDivider = styled(Divider);
const StyledSafeAreaView = styled(SafeAreaView);
const StyledLinearGradient = styled(LinearGradient);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledTextInput = styled(TextInput);
const StyledCard = styled(Card);

export default function CheckoutScreen() {
  // 1. First, declare all context hooks
  const { t } = useTranslation();
  const router = useRouter();
  const { session } = useAuth();
  const { isRTL } = useRTLContext();
  const { isDarkMode } = useTheme();

  // 2. Declare all params and data fetching hooks
  const { id: dumpsterId, partnerId } = useLocalSearchParams<{ 
    id: string;
    partnerId: string;
  }>();
  const { addresses, isLoading: isLoadingAddresses } = useAddresses();
  const { createOrder, isLoading: isCreating } = useCreateOrder();
  const { data: dumpster, isLoading: isLoadingDumpster } = useDumpster(dumpsterId as string);

  // 3. Declare all state hooks
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [loading, setLoading] = useState(false);
  const [location, setLocation] = useState('');
  const [deliveryDate, setDeliveryDate] = useState(new Date(new Date().setDate(new Date().getDate() + 1)));
  const [formValid, setFormValid] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  const [customCoordinates, setCustomCoordinates] = useState({ latitude: 0, longitude: 0 });

  // 4. Group related useEffects together
  useEffect(() => {
    let mounted = true;

    const checkAuth = async () => {
      try {
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        if (!mounted) return;

        if (!currentSession?.user?.id) {
          console.log('No active session found, redirecting to login');
          router.replace('/login.v2');
          return;
        }

        setIsAuthReady(true);
      } catch (error) {
        console.error('Auth check error:', error);
        if (mounted) {
          router.replace('/login.v2');
        }
      }
    };

    checkAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (!mounted) return;

      console.log('Auth state changed:', event);
      if (event === 'SIGNED_OUT' || !session) {
        router.replace('/login.v2');
      } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        setIsAuthReady(true);
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [router]);

  useEffect(() => {
    if ((selectedAddress || (location.trim().length > 0 && customCoordinates.latitude !== 0)) && dumpster) {
      setFormValid(true);
    } else {
      setFormValid(false);
    }
  }, [location, selectedAddress, dumpster, customCoordinates]);

  useEffect(() => {
    if (addresses && addresses.length > 0) {
      const defaultAddress = addresses.find(addr => addr.is_default);
      if (defaultAddress) {
        setSelectedAddress(defaultAddress);
      }
    }
  }, [addresses]);

  // 5. Define handlers after all hooks
  const handlePlaceOrder = async () => {
    try {
      setLoading(true);
      
      if (!session?.user?.id) {
        router.replace('/login.v2');
        return;
      }

      if (!formValid) {
        Alert.alert(
          t('Missing Information'),
          t('Please provide your location before proceeding.'),
          [{ text: t('OK') }]
        );
        return;
      }

      if (!dumpster) {
        throw new Error('Dumpster data is required');
      }

      // Simplified order data without any user references
      const { data: newOrder, error } = await supabase
        .from('orders')
        .insert({
          dumpster_id: dumpster.id,
          customer_id: GlobalUserIdentifier.profileId,
          partner_id: partnerId, // Add partner_id
          waste_type: dumpster.compatibleWasteTypes?.[0] || '1',
          delivery_address: selectedAddress ? selectedAddress.street_address : location,
          delivery_coordinates: {
            latitude: selectedAddress ? selectedAddress.latitude : customCoordinates.latitude,
            longitude: selectedAddress ? selectedAddress.longitude : customCoordinates.longitude
          },
          delivery_date: deliveryDate.toISOString(),
          rental_duration_days: 1,
          status: 'pending',
          payment_status: 'pending',
          payment_method: 'cash',
          base_price: dumpster.pricePerDay || 0,
          total_amount: dumpster.pricePerDay || 0
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Update the navigation path
      router.replace({
        pathname: '/order-completed',
        params: { orderId: newOrder.id }
      });
    } catch (error: any) {
      console.error('Error creating order:', error);
      Alert.alert(
        t('Error'),
        t('Failed to create order. Please try again.'),
        [{ text: t('OK') }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleAddressSelect = () => {
    router.push('/addresses');
  };

  // Fake coordinates - in a real app, this would use a map or geolocation
  const simulateGetCoordinates = () => {
    if (location.trim().length > 0) {
      // Simulate getting coordinates for the entered address
      // These would normally come from a geocoding service
      setCustomCoordinates({
        latitude: 24.7136 + (Math.random() * 0.05),
        longitude: 46.6753 + (Math.random() * 0.05)
      });
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // 6. Loading states and early returns
  if (!isAuthReady) {
    return (
      <StyledView className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
        <StyledText className="mt-4">{t('Loading...')}</StyledText>
      </StyledView>
    );
  }

  if (isLoadingDumpster) {
    return (
      <StyledView className="flex-1 justify-center items-center p-4">
        <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
        <StyledText className="mt-4 text-lg text-center">
          {t('Loading dumpster details...')}
        </StyledText>
      </StyledView>
    );
  }

  if (!dumpster) {
    return (
      <StyledView className="flex-1 justify-center items-center p-4">
        <MaterialIcons name="error-outline" size={48} color={colors.brandColors.danger[500]} />
        <StyledText className="mt-4 text-lg text-center">
          {t('Dumpster not found. Please go back and select a dumpster.')}
        </StyledText>
        <StyledButton mode="contained" onPress={() => router.back()}>
          {t('Go Back')}
        </StyledButton>
      </StyledView>
    );
  }

  // 7. Main render
  return (
    <StyledLinearGradient
      className="flex-1"
      colors={isDarkMode ? ['#1a202c', '#1a202c', '#1a202c'] : isDarkMode ? ['#1a202c', '#1a202c', '#1a202c'] : ['#f5f7fa', '#e8f0fe', '#e1eafc']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: t('Checkout'),
          headerLeft: () => (
            <IconButton
              icon={isRTL ? 'arrow-right' : 'arrow-left'}
              size={24}
              onPress={() => router.back()}
              
            />
          ),
          headerStyle: {
            backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
          },
          headerTitleStyle: {
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
          },
          headerTintColor: isDarkMode ? colors.textColors.light : colors.textColors.dark,
          
        }}
      />
      
      <StyledScrollView className="flex-1 px-4 py-6 " style={{ backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }}>
        {/* Dumpster Summary */}
        <StyledView className="p-6 rounded-xl mb-6" style={[shadowStyles.sm, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
          <StyledText className="text-lg font-bold mb-4" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
            {t('Selected Dumpster')}
          </StyledText>
          
          <StyledView className="flex-row">
            <Image
              source={{ uri: dumpster.imageUrl }}
              style={{ width: 80, height: 80, borderRadius: 8 }}
              resizeMode="cover"
            />
            <StyledView className="ml-4 flex-1">
              <StyledText className="font-bold text-lg" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                {dumpster.name}
              </StyledText>
              <StyledView className="flex-row items-center mt-1">
                {isDarkMode ? (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />) : (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />)}
                <StyledText style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                  {dumpster.pricePerDay} / {t('Load')}
                </StyledText>
              </StyledView>
              {dumpster.description && (
                <StyledText className="mt-1 text-sm" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  {dumpster.description.length > 60 
                    ? `${dumpster.description.substring(0, 60)}...` 
                    : dumpster.description}
                </StyledText>
              )}
            </StyledView>
          </StyledView>
        </StyledView>

        {/* Location Input - Required */}
        <StyledView className=" p-6 rounded-xl mb-6" style={[shadowStyles.sm, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
          <StyledView className="flex-row justify-between items-center mb-4">
            <StyledText className="text-lg font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
              {t('Location')} <StyledText className="text-red-500">*</StyledText>
            </StyledText>
            
            <StyledButton
              mode="text"
              onPress={handleAddressSelect}
              icon="map-marker-plus"
              textColor={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]}
            >
              {selectedAddress ? t('Change Address') : t('Select Address')}
            </StyledButton>
          </StyledView>
          
          {isLoadingAddresses ? (
            <StyledView className="items-center py-4">
              <ActivityIndicator size="small" color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
            </StyledView>
          ) : selectedAddress ? (
            <StyledCard mode="outlined" className="mb-2" style={{ backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light, borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light }}>
              <Card.Content>
                <StyledView className="flex-row items-center mb-2">
                  <FontAwesome5
                    name={
                      selectedAddress.type === 'home' ? 'home' : 
                      selectedAddress.type === 'office' ? 'briefcase' : 'map-marker'
                    }
                    size={18}
                    color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]}
                  />
                  <StyledText className="ml-2 font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                    {selectedAddress.name || t('Address')}
                  </StyledText>
                  {selectedAddress.is_default && (
                    <StyledView className="ml-2 bg-primary-50 px-2 py-1 rounded">
                      <StyledText className="text-xs" style={{ color: isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500] }}>{t('Default')}</StyledText>
                    </StyledView>
                  )}
                </StyledView>
                
                <StyledText className="text-gray-600" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  {selectedAddress.street_address}
                </StyledText>
                <StyledText className="text-gray-600" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
                  {`${selectedAddress.city}, ${selectedAddress.state} ${selectedAddress.zip_code}`}
                </StyledText>
              </Card.Content>
            </StyledCard>
          ) : addresses && addresses.length > 0 ? (
            <StyledTouchableOpacity
              className="border border-gray-300 rounded-lg p-4 mb-2 flex-row items-center"
              onPress={handleAddressSelect}
            >
              <MaterialIcons name="location-on" size={24} color="#00d1b2" />
              <StyledText className="ml-2 text-gray-700">
                {t('Select from your saved addresses')}
              </StyledText>
              <MaterialIcons name="chevron-right" size={24} color="#00d1b2" style={{ marginLeft: 'auto' }} />
            </StyledTouchableOpacity>
          ) : (
            <StyledView>
              <StyledTextInput
                mode="outlined"
                label={t('Enter your address')}
                value={location}
                onChangeText={setLocation}
                className="mb-4"
                placeholder={t('e.g. 37t76272, alfajer st, al yasmin, riyadh, 13132')}
                right={
                  <TextInput.Icon 
                    icon="map-marker" 
                    onPress={() => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      simulateGetCoordinates();
                    }} 
                  />
                }
              />
              
              <StyledButton
                mode="text"
                onPress={handleAddressSelect}
                icon="plus"
                textColor="#00d1b2"
              >
                {t('Add New Address')}
              </StyledButton>
            </StyledView>
          )}
          
          <StyledText className="text-sm" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
            {t('Your location helps us find available dumpsters in your area')}
          </StyledText>
        </StyledView>
        
        {/* Delivery Date Selection */}
        <StyledView className=" p-6 rounded-xl mb-6" style={[shadowStyles.sm, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
          <StyledText className="text-lg font-bold mb-2" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
            {t('When do you need it?')}
          </StyledText>
          
          <CustomDatePicker
            date={deliveryDate}
            onDateChange={(date: Date) => setDeliveryDate(date)}
            minimumDate={new Date(new Date().setDate(new Date().getDate() + 1))}
            maximumDate={new Date(new Date().setDate(new Date().getDate() + 60))}
            formatDate={formatDate}
          />
          
          <StyledView 
            className="flex-row items-center mt-4 p-3 rounded-lg"
            style={{ backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light }}
          >
            <MaterialIcons name="info-outline" size={18} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
            <StyledText className="text-sm text-gray-600 ml-2" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
              {t('It may need 24h to process your order due to municipality rules')}
            </StyledText>
          </StyledView>
          
          
        </StyledView>
        
        {/* Order Summary */}
        <StyledView className=" p-6 rounded-xl mb-6" style={[shadowStyles.sm, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
          <StyledText className="text-2xl font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
            {t('Order Summary')}
          </StyledText>
          
          <StyledDivider className="my-4" />
          
          <StyledView className="flex-row justify-between mb-2">
            <StyledText className="text-gray-600" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>{t('Subtotal')}</StyledText>
            <StyledView className="flex-row items-center">
            {isDarkMode ? (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />) : (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />)}
              <StyledText className="font-semibold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>{dumpster.pricePerDay * 3}</StyledText>
            </StyledView>
          </StyledView>
          
          <StyledView className="flex-row justify-between mb-2">
            <StyledText className="text-gray-600" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>{t('Delivery')}</StyledText>
            <StyledView className="flex-row items-center">
            {isDarkMode ? (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />) : (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                  style={{ width: 16, height: 16, marginRight: 2 }}
                  resizeMode="contain"
                />)}
              <StyledText className="font-semibold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>0.00</StyledText>
            </StyledView>
          </StyledView>
          
          <StyledDivider className="my-4" />
          
          <StyledView className="flex-row justify-between mb-4">
            <StyledText className="text-lg font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>{t('Total')}</StyledText>
            <StyledView className="flex-row items-center">
            {isDarkMode ? (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                  style={{ width: 18, height: 18, marginRight: 2 }}
                  resizeMode="contain"
                />) : (<Image
                  source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                  style={{ width: 18, height: 18, marginRight: 2 }}
                  resizeMode="contain"
                />)}
              <StyledText className="text-lg font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>{dumpster.pricePerDay * 3}</StyledText>
            </StyledView>
          </StyledView>
          
          <StyledText className="text-xs text-gray-500" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
            {t('All Prices is Vat inclusive')}
          </StyledText>
        </StyledView>
        
        {/* Payment Information */}
        <StyledView className=" p-6 rounded-xl mb-10" style={[shadowStyles.sm, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}>
          <StyledText className="text-xl font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
            {t('Payment')}
          </StyledText>
          
          <StyledView className="flex-row items-center">
            <MaterialIcons name="money" size={24} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
            <StyledView className="ml-3">
              <StyledText className="text-gray-800 font-semibold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>{t('Cash on delivery')}</StyledText>
              <StyledText className="text-gray-600 text-sm" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>{t('Pay when your dumpster is delivered')}</StyledText>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledScrollView>
      
      <StyledView className="px-4 pb-4" style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}>
        <StyledSafeAreaView edges={['bottom']}>
          <StyledText className="text-center text-gray-500 mb-2 mt-3" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
            {t('By Submitting the order i confirm order details')}
          </StyledText>
          
          <StyledButton
            mode="contained"
            onPress={handlePlaceOrder}
            loading={loading || isCreating}
            disabled={loading || isCreating || !formValid}
            contentStyle={{ paddingVertical: 8 }}
            labelStyle={{ fontSize: 16, fontWeight: 'bold' }}
            style={{ 
              backgroundColor: formValid 
                ? isDarkMode 
                  ? colors.brandColors.primary[400] 
                  : colors.brandColors.primary[500]
                : isDarkMode 
                  ? colors.brandColors.primary[400] 
                  : colors.brandColors.primary[500],
              opacity: (loading || isCreating || !formValid) ? 0.8 : 1
            }}
          >
            {loading || isCreating ? t('orders.processingOrder') : t('common.submit')}
          </StyledButton>
          
          
        </StyledSafeAreaView>
      </StyledView>
    </StyledLinearGradient>
  );
} 
