import React, { useEffect, useState, useRef } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Dimensions, Pressable } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { styled } from 'nativewind';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Feather } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate,
  Extrapolation
} from 'react-native-reanimated';
import { Surface } from 'react-native-paper';

// Hooks
import { useAuth, GlobalUserIdentifier } from '@/context/AuthContext';
import { useProfile } from '@/hooks/useProfile';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { useDumpsters } from '@/hooks/v2/useDumpsters';
import { useDumpsterGroups, DumpsterGroupFilters } from '@/hooks/v2/useDumpsterGroups';
import { useQueryClient } from '@tanstack/react-query';
import { DumpsterKeys } from '@/hooks/v2/useDumpsters';

// Components
import { NewRTLText } from '@/components/rtl/new-index';
import MarketingCarousel, { BannerItem } from '@/components/home/<USER>';
import StickySearchHeader, { FilterOption } from '@/components/molecules/StickySearchHeader';
import DumpsterGroupCard from '@/components/home/<USER>';
import { City, SAUDI_CITIES } from '@/components/home/<USER>';
import CityBottomSheet from '@/components/home/<USER>';
import FilterBottomSheet from '@/components/home/<USER>';
import SortBottomSheet from '@/components/home/<USER>';

// Utils
import { safeTranslate } from '@/utils/i18nHelpers';
import { getUserName } from '@/utils/user';
import * as colors from '@/theme/colors';

// Types
import { DumpsterFilters } from '@/types/v2/dumpster';

const StyledSafeAreaView = styled(SafeAreaView);

// Mock data removed - not used in this implementation
// Mock banner data - in production, this would come from an API
const MARKETING_BANNERS: BannerItem[] = [
  {
    id: '1',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner.png',
    title: 'Spring Cleaning Special',
    subtitle: 'Get 15% off your next dumpster rental',
    actionUrl: '/promotions/spring-cleaning',
  },
  {
    id: '2',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner-en.png',
    title: 'New Eco-Friendly Options',
    subtitle: 'Sustainable waste management solutions',
    actionUrl: '/eco-friendly',
  },
  {
    id: '3',
    imageUrl: 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/app/banners/banner-ar.png',
    title: 'Same-Day Delivery',
    subtitle: 'For orders placed before noon',
    actionUrl: '/same-day-delivery',
  },
];

// Project types for different project sizes
const PROJECT_TYPES: FilterOption[] = [
  { id: 'very-small', label: 'Very Small Project' }, // Bathroom renovation, small repairs
  { id: 'small', label: 'Small Project' }, // Room renovation, small landscaping
  { id: 'medium', label: 'Medium Project' }, // House or villa renovation
  { id: 'large', label: 'Large Project' }, // Demolition, 3-5 floor building renovation
  { id: 'business', label: 'Business' }, // Restaurant, supermarket, commercial
];

// Count options for number of dumpsters (max 3)
const COUNT_OPTIONS: FilterOption[] = [
  { id: 'count-1', label: '1 Dumpster' },
  { id: 'count-2', label: '2 Dumpsters' },
  { id: 'count-3', label: '3 Dumpsters' },
];

// Waste types for filtering
const WASTE_TYPES: { id: string, nameEn: string, nameAr: string }[] = [
  { id: 'construction', nameEn: 'Construction', nameAr: 'بناء' },
  { id: 'household', nameEn: 'Household', nameAr: 'منزلي' },
  { id: 'green', nameEn: 'Green Waste', nameAr: 'نفايات خضراء' },
  { id: 'metal', nameEn: 'Metal', nameAr: 'معدن' },
  { id: 'wood', nameEn: 'Wood', nameAr: 'خشب' },
  { id: 'concrete', nameEn: 'Concrete', nameAr: 'خرسانة' },
  { id: 'plastic', nameEn: 'Plastic', nameAr: 'بلاستيك' },
  { id: 'mixed', nameEn: 'Mixed', nameAr: 'مختلط' },
];

/**
 * HomeV4Screen - A unified home screen combining elements from home-v2 and discover-v3
 *
 * Features:
 * - Compact app bar with document icon and avatar
 * - Personalized greeting and hero card
 * - Sticky search/filter bar
 * - Two-column FlatList grid of dumpster tiles
 * - Supports RTL layouts and dark mode
 */
export default function HomeV4Screen() {
  const router = useRouter();
  const { t, i18n } = useTranslation();
  const { } = useAuth(); // Auth context is required but session not used
  const { profile } = useProfile(GlobalUserIdentifier.profileId || '');
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenWidth = Dimensions.get('window').width;
  const queryClient = useQueryClient();

  // Add missing translations
  useEffect(() => {
    // Add translations if they don't exist
    if (!i18n.exists('select_city')) {
      i18n.addResources('en', 'translation', {
        select_city: 'Select City',
        search_cities: 'Search cities',
        current_location: 'Current Location',
      });

      i18n.addResources('ar', 'translation', {
        select_city: 'اختر المدينة',
        search_cities: 'ابحث عن المدن',
        current_location: 'الموقع الحالي',
      });
    }
  }, []);

  // Refs for sticky header
  const scrollY = useSharedValue(0);
  const headerHeight = 320; // Height of top nav + greeting + marketing carousel

  // Refs
  const cityBottomSheetRef = useRef<any>(null);
  const filterBottomSheetRef = useRef<any>(null);
  const sortBottomSheetRef = useRef<any>(null);

  // State
  const [userName, setUserName] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [selectedProjectType, setSelectedProjectType] = useState<string | null>(null);
  const [selectedCount, setSelectedCount] = useState<string | null>(null);
  const [selectedCity, setSelectedCity] = useState<City>(SAUDI_CITIES[0]); // Default to Riyadh
  const [selectedWasteTypes, setSelectedWasteTypes] = useState<string[]>([]);
  const [selectedSortOption, setSelectedSortOption] = useState<string | null>('price-asc'); // Default sort by price low to high
  const [filters, setFilters] = useState<DumpsterGroupFilters>({
    availability: true,
    cityId: selectedCity.id,
  });

  // Update filters when city changes
  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      cityId: selectedCity.id
    }));

    // Invalidate queries to refetch data with new city
    queryClient.invalidateQueries({ queryKey: DumpsterKeys.lists() });
  }, [selectedCity, queryClient]);

  // Update filters when search value changes
  useEffect(() => {
    // Debounce search to avoid too many requests
    const debounceTimeout = setTimeout(() => {
      if (searchValue.trim()) {
        setFilters(prev => ({
          ...prev,
          searchTerm: searchValue.trim()
        }));
      } else if (filters.searchTerm) {
        // Remove search term if it exists and search value is empty
        const { searchTerm, ...rest } = filters;
        setFilters(rest);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(debounceTimeout);
  }, [searchValue]);

  const handleBannerPress = (banner: BannerItem) => {
    if (banner.actionUrl) {
      router.push(banner.actionUrl);
    }
  };

  // Fetch dumpster groups (grouped by size)
  const {
    data: dumpsterGroups,
    isLoading: isLoadingDumpsters
  } = useDumpsterGroups(filters);

  // Get user name
  useEffect(() => {
    if (profile?.id) {
      const fetchUserName = async () => {
        const name = await getUserName();
        if (name) {
          setUserName(name.split(' ')[0]);
        } else {
          setUserName('User');
        }
      };
      fetchUserName();
    }
  }, [profile]);

  // Handle scroll events
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  // Animated styles for sticky search header
  const searchHeaderAnimatedStyle = useAnimatedStyle(() => {
    // Interpolate opacity from 0 to 1 when scrolled past threshold
    const opacity = interpolate(
      scrollY.value,
      [headerHeight - 20, headerHeight],
      [0, 1],
      Extrapolation.CLAMP
    );

    return {
      opacity,
      zIndex: opacity > 0 ? 1000 : 0,
    };
  });

  // Get time of day for greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return safeTranslate(t, 'home.greeting.morning', 'Good morning');
    if (hour < 18) return safeTranslate(t, 'home.greeting.afternoon', 'Good afternoon');
    return safeTranslate(t, 'home.greeting.evening', 'Good evening');
  };

  // Navigation handlers
  const openOrderHistory = () => {
    router.push('/orders');
  };

  const openProfile = () => {
    router.push('/profile');
  };

  const handleDumpsterGroupPress = (group: any) => {
    // Navigate to discover screen with size filter
    router.push({
      pathname: '/discover-v3',
      params: { sizeId: group.sizeId }
    });
  };

  const handleFilterPress = () => {
    // Open filter bottom sheet
    if (filterBottomSheetRef.current) {
      filterBottomSheetRef.current.open();
    }
  };

  const handleSortPress = () => {
    // Open sort options
    if (sortBottomSheetRef.current) {
      sortBottomSheetRef.current.open();
    }
  };

  const handleCityPress = () => {
    // Open city selector bottom sheet
    if (cityBottomSheetRef.current) {
      cityBottomSheetRef.current.open();
    }
  };

  // Handle waste type changes
  const handleWasteTypeChange = (wasteTypes: string[]) => {
    setSelectedWasteTypes(wasteTypes);
  };

  // Handle applying filters
  const handleApplyFilters = () => {
    setFilters(prev => ({
      ...prev,
      wasteTypeIds: selectedWasteTypes.length > 0 ? selectedWasteTypes : undefined,
      searchTerm: searchValue.length > 0 ? searchValue : undefined,
      // Add project type and count to filters
      projectType: selectedProjectType,
      count: selectedCount ? COUNT_OPTIONS.find(c => c.id === selectedCount)?.id : undefined,
    }));
  };

  // Handle resetting filters
  const handleResetFilters = () => {
    setSelectedWasteTypes([]);
    setSelectedProjectType(null);
    setSelectedCount(null);
    setSearchValue('');
    setFilters({
      availability: true,
      cityId: selectedCity.id,
    });
  };

  // Handle sort option change
  const handleSortOptionChange = (sortOption: string) => {
    setSelectedSortOption(sortOption);
    // Note: Sorting is now handled by the backend in the useDumpsterGroups hook
    // The groups are already sorted by capacity (size) by default
  };

  // Render dumpster group card
  const renderDumpsterGroupTile = ({ item }: { item: any }) => {
    return (
      <View style={{ width: screenWidth / 2 - 20, marginBottom: 16 }}>
        <DumpsterGroupCard
          group={item}
          onPress={() => handleDumpsterGroupPress(item)}
          bestFor={item.bestFor}
          availableCount={item.dumpsterCount}
        />
      </View>
    );
  };

  return (
    <StyledSafeAreaView className="flex-1" style={{ backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }} >
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      {/* Fixed Safe-Area Header */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        zIndex: 1010
      }}>
        {/* Order History Icon */}
        <TouchableOpacity
          onPress={openOrderHistory}
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}
          testID="order-history-button"
        >
          <Feather name="file-text" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </TouchableOpacity>

        {/* Profile Avatar */}
        <TouchableOpacity
          onPress={openProfile}
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}
          testID="profile-button"
        >
          <Feather name="user" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
        </TouchableOpacity>
      </View>

      {/* Sticky Search Header - Shows when scrolled past threshold */}
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: 114, // Below the fixed header
            left: 0,
            right: 0,
            backgroundColor: isDarkMode ? colors.backgroundColors.outline.dark : colors.backgroundColors.outline.light,
            opacity: 0, // Start hidden, will be controlled by animation
          },
          searchHeaderAnimatedStyle
        ]}
        testID="sticky-search-header"
      >
        <Surface
          elevation={0}
          style={{
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
            paddingHorizontal: 16,
            paddingVertical: 12,
            elevation: 0, // Explicitly set elevation to 0 to remove shadow
          }}
        >
          <StickySearchHeader
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            onFilterPress={handleFilterPress}
            onSortPress={handleSortPress}
            projectTypes={PROJECT_TYPES}
            selectedProjectType={selectedProjectType}
            onProjectTypeChange={setSelectedProjectType}
            countOptions={COUNT_OPTIONS}
            selectedCount={selectedCount}
            onCountChange={setSelectedCount}
            scrollY={scrollY}
            stickyThreshold={headerHeight}
            testID="top-search-header"
            isTopHeader={true}
          />
        </Surface>
      </Animated.View>

      {/* Main Content */}
      <Animated.ScrollView
        contentContainerStyle={{ paddingBottom: 20 }}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      >
        {/* Greeting and City Selector */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingHorizontal: 16,
          paddingTop: 8,
          marginBottom: 12
        }}>
          {/* Greeting Text */}
          <View>
            <NewRTLText
              style={{
                fontSize: 24,
                fontWeight: '600',
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                textAlign: isRTL ? 'right' : 'left',
              }}
            >
              {getGreeting()},
            </NewRTLText>
            <NewRTLText
              style={{
                fontSize: 24,
                fontWeight: '400',
                color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
                textAlign: isRTL ? 'right' : 'left',
              }}
            >
              {userName || 'User'}
            </NewRTLText>
          </View>

          {/* City Selector Pill */}
          <TouchableOpacity
            onPress={handleCityPress}
            style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              paddingHorizontal: 12,
              paddingVertical: 8,
              borderRadius: 16,
              borderWidth: 1,
              borderColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[300],
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
            }}
            testID="city-selector"
          >
            <Feather name="map-pin" size={16} color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600]} />
            <Text
              style={{
                marginLeft: isRTL ? 0 : 6,
                marginRight: isRTL ? 6 : 0,
                color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
                fontSize: 14,
                fontWeight: '500',
              }}
            >
              {isRTL ? selectedCity.nameAr : selectedCity.nameEn}
            </Text>
            <Feather name="chevron-down" size={16} color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600]} />
          </TouchableOpacity>
        </View>

        {/* Marketing Carousel */}
        <View style={{ paddingHorizontal: 16 }}>
          <MarketingCarousel
            banners={MARKETING_BANNERS}
            onBannerPress={handleBannerPress}
          />
        </View>

        {/* Search and Filter Strip */}
        <View style={{ paddingHorizontal: 16 }}>
          <StickySearchHeader
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            onFilterPress={handleFilterPress}
            onSortPress={handleSortPress}
            projectTypes={PROJECT_TYPES}
            selectedProjectType={selectedProjectType}
            onProjectTypeChange={setSelectedProjectType}
            countOptions={COUNT_OPTIONS}
            selectedCount={selectedCount}
            onCountChange={setSelectedCount}
            scrollY={scrollY}
            stickyThreshold={headerHeight}
            testID="content-search-header"
            isTopHeader={false}
          />
        </View>

        {/* Dumpster Groups Grid */}
        <View style={{ paddingHorizontal: 16, marginTop: 16 }}>
          {isLoadingDumpsters ? (
            <View style={{ paddingVertical: 40, alignItems: 'center' }}>
              <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
            </View>
          ) : dumpsterGroups && dumpsterGroups.length > 0 ? (
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
              {dumpsterGroups.map((group) => (
                <View key={group.sizeId} style={{ width: screenWidth / 2 - 20, marginBottom: 16 }}>
                  {renderDumpsterGroupTile({ item: group })}
                </View>
              ))}
            </View>
          ) : (
            <View style={{ paddingVertical: 40, alignItems: 'center' }}>
              <NewRTLText
                style={{
                  color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                  textAlign: 'center',
                }}
              >
                {safeTranslate(t, 'home.noDumpsterGroups', 'No dumpster sizes available')}
              </NewRTLText>
            </View>
          )}
        </View>
      </Animated.ScrollView>

      {/* City Bottom Sheet */}
      <CityBottomSheet
        cities={SAUDI_CITIES}
        selectedCity={selectedCity}
        onSelectCity={setSelectedCity}
        sheetRef={cityBottomSheetRef}
      />

      {/* Filter Bottom Sheet */}
      <FilterBottomSheet
        sheetRef={filterBottomSheetRef}
        selectedWasteTypes={selectedWasteTypes}
        onWasteTypeChange={handleWasteTypeChange}
        selectedProjectType={selectedProjectType}
        onProjectTypeChange={setSelectedProjectType}
        selectedCount={selectedCount}
        onCountChange={setSelectedCount}
        onApplyFilters={handleApplyFilters}
        onResetFilters={handleResetFilters}
      />

      {/* Sort Bottom Sheet */}
      <SortBottomSheet
        sheetRef={sortBottomSheetRef}
        selectedSortOption={selectedSortOption}
        onSortOptionChange={handleSortOptionChange}
      />

      {/* Floating Action Button */}
      <View style={{
        position: 'absolute',
        bottom: 40,
        left: 20,
        right: 20,
        alignItems: 'center',
        width: '90%'
      }}>
        <Pressable
          onPress={() => router.push('/dumpster-selection')}
          style={{
            width: '100%',
            backgroundColor: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500],
            paddingVertical: 15,
            paddingHorizontal: 25,
            borderRadius: 100,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3
          }}
          testID="fab-button"
        >
          <View style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%'
          }}>
            <View style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: isDarkMode ? colors.brandColors.primary[100] : colors.brandColors.primary[900],
              justifyContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 4
            }}>
              <Feather name="mic" size={20} color={isDarkMode ? colors.textColors.light : colors.textColors.dark} />
            </View>
            <NewRTLText style={{
              flex: 1,
              fontSize: 16,
              color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[700],
              fontWeight: '500',
              marginLeft: isRTL ? 0 : 12,
              marginRight: isRTL ? 12 : 0,
              textAlign: isRTL ? 'right' : 'left'
            }}>
              {safeTranslate(t, 'home.inputPrompt', 'Tap to type')}
            </NewRTLText>
          </View>
        </Pressable>
      </View>
    </StyledSafeAreaView>
  );
}
