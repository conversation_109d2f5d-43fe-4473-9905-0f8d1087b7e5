import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';
import { resetAppState } from '@/utils/resetAppState';

export default function DebugScreen() {
  const router = useRouter();
  const [storageItems, setStorageItems] = React.useState<{[key: string]: string}>({});
  const [sessionInfo, setSessionInfo] = React.useState<any>(null);

  async function loadDebugInfo() {
    // Get all AsyncStorage keys
    const keys = await AsyncStorage.getAllKeys();
    const items: {[key: string]: string} = {};
    
    for (const key of keys) {
      const value = await AsyncStorage.getItem(key);
      items[key] = value || 'null';
    }
    
    setStorageItems(items);
    
    // Get session info
    const { data } = await supabase.auth.getSession();
    setSessionInfo(data.session);
  }

  async function handleReset() {
    await resetAppState();
    await loadDebugInfo();
  }

  React.useEffect(() => {
    loadDebugInfo();
  }, []);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Debug Information</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Session Info</Text>
        <Text>{sessionInfo ? 'User is logged in' : 'No session'}</Text>
        {sessionInfo && (
          <Text>User ID: {sessionInfo.user.id}</Text>
        )}
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>AsyncStorage Items</Text>
        {Object.entries(storageItems).map(([key, value]) => (
          <View key={key} style={styles.item}>
            <Text style={styles.itemKey}>{key}</Text>
            <Text style={styles.itemValue}>{value}</Text>
          </View>
        ))}
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={handleReset}>
          <Text style={styles.buttonText}>Reset App State</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => router.replace('/(auth)/language-select')}
        >
          <Text style={styles.buttonText}>Go to Language Select</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button}
          onPress={() => router.replace('/')}
        >
          <Text style={styles.buttonText}>Go to Home</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    marginTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  item: {
    marginBottom: 5,
  },
  itemKey: {
    fontWeight: 'bold',
  },
  itemValue: {
    fontSize: 12,
  },
  buttonContainer: {
    marginTop: 20,
  },
  button: {
    backgroundColor: '#3b82f6',
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
}); 