import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Redirect } from 'expo-router';
import OnboardingScreen from './onboarding/OnboardingScreen';
import HomeScreen from './HomeScreen';
import HomeScreenV2 from './home-v2';
import HomeV4Screen from './home-v4';
import HomeV5Screen from './home-v5';

const ONBOARDING_KEY = '@app:onboarding_completed';

export default function AppEntry() {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<boolean | null>(null);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      const status = await AsyncStorage.getItem(ONBOARDING_KEY);
      setHasCompletedOnboarding(status === 'true');
    };
    checkOnboardingStatus();
  }, []);

  const handleOnboardingComplete = async () => {
    await AsyncStorage.setItem(ONBOARDING_KEY, 'true');
    setHasCompletedOnboarding(true);
  };

  if (hasCompletedOnboarding === null) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!hasCompletedOnboarding) {
    return <OnboardingScreen onComplete={handleOnboardingComplete} />;
  }

  // Using the new HomeV5Screen with improved touch handling
  // To test other screens, uncomment one of the lines below:
  // return <Redirect href="/discover-v3" />;
  // return <HomeScreen />;
  // return <HomeScreenV2 />;
   return <HomeV4Screen />;
 // return <HomeV5Screen />;

}