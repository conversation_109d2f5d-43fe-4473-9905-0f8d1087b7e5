import 'react-native-get-random-values';
import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions, FlatList, KeyboardAvoidingView, Platform, Alert, SafeAreaView, ScrollView, TouchableOpacity, TextInput, I18nManager, NativeModules } from 'react-native';
import { Button, Text, TextInput as PaperTextInput, List, FAB, ActivityIndicator, Snackbar, IconButton, SegmentedButtons, Switch } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import MapView, { Marker, MapPressEvent, PROVIDER_DEFAULT } from 'react-native-maps';
// import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
// import { GOOGLE_MAPS_API_KEY } from '@env';
import { useRouter } from 'expo-router';
import { useAddresses, AddressInput } from '@/hooks/useAddresses';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { NewRTLView, NewRTLText, useRTLContext, NewRTLTextInput } from '@/components/rtl/new-index';
import { useTranslation } from 'react-i18next';

const { MapKitSearch } = NativeModules;

interface LocationCoordinates {
  latitude: number;
  longitude: number;
  address?: string;
}

interface AddressData {
  name: string;
  type: 'home' | 'office' | 'other';
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  is_default: boolean;
}

const ADDRESS_TYPES = [
  { value: 'home', label: 'Home', icon: 'home' },
  { value: 'office', label: 'Office', icon: 'briefcase' },
  { value: 'other', label: 'Other', icon: 'map-marker' },
] as const;

interface SearchResult {
  title: string;
  subtitle: string;
  latitude: number;
  longitude: number;
  name?: string;
}

interface MapItem {
  name: string;
  placemark: {
    title: string;
    coordinate: {
      latitude: number;
      longitude: number;
    };
  };
}

interface AddressSearchInputProps {
  onLocationSelect: (location: LocationCoordinates) => void;
  value: string;
  onChangeText: (text: string) => void;
}

const AddressSearchInput: React.FC<AddressSearchInputProps> = ({ onLocationSelect, value, onChangeText }) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [searchText, setSearchText] = useState(value);
  const [displayName, setDisplayName] = useState('');

  useEffect(() => {
    setSearchText(value);
  }, [value]);

  const handleSearch = async (text: string) => {
    setSearchText(text);
    setDisplayName(text);
    onChangeText(text);
    
    if (Platform.OS === 'ios' && text.length > 2) {
      try {
        const results = await MapKitSearch.search(text);
        console.log('Search results:', results);
        const transformedResults = results.map((result: any) => ({
          latitude: result.latitude,
          longitude: result.longitude,
          name: result.name || '',
          title: result.title || result.name || '',
          subtitle: result.subtitle || ''
        }));
        console.log('Transformed results:', transformedResults);
        setSearchResults(transformedResults);
        setShowResults(true);
      } catch (error) {
        console.error('Search error:', error);
        setShowResults(false);
      }
    } else if (text.length <= 2) {
      setShowResults(false);
      setSearchResults([]);
    }
  };

  const handleSelectLocation = (result: SearchResult) => {
    const locationName = result.name || result.title || '';
    console.log('Selected location:', result);
    
    const location = {
      latitude: result.latitude,
      longitude: result.longitude,
      address: locationName
    };
    
    setDisplayName(locationName);
    setSearchText(locationName);
    onLocationSelect(location);
    setShowResults(false);
    setSearchResults([]);
  };

  const clearSearch = () => {
    setSearchText('');
    setDisplayName('');
    setShowResults(false);
    setSearchResults([]);
    handleSearch('');
  };

  return (
    <View style={{ zIndex: 999 }}>
      <View style={[AddressesStyles.searchInputContainer, { 
        backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 4,
        flexDirection: isRTL ? 'row-reverse' : 'row'
      }]}>
        <Ionicons 
          name="search" 
          size={20} 
          color={isDarkMode ? colors.textColors.dark : colors.textColors.light} 
          style={[AddressesStyles.searchIcon, { marginRight: isRTL ? 0 : 10, marginLeft: isRTL ? 10 : 0 }]}
        />
        <TextInput
          style={[
            AddressesStyles.searchInput,
            { color: isDarkMode ? colors.textColors.dark : colors.textColors.light },
            isRTL && { textAlign: 'right' }
          ]}
          placeholder={t('addresses.searchPlaceholder') || "Search the map"}

          placeholderTextColor={(isDarkMode ? colors.textColors.dark : colors.textColors.light) + '80'}
          value={displayName || searchText}
          onChangeText={handleSearch}
          returnKeyType="search"
        />
        {(displayName || searchText).length > 0 && (
          <TouchableOpacity
            onPress={clearSearch}
            
          >
            <Ionicons 
              name="close-circle" 
              size={20} 
              color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} 
            />
          </TouchableOpacity>
        )}
      </View>
      
      {showResults && searchResults.length > 0 && (
        <ScrollView 
          style={[
            AddressesStyles.searchResults,
            { 
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
              top: 48,
              maxHeight: 200,
              borderWidth: 1,
              borderColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
              borderRadius: 8
            }
          ]}
          keyboardShouldPersistTaps="handled"
          nestedScrollEnabled={true}
        >
          {searchResults.map((result, index) => (
            <TouchableOpacity
              key={index}
              style={[
                AddressesStyles.searchResultItem,
                { paddingVertical: 12, paddingHorizontal: 16 }
              ]}
              onPress={() => handleSelectLocation(result)}
            >
              <View style={AddressesStyles.resultIconContainer}>
                <View style={[AddressesStyles.resultIcon, { backgroundColor: '#00BFA5' }]}>
                  <Ionicons name="location" size={18} color="white" />
                </View>
              </View>
              <View style={[AddressesStyles.resultTextContainer, { flex: 1, marginLeft: 12 }]}>
                <Text 
                  style={[
                    AddressesStyles.resultTitle, 
                    { 
                      color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                      fontSize: 16,
                      fontWeight: '500',
                      marginBottom: 4
                    }
                  ]}
                  numberOfLines={1}
                >
                  {result.name || result.title}
                </Text>
                <Text 
                  style={[
                    AddressesStyles.resultSubtitle, 
                    { 
                      color: isDarkMode ? colors.textColors.dark + '80' : colors.textColors.light + '80',
                      fontSize: 14
                    }
                  ]}
                  numberOfLines={1}
                >
                  {result.subtitle}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
    </View>
  );
};

export default function AddressesScreen() {
  const router = useRouter();
  const { t, i18n } = useTranslation();
  const { session } = useAuth();
  const {
    addresses,
    isLoading,
    error: addressesError,
    createAddress,
    updateAddress,
    deleteAddress,
    isCreating,
    isDeleting
  } = useAddresses();

  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<LocationCoordinates | null>(null);
  const [addressData, setAddressData] = useState<AddressData>({
    name: '',
    type: 'home',
    streetAddress: '',
    city: '',
    state: '',
    zipCode: '',
    is_default: false,
  });
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { isRTL } = useRTLContext();
  const { isDarkMode } = useTheme();
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const defaultLocation = {
    latitude: 37.7749,  // Default to US center point if location not available
    longitude: -122.4194,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };
  const mapRef = useRef<MapView>(null);

  useEffect(() => {
    if (addressesError) {
      setErrorMsg('Failed to load addresses');
    }
  }, [addressesError]);

  const requestLocationPermission = async () => {
    setIsLoadingLocation(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg('Permission to access location was denied');
        return false;
      }
      return true;
    } catch (error) {
      setErrorMsg('Error requesting location permission');
      return false;
    } finally {
      setIsLoadingLocation(false);
    }
  };

  const getCurrentLocation = async () => {
    setIsLoadingLocation(true);
    try {
      const hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        setErrorMsg('Location permission denied');
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const newLocation = {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
      };

      setLocation(currentLocation);
      setSelectedLocation(newLocation);
      
      // Immediately get address for the location
      reverseGeocode(newLocation.latitude, newLocation.longitude);

      // Animate map to current location
      if (mapRef.current) {
        mapRef.current.animateToRegion({
          ...newLocation,
          latitudeDelta: 0.005,
          longitudeDelta: 0.005,
        }, 1000);
      }
    } catch (error) {
      console.error('Error getting location:', error);
      setErrorMsg('Error getting current location');
      setSelectedLocation({
        latitude: defaultLocation.latitude,
        longitude: defaultLocation.longitude,
      });
    } finally {
      setIsLoadingLocation(false);
    }
  };

  const handleCurrentLocation = async () => {
    setIsLoadingLocation(true);
    try {
      const hasPermission = await requestLocationPermission();
      if (!hasPermission) return;

      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const newRegion = {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        latitudeDelta: 0.005, // Zoom in more for current location
        longitudeDelta: 0.005,
      };

      // Animate to the new location
      mapRef.current?.animateToRegion(newRegion, 1000);

      setLocation(currentLocation);
      setSelectedLocation({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
      });
    } catch (error) {
      setErrorMsg('Error getting current location');
    } finally {
      setIsLoadingLocation(false);
    }
  };

  useEffect(() => {
    if (isAddingAddress) {
      getCurrentLocation();
    }
  }, [isAddingAddress]);

  const handleMapPress = (event: MapPressEvent) => {
    if (!event.nativeEvent.coordinate) {
      console.error('No coordinate in map press event');
      return;
    }

    const { latitude, longitude } = event.nativeEvent.coordinate;
    
    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      console.error('Invalid coordinates:', { latitude, longitude });
      return;
    }

    const newLocation = {
      latitude,
      longitude,
      address: ''
    };

    setSelectedLocation(newLocation);
    reverseGeocode(latitude, longitude);
  };

  const clearError = () => {
    setErrorMsg(null);
  };

  const handleDeleteAddress = (id: string) => {
    Alert.alert(
      "Delete Address",
      "Are you sure you want to delete this address?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            deleteAddress(id);
            setSuccessMessage('Address deleted successfully');
          }
        }
      ]
    );
  };

  const handleAddAddress = async () => {
    clearError();
    if (!selectedLocation) {
      setErrorMsg('Please select a location on the map');
      return;
    }
    if (!addressData.streetAddress || !addressData.city || !addressData.state || !addressData.zipCode) {
      setErrorMsg('Please fill in all address fields');
      return;
    }

    if (!session?.user) {
      setErrorMsg('You must be logged in to save addresses');
      return;
    }

    // Check if trying to set as default address
    if (addressData.is_default && addresses) {
      const existingDefault = addresses.find(addr => addr.is_default);
      if (existingDefault) {
        // Show confirmation dialog
        Alert.alert(
          "Default Address Exists",
          `Already have default address "${existingDefault.name || existingDefault.street_address}". Are you sure you want to set this new address as default? You can only have one default address.`,
          [
            {
              text: "Cancel",
              style: "cancel",
              onPress: () => {
                // Set is_default to false and proceed with save
                setAddressData(prev => ({ ...prev, is_default: false }));
              }
            },
            {
              text: "Yes",
              onPress: async () => {
                try {
                  // Update the old default address first
                  await updateAddress({
                    addressId: existingDefault.id,
                    addressData: { is_default: false }
                  });
                  
                  // Then create the new address
                  const newAddress: AddressInput = {
                    name: addressData.name,
                    type: addressData.type,
                    street_address: addressData.streetAddress,
                    city: addressData.city,
                    state: addressData.state,
                    zip_code: addressData.zipCode,
                    latitude: selectedLocation.latitude,
                    longitude: selectedLocation.longitude,
                    is_default: true,
                  };
                  
                  createAddress(newAddress);
                  setSuccessMessage('Address saved successfully');
                  resetForm();
                  
                } catch (error) {
                  console.error('Error updating addresses:', error);
                  setErrorMsg('Failed to update addresses');
                }
              }
            }
          ]
        );
        return;
      }
    }

    // If not setting as default or no existing default, proceed normally
    const newAddress: AddressInput = {
      name: addressData.name,
      type: addressData.type,
      street_address: addressData.streetAddress,
      city: addressData.city,
      state: addressData.state,
      zip_code: addressData.zipCode,
      latitude: selectedLocation.latitude,
      longitude: selectedLocation.longitude,
      is_default: addressData.is_default,
    };

    createAddress(newAddress);
    setSuccessMessage('Address saved successfully');
    resetForm();
  };

  const resetForm = () => {
    setAddressData({
      name: '',
      type: 'home',
      streetAddress: '',
      city: '',
      state: '',
      zipCode: '',
      is_default: false,
    });
    setSelectedLocation(null);
    setIsAddingAddress(false);
  };

  const handleRegionChange = (region: any) => {
    if (selectedLocation) {
      setSelectedLocation({
        ...selectedLocation,
        latitude: region.latitude,
        longitude: region.longitude,
      });
    }
  };

  const handleLocationSelect = (location: LocationCoordinates) => {
    if (!location || typeof location.latitude !== 'number' || typeof location.longitude !== 'number') {
      console.error('Invalid location data:', location);
      return;
    }

    const newLocation = {
      latitude: location.latitude,
      longitude: location.longitude,
      address: location.address || ''
    };

    setSelectedLocation(newLocation);
    reverseGeocode(location.latitude, location.longitude);

    if (mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: location.latitude,
        longitude: location.longitude,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      }, 1000);
    }
  };

  const reverseGeocode = async (latitude: number, longitude: number) => {
    try {
      const addressDetails = await Location.reverseGeocodeAsync({
        latitude,
        longitude
      });
      
      if (addressDetails && addressDetails.length > 0) {
        const address = addressDetails[0];
        
        // Auto-fill the hidden address fields silently
        setAddressData({
          ...addressData,
          streetAddress: [address.streetNumber, address.street].filter(Boolean).join(' '),
          city: address.city || '',
          state: address.region || '',
          zipCode: address.postalCode || '',
        });
        
        // Update the selected location with a simplified address display
        if (selectedLocation) {
          const displayAddress = [
            address.street,
            address.city,
            address.region
          ].filter(Boolean).join(', ');
          
          setSelectedLocation({
            ...selectedLocation,
            address: displayAddress // Simplified address for display
          });
        }
      }
    } catch (error) {
      console.error('Error getting address details:', error);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (isAddingAddress) {
    return (
      <SafeAreaView style={[AddressesStyles.container, { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }]}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={AddressesStyles.mainContainer}
          keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
        >
          <View style={[AddressesStyles.header, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <Text variant="headlineMedium" style={AddressesStyles.title}>{t('addresses.addNew')}</Text>
            <IconButton
              icon='close'
              size={24}
              onPress={() => setIsAddingAddress(false)}
              style={{ marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }}
            />
          </View>

          {errorMsg ? (
            <Text style={{ color: 'red', marginBottom: 10 }}>{errorMsg}</Text>
          ) : null}

          <View style={AddressesStyles.contentContainer}>
            <View style={AddressesStyles.mapContainer}>
              {isLoadingLocation ? (
                <View style={[AddressesStyles.map, AddressesStyles.centerContainer]}>
                  <ActivityIndicator size="large" color="#00BFA5" />
                </View>
              ) : (
                <View style={AddressesStyles.mapWrapper}>
                  <MapView
                    ref={mapRef}
                    provider={PROVIDER_DEFAULT}
                    style={AddressesStyles.map}
                    initialRegion={{
                      latitude: selectedLocation?.latitude || defaultLocation.latitude,
                      longitude: selectedLocation?.longitude || defaultLocation.longitude,
                      latitudeDelta: defaultLocation.latitudeDelta,
                      longitudeDelta: defaultLocation.longitudeDelta,
                    }}
                    showsUserLocation
                    showsMyLocationButton={false}
                    onRegionChangeComplete={handleRegionChange}
                    onPress={handleMapPress}
                  >
                    {selectedLocation && (
                      <Marker
                        coordinate={{
                          latitude: selectedLocation.latitude,
                          longitude: selectedLocation.longitude,
                        }}
                        title={selectedLocation.address || t('addresses.selectedLocation')}
                        description=""
                        pinColor="#00BFA5"
                      >
                        <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                          <View style={{ 
                            width: 24, 
                            height: 24, 
                            borderRadius: 12, 
                            backgroundColor: 'rgba(0, 191, 165, 0.3)', 
                            alignItems: 'center', 
                            justifyContent: 'center' 
                          }}>
                            <View style={{ 
                              width: 12, 
                              height: 12, 
                              borderRadius: 6, 
                              backgroundColor: '#00BFA5', 
                              borderWidth: 2, 
                              borderColor: 'white' 
                            }} />
                          </View>
                        </View>
                      </Marker>
                    )}
                  </MapView>
                  
                  <View style={AddressesStyles.mapSearchContainer}>
                    <AddressSearchInput
                      onLocationSelect={handleLocationSelect}
                      value={selectedLocation?.address || ''}
                      onChangeText={(text: string) => {
                        if (selectedLocation) {
                          setSelectedLocation({
                            ...selectedLocation,
                            address: text
                          });
                        }
                      }}
                    />
                  </View>
                  
                  <View style={AddressesStyles.mapButtonsContainer}>
                    <IconButton
                      icon="crosshairs-gps"
                      mode="contained"
                      containerColor={isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light}
                      size={24}
                      onPress={handleCurrentLocation}
                      style={AddressesStyles.currentLocationButton}
                      disabled={isLoadingLocation}
                    />
                  </View>
                </View>
              )}
              
            </View>
            
          </View>

          <View 
            style={AddressesStyles.scrollView} 
            
            
          >
            <View style={[AddressesStyles.inputGroup, { marginTop: 0, marginBottom: 8 }]}>
                
                <NewRTLTextInput
                  style={[
                    AddressesStyles.input, 
                    { color: isDarkMode ? colors.textColors.dark : colors.textColors.light },
                    {borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light},
                    {backgroundColor: isDarkMode ? colors.surfaceColors.onPrimaryContainer.dark : colors.surfaceColors.container.light}
                  ]}
                  placeholder={t('addresses.showSelectedLocation')}
                  placeholderTextColor={(isDarkMode ? colors.textColors.dark : colors.textColors.light) + '80'}
                  value={addressData.streetAddress}
                  editable={false}
                />
              </View>
            <View style={AddressesStyles.formContainer}>

              <View style={AddressesStyles.inputGroup}>
                <Text style={[AddressesStyles.label, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }]}>{t('addresses.name')}</Text>
                <TextInput
                  style={[
                    AddressesStyles.input,
                    { color: isDarkMode ? colors.textColors.dark : colors.textColors.light },
                    {backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light},
                    {borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light},
                    isRTL && { textAlign: 'right' }
                  ]}
                  placeholder={t('addresses.namePlaceholder')}
                  placeholderTextColor={(isDarkMode ? colors.textColors.dark : colors.textColors.light) + '80'}
                  value={addressData.name}
                  onChangeText={(text) => setAddressData({ ...addressData, name: text })}
                />
              </View>

              

              <View style={AddressesStyles.inputGroup}>
                <Text style={[AddressesStyles.label, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light, textAlign: isRTL ? 'right' : 'left' }]}>{t('addresses.type')}</Text>
                <SegmentedButtons
                  value={addressData.type}
                  onValueChange={(value) =>
                    setAddressData({ ...addressData, type: value as 'home' | 'office' | 'other' })
                  }
                  buttons={[
                    {
                      value: 'home',
                      label: t('addresses.home'),
                      icon: 'home',
                      style: { backgroundColor: addressData.type === 'home' ? '#00BFA5' : 'transparent', borderTopLeftRadius: isRTL ? 0 : 8, borderBottomLeftRadius: isRTL ? 0 : 8, transform: [{scaleX: isRTL ? -1 : 1}] },
                      checkedColor: 'white',
                      labelStyle: { textAlign: isRTL ? 'right' : 'left', transform: [{scaleX: isRTL ? -1 : 1}] },
                      uncheckedColor: '#00BFA5'
                    },
                    {
                      value: 'office',
                      label: t('addresses.office'),
                      icon: 'briefcase',
                      style: { backgroundColor: addressData.type === 'office' ? '#00BFA5' : 'transparent', transform: [{scaleX: isRTL ? -1 : 1}] },
                      checkedColor: 'white',
                      labelStyle: { textAlign: isRTL ? 'right' : 'left', transform: [{scaleX: isRTL ? -1 : 1}] },
                      uncheckedColor: '#00BFA5'
                    },
                    {
                      value: 'other',
                      label: t('addresses.other'),
                      icon: 'map-marker',
                      style: { backgroundColor: addressData.type === 'other' ? '#00BFA5' : 'transparent', borderTopRightRadius: isRTL ? 0 : 8, borderBottomRightRadius: isRTL ? 0 : 8, transform: [{scaleX: isRTL ? -1 : 1}] },
                      checkedColor: 'white',
                      labelStyle: { textAlign: isRTL ? 'right' : 'left', transform: [{scaleX: isRTL ? -1 : 1}] },
                      uncheckedColor: '#00BFA5'
                    }
                  ]}
                  style={[AddressesStyles.segmentedButtons, { borderWidth: 1, borderColor: colors.brandColors.primary[500] }, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}
                />
              </View>

              <View style={[AddressesStyles.switchContainer, {flexDirection: isRTL ? 'row-reverse' : 'row', marginTop: -8}]}>
                <Text style={[AddressesStyles.switchLabel, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>{t('addresses.setAsDefault')}</Text>
                <Switch
                  value={addressData.is_default}
                  onValueChange={(value) => setAddressData({ ...addressData, is_default: value })}
                  color="#00BFA5"
                  style={{transform: [{scaleX: isRTL ? -1 : 1}]}}
                />
              </View>
            </View>
          </View>

          <View style={AddressesStyles.footer}>
            <Button
              mode="contained"
              onPress={handleAddAddress}
              style={AddressesStyles.saveButton}
              loading={isCreating}
              disabled={isCreating}
              buttonColor="#00BFA5"
            >
              {t('addresses.save')}
            </Button>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }]}>
      <View style={{flex: 1, paddingHorizontal: 16}}>
      <View style={[styles.header, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Text style={styles.title}>My Addresses</Text>
        <IconButton
          icon={isRTL ? "arrow-left" : "arrow-right"}
          size={24}
          onPress={() => router.back()}
          style={{marginLeft: isRTL ? 0 : 'auto', marginRight: isRTL ? 'auto' : 0}}
        />
      </View>
      
      {addresses && addresses.length > 0 ? (
        <ScrollView>
          {[...addresses]
            .sort((a, b) => (b.is_default ? 1 : -1))
            .map((item) => (
              <NewRTLView 
                key={item.id}
                style={[
                  styles.listItem, 
                  {flexDirection: isRTL ? 'row-reverse' : 'row'},
                  {backgroundColor: isDarkMode ? item.is_default ? colors.brandColors.primary[900] : colors.surfaceColors.container.dark : item.is_default ? colors.brandColors.primary[100] : colors.surfaceColors.container.light},
                  {marginBottom: 12, padding: 12, borderWidth: 1},
                  {borderColor: isDarkMode ? item.is_default ? colors.brandColors.primary[500] : colors.surfaceColors.outline.dark : item.is_default ? colors.brandColors.primary[500] : colors.surfaceColors.outline.light}
                ]}
              >
                <IconButton
                  icon={ADDRESS_TYPES.find(type => type.value === item.type)?.icon || 'map-marker'} 
                  size={24}
                  style={{marginHorizontal: 8, marginLeft: isRTL ? 0 : 'auto', marginRight: isRTL ? 'auto' : 0}}
                />
                
                <NewRTLView style={{flex: 1, justifyContent: 'center', flexDirection: 'column'}}>
                
                  <NewRTLText style={{fontSize: 18, fontWeight: '500', color: isDarkMode ? colors.textColors.dark : colors.textColors.light, flexDirection: 'row', alignItems: 'center', marginBottom: 4}}>
                    {item.name || item.street_address} 
                    {item.is_default && (
                        <View style={{
                          backgroundColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[100],
                          paddingHorizontal: 8,
                          paddingVertical: 3,
                          borderRadius: 12,
                          
                          marginLeft: isRTL ? 0 : 8,
                          marginRight: isRTL ? 8 : 0,
                        }}>
                          <NewRTLText style={{
                            fontSize: 12,
                            color: isDarkMode ? colors.brandColors.primary[200] : colors.brandColors.primary[700]
                          }}>
                            {t('profile.addresses.default', 'Default')}
                          </NewRTLText>
                        </View>
                      )}
                  </NewRTLText>
                  
                  <NewRTLText style={{fontSize: 14, color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}}>
                    {`${item.street_address}, ${item.city}, ${item.state} ${item.zip_code}`}
                  </NewRTLText>
                </NewRTLView>

                <IconButton
                  icon="delete"
                  size={24} 
                  onPress={() => handleDeleteAddress(item.id)}
                  disabled={isDeleting}
                  iconColor={colors.brandColors.danger[500]}
                  style={{marginHorizontal: 8, marginLeft: isRTL ? 'auto' : 0, marginRight: isRTL ? 0 : 'auto'}}
                />
              </NewRTLView>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyContainer}>
          <Text>You don't have any saved addresses yet.</Text>
        </View>
      )}

      <FAB
        style={[styles.fab, {backgroundColor: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500]}]}
        icon="plus"
        color={isDarkMode ? colors.textColors.light : colors.textColors.dark}
        onPress={() => setIsAddingAddress(true)}
      />

      <Snackbar
        visible={!!successMessage}
        onDismiss={() => setSuccessMessage(null)}
        duration={3000}
      >
        {successMessage}
      </Snackbar>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,

  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  list: {
    flex: 1,
  },
  listItem: {
    borderRadius: 12,
    
    alignItems: 'center',
    justifyContent: 'space-between',
    
  },
  defaultListItem: {
    backgroundColor: colors.brandColors.primary[900],
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  errorText: {
    color: 'red',
    marginBottom: 10,
  },
});

const AddressesStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    //paddingHorizontal: 16,
    paddingTop: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  contentContainer: {
    zIndex: 1,
    marginBottom: 0,
  },
  scrollView: {
    flex: 1,
    zIndex: 0,
    paddingHorizontal: 16,
  },
  // scrollViewContent: {
  //   paddingBottom: 16,
  // },
  searchContainer: {
    marginBottom: 16,
    zIndex: 2,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 44,
    borderRadius: 22,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchResults: {
    position: 'absolute',
    left: 0,
    right: 0,
    borderRadius: 12,
    zIndex: 9999,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    overflow: 'hidden',
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  resultIconContainer: {
    marginRight: 12,
  },
  resultIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultTextContainer: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  resultSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  formContainer: {
    paddingTop: 8,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'right',
  },
  input: {
    borderRadius: 8,
    borderWidth: 1,
    
    
    height: 44,
    paddingHorizontal: 12,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
  },
  mapContainer: {
    height: 300,
    marginBottom: 8,
    //borderRadius: 10,
    overflow: 'hidden',
    zIndex: 0,
    position: 'relative',
  },
  map: {
    width: Dimensions.get('window').width,
    height: 300,
  },
  mapWrapper: {
    flex: 1,
    position: 'relative',
  },
  mapSearchContainer: {
    position: 'absolute',
    top: 8,
    left: 16,
    right: 16,
    zIndex: 10,
  },
  mapButtonsContainer: {
    position: 'absolute',
    right: 16,
    bottom: 20,
    zIndex: 10,
  },
  currentLocationButton: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  footer: {
    paddingTop: 8,
    paddingBottom: Platform.OS === 'ios' ? 16 : 24,
    paddingHorizontal: 16,
  },
  segmentedButtons: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  saveButton: {
    borderRadius: 8,
    //height: 50,
  },
  errorText: {
    color: 'red',
    marginBottom: 10,
  },
});