import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Image, StyleSheet, ActivityIndicator, Dimensions, TouchableOpacity, StatusBar as RNStatusBar, LogBox } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Button, Divider, IconButton } from 'react-native-paper';
import { useDumpster, useWasteTypes } from '@/hooks/useDumpsters';
import { Dumpster, DumpsterSize } from '@/types/dumpster';
import { Dumpster as NewDumpster } from '@/types/new/dumpster';
import { convertToNewDumpster, getSafeImageUrl } from '@/utils/dumpsterAdapter';
import { AntDesign, MaterialCommunityIcons, FontAwesome5, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { useRTLContext } from '@/components/rtl/RTLContext';
import NewRTLText from '@/components/rtl/NewRTLText';

LogBox.ignoreAllLogs(true);

const { width } = Dimensions.get('window');

// Error boundary component
function ErrorBoundary({ children, fallback = null }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  if (hasError) {
    return (
      <View style={styles.errorContainer}>
        <NewRTLText style={styles.errorText}>
          Something went wrong {fallback}
        </NewRTLText>
      </View>
    );
  }

  try {
    return <View style={{ flex: 1 }}>{children}</View>;
  } catch (error) {
    console.error('Render error in ErrorBoundary:', error);
    setHasError(true);
    return (
      <View style={styles.errorContainer}>
        <NewRTLText style={styles.errorText}>
          Error rendering content | {fallback}
        </NewRTLText>
      </View>
    );
  }
}

// Function to safely render text content
const SafeText = ({ children, style }: { children: React.ReactNode, style?: any }) => {
  if (children === null || children === undefined) {
    return null;
  }
  return <NewRTLText style={style}>{children}</NewRTLText>;
};

export default function DumpsterDetailScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const dumpsterId = id as string;
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  // State
  const [selectedSize, setSelectedSize] = useState<DumpsterSize | null>(null);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [convertedDumpster, setConvertedDumpster] = useState<NewDumpster | null>(null);
  const [renderError, setRenderError] = useState<string | null>(null);
  const [compatibleWasteTypeNames, setCompatibleWasteTypeNames] = useState<string[]>([]);

  // Fetch dumpster data (returns OLD format)
  const { data: dumpster, isLoading, error } = useDumpster(dumpsterId);
  const { data: wasteTypes } = useWasteTypes();

  // DEBUG: Log raw dumpster data
  useEffect(() => {
    console.log('==== DEBUG [id].tsx ====');
    console.log('Raw dumpster data:', JSON.stringify(dumpster, null, 2));
    console.log('Raw wasteTypes data:', JSON.stringify(wasteTypes, null, 2));
    console.log('dumpsterId:', dumpsterId);
    
    // Debugging size data structure specifically
    if (dumpster) {
      console.log('==== DEBUG Size Data Structure ====');
      console.log('dumpster.size:', dumpster.size);
      console.log('dumpster.sizes:', dumpster.sizes);
      console.log('typeof dumpster.size:', typeof dumpster.size);
      console.log('typeof dumpster.sizes:', typeof dumpster.sizes);
      console.log('Array.isArray(dumpster.sizes):', Array.isArray(dumpster.sizes));
      
      // Handle weird data cases
      if (dumpster.sizes) {
        console.log('dumpster.sizes.length:', dumpster.sizes.length);
        if (dumpster.sizes.length > 0) {
          console.log('First size object structure:', Object.keys(dumpster.sizes[0]));
          console.log('First size object values:', Object.values(dumpster.sizes[0]));
        }
      }
      
      // Log feature data structure
      console.log('==== DEBUG Feature Data Structure ====');
      console.log('dumpster.features:', dumpster.features);
      console.log('dumpster.featureIds:', dumpster.featureIds);
      console.log('typeof dumpster.features:', typeof dumpster.features);
      console.log('Array.isArray(dumpster.features):', Array.isArray(dumpster.features));
      console.log('Array.isArray(dumpster.featureIds):', Array.isArray(dumpster.featureIds));
    }
  }, [dumpster, wasteTypes, dumpsterId]);

  // DEBUG: Add schema update debugging
  useEffect(() => {
    if (dumpster) {
      console.log('==== DEBUG Schema Update ====');
      console.log('Does dumpster follow new schema?');
      console.log('- Has direct size property:', dumpster.size !== undefined);
      console.log('- Has size_id property:', (dumpster as any).size_id !== undefined);
      console.log('- Has featureIds instead of features:', dumpster.featureIds !== undefined);
      console.log('- Has single size instead of sizes array:', Array.isArray((dumpster as any).sizes) === false);
      
      // Check for size properties
      if (dumpster.size) {
        console.log('Size object properties:', Object.keys(dumpster.size));
      }
      
      // Log raw size data without typescript errors
      console.log('Raw size data (ignore typescript errors):');
      const rawDumpster = dumpster as any;
      console.log('- size:', rawDumpster.size);
      console.log('- sizes:', rawDumpster.sizes);
      console.log('- size_id:', rawDumpster.size_id);
      
      // Check waste type data structure
      console.log('==== DEBUG Waste Type Structure ====');
      console.log('- compatibleWasteTypes:', rawDumpster.compatibleWasteTypes);
      console.log('- Is compatibleWasteTypes an array?', Array.isArray(rawDumpster.compatibleWasteTypes));
      if (Array.isArray(rawDumpster.compatibleWasteTypes) && rawDumpster.compatibleWasteTypes.length > 0) {
        console.log('- First waste type:', rawDumpster.compatibleWasteTypes[0]);
        console.log('- Type of first waste type:', typeof rawDumpster.compatibleWasteTypes[0]);
      }
    }
  }, [dumpster]);

  // Use the original dumpster object for properties not in NewDumpster
  const displayRating = dumpster?.rating ?? 4.0;
  const displayReviewCount = dumpster?.reviewCount ?? 0;
  const displayName = dumpster?.name ?? t('Unnamed Dumpster');
  const displayDescription = dumpster?.description ?? '';
  const displayPrice = dumpster?.pricePerDay ?? 0;
  const displayPartnerId = dumpster?.partnerId ?? 'N/A';
  const displaySizes = dumpster?.size ?? [];
  const displayCompatibleWasteTypes = dumpster?.compatibleWasteTypes ?? [];
  const displayFeatures = dumpster?.featureIds ?? [];
  const displayIsAvailable = dumpster?.availability?.isAvailable !== undefined ? dumpster.availability.isAvailable : true;
  const displayNextAvailableDate = dumpster?.availability?.nextAvailableDate;

  // DEBUG: Log display properties
  useEffect(() => {
    console.log('==== DEBUG Display Properties ====');
    console.log('Display rating:', displayRating);
    console.log('Display sizes:', JSON.stringify(displaySizes, null, 2));
    console.log('Display compatibleWasteTypes:', JSON.stringify(displayCompatibleWasteTypes, null, 2));
    console.log('Display features:', JSON.stringify(displayFeatures, null, 2));
    console.log('Display isAvailable:', displayIsAvailable);
    console.log('Display partnerId:', displayPartnerId);
  }, [displayRating, displaySizes, displayCompatibleWasteTypes, displayFeatures, displayIsAvailable, displayPartnerId]);

  // Convert dumpster to new format when data is loaded
  useEffect(() => {
    try {
      if (dumpster) {
        const converted = convertToNewDumpster(dumpster);
        setConvertedDumpster(converted);
        // DEBUG: Log converted dumpster
        console.log('==== DEBUG Converted Dumpster ====');
        console.log('Converted dumpster:', JSON.stringify(converted, null, 2));
      }
    } catch (err) {
      console.error('Error converting dumpster:', err);
      setRenderError('Failed to convert dumpster data');
      // DEBUG: Log conversion error with more details
      console.error('Conversion error details:', err);
      console.error('Original dumpster that failed conversion:', JSON.stringify(dumpster, null, 2));
    }
  }, [dumpster]);

  // Set initial selected size when dumpster data loads
  useEffect(() => {
    if (Array.isArray(displaySizes) && displaySizes.length > 0) {
      const firstSize = displaySizes[0];
      setSelectedSize(firstSize);
      // DEBUG: Log initial selected size
      console.log('==== DEBUG Initial Selected Size ====');
      console.log('Initial selected size:', JSON.stringify(firstSize, null, 2));
    } else {
      // DEBUG: Log when no sizes are available
      console.log('==== DEBUG No Sizes Available ====');
      console.log('displaySizes is empty or undefined');
    }
  }, [displaySizes]);

  // Handle image load errors
  const handleImageError = () => {
    console.error('Error loading image for dumpster');
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  // Render loading state
  if (isLoading) {
    return (
      <LinearGradient
        style={styles.fullScreenCenter}
        colors={['#f5f7fa', '#e8f0fe', '#e1eafc']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <StatusBar style="dark" />
        <ActivityIndicator size="large" color="#3b82f6" />
        <NewRTLText style={styles.loadingText}>
          {t('Loading dumpster details...')}
        </NewRTLText>
      </LinearGradient>
    );
  }

  // If we reach here, dumpster should exist
  if (!dumpster) {
    return (
      <View style={styles.errorScreen}>
        <NewRTLText style={styles.errorTitle}>
          {t('Dumpster not found')}
        </NewRTLText>
        <TouchableOpacity
          style={styles.errorButton}
          onPress={() => router.back()}
        >
          <NewRTLText style={styles.errorButtonText}>
            {t('Go Back')}
          </NewRTLText>
        </TouchableOpacity>
      </View>
    );
  }

  // Render error state if something went wrong during conversion
  if (renderError) {
    return (
      <View style={styles.errorScreen}>
        <NewRTLText style={styles.errorTitle}>
          {renderError}
        </NewRTLText>
        <TouchableOpacity
          style={styles.errorButton}
          onPress={() => router.back()}
        >
          <NewRTLText style={styles.errorButtonText}>
            {t('Go Back')}
          </NewRTLText>
        </TouchableOpacity>
      </View>
    );
  }

  // Use the image URL from the converted dumpster object if available, with safe handling
  const imageUrl = convertedDumpster?.imageUrl ?? getSafeImageUrl(dumpster?.imageUrl);


  // Handlers
  const handleSizeSelect = (size: DumpsterSize) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedSize(size);
  };

  const handleAddToCart = () => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    router.push({
      pathname: '/checkout',
      params: {
        id: dumpster.id,
        partnerId: dumpster.partnerId  // Add partnerId to params
      }
    });
  };

  // Replace the current implementation with this fixed version
  useEffect(() => {
    // Map waste type IDs to names - do NOT use any hooks inside conditionals
    // Commenting out the problematic TypeScript code for now
    /*
    let mappedTypeNames = [];
    
    if (dumpster?.compatibleWasteTypes && wasteTypes) {
      try {
        const wasteTypeMap = {};
        
        // First create a lookup map (avoid conditionals that affect hook order)
        if (Array.isArray(wasteTypes)) {
          wasteTypes.forEach(wt => {
            if (wt && wt.id) {
              wasteTypeMap[wt.id] = wt;
            }
          });
        }
        
        // Then use the map to find waste type names
        if (Array.isArray(dumpster.compatibleWasteTypes)) {
          mappedTypeNames = dumpster.compatibleWasteTypes
            .map(id => wasteTypeMap[id]?.name)
            .filter(Boolean); // Filter out undefined/null values
        }
        
        // Debug logging
        console.log("==== DEBUG Compatible Waste Types ====");
        console.log("Mapped waste types:", mappedTypeNames);
      } catch (error) {
        console.error("Error mapping waste types:", error);
      }
    }
    
    // Always call setCompatibleWasteTypeNames exactly once per render
    setCompatibleWasteTypeNames(mappedTypeNames);
    */

    // Simplified version until we fix TypeScript types
    console.log("==== DEBUG Compatible Waste Types ====");
    console.log("dumpster?.compatibleWasteTypes:", dumpster?.compatibleWasteTypes);
    console.log("wasteTypes:", wasteTypes);

    // Just display the raw waste type IDs for now
    const wasteTypeStrings = Array.isArray(dumpster?.compatibleWasteTypes) 
      ? dumpster.compatibleWasteTypes
          .map(id => String(id))
          .filter(Boolean)
      : [];
      
    setCompatibleWasteTypeNames(wasteTypeStrings);
  }, [dumpster, wasteTypes]);

  // Get the largest size for default selection using displaySizes
  if (!selectedSize && Array.isArray(displaySizes) && displaySizes.length > 0) {
    try {
      // DEBUG: Log size selection data
      console.log('==== DEBUG Size Selection ====');
      console.log('Available sizes for selection:', JSON.stringify(displaySizes, null, 2));
      
      const largestSize = Array.isArray(displaySizes) && displaySizes.length > 0 
        ? displaySizes.reduce((max: DumpsterSize, size: DumpsterSize) => 
            (size.volumeCubicYards ?? 0) > (max.volumeCubicYards ?? 0) ? size : max
          , displaySizes[0])
        : null;
      
      // DEBUG: Log selected largest size
      console.log('Selected largest size:', JSON.stringify(largestSize, null, 2));
      setSelectedSize(largestSize);
    } catch (err) {
      console.error('Error selecting largest size:', err);
      // DEBUG: Log size selection error
      console.error('Size selection error details:', err);
      console.error('displaySizes that caused error:', JSON.stringify(displaySizes, null, 2));
    }
  }

  return (
    <LinearGradient
      style={styles.container}
      colors={[isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light, isDarkMode ? colors.brandColors.secondary[800] : colors.brandColors.secondary[300]]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <RNStatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />

      <Stack.Screen
        options={{
          headerShown: true,
          headerTransparent: true,
          headerTitle: '',
          headerLeft: () => (
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={() => router.back()}
              style={[styles.headerIcon, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}
            />
          ),
          headerRight: () => (
            <IconButton
              icon="share-variant"
              size={24}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              style={[styles.headerIcon, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}
            />
          ),
        }}
      />

      <ScrollView style={styles.scrollView}>
        <View style={[styles.heroImageContainer]}>
           {imageLoading && (
            <View style={styles.imageLoadingContainer}>
              <ActivityIndicator size="large" color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
            </View>
          )}

          <Image
            source={{ uri: imageUrl }}
            style={styles.heroImage}
            resizeMode="cover"
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
            onError={handleImageError}
          />

          {imageError && (
            <View style={styles.imageErrorContainer}>
              <MaterialCommunityIcons name="image-off" size={48} color={isDarkMode ? colors.brandColors.danger[400] : colors.brandColors.danger[500]} />
              <NewRTLText style={styles.imageErrorText}>
                {t('Image not available')}
              </NewRTLText>
            </View>
          )}

          {!displayIsAvailable && (
            <View style={styles.availabilityBadge}>
              <NewRTLText style={styles.availabilityText}>
                {displayNextAvailableDate
                  ? t('Available from {{date}}', { date: new Date(displayNextAvailableDate).toLocaleDateString() })
                  : t('Not Available')}
              </NewRTLText>
            </View>
          )}

          {displayRating ? (
            <View style={[styles.ratingBadge, { backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light }]}>
              <MaterialIcons name="star" size={16} color={isDarkMode ? colors.brandColors.warning[400] : colors.brandColors.warning[500]} />
              <NewRTLText style={[styles.ratingText, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>
                {`${Number(displayRating).toFixed(1)}`}
              </NewRTLText>
              <NewRTLText style={[styles.ratingText, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>
                {` (${displayReviewCount})`}
              </NewRTLText>
            </View>
          ) : null}
        </View>

        {/* --- Restore structure using NewRTLText --- */}
        <ErrorBoundary fallback={
          <View style={styles.errorContainer}>
            <NewRTLText style={styles.errorText}>
              {t('Error displaying dumpster details')}
            </NewRTLText>
          </View>
        }>
          <View style={styles.contentContainer}>
             <View style={styles.headerSection}>
                <NewRTLText style={[styles.dumpsterTitle, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>{displayName}</NewRTLText>
                <View style={styles.priceContainer}>
                  <View style={styles.priceTextContainer}>
                     {isDarkMode ? (<Image
                      source={require('assets/images/currency/Saudi_Riyal_Symbol_primary.png')}
                      style={styles.currencySymbol}
                      resizeMode="contain"
                    />) : (<Image
                      source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                      style={styles.currencySymbol}
                      resizeMode="contain"
                    />)}
                    <NewRTLText style={[styles.priceText, {color: isDarkMode ? colors.brandColors.primary[500] : colors.textColors.light}]}>
                      {`${displayPrice} / ${t('Load')}`}
                    </NewRTLText>
                  </View>
                </View>
                {displayDescription && (
                  <NewRTLText style={[styles.descriptionText, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                    {displayDescription}
                  </NewRTLText>
                )}
                 
             </View>

             <View style={styles.section}>
              <NewRTLText style={[styles.sectionTitle, { color: isDarkMode ? colors.textColors.dark : colors.textColors.light }]}>
                {t('Select Size')}
              </NewRTLText>
              <View style={[styles.sizeOptionsContainer, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
                {Array.isArray(displaySizes) && displaySizes.map((size: DumpsterSize) => (
                  <TouchableOpacity
                    key={size.id}
                    onPress={() => handleSizeSelect(size)}
                    style={[
                      styles.sizeOption,
                      {marginRight: isRTL ? 0 : 12, marginLeft: isRTL ? 12 : 0},
                      selectedSize?.id === size.id ? styles.selectedSizeOption : null,
                      { borderColor: isDarkMode ? colors.brandColors.primary[200] : colors.brandColors.primary[300] },
                      { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }
                    ]}
                  >
                    <NewRTLText
                       style={[styles.sizeOptionTitle, { color: selectedSize?.id === size.id ? isDarkMode ? colors.brandColors.primary[200] : colors.brandColors.primary[300] : isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500] }]}
                    >
                       {size.name ?? t('Standard Size')}
                    </NewRTLText>
                    <NewRTLText
                      style={[styles.sizeOptionDetails, { color: selectedSize?.id === size.id ? isDarkMode ? colors.brandColors.primary[200] : colors.brandColors.primary[300] : isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500] }]}
                    >
                      {size.volumeCubicYards ?? 0} {t('cubic yards')}
                    </NewRTLText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {selectedSize && (
              <View style={[styles.specificationsContainer, {backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light}]}>
                <NewRTLText style={[styles.sectionTitle, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                  {t('Specifications')}
                </NewRTLText>
                <View style={styles.specificationsGrid}>
                  <View style={styles.specificationItem}>
                    <MaterialCommunityIcons name="cube-outline" size={24} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                    <NewRTLText style={[styles.specificationLabel, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                      {t('Volume')}
                    </NewRTLText>
                    <NewRTLText style={[styles.specificationValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                      {selectedSize.volumeCubicYards ?? 0}
                      {t('yd³')}
                    </NewRTLText>
                  </View>
                  <View style={styles.specificationItem}>
                    <MaterialCommunityIcons name="weight" size={24} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                    <NewRTLText style={[styles.specificationLabel, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                      {t('Max Weight')}
                    </NewRTLText>
                    <NewRTLText style={[styles.specificationValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                      {selectedSize.maxWeightPounds ?? 0}
                      {t('lbs')}
                    </NewRTLText>
                  </View>
                  <View style={styles.specificationItem}>
                    <MaterialCommunityIcons name="ruler" size={24} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                    <NewRTLText style={[styles.specificationLabel, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                      {t('Dimensions')}
                    </NewRTLText>
                    <NewRTLText style={[styles.specificationValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                      {`${selectedSize.dimensions?.length ?? 0}" × ${selectedSize.dimensions?.width ?? 0}" × ${selectedSize.dimensions?.height ?? 0}"`}
                    </NewRTLText>
                  </View>
                </View>
                {selectedSize.description && (
                  <NewRTLText style={[styles.sizeDescription, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                    {selectedSize.description}
                  </NewRTLText>
                )}
              </View>
            )}

            <View style={styles.section}>
              <NewRTLText style={[styles.sectionTitle, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                {t('Compatible Waste Types')}
              </NewRTLText>
              <View style={[styles.tagsContainer, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
                {compatibleWasteTypeNames.length > 0 ? (
                  compatibleWasteTypeNames.map((name, index) => (
                    <View key={index} style={[styles.tag, {flexDirection: isRTL ? 'row-reverse' : 'row'}, {marginRight: isRTL ? 0 : 12, marginLeft: isRTL ? 12 : 0}, {backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light}]}>
                      <MaterialCommunityIcons name="check-circle" size={16} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                      <NewRTLText style={[styles.tagText, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}, {marginRight: isRTL ? 8 : 0, marginLeft: isRTL ? 0 : 8}]}>{name}</NewRTLText>
                    </View>
                  ))
                ) : (
                  <NewRTLText style={[styles.noContentText, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                    {t('No specific waste type restrictions')}
                  </NewRTLText>
                )}
              </View>
            </View>

            {displayFeatures.length > 0 && (
              <View style={styles.section}>
                 <NewRTLText style={[styles.sectionTitle, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>{t('Features')}</NewRTLText>
                 <View style={[styles.featuresContainer, {alignItems: isRTL ? 'flex-end' : 'flex-start'},{backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light}]}>
                   {displayFeatures.map((feature, index) => (
                     <View key={index} style={[styles.featureItem, {flexDirection: isRTL ? 'row-reverse' : 'row'}, {marginRight: isRTL ? 0 : 12, marginLeft: isRTL ? 12 : 0}]}>
                       <MaterialCommunityIcons name="check-circle" size={18} color={isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]} />
                       <NewRTLText style={[styles.featureText, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>{feature ?? ''}</NewRTLText>
                     </View>
                   ))}
                 </View>
              </View>
            )}
          </View>
        </ErrorBoundary>
      </ScrollView>

      {/* --- Restore BOTTOM BAR --- */}
        <View style={[styles.bottomBar, {backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light},{borderTopColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light}]}>
          <SafeAreaView edges={['bottom']}>
            <View style={[styles.bottomBarContent, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
             <View>
               <NewRTLText style={[styles.totalPriceLabel, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>{t('Total Price')}</NewRTLText>
               <View style={styles.totalPriceValueContainer}>
                 {isDarkMode ? (<Image
                   source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                   style={styles.currencySymbolLarge}
                   resizeMode="contain"
                 />) : (<Image
                   source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                   style={styles.currencySymbolLarge}
                   resizeMode="contain"
                 />)}
                 <NewRTLText style={[styles.totalPriceValue, {color: isDarkMode ? colors.textColors.dark : colors.textColors.light}]}>
                    {`${displayPrice} / ${t('Load')}`}
                 </NewRTLText>
               </View>
             </View>
             <TouchableOpacity
               style={[
                 styles.actionButton,
                 {backgroundColor: isDarkMode ? colors.brandColors.primary[400] : colors.brandColors.primary[500]},
                 (!selectedSize || !displayIsAvailable) ? {backgroundColor: isDarkMode ? colors.brandColors.primary[200] : colors.brandColors.primary[300]} : null
               ]}
               onPress={handleAddToCart}
               disabled={!selectedSize || !displayIsAvailable}
             >
               <NewRTLText style={[styles.actionButtonText, {color: isDarkMode ? colors.textColors.light : colors.textColors.dark}]}>{t('Proceed to Checkout')}</NewRTLText>
             </TouchableOpacity>
            </View>
            {!displayIsAvailable && (
              <NewRTLText style={[styles.unavailableText, {color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}]}>
                {t('This dumpster is currently unavailable')}
              </NewRTLText>
            )}
          </SafeAreaView>
        </View>
       {/* --- END BOTTOM BAR --- */}

     </LinearGradient>
   );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  fullScreenCenter: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    color: '#4b5563',
    fontSize: 16,
  },
  errorScreen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorTitle: {
    fontSize: 20,
    textAlign: 'center',
    color: '#4b5563',
    marginBottom: 16,
  },
  errorButton: {
    marginTop: 16,
    backgroundColor: '#3b82f6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#fee2e2',
    borderRadius: 8,
  },
  errorText: {
    color: '#ef4444',
    fontWeight: 'bold',
  },
  headerIcon: {
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 20,
  },
  heroImageContainer: {
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: width * 0.7,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  imageLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#e5e7eb',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  imageErrorContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#e5e7eb',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageErrorText: {
    marginTop: 8,
    color: '#6b7280',
  },
  availabilityBadge: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: '#ef4444',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  availabilityText: {
    color: 'white',
    fontWeight: 'bold',
  },
  ratingBadge: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 4,
    fontWeight: 'bold',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 120, // Add padding to avoid overlap with bottom bar
  },
  headerSection: {
    marginBottom: 24,
  },
  dumpsterTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  priceTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  currencySymbol: {
    width: 16,
    height: 16,
    marginRight: 2
  },
  currencySymbolLarge: {
    width: 18,
    height: 18,
    marginRight: 4,
    marginTop: 6
  },
  descriptionText: {
    fontSize: 16,
    color: '#4b5563', // Default light mode color
    lineHeight: 22,
    marginBottom: 8, // Added margin
  },
  descriptionTextDark: {
    fontSize: 16,
    color: colors.textColors.secondary.dark, // Dark mode color
    lineHeight: 22,
    marginBottom: 8, // Added margin
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  sizeOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  sizeOption: {
    marginRight: 12,
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    
  },
  selectedSizeOption: {
    backgroundColor: '#ebf5ff',
    borderColor: '#3b82f6',
  },
  sizeOptionTitle: {
    fontWeight: 'bold',
    color: '#4b5563',
  },
  selectedSizeOptionText: {
    color: '#3b82f6',
  },
  sizeOptionDetails: {
    fontSize: 14,
    color: '#6b7280',
  },
  selectedSizeOptionDetailsText: {
    color: '#60a5fa',
  },
  specificationsContainer: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  specificationsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  specificationItem: {
    alignItems: 'center',
  },
  specificationLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  specificationValue: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  sizeDescription: {
    marginTop: 8,
    fontSize: 14,
    color: '#6b7280',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    marginLeft: 4,
    color: '#4b5563',
  },
  noContentText: {
    fontStyle: 'italic',
    color: '#6b7280',
  },
  featuresContainer: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    marginLeft: 8,
    color: '#4b5563',
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    padding: 16,
  },
  bottomBarContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  totalPriceLabel: {
    color: '#6b7280',
  },
  totalPriceValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalPriceValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  actionButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#3b82f6',
    borderRadius: 32,
  },
  actionButtonDisabled: {
    backgroundColor: '#d1d5db',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  unavailableText: {
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 8,
  },
}); 
