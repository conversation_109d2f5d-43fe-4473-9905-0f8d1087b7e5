import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, Pressable, Text, Platform, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useProfile } from '@/hooks/useProfile';
import { useAuth, GlobalUserIdentifier } from '@/context/AuthContext';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { safeTranslate } from '@/utils/i18nHelpers';
import * as colors from '@/theme/colors';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import { getUserName, refreshUserProfile, syncProfileAuthIds, getUserOrders } from '@/utils/user';
import * as Notifications from 'expo-notifications';
import { supabase } from '@/lib/supabase';
import { useTheme } from '@/context/ThemeContext';
import { useAppTheme } from '@/hooks/useAppTheme';
import { brandColors } from '@/theme/colors';
import { textColors } from '@/theme/colors';
import Constants from 'expo-constants';

// --- Push Notification Helper Functions ---
async function registerForPushNotificationsAsync(): Promise<string | undefined> {
  let token;
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  if (existingStatus !== 'granted') {
    console.log('Requesting push notification permissions...');
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }

  if (finalStatus !== 'granted') {
    // User denied permission or it failed
    // Consider showing a user-friendly message or guiding them to settings
    console.warn('Push notification permission not granted.');
    // Alert.alert('Permission Denied', 'Push notifications are disabled. You can enable them in settings.');
    return;
  }

  try {
    // Ensure you have your EAS Project ID configured in app.json/app.config.js
    // Replace 'YOUR_EAS_PROJECT_ID' if not automatically picked up
    const projectId = '5397a280-f94c-492d-84e9-3b24c69f8c98';
    if (!projectId) {
      console.error('EAS Project ID not found in app config. Cannot get push token.');
      Alert.alert('Configuration Error', 'Cannot register for push notifications. Project ID missing.');
      return;
    }
    token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
    console.log("Expo Push Token fetched:", token);
  } catch (error) {
    console.error('Error getting Expo push token:', error);
    Alert.alert('Error', 'Could not get push token for notifications.');
  }

  return token;
}

async function savePushToken(userId: string, token: string | null) {
    if (!userId || !token) {
        console.log('User ID or push token missing, cannot save.');
        return;
    }
    console.log(`Attempting to save push token for user ${userId}...`);
    const { error } = await supabase
        .from('profiles')
        .update({ push_token: token })
        .eq('id', userId);

    if (error) {
        console.error('Error saving push token to Supabase:', error);
        // Consider alerting the user or logging more details
    } else {
         console.log('Push token saved successfully to Supabase for user:', userId);
    }
}
// ------------------------------------------

export default function HomeScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { session } = useAuth();
  const { profile } = useProfile(GlobalUserIdentifier.profileId || '');
  const { isDarkMode } = useTheme();
  const { theme } = useAppTheme();
  const { isRTL } = useRTLContext();
  const [userName, setUserName] = useState('');
  const [orderCount, setOrderCount] = useState(0);

  // Sync auth IDs on app start
  useEffect(() => {
    if (session?.user?.id) {
      syncProfileAuthIds(false).catch(console.error);
    }
  }, [session?.user?.id]);

  // Use the new utility to get user name from AsyncStorage
  useEffect(() => {
    const fetchUserName = async () => {
      const name = await getUserName();
      if (name) {
        // If we have a name in AsyncStorage, use it
        setUserName(name.split(' ')[0]);
      } else if (profile?.full_name) {
        // If not, but we have it from the profile hook, use that and refresh profile data
        setUserName(profile.full_name.split(' ')[0]);
        refreshUserProfile(false).catch(console.error);
      } else {
        // Last resort, refresh profile data from server
        const refreshedProfile = await refreshUserProfile(false);
        if (refreshedProfile?.full_name) {
          setUserName(refreshedProfile.full_name.split(' ')[0]);
        } else {
          setUserName('User');
        }
      }
    };

    fetchUserName().catch(console.error);
  }, [profile]);

  // Get order count using the profile-based query
  useEffect(() => {
    const loadOrders = async () => {
      if (session?.user?.id) {
        const orders = await getUserOrders();
        if (orders) {
          setOrderCount(orders.length);
          console.log('Order count:', orderCount);
        }
      }
    };
    
    loadOrders().catch(console.error);
  }, [session?.user?.id]);

  // --- Register and Save Push Token ---
  useEffect(() => {
    const registerAndSaveToken = async () => {
      if (session?.user?.id) {
        console.log('User session active, attempting push token registration...');
        const token = await registerForPushNotificationsAsync();
        if (token) {
          // Check if token needs updating before saving (optional optimization)
          // const currentProfileToken = profile?.push_token; // Need push_token in useProfile hook
          // if (token !== currentProfileToken) {
             await savePushToken(session.user.id, token);
          // } else {
          //    console.log('Push token is already up-to-date.');
          // }
        } else {
          console.log('Failed to get push token, not saving.');
        }
      } else {
        console.log('No active session, skipping push token registration.');
      }
    };

    registerAndSaveToken().catch(console.error);
    // Run only once when the user session becomes available
  }, [session?.user?.id]); // Dependency ensures it runs when session is ready
  // ------------------------------------

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return safeTranslate(t, 'home.greeting.morning', 'Good morning');
    if (hour < 18) return safeTranslate(t, 'home.greeting.afternoon', 'Good afternoon');
    return safeTranslate(t, 'home.greeting.evening', 'Good evening');
  };

  const openOrders = () => router.push('/orders');
  const openProfile = () => router.push('/profile');

  return (
    <LinearGradient
      colors={isDarkMode ? [colors.brandColors.primary[700], colors.brandColors.secondary[700]] : [colors.brandColors.primary[500], colors.brandColors.secondary[500]]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1.3 }}
      style={{ flex: 1 }}
    >
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <SafeAreaView style={{ flex: 1 }}>
        {/* Top Navigation */}
        <View style={{ 
          flexDirection: isRTL ? 'row-reverse' : 'row', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          paddingHorizontal: 20,
          paddingTop: 10 
        }}>
          <TouchableOpacity
            onPress={openOrders}
            style={{
              width: 40,
              height: 40,
              backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light,
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3
            }}
          >
            <Feather name="file-text" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={openProfile}
            style={{
              width: 40,
              height: 40,
              backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light,
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3
            }}
          >
            <Feather name="user" size={20} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
          </TouchableOpacity>
        </View>

        {/* Main Content */}
        <View style={{ flex: 1, paddingHorizontal: 20 }}>
          {/* Weather Widget */}
          <View style={{ 
            flexDirection: isRTL ? 'row-reverse' : 'row', 
            alignItems: 'center',
            marginTop: 60,
            alignSelf: isRTL ? 'flex-end' : 'flex-start'
          }}>
            <Feather name="cloud" size={48} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
            <NewRTLText style={{ 
              fontSize: 48,
              marginLeft: isRTL ? 0 : 10,
              marginRight: isRTL ? 10 : 0,
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              fontWeight: '300'
            }}>
              16°
            </NewRTLText>
          </View>

          {/* Greeting */}
          <View style={{ 
            marginTop: 40,
            alignItems: isRTL ? 'flex-end' : 'flex-start',
            width: '100%'
          }}>
            <NewRTLText style={{ 
              fontSize: 36,
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              fontWeight: '600',
              textAlign: isRTL ? 'right' : 'left',
              width: '100%'
            }}>
              {getGreeting()},
            </NewRTLText>
            <NewRTLText style={{ 
              fontSize: 36,
              color: isDarkMode ? colors.brandColors.primary[200] : colors.brandColors.primary[800],
              fontWeight: '400',
              marginTop: 5,
              textAlign: isRTL ? 'right' : 'left',
              width: '100%'
            }}>
              {userName || 'User'}
            </NewRTLText>
          </View>
        </View>

        {/* Bottom Input Button */}
        <View style={{ 
          position: 'absolute',
          bottom: 40,
          left: 20,
          right: 20,
          alignItems: 'center',
          width: '90%'
        }}>
          <Pressable
            onPress={() => router.push('/dumpster-selection')}
            style={{
              width: '100%',
              backgroundColor: isDarkMode ? colors.brandColors.primary[500] : colors.brandColors.primary[500],
              paddingVertical: 15,
              paddingHorizontal: 25,
              borderRadius: 100,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3
            }}
          >
            <View style={{
              flexDirection: isRTL ? 'row-reverse' : 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%'
            }}>
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: isDarkMode ? colors.brandColors.primary[100] : colors.brandColors.primary[900],
                justifyContent: 'center',
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 4,
                elevation: 4
              }}>
                <Feather name="mic" size={20} color={isDarkMode ? colors.textColors.light : colors.textColors.dark} />
              </View>
              <NewRTLText style={{ 
                flex: 1,
                fontSize: 16,
                color: isDarkMode ? textColors.light : brandColors.secondary[700],
                fontWeight: '500',
                marginLeft: isRTL ? 0 : 12,
                marginRight: isRTL ? 12 : 0,
                textAlign: isRTL ? 'right' : 'left'
              }}>
                {safeTranslate(t, 'home.inputPrompt', 'Tap to type')}
              </NewRTLText>
            </View>
          </Pressable>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
} 