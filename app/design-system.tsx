import React, { useMemo } from 'react';
import { ScrollView, View, StyleSheet, TouchableOpacity, SectionList, ViewStyle } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { backgroundColors, brandColors, surfaceColors, textColors } from '@/theme/colors';


export default function DesignSystemScreen() {
  const router = useRouter();
  const theme = useTheme();
  const { isRTL } = useRTLContext();

  // Create RTL-aware dynamic styles
  const dynamicStyles = useMemo(() => ({
    colorGrid: {
      flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
      flexWrap: 'wrap' as ViewStyle['flexWrap'],
      justifyContent: 'flex-start' as ViewStyle['justifyContent'],
      alignItems: 'flex-start' as ViewStyle['alignItems'],
      gap: 32,
    },
    
  }), [isRTL]);

  const ColorSwatch = ({ color, name }: { color: string; name: string }) => (
    <View style={styles.colorSwatchContainer}>
      <View 
        style={[styles.colorSwatch, { backgroundColor: color }, { borderWidth: 1, borderColor: theme.dark ? backgroundColors.outline.dark : backgroundColors.outline.light }]}
      />
      <NewRTLText style={[styles.colorSwatchText, { color: theme.dark ? textColors.dark : textColors.light }]}>{name}</NewRTLText>
    </View>
  );

  const Section = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.section}>
      <NewRTLText style={[styles.sectionTitle, { color: theme.dark ? textColors.dark : textColors.light }]}>{title}</NewRTLText>
      {children}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.dark ? backgroundColors.main.dark : backgroundColors.main.light }]}>
      {/* Header */}
     
      <NewRTLView style={[styles.header,  { backgroundColor: theme.dark ? backgroundColors.main.dark : backgroundColors.main.light, borderBottomColor: theme.dark ? backgroundColors.outline.dark : backgroundColors.outline.light }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={{ width: 'auto', alignItems: 'center' as ViewStyle['alignItems'] }}
        >
          <Feather name={isRTL ? "arrow-right" : "arrow-left"} size={20} color={theme.dark ? textColors.dark : textColors.light} />
        </TouchableOpacity>
        <NewRTLText style={[styles.headerTitle, { color: theme.dark ? textColors.dark : textColors.light }]}>Design System</NewRTLText>
      </NewRTLView>

      <ScrollView style={[styles.content, { backgroundColor: theme.dark ? backgroundColors.main.dark : backgroundColors.main.light }]}>
        {/* Brand Colors */}
        <Section title="Brand Colors">
          <View style={dynamicStyles.colorGrid}>
            <ColorSwatch color={brandColors.primary.DEFAULT} name="Primary" />
            <ColorSwatch color={brandColors.secondary.DEFAULT} name="Secondary" />
            <ColorSwatch color={brandColors.success.DEFAULT} name="Success" />
            <ColorSwatch color={brandColors.warning.DEFAULT} name="Warning" />
            <ColorSwatch color={brandColors.danger.DEFAULT} name="Danger" />
            <ColorSwatch color={brandColors.info.DEFAULT} name="Info" />
          </View>
        </Section>

        {/* Primary Color Shades */}
        <Section title="Primary Shades">
          <View style={dynamicStyles.colorGrid}>
            {Object.entries(brandColors.primary).map(([shade, color]) => (
              <ColorSwatch key={shade} color={color} name={shade} />
            ))}
          </View>
        </Section>

        {/* Surface Colors */}
        <Section title="Surface Colors">
          <View style={dynamicStyles.colorGrid}>
            <ColorSwatch color={surfaceColors.light} name="Light" />
            <ColorSwatch color={surfaceColors.dark} name="Dark" />
          </View>
        </Section>

        {/* Text Colors */}
        <Section title="Text Colors">
          <View style={dynamicStyles.colorGrid}>
            <ColorSwatch color={textColors.light} name="Light" />
            <ColorSwatch color={textColors.dark} name="Dark" />
            <ColorSwatch color={textColors.secondary.light} name="Secondary Light" />
            <ColorSwatch color={textColors.secondary.dark} name="Secondary Dark" />
          </View>
        </Section>

        {/* Typography */}
        <Section title="Typography">
          <NewRTLText style={[styles.h1, { color: theme.dark ? textColors.dark : textColors.light }]}>Heading 1</NewRTLText>
          <NewRTLText style={[styles.h2, { color: theme.dark ? textColors.dark : textColors.light }]}>Heading 2</NewRTLText>
          <NewRTLText style={[styles.h3, { color: theme.dark ? textColors.dark : textColors.light }]}>Heading 3</NewRTLText>
          <NewRTLText style={[styles.h4, { color: theme.dark ? textColors.dark : textColors.light }]}>Heading 4</NewRTLText>
          <NewRTLText style={[styles.textLg, { color: theme.dark ? textColors.dark : textColors.light }]}>Large Text</NewRTLText>
          <NewRTLText style={[styles.textBase, { color: theme.dark ? textColors.dark : textColors.light }]}>Base Text</NewRTLText>
          <NewRTLText style={[styles.textSm, { color: theme.dark ? textColors.dark : textColors.light }]}>Small Text</NewRTLText>
          <NewRTLText style={[styles.textXs, { color: theme.dark ? textColors.dark : textColors.light }]}>Extra Small Text</NewRTLText>
        </Section>

        {/* Buttons */}
        <Section title="Buttons">
          <View style={styles.buttonContainer}>
            <Button variant="solid" textStyle={{ color: theme.dark ? textColors.light : brandColors.secondary[700] }}>Solid Button</Button>
            <Button variant="light" >Light Button</Button>
            <Button variant="gradient" textStyle={{ color: theme.dark ? textColors.light : brandColors.secondary[800] }}>Gradient Button</Button>
            <Button variant="solid" disabled >Disabled Button</Button>
          </View>
        </Section>

        {/* Cards */}
        <Section title="Cards">
          <View style={styles.cardContainer}>
            <Card variant="elevated">
              <NewRTLText style={[styles.cardTitle, { color: theme.dark ? textColors.dark : textColors.light }]}>Elevated Card</NewRTLText>
              <NewRTLText style={[styles.cardText, { color: theme.dark ? textColors.secondary.dark : textColors.secondary.light }]}>
                This is an elevated card with shadow.
              </NewRTLText>
            </Card>

            <Card variant="outlined">
              <NewRTLText style={[styles.cardTitle, { color: theme.dark ? textColors.dark : textColors.light }]}>Outlined Card</NewRTLText>
              <NewRTLText style={[styles.cardText, { color: theme.dark ? textColors.secondary.dark : textColors.secondary.light }]}>
                This is an outlined card with border.
              </NewRTLText>
            </Card>

            <Card variant="filled">
              <NewRTLText style={[styles.cardTitle, { color: theme.dark ? textColors.dark : textColors.light }]}>Filled Card</NewRTLText>
              <NewRTLText style={[styles.cardText, { color: theme.dark ? textColors.secondary.dark : textColors.secondary.light }]}>
                This is a filled card with background color.
              </NewRTLText>
            </Card>
          </View>
        </Section>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: brandColors.primary[100],
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    marginHorizontal: 16,
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  colorSwatchContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 0,
    marginRight: 0,
  },
  colorSwatch: {
    width: 64,
    height: 64,
    borderRadius: 12,
    marginBottom: 6,
  },
  colorSwatchText: {
    fontSize: 14,
  },
  h1: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  h2: {
    fontSize: 30,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  h3: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  h4: {
    fontSize: 20,
    fontWeight: '500',
    marginBottom: 8,
  },
  textLg: {
    fontSize: 18,
    marginBottom: 8,
  },
  textBase: {
    fontSize: 16,
    marginBottom: 8,
  },
  textSm: {
    fontSize: 14,
    marginBottom: 8,
  },
  textXs: {
    fontSize: 12,
  },
  buttonContainer: {
    gap: 16,
  },
  cardContainer: {
    gap: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
  },
  cardText: {
    fontSize: 16,
    color: textColors.secondary.light,
  },
}); 