import React, { useState } from 'react';
import { ActivityIndicator, FlatList, StyleSheet, Text, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Stack } from 'expo-router';
import { useTheme as usePaperTheme } from 'react-native-paper';

import { DumpsterFilters } from '@/types/v2';
import { useDumpsters, useWasteTypes, useDumpsterSizes } from '@/hooks/v2';
import DumpsterCardV2 from '../components/v2/DumpsterCard';
import { useAppTheme } from '@/hooks/useAppTheme';

export default function DumpstersV2Screen() {
  const { t } = useTranslation();
  const { isDarkMode } = useAppTheme();
  const paperTheme = usePaperTheme();
  const colors = paperTheme.colors;
  
  // Default filters
  const [filters, setFilters] = useState<DumpsterFilters>({
    availability: true,
    rating: 3,
  });
  
  // Use the v2 hooks
  const { data: dumpsters, isLoading: dumpstersLoading, error: dumpstersError } = useDumpsters(filters);
  const { data: wasteTypes, isLoading: wasteTypesLoading } = useWasteTypes();
  const { data: dumpsterSizes, isLoading: sizesLoading } = useDumpsterSizes();
  
  const isLoading = dumpstersLoading || wasteTypesLoading || sizesLoading;
  
  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.onSurface }]}>
          {t('loading_dumpsters')}
        </Text>
      </View>
    );
  }
  
  if (dumpstersError) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <Text style={[styles.errorText, { color: colors.error }]}>
          {t('error_fetching_dumpsters')}
        </Text>
        <Text style={[styles.errorDetails, { color: colors.onSurfaceVariant }]}>
          {dumpstersError.message}
        </Text>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen options={{ title: t('v2_dumpsters') }} />
      
      <View style={styles.statsContainer}>
        <Text style={[styles.statsText, { color: colors.onSurface }]}>
          {t('showing')} {dumpsters?.length || 0} {t('dumpsters')}
        </Text>
        <Text style={[styles.statsText, { color: colors.onSurface }]}>
          {t('waste_types')}: {wasteTypes?.length || 0}
        </Text>
        <Text style={[styles.statsText, { color: colors.onSurface }]}>
          {t('sizes')}: {dumpsterSizes?.length || 0}
        </Text>
      </View>
      
      <FlatList
        data={dumpsters}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <DumpsterCardV2 dumpster={item} />}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.onSurfaceVariant }]}>
              {t('no_dumpsters_found')}
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  errorDetails: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  listContent: {
    paddingVertical: 8,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  statsText: {
    fontSize: 12,
  },
}); 