import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, ActivityIndicator, StyleSheet, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { IconButton, Button as PaperButton } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme as usePaperTheme } from 'react-native-paper';

// Import from v2 for types and hooks
import { DumpsterFilters, WasteType } from '@/types/v2';
import { useDumpsters, useWasteTypes, useDumpsterSizes } from '@/hooks/v2';
import DumpsterCardV2 from '../components/v2/DumpsterCard';

// Context imports
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';

export default function AllDumpstersV2Screen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const paperTheme = usePaperTheme();
  const themeColors = paperTheme.colors;

  // State for filters
  const [filters, setFilters] = useState<DumpsterFilters>({
    availability: true,
  });
  const [selectedWasteTypeId, setSelectedWasteTypeId] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState<'default' | 'rating_desc' | 'size_desc' | 'size_asc'>('default');

  // Use v2 hooks
  const { data: dumpsters, isLoading: isLoadingDumpsters, error: dumpstersError } = useDumpsters(filters);
  const { data: wasteTypes, isLoading: isLoadingWasteTypes } = useWasteTypes();
  const { data: dumpsterSizes, isLoading: isLoadingSizes } = useDumpsterSizes();

  const isLoading = isLoadingDumpsters || isLoadingWasteTypes || isLoadingSizes;

  // Handle card press - navigate to details
  const handleDumpsterPress = (dumpsterId: string) => {
    // For now, just go back as we don't have a v2 detail screen yet
    router.back();
    alert(`Would navigate to dumpster ${dumpsterId} (implement v2 detail screen)`);
  };

  // Waste Type selection
  const openWasteTypeSelector = () => {
    const options = [
      { id: null, text: t('allDumpsters.filters.all', 'All Waste Types') },
      ...(wasteTypes?.map(wt => ({ id: wt.id, text: isRTL ? wt.nameAr : wt.nameEn })) ?? [])
    ];
    
    Alert.alert(
      t('allDumpsters.filters.selectWasteType', 'Select Waste Type'),
      '',
      options.map(opt => ({
        text: opt.text,
        onPress: () => {
          setSelectedWasteTypeId(opt.id);
          
          // Update filters to include selected waste type
          if (opt.id) {
            setFilters(prev => ({
              ...prev,
              wasteTypeIds: [opt.id]
            }));
          } else {
            // Remove waste type filter
            const { wasteTypeIds, ...rest } = filters;
            setFilters(rest);
          }
        }
      })),
      { cancelable: true }
    );
  };

  // Sort selector
  const openSortSelector = () => {
    const options = [
      { key: 'default', text: t('allDumpsters.sorting.default', 'Default') },
      { key: 'rating_desc', text: t('allDumpsters.sorting.ratingDesc', 'Rating (Highest)') },
      { key: 'size_desc', text: t('allDumpsters.sorting.sizeDesc', 'Size (Largest)') },
      { key: 'size_asc', text: t('allDumpsters.sorting.sizeAsc', 'Size (Smallest)') },
    ];
    
    Alert.alert(
      t('allDumpsters.sorting.selectSort', 'Sort By'),
      '',
      options.map(opt => ({
        text: opt.text,
        onPress: () => setSortOption(opt.key as any)
      })),
      { cancelable: true }
    );
  };

  // Get display name for current waste type selection
  const selectedWasteTypeName = (() => {
    if (!selectedWasteTypeId) return t('allDumpsters.filters.all', 'All');
    const wasteType = wasteTypes?.find(wt => wt.id === selectedWasteTypeId);
    return wasteType ? (isRTL ? wasteType.nameAr : wasteType.nameEn) : t('allDumpsters.filters.all', 'All');
  })();

  // Get display name for current sort option
  const selectedSortName = (() => {
    switch (sortOption) {
      case 'rating_desc': return t('allDumpsters.sorting.ratingDesc', 'Rating (Highest)');
      case 'size_desc': return t('allDumpsters.sorting.sizeDesc', 'Size (Largest)');
      case 'size_asc': return t('allDumpsters.sorting.sizeAsc', 'Size (Smallest)');
      case 'default':
      default: return t('allDumpsters.sorting.default', 'Default');
    }
  })();

  // Apply client-side sorting if needed
  const sortedDumpsters = React.useMemo(() => {
    if (!dumpsters || dumpsters.length === 0) return [];
    
    let sorted = [...dumpsters];
    
    switch (sortOption) {
      case 'rating_desc':
        sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'size_desc':
        sorted.sort((a, b) => {
          const volumeA = (a.length || 0) * (a.width || 0) * (a.height || 0);
          const volumeB = (b.length || 0) * (b.width || 0) * (b.height || 0);
          return volumeB - volumeA;
        });
        break;
      case 'size_asc':
        sorted.sort((a, b) => {
          const volumeA = (a.length || 0) * (a.width || 0) * (a.height || 0);
          const volumeB = (b.length || 0) * (b.width || 0) * (b.height || 0);
          return volumeA - volumeB;
        });
        break;
      default:
        // Keep original order
        break;
    }
    
    return sorted;
  }, [dumpsters, sortOption]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <Stack.Screen
        options={{
          presentation: 'modal',
          headerShown: false,
        }}
      />

      {/* Header */}
      <View style={[styles.header, { 
        borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
        flexDirection: isRTL ? 'row-reverse' : 'row',
      }]}>
        <Text style={[styles.headerTitle, { color: themeColors.onBackground }]}>
          V2 {t('allDumpsters.title', 'All Dumpsters')}
        </Text>
        <IconButton
          icon="close"
          iconColor={themeColors.onBackground}
          onPress={() => router.back()}
        />
      </View>

      {/* Filters */}
      <View style={[styles.filtersContainer, {
        borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
        flexDirection: isRTL ? 'row-reverse' : 'row',
      }]}>
        <View style={styles.filterItem}>
          <Text style={[styles.filterLabel, { color: themeColors.onSurfaceVariant }]}>
            {t('allDumpsters.filters.wasteType', 'Waste Type')}
          </Text>
          <PaperButton
            mode="outlined"
            onPress={openWasteTypeSelector}
            textColor={themeColors.primary}
            style={{ borderColor: themeColors.outline }}
            labelStyle={{ fontSize: 14 }}
            compact
          >
            {selectedWasteTypeName}
          </PaperButton>
        </View>
      </View>

      {/* Sorting */}
      <View style={[styles.sortingContainer, {
        borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
        flexDirection: isRTL ? 'row-reverse' : 'row',
      }]}>
        <Text style={[styles.sortingText, { color: themeColors.onBackground }]}>
          {t('allDumpsters.sorting.sortBy', 'Sort by:')} {selectedSortName}
        </Text>
        <PaperButton
          mode="outlined"
          onPress={openSortSelector}
          textColor={themeColors.primary}
          style={{ borderColor: themeColors.outline }}
          labelStyle={{ fontSize: 14 }}
          compact
        >
          {t('allDumpsters.change', 'Change')}
        </PaperButton>
      </View>

      {/* Dumpster List */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.onBackground }]}>
            {t('loading_dumpsters', 'Loading dumpsters...')}
          </Text>
        </View>
      ) : (
        <FlatList
          data={sortedDumpsters}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <View style={styles.cardContainer}>
              <DumpsterCardV2
                dumpster={item}
                onPress={() => handleDumpsterPress(item.id)}
              />
            </View>
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialIcons name="info-outline" size={36} color={themeColors.onSurfaceVariant} />
              <Text style={[styles.emptyText, { color: themeColors.onBackground }]}>
                {t('allDumpsters.noDumpstersFound', 'No dumpsters found')}
              </Text>
              <Text style={[styles.emptySubtext, { color: themeColors.onSurfaceVariant }]}>
                {t('allDumpsters.tryDifferentFilters', 'Try adjusting your filters')}
              </Text>
            </View>
          }
        />
      )}

      {/* Stats Footer */}
      <View style={[styles.statsFooter, { 
        borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
        backgroundColor: themeColors.surface
      }]}>
        <Text style={[styles.statsText, { color: themeColors.onSurfaceVariant }]}>
          {t('allDumpsters.dumpsterCount', 'Dumpsters')}: {sortedDumpsters.length}
        </Text>
        <Text style={[styles.statsText, { color: themeColors.onSurfaceVariant }]}>
          {t('allDumpsters.wasteTypeCount', 'Waste Types')}: {wasteTypes?.length || 0}
        </Text>
        <Text style={[styles.statsText, { color: themeColors.onSurfaceVariant }]}>
          {t('allDumpsters.sizeCount', 'Sizes')}: {dumpsterSizes?.length || 0}
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  filtersContainer: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
  },
  filterItem: {
    flex: 1,
  },
  filterLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  sortingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  sortingText: {
    fontSize: 16,
  },
  listContent: {
    padding: 8,
  },
  cardContainer: {
    marginVertical: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  statsFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    borderTopWidth: 1,
  },
  statsText: {
    fontSize: 12,
  },
}); 