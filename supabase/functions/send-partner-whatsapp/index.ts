import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Define interfaces for the expected webhook payload
interface WebhookPayload {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  schema: string;
  record: Record<string, any> | null;
  old_record: Record<string, any> | null;
}

// Create a Supabase client with the service role key (has admin privileges)
const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

serve(async (req: Request) => {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  let payload: WebhookPayload;
  try {
    payload = await req.json();
    console.log('Received webhook payload:', JSON.stringify(payload, null, 2));
  } catch (error) {
    console.error('Error parsing request body:', error);
    return new Response(JSON.stringify({ error: 'Bad Request: Invalid JSON' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    // Only process new order insertions
    if (payload.table === 'orders' && payload.type === 'INSERT' && payload.record) {
      const orderId = payload.record.id;
      const partnerId = payload.record.partner_id;
      
      if (!partnerId) {
        console.log('No partner assigned to this order, skipping WhatsApp notification');
        return new Response(JSON.stringify({ success: true, message: 'No partner to notify' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      
      // Get partner details including contact phone
      const { data: partnerData, error: partnerError } = await supabaseAdmin
        .from('partners')
        .select('id, company_name, contact_phone')
        .eq('id', partnerId)
        .single();
      
      if (partnerError || !partnerData) {
        console.error('Error fetching partner details:', partnerError);
        throw new Error(`Database error fetching partner: ${partnerError?.message || 'Partner not found'}`);
      }
      
      // Get order details for the message
      const { data: orderDetails, error: orderError } = await supabaseAdmin
        .from('orders')
        .select(`
          id, 
          created_at, 
          delivery_address, 
          delivery_date, 
          waste_type,
          dumpster_id,
          dumpsters:dumpster_id (name_en, name_ar)
        `)
        .eq('id', orderId)
        .single();
      
      if (orderError || !orderDetails) {
        console.error('Error fetching order details:', orderError);
        throw new Error(`Database error fetching order: ${orderError?.message || 'Order not found'}`);
      }
      
      // Format the phone number to ensure it's in international format
      const partnerPhone = formatPhoneNumber(partnerData.contact_phone);
      
      if (!partnerPhone) {
        console.error('Invalid partner phone number:', partnerData.contact_phone);
        throw new Error('Invalid partner phone number');
      }
      
      // Prepare the WhatsApp message
      const orderIdShort = orderId.substring(0, 6);
      const dumpsterName = orderDetails.dumpsters?.name_en || 'Unknown dumpster';
      const deliveryDate = new Date(orderDetails.delivery_date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      const message = `New Order #${orderIdShort}!\n\n` +
        `You have a new dumpster order request.\n` +
        `Dumpster: ${dumpsterName}\n` +
        `Delivery Address: ${orderDetails.delivery_address}\n` +
        `Delivery Date: ${deliveryDate}\n` +
        `Waste Type: ${orderDetails.waste_type}\n\n` +
        `Please log in to the partner portal to manage this order.`;
      
      // Send WhatsApp message using Authenticasa API
      const whatsappResult = await sendWhatsAppMessage(partnerPhone, message);
      
      if (!whatsappResult.success) {
        console.error('Error sending WhatsApp message:', whatsappResult.error);
        throw new Error(`WhatsApp API error: ${whatsappResult.error}`);
      }
      
      console.log('WhatsApp notification sent successfully to partner:', partnerData.company_name);
      
      return new Response(JSON.stringify({ 
        success: true, 
        message: 'WhatsApp notification sent to partner',
        details: {
          partnerId: partnerId,
          partnerName: partnerData.company_name,
          orderId: orderId
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    // If not a new order, just return success
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Event processed, no WhatsApp notification needed' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error processing webhook:', error);
    // Type check the error before accessing .message
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
});

// Helper function to format phone number to international format
function formatPhoneNumber(phone: string): string | null {
  if (!phone) return null;
  
  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // If it already starts with +, return as is
  if (phone.startsWith('+')) return phone;
  
  // If it starts with 00, replace with +
  if (phone.startsWith('00')) return '+' + digits.substring(2);
  
  // If it's a Saudi number starting with 0, add +966
  if (digits.startsWith('0') && digits.length === 10) return '+966' + digits.substring(1);
  
  // If it's a Saudi number without leading 0, add +966
  if (digits.length === 9 && (digits.startsWith('5') || digits.startsWith('9'))) return '+966' + digits;
  
  // Otherwise, return as is with + prefix
  return '+' + digits;
}

// Function to send WhatsApp message using Authenticasa API
async function sendWhatsAppMessage(phone: string, message: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Get the API key from environment variables
    const apiKey = Deno.env.get('AUTHENTICASA_API_KEY');
    if (!apiKey) {
      throw new Error('AUTHENTICASA_API_KEY environment variable is not set');
    }
    
    // Prepare the request to Authenticasa API
    const response = await fetch('https://api.authentica.sa/api/v1/send-otp', {
      method: 'POST',
      headers: {
        'X-Authorization': apiKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        phone: phone,
        method: 'whatsapp',
        template_id: 8, // Use an appropriate template ID for WhatsApp messages
        otp_format: 'numeric',
        number_of_digits: 6,
        is_fallback_on: false,
        otp: '123456', // This will be replaced by the template message
        sender_name: 'DumpsterOD'
      })
    });
    
    const result = await response.json();
    
    if (!response.ok || !result.success) {
      console.error('Authenticasa API error:', result);
      return { 
        success: false, 
        error: result.errors?.[0]?.message || result.message || 'Unknown error from Authenticasa API' 
      };
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error sending WhatsApp message' 
    };
  }
}
