# Partner WhatsApp Notification Edge Function

This Edge Function sends WhatsApp notifications to partners when new orders are created.

## Functionality

When a new order is created in the `orders` table, this function:

1. Retrieves the partner details from the database
2. Formats a WhatsApp message with order details
3. Sends the message to the partner's phone number using the Authenticasa API

## Deployment

### Prerequisites

- Supabase CLI installed
- Access to the Supabase project
- Authenticasa API key

### Steps to Deploy

1. Set the required secrets:

```bash
supabase secrets set AUTHENTICASA_API_KEY=your_authenticasa_api_key
```

2. Deploy the Edge Function:

```bash
supabase functions deploy send-partner-whatsapp --project-ref ejjnlnwinrmnwnyvwlhj
```

3. Apply the database migration to create the trigger:

```bash
supabase db push
```

## Testing

You can test the function by creating a new order in the app or by directly inserting a record into the `orders` table with a valid `partner_id`.

### Manual Testing

1. Get a valid partner ID from the database:

```sql
SELECT id, company_name, contact_phone FROM partners LIMIT 1;
```

2. Insert a test order:

```sql
INSERT INTO orders (
  customer_id, 
  partner_id, 
  dumpster_id, 
  status, 
  delivery_address, 
  delivery_date, 
  waste_type, 
  base_price, 
  total_amount
) VALUES (
  'customer_profile_id', 
  'partner_id_from_step_1', 
  'valid_dumpster_id', 
  'pending', 
  'Test Address', 
  NOW() + INTERVAL '1 day', 
  'general', 
  100, 
  100
) RETURNING id;
```

3. Check the Edge Function logs to verify the WhatsApp message was sent:

```bash
supabase functions logs send-partner-whatsapp --project-ref ejjnlnwinrmnwnyvwlhj
```

## Troubleshooting

If the WhatsApp messages are not being sent:

1. Check the Edge Function logs for errors
2. Verify the partner's phone number is in the correct format
3. Ensure the Authenticasa API key is set correctly
4. Verify the WhatsApp template ID is valid and approved

## Environment Variables

- `AUTHENTICASA_API_KEY`: The API key for the Authenticasa service
- `SUPABASE_URL`: Automatically set by Supabase
- `SUPABASE_SERVICE_ROLE_KEY`: Automatically set by Supabase
