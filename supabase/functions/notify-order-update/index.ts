import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import type { Database } from '../_shared/database.types.ts'; // Assuming you generate types

// Define interfaces for the expected webhook payload
interface WebhookPayload {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  schema: string;
  record: Record<string, any> | null;
  old_record: Record<string, any> | null;
}

// Define structure for Expo Push API
interface ExpoPushMessage {
  to: string; // Expo push token
  sound?: 'default';
  title?: string;
  body?: string;
  data?: Record<string, unknown>;
}

// --- Environment Variables (Set these in Supabase Function settings) ---
const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
// ---------------------------------------------------------------------

// Initialize Supabase client with SERVICE_ROLE key for elevated privileges
const supabaseAdmin = createClient<Database>(
  SUPABASE_URL!,
  SUPABASE_SERVICE_ROLE_KEY!
);

console.log('Notify Order Update Function Initialized');

serve(async (req: Request) => {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  let payload: WebhookPayload;
  try {
    payload = await req.json();
    console.log('Received webhook payload:', JSON.stringify(payload, null, 2));
  } catch (error) {
    console.error('Error parsing request body:', error);
    return new Response(JSON.stringify({ error: 'Bad Request: Invalid JSON' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    let userIdsToNotify: string[] = [];
    let notificationTitle = 'Order Update';
    let notificationBody = 'An order has been updated.';

    // --- Logic to determine who to notify and the message ---
    if (payload.table === 'orders' && payload.type === 'UPDATE' && payload.record) {
      const oldStatus = payload.old_record?.status;
      const newStatus = payload.record?.status;
      const orderIdShort = payload.record?.id?.substring(0, 6); // Short ID for message

      if (oldStatus !== newStatus) {
        notificationTitle = `Order #${orderIdShort} Status Updated`;
        notificationBody = `Your order status changed to: ${newStatus}`;
        // Notify customer
        if (payload.record?.customer_id) userIdsToNotify.push(payload.record.customer_id);
        // Notify partner (if assigned)
        if (payload.record?.partner_id) {
           // Need to get partner's profile_id from partner_id
           const { data: partnerData, error: partnerError } = await supabaseAdmin
             .from('partners')
             .select('profile_id')
             .eq('id', payload.record.partner_id)
             .single();
           if (partnerData?.profile_id) userIdsToNotify.push(partnerData.profile_id);
           if (partnerError) console.error("Error fetching partner profile ID:", partnerError);
        }
        // Potentially notify assigned driver too, might need another query based on order_id
      }
      // Add logic for other order updates if needed (e.g., delivery date change)

    } else if (payload.table === 'driver_assignments' && (payload.type === 'INSERT' || payload.type === 'UPDATE') && payload.record) {
        const orderId = payload.record?.order_id;
        const driverId = payload.record?.driver_id; // This is the profile ID for the driver
        const assignmentStatus = payload.record?.status;
        const orderIdShort = orderId?.substring(0, 6);

        if (driverId) {
           notificationTitle = `Update on Order #${orderIdShort}`;
           if (payload.type === 'INSERT') {
               notificationBody = `A driver has been assigned to your order.`;
           } else { // UPDATE
               notificationBody = `Driver assignment status updated to: ${assignmentStatus}`;
           }
           // Notify the assigned driver
           userIdsToNotify.push(driverId);
           // Notify the customer
           const { data: orderData, error: orderError } = await supabaseAdmin
              .from('orders')
              .select('customer_id')
              .eq('id', orderId)
              .single();
           if (orderData?.customer_id) userIdsToNotify.push(orderData.customer_id);
           if (orderError) console.error("Error fetching customer ID for assignment:", orderError);
           // Notify partner if needed
        }
    }

    // --- Fetch Push Tokens ---
    if (userIdsToNotify.length > 0) {
      // Deduplicate user IDs
      const uniqueUserIds = [...new Set(userIdsToNotify)];
      console.log('Attempting to notify user IDs:', uniqueUserIds);

      // **IMPORTANT:** Assumes you have a 'push_token' column in your 'profiles' table
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, push_token') // Select the push token column
        .in('id', uniqueUserIds)
        .not('push_token', 'is', null); // Only get profiles with a token

      if (profileError) {
        console.error('Error fetching user push tokens:', profileError);
        throw new Error(`Database error fetching tokens: ${profileError.message}`);
      }

      if (!profiles || profiles.length === 0) {
         console.log('No valid push tokens found for the target users.');
         return new Response(JSON.stringify({ message: 'No push tokens found for users.' }), { status: 200 });
      }

      // --- Prepare and Send Notifications via Expo API ---
      // Explicitly define the expected profile type after filtering
      type ProfileWithToken = { id: string; push_token: string };

      const messages: ExpoPushMessage[] = profiles
         // Type guard to ensure profile and push_token are valid
         .filter((p): p is ProfileWithToken => p !== null && typeof p.push_token === 'string' && p.push_token.length > 0)
         .map((profile: ProfileWithToken) => ({
            to: profile.push_token,
            //sound: 'default',
            title: notificationTitle,
            body: notificationBody,
            data: { orderId: payload.record?.id || payload.record?.order_id }, // Send relevant data
      }));

      console.log(`Sending ${messages.length} push notifications...`);

      if (messages.length > 0) {
        const expoResponse = await fetch('https://exp.host/--/api/v2/push/send', {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(messages),
        });

        const expoResult = await expoResponse.json();
        console.log('Expo Push API Response:', JSON.stringify(expoResult, null, 2));

        if (!expoResponse.ok || expoResult.data?.some((ticket: any) => ticket.status === 'error')) {
          console.error('Error sending push notifications:', expoResult);
          // Handle specific errors like 'DeviceNotRegistered' if necessary
        } else {
          console.log('Push notifications sent successfully.');
        }
      }
    } else {
       console.log('No users identified for notification for this event.');
    }

    return new Response(JSON.stringify({ success: true, message: 'Webhook processed.' }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error processing webhook:', error);
    // Type check the error before accessing .message
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
});
