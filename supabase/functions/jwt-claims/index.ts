// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
// jwt-claims.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

// This function handles the JWT claims request
serve(async (req) => {
  try {
    // Parse the request body
    const event = await req.json()
    console.log('JWT hook called with:', event)
    
    // Return a valid claims object
    const claims = {
      role: 'authenticated',
      app_metadata: {
        user_role: 'customer'
      }
    }
    
    return new Response(
      JSON.stringify(claims),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error in JWT claims hook:', error)
    
    // Even on error, return a valid minimal claims object
    return new Response(
      JSON.stringify({ role: 'authenticated' }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/jwt-claims' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
