-- Disable R<PERSON> on all tables first
ALTER TABLE IF EXISTS notifications D<PERSON><PERSON><PERSON> ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ai_feedback DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ai_conversations DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS reviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS payments DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS order_status_history DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS discounts DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dumpster_waste_types DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dumpster_size_options DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dumpster_features DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dumpster_images DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS pricing_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dumpsters DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dumpster_sizes DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dumpster_types DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS waste_type_tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS waste_types DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS waste_tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS partners DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS addresses DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS user_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS profiles DISABLE ROW LEVEL SECURITY;

-- Drop functions
DROP FUNCTION IF EXISTS is_admin(UUID);
DROP FUNCTION IF EXISTS is_partner(UUID);

-- Drop tables in reverse order of creation to avoid dependency issues
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS ai_feedback CASCADE;
DROP TABLE IF EXISTS ai_conversations CASCADE;
DROP TABLE IF EXISTS reviews CASCADE;
DROP TABLE IF EXISTS payments CASCADE;
DROP TABLE IF EXISTS order_status_history CASCADE;
DROP TABLE IF EXISTS orders CASCADE;
DROP TABLE IF EXISTS discounts CASCADE;
DROP TABLE IF EXISTS dumpster_waste_types CASCADE;
DROP TABLE IF EXISTS dumpster_size_options CASCADE;
DROP TABLE IF EXISTS dumpster_features CASCADE;
DROP TABLE IF EXISTS dumpster_images CASCADE;
DROP TABLE IF EXISTS pricing_plans CASCADE;
DROP TABLE IF EXISTS dumpsters CASCADE;
DROP TABLE IF EXISTS dumpster_sizes CASCADE;
DROP TABLE IF EXISTS dumpster_types CASCADE;
DROP TABLE IF EXISTS waste_type_tags CASCADE;
DROP TABLE IF EXISTS waste_types CASCADE;
DROP TABLE IF EXISTS waste_tags CASCADE;
DROP TABLE IF EXISTS partners CASCADE;
DROP TABLE IF EXISTS addresses CASCADE;
DROP TABLE IF EXISTS user_settings CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- Note: We don't drop spatial_ref_sys as it's a system table from PostGIS 