-- Update user_settings table
-- First, drop the old settings data since we don't need to keep it
UPDATE user_settings
SET settings = '{
  "theme": "system",
  "notifications": {
    "push_enabled": true
  },
  "location_sharing": false,
  "privacy": {
    "policy_url": "https://dumpster.app/privacy",
    "terms_url": "https://dumpster.app/terms"
  }
}'::jsonb;

-- Add a check constraint to ensure settings has required fields
ALTER TABLE user_settings
ADD CONSTRAINT user_settings_required_fields CHECK (
  settings ? 'theme' AND
  settings ? 'notifications' AND
  settings ? 'location_sharing' AND
  settings ? 'privacy'
);

-- Add a check constraint for theme values
ALTER TABLE user_settings
ADD CONSTRAINT user_settings_theme_values CHECK (
  settings->>'theme' IN ('light', 'dark', 'system')
);

-- Create an index for faster theme queries
CREATE INDEX idx_user_settings_theme ON user_settings ((settings->>'theme'));

-- Add updated_at column if it doesn't exist
DO $$ 
BEGIN 
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'user_settings' 
    AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE user_settings 
    ADD COLUMN updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();
  END IF;
END $$;

-- Add trigger to update updated_at
CREATE OR REPLACE FUNCTION update_user_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_user_settings_updated_at_trigger ON user_settings;
CREATE TRIGGER update_user_settings_updated_at_trigger
  BEFORE UPDATE ON user_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_user_settings_updated_at();
