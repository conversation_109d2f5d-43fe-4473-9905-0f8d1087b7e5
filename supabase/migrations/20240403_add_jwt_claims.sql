-- Create a function to add custom claims to JWT tokens
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := request->>'sub';
  user_role TEXT;
BEGIN
  -- Get the user's role from the profiles table
  SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Return the custom claims to be added to the JWT
  RETURN jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO service_role;

-- Update the profiles table to ensure user_type is always set
ALTER TABLE public.profiles 
  ALTER COLUMN user_type SET DEFAULT 'customer',
  ADD CONSTRAINT valid_user_type CHECK (user_type IN ('customer', 'partner', 'admin', 'driver'));

-- Create a trigger to update JWT claims when user_type changes
CREATE OR REPLACE FUNCTION public.handle_user_type_change()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.user_type IS DISTINCT FROM NEW.user_type THEN
    -- Force refresh of JWT claims by updating auth.users
    UPDATE auth.users SET updated_at = now() WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_user_type_change
  AFTER UPDATE ON public.profiles
  FOR EACH ROW
  WHEN (OLD.user_type IS DISTINCT FROM NEW.user_type)
  EXECUTE FUNCTION public.handle_user_type_change();

-- Update RLS policies to use the JWT claims
-- First, drop existing policies
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;

-- Create new policies based on roles
-- All users can view their own profile
CREATE POLICY "users_can_view_own_profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

-- Admin users can view all profiles
CREATE POLICY "admins_can_view_all_profiles"
ON profiles FOR SELECT
USING ((auth.jwt()->>'role')::text = 'admin');

-- Partners can view their own profile and customer profiles
CREATE POLICY "partners_can_view_customer_profiles"
ON profiles FOR SELECT
USING (
  ((auth.jwt()->>'role')::text = 'partner' AND 
   (id = auth.uid() OR (SELECT user_type FROM profiles WHERE id = auth.uid()) = 'customer'))
);

-- Users can update their own profile
CREATE POLICY "users_can_update_own_profile"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- Admin users can update any profile
CREATE POLICY "admins_can_update_any_profile"
ON profiles FOR UPDATE
USING ((auth.jwt()->>'role')::text = 'admin');

-- Users can insert their own profile
CREATE POLICY "users_can_insert_own_profile"
ON profiles FOR INSERT
WITH CHECK (auth.uid() = id);

-- Admin users can create any profile
CREATE POLICY "admins_can_create_any_profile"
ON profiles FOR INSERT
WITH CHECK ((auth.jwt()->>'role')::text = 'admin');
