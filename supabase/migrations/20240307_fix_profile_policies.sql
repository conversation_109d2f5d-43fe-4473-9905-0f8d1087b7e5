-- First, drop any existing policies
DROP POLICY IF EXISTS "Allow public read access to profiles" ON profiles;
DROP POLICY IF EXISTS "Allow users to manage their own profile" ON profiles;

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Add policy for public read access to profiles
CREATE POLICY "Allow public read access to profiles"
ON profiles
FOR SELECT
USING (true);  -- Allows reading all profiles

-- Add policy for users to manage their own profile
CREATE POLICY "Allow users to manage their own profile"
ON profiles
FOR ALL
USING (auth.uid() = id);

-- Grant necessary privileges
GRANT SELECT ON profiles TO authenticated;
GRANT SELECT ON profiles TO anon;