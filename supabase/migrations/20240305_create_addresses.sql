-- Create addresses table
create table if not exists public.addresses (
    id uuid default gen_random_uuid() primary key,
    user_id uuid references auth.users(id) not null,
    street_address text not null,
    city text not null,
    state text not null,
    zip_code text not null,
    latitude double precision not null,
    longitude double precision not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null
);

-- Enable RLS
alter table public.addresses enable row level security;

-- Create policies
create policy "Users can view their own addresses"
    on public.addresses
    for select
    using (auth.uid() = user_id);

create policy "Users can insert their own addresses"
    on public.addresses
    for insert
    with check (auth.uid() = user_id);

create policy "Users can update their own addresses"
    on public.addresses
    for update
    using (auth.uid() = user_id);

create policy "Users can delete their own addresses"
    on public.addresses
    for delete
    using (auth.uid() = user_id);

-- Create updated_at trigger
create or replace function public.handle_updated_at()
returns trigger as $$
begin
    new.updated_at = now();
    return new;
end;
$$ language plpgsql;

create trigger handle_addresses_updated_at
    before update on public.addresses
    for each row
    execute procedure public.handle_updated_at(); 