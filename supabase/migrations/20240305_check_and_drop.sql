-- Drop all policies for our application tables
DO $$ 
DECLARE
    app_tables TEXT[] := ARRAY[
        'profiles', 'partners', 'waste_tags', 'waste_types', 'waste_type_tags',
        'dumpster_types', 'dumpster_sizes', 'dumpsters', 'pricing_plans',
        'dumpster_images', 'dumpster_features', 'dumpster_size_options',
        'dumpster_waste_types', 'discounts', 'orders', 'order_status_history',
        'payments', 'reviews', 'ai_conversations', 'ai_feedback', 'notifications'
    ];
    t TEXT;
    pol RECORD;
BEGIN
    -- Drop policies for each application table
    FOREACH t IN ARRAY app_tables
    LOOP
        FOR pol IN (
            SELECT policyname 
            FROM pg_policies 
            WHERE tablename = t
        ) LOOP
            EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(pol.policyname) || ' ON ' || quote_ident(t);
            RAISE NOTICE 'Dropped policy % on table %', pol.policyname, t;
        END LOOP;
    END LOOP;
END $$;

-- Drop functions
DROP FUNCTION IF EXISTS is_admin(UUID);
DROP FUNCTION IF EXISTS is_partner(UUID);

-- Check and drop tables only if they exist
DO $$ 
DECLARE
    tables_to_drop TEXT[] := ARRAY[
        'notifications', 'ai_feedback', 'ai_conversations', 'reviews', 
        'payments', 'order_status_history', 'orders', 'discounts', 
        'dumpster_waste_types', 'dumpster_size_options', 'dumpster_features', 
        'dumpster_images', 'pricing_plans', 'dumpsters', 'dumpster_sizes', 
        'dumpster_types', 'waste_type_tags', 'waste_types', 'waste_tags', 
        'partners', 'profiles'
    ];
    t TEXT;
BEGIN
    FOREACH t IN ARRAY tables_to_drop
    LOOP
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = t) THEN
            -- Disable RLS before dropping
            EXECUTE 'ALTER TABLE ' || quote_ident(t) || ' DISABLE ROW LEVEL SECURITY';
            RAISE NOTICE 'Disabled RLS on table %', t;
            
            -- Drop the table
            EXECUTE 'DROP TABLE ' || quote_ident(t) || ' CASCADE';
            RAISE NOTICE 'Dropped table %', t;
        ELSE
            RAISE NOTICE 'Table % does not exist', t;
        END IF;
    END LOOP;
END $$; 