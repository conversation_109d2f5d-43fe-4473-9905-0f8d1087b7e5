-- Create a webhook trigger for new orders to send WhatsApp notifications to partners
CREATE OR REPLACE FUNCTION public.handle_new_order_whatsapp_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Call the Edge Function to send WhatsApp notification
  PERFORM
    net.http_post(
      url := 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/functions/v1/send-partner-whatsapp',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('request.jwt.claim.service_role', true)
      ),
      body := jsonb_build_object(
        'type', TG_OP,
        'table', TG_TABLE_NAME,
        'schema', TG_TABLE_SCHEMA,
        'record', to_jsonb(NEW),
        'old_record', to_jsonb(OLD)
      )
    );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on the orders table
DROP TRIGGER IF EXISTS orders_whatsapp_notification_trigger ON public.orders;
CREATE TRIGGER orders_whatsapp_notification_trigger
  AFTER INSERT ON public.orders
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_order_whatsapp_notification();

-- Add a comment to explain the trigger
COMMENT ON TRIGGER orders_whatsapp_notification_trigger ON public.orders IS 
  'Trigger to send WhatsApp notifications to partners when new orders are created';
