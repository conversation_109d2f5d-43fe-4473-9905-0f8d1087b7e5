-- First disable RLS
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;

-- Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies with explicit schema reference and simpler conditions
CREATE POLICY "profiles_select_policy" 
ON profiles FOR SELECT 
TO authenticated 
USING (
  auth.role() = 'authenticated' AND 
  id = auth.uid()
);

CREATE POLICY "profiles_insert_policy"
ON profiles FOR INSERT 
TO authenticated 
WITH CHECK (
  auth.role() = 'authenticated' AND 
  id = auth.uid()
);

CREATE POLICY "profiles_update_policy"
ON profiles FOR UPDATE 
TO authenticated 
USING (
  auth.role() = 'authenticated' AND 
  id = auth.uid()
);

-- Ensure proper grants
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON profiles TO authenticated;

-- Verify the profile exists and permissions are correct
DO $$
DECLARE
  current_user_id UUID;
BEGIN
  SELECT auth.uid() INTO current_user_id;
  RAISE NOTICE 'Checking profile for user %', current_user_id;
  
  -- Check if the profile exists
  PERFORM id FROM profiles WHERE id = current_user_id;
  
  IF NOT FOUND THEN
    RAISE NOTICE 'Profile not found for user %', current_user_id;
  ELSE
    RAISE NOTICE 'Profile found for user %', current_user_id;
  END IF;
END $$; 