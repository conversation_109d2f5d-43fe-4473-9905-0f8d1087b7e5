-- First, drop any existing policies
DROP POLICY IF EXISTS "Allow public read access to partners" ON partners;
DROP POLICY IF EXISTS "Allow partners to manage their own data" ON partners;

-- Enable RLS if not already enabled
ALTER TABLE partners ENABLE ROW LEVEL SECURITY;

-- Add a policy for public read access
CREATE POLICY "Allow public read access to partners"
ON partners
FOR SELECT
USING (true);  -- This allows anyone to read partner data

-- Add a policy for partners to manage their own data
CREATE POLICY "Allow partners to manage their own data"
ON partners
FOR ALL
USING (
    auth.uid() = profile_id
    OR 
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND user_type = 'admin'
    )
);

-- Grant necessary privileges
GRANT SELECT ON partners TO authenticated;
GRANT SELECT ON partners TO anon;