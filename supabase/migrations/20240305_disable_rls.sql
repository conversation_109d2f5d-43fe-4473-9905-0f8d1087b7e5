-- Disable <PERSON><PERSON> completely
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;

-- Grant necessary permissions
GRANT ALL ON profiles TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Verify the table structure and data
SELECT 
  table_schema,
  table_name,
  column_name,
  data_type,
  is_nullable
FROM 
  information_schema.columns 
WHERE 
  table_name = 'profiles';

-- Check if the profile exists
SELECT COUNT(*) FROM profiles; 