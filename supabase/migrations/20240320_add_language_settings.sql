-- Add language field to user_settings if it doesn't exist
DO $$ 
BEGIN
  -- Check if any rows have settings without language field
  UPDATE user_settings
  SET settings = settings || jsonb_build_object('language', 'en')
  WHERE NOT (settings ? 'language');

  -- Add a check constraint to ensure language is either 'en' or 'ar'
  ALTER TABLE user_settings
  ADD CONSTRAINT valid_language 
  CHECK (
    (settings->>'language')::text IN ('en', 'ar')
  );
END $$; 