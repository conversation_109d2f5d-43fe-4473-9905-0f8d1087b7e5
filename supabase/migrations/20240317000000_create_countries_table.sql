-- Create countries table
CREATE TABLE IF NOT EXISTS countries (
  id SERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  native_name VARCHAR(100),
  code VARCHAR(2) NOT NULL, -- ISO 3166-1 alpha-2 code (e.g., 'US', 'GB')
  dial_code VARCHAR(10) NOT NULL, -- Phone code with plus (e.g., '+1', '+44')
  flag_url VARCHAR(255) NOT NULL, -- URL to flag image from Flagpedia
  flag_emoji VARCHAR(10) NOT NULL, -- Unicode flag emoji
  rtl BOOLEAN DEFAULT false, -- Whether the country's primary language is RTL
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create unique constraints
ALTER TABLE countries ADD CONSTRAINT countries_code_unique UNIQUE (code);
ALTER TABLE countries ADD CONSTRAINT countries_dial_code_unique UNIQUE (dial_code);

-- <PERSON>reate indexes
CREATE INDEX countries_code_idx ON countries(code);
CREATE INDEX countries_dial_code_idx ON countries(dial_code);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_countries_updated_at
    BEFORE UPDATE ON countries
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 