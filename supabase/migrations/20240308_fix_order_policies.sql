-- First, ensure R<PERSON> is enabled
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies for orders
DROP POLICY IF EXISTS "Allow users to create orders" ON orders;
DROP POLICY IF EXISTS "Allow users to view their own orders" ON orders;
DROP POLICY IF EXISTS "Allow users to update their own orders" ON orders;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON orders;
DROP POLICY IF EXISTS "Enable select for own orders" ON orders;
DROP POLICY IF EXISTS "Enable update for own orders" ON orders;

-- Simple insert policy
CREATE POLICY "orders_insert_policy"
ON orders
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Simple select policy
CREATE POLICY "orders_select_policy"
ON orders
FOR SELECT
TO authenticated
USING (customer_id = auth.uid());

-- Simple update policy
CREATE POLICY "orders_update_policy"
ON orders
FOR UPDATE
TO authenticated
USING (customer_id = auth.uid() AND status IN ('pending', 'confirmed'));

-- Ensure customer_id defaults to auth.uid()
ALTER TABLE orders 
ALTER COLUMN customer_id SET DEFAULT auth.uid();

-- Grant privileges
GRANT ALL ON orders TO authenticated;
