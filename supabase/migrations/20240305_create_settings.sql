-- Create user_settings table
create table if not exists public.user_settings (
    id uuid default gen_random_uuid() primary key,
    user_id uuid references auth.users(id) not null unique,
    settings jsonb not null default '{}'::jsonb,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null
);

-- Enable RLS
alter table public.user_settings enable row level security;

-- Create policies
create policy "Users can view their own settings"
    on public.user_settings
    for select
    using (auth.uid() = user_id);

create policy "Users can insert their own settings"
    on public.user_settings
    for insert
    with check (auth.uid() = user_id);

create policy "Users can update their own settings"
    on public.user_settings
    for update
    using (auth.uid() = user_id);

-- Create updated_at trigger
create trigger handle_updated_at
    before update on public.user_settings
    for each row
    execute procedure public.handle_updated_at(); 