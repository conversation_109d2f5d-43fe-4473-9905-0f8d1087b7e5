-- Drop all policies first to avoid dependency issues
DROP POLICY IF EXISTS "Allow public read access for profiles" ON profiles;
DROP POLICY IF EXISTS "Allow users to manage their own profile" ON profiles;
DROP POLICY IF EXISTS "Allow admin access to all profiles" ON profiles;

DROP POLICY IF EXISTS "Allow public read access for partners" ON partners;
DROP POLICY IF EXISTS "Allow partners to view their own data" ON partners;
DROP POLICY IF EXISTS "Allow partners to update their own data" ON partners;
DROP POLICY IF EXISTS "Allow admin access to all partners" ON partners;

DROP POLICY IF EXISTS "Allow public read access for waste_tags" ON waste_tags;
DROP POLICY IF EXISTS "Allow admin access to all waste_tags" ON waste_tags;

DROP POLICY IF EXISTS "Allow public read access for waste_types" ON waste_types;
DROP POLICY IF EXISTS "Allow admin access to all waste_types" ON waste_types;

DROP POLICY IF EXISTS "Allow public read access for waste_type_tags" ON waste_type_tags;
DROP POLICY IF EXISTS "Allow admin access to all waste_type_tags" ON waste_type_tags;

DROP POLICY IF EXISTS "Allow public read access for dumpster_types" ON dumpster_types;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_types" ON dumpster_types;

DROP POLICY IF EXISTS "Allow public read access for dumpster_sizes" ON dumpster_sizes;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_sizes" ON dumpster_sizes;

DROP POLICY IF EXISTS "Allow public read access for dumpsters" ON dumpsters;
DROP POLICY IF EXISTS "Allow partners to view their own dumpsters" ON dumpsters;
DROP POLICY IF EXISTS "Allow partners to manage their own dumpsters" ON dumpsters;
DROP POLICY IF EXISTS "Allow admin access to all dumpsters" ON dumpsters;

DROP POLICY IF EXISTS "Allow public read access for pricing_plans" ON pricing_plans;
DROP POLICY IF EXISTS "Allow partners to manage their own pricing plans" ON pricing_plans;
DROP POLICY IF EXISTS "Allow admin access to all pricing_plans" ON pricing_plans;

DROP POLICY IF EXISTS "Allow public read access for dumpster_images" ON dumpster_images;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_images" ON dumpster_images;

DROP POLICY IF EXISTS "Allow public read access for dumpster_features" ON dumpster_features;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_features" ON dumpster_features;

DROP POLICY IF EXISTS "Allow public read access for dumpster_size_options" ON dumpster_size_options;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_size_options" ON dumpster_size_options;

DROP POLICY IF EXISTS "Allow public read access for dumpster_waste_types" ON dumpster_waste_types;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_waste_types" ON dumpster_waste_types;

DROP POLICY IF EXISTS "Allow public read access for discounts" ON discounts;
DROP POLICY IF EXISTS "Allow admin access to all discounts" ON discounts;

DROP POLICY IF EXISTS "Allow users to view their own orders" ON orders;
DROP POLICY IF EXISTS "Allow users to create orders" ON orders;
DROP POLICY IF EXISTS "Allow users to update their own orders" ON orders;
DROP POLICY IF EXISTS "Allow partners to view their own orders" ON orders;
DROP POLICY IF EXISTS "Allow partners to update their own orders" ON orders;
DROP POLICY IF EXISTS "Allow admin access to all orders" ON orders;

DROP POLICY IF EXISTS "Allow admin access to all order_status_history" ON order_status_history;

DROP POLICY IF EXISTS "Allow users to view their own payments" ON payments;
DROP POLICY IF EXISTS "Allow partners to view their own payments" ON payments;
DROP POLICY IF EXISTS "Allow admin access to all payments" ON payments;

DROP POLICY IF EXISTS "Allow public read access for reviews" ON reviews;
DROP POLICY IF EXISTS "Allow users to view their own reviews" ON reviews;
DROP POLICY IF EXISTS "Allow users to create reviews" ON reviews;
DROP POLICY IF EXISTS "Allow users to update their own reviews" ON reviews;
DROP POLICY IF EXISTS "Allow partners to respond to reviews" ON reviews;
DROP POLICY IF EXISTS "Allow admin access to all reviews" ON reviews;

DROP POLICY IF EXISTS "Allow users to view their own AI conversations" ON ai_conversations;
DROP POLICY IF EXISTS "Allow admin access to all ai_conversations" ON ai_conversations;

DROP POLICY IF EXISTS "Allow users to create AI feedback" ON ai_feedback;
DROP POLICY IF EXISTS "Allow admin access to all ai_feedback" ON ai_feedback;

DROP POLICY IF EXISTS "Allow users to view their own notifications" ON notifications;
DROP POLICY IF EXISTS "Allow users to update their own notifications" ON notifications;
DROP POLICY IF EXISTS "Allow admin access to all notifications" ON notifications;

-- Drop functions
DROP FUNCTION IF EXISTS is_admin(UUID);
DROP FUNCTION IF EXISTS is_partner(UUID);

-- Drop tables in reverse order of creation to avoid dependency issues
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS ai_feedback;
DROP TABLE IF EXISTS ai_conversations;
DROP TABLE IF EXISTS reviews;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS order_status_history;
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS discounts;
DROP TABLE IF EXISTS dumpster_waste_types;
DROP TABLE IF EXISTS dumpster_size_options;
DROP TABLE IF EXISTS dumpster_features;
DROP TABLE IF EXISTS dumpster_images;
DROP TABLE IF EXISTS pricing_plans;
DROP TABLE IF EXISTS dumpsters;
DROP TABLE IF EXISTS dumpster_sizes;
DROP TABLE IF EXISTS dumpster_types;
DROP TABLE IF EXISTS waste_type_tags;
DROP TABLE IF EXISTS waste_types;
DROP TABLE IF EXISTS waste_tags;
DROP TABLE IF EXISTS partners;
DROP TABLE IF EXISTS profiles; 