-- Create a function to add custom claims to JWT tokens
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := request->>'sub';
  user_role TEXT;
BEGIN
  -- Get the user's role from the profiles table
  SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Log the JWT claim generation for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_claim_generated', jsonb_build_object('user_id', user_id, 'role', user_role));
  
  -- Return the custom claims to be added to the JWT
  RETURN jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO service_role;

-- Update the profiles table to ensure user_type is always set
ALTER TABLE public.profiles 
  ALTER COLUMN user_type SET DEFAULT 'customer';

-- Add driver role to valid user types if not already included
DO $$
BEGIN
  -- Check if the constraint exists
  IF EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'profiles_user_type_check' 
    AND conrelid = 'public.profiles'::regclass
  ) THEN
    -- Drop the existing constraint
    ALTER TABLE public.profiles DROP CONSTRAINT profiles_user_type_check;
  END IF;
  
  -- Add the new constraint with all roles
  ALTER TABLE public.profiles 
    ADD CONSTRAINT profiles_user_type_check 
    CHECK (user_type IN ('customer', 'partner', 'admin', 'driver'));
END $$;

-- Create a trigger to update JWT claims when user_type changes
CREATE OR REPLACE FUNCTION public.handle_user_type_change()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.user_type IS DISTINCT FROM NEW.user_type THEN
    -- Log the role change
    INSERT INTO public.logs (event_type, details)
    VALUES ('user_role_changed', jsonb_build_object(
      'user_id', NEW.id, 
      'old_role', OLD.user_type, 
      'new_role', NEW.user_type
    ));
    
    -- Force refresh of JWT claims by updating auth.users
    UPDATE auth.users SET updated_at = now() WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_user_type_change ON public.profiles;

-- Create the trigger
CREATE TRIGGER on_user_type_change
  AFTER UPDATE ON public.profiles
  FOR EACH ROW
  WHEN (OLD.user_type IS DISTINCT FROM NEW.user_type)
  EXECUTE FUNCTION public.handle_user_type_change();

-- Update RLS policies to use JWT claims
-- First, drop existing policies
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;
DROP POLICY IF EXISTS "users_can_view_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_can_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_can_insert_own_profile" ON profiles;
DROP POLICY IF EXISTS "admins_can_view_all_profiles" ON profiles;
DROP POLICY IF EXISTS "partners_can_view_customer_profiles" ON profiles;
DROP POLICY IF EXISTS "admins_can_update_any_profile" ON profiles;
DROP POLICY IF EXISTS "admins_can_create_any_profile" ON profiles;

-- Create new policies based on roles
-- All users can view their own profile
CREATE POLICY "users_can_view_own_profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

-- Admin users can view all profiles
CREATE POLICY "admins_can_view_all_profiles"
ON profiles FOR SELECT
USING ((auth.jwt()->>'role')::text = 'admin');

-- Partners can view their own profile and customer profiles
CREATE POLICY "partners_can_view_customer_profiles"
ON profiles FOR SELECT
USING (
  ((auth.jwt()->>'role')::text = 'partner' AND 
   (id = auth.uid() OR (SELECT user_type FROM profiles WHERE id = auth.uid()) = 'customer'))
);

-- Users can update their own profile
CREATE POLICY "users_can_update_own_profile"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- Admin users can update any profile
CREATE POLICY "admins_can_update_any_profile"
ON profiles FOR UPDATE
USING ((auth.jwt()->>'role')::text = 'admin');

-- Users can insert their own profile
CREATE POLICY "users_can_insert_own_profile"
ON profiles FOR INSERT
WITH CHECK (auth.uid() = id);

-- Admin users can create any profile
CREATE POLICY "admins_can_create_any_profile"
ON profiles FOR INSERT
WITH CHECK ((auth.jwt()->>'role')::text = 'admin');

-- Create or replace helper functions for role checking
CREATE OR REPLACE FUNCTION is_admin(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_partner(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'partner'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_driver(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'driver'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_user_role(uid UUID)
RETURNS TEXT AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT user_type INTO user_role FROM profiles WHERE id = uid;
  RETURN COALESCE(user_role, 'customer');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comment explaining how to configure the JWT claim function in Supabase
COMMENT ON FUNCTION auth.jwt_claim IS 
'Custom JWT claim function for RBAC. 
This function adds the user_role claim to the JWT token based on the user_type in the profiles table.
No additional configuration is needed in the Supabase Dashboard as this function uses the standard auth.jwt_claim hook.';
