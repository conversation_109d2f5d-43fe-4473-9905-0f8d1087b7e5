-- First, check if the function exists and drop it
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_proc 
    WHERE proname = 'jwt_claim' 
    AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'auth')
  ) THEN
    DROP FUNCTION auth.jwt_claim(jsonb);
  END IF;
END $$;

-- Now create the function with the correct parameter name
CREATE OR REPLACE FUNCTION auth.jwt_claim(payload JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := payload->>'sub';
  user_role TEXT;
BEGIN
  -- Get the user's role from the profiles table
  SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Create a logs table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    event_type TEXT NOT NULL,
    details JSONB DEFAULT '{}'::jsonb
  );
  
  -- Log the JWT claim generation for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_claim_generated', jsonb_build_object('user_id', user_id, 'role', user_role));
  
  -- Return the custom claims to be added to the JWT
  RETURN jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO service_role;

-- Update the profiles table to ensure user_type is always set
ALTER TABLE public.profiles 
  ALTER COLUMN user_type SET DEFAULT 'customer';

-- Add driver role to valid user types if not already included
DO $$
BEGIN
  -- Check if the constraint exists
  IF EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'profiles_user_type_check' 
    AND conrelid = 'public.profiles'::regclass
  ) THEN
    -- Drop the existing constraint
    ALTER TABLE public.profiles DROP CONSTRAINT profiles_user_type_check;
  END IF;
  
  -- Add the new constraint with all roles
  ALTER TABLE public.profiles 
    ADD CONSTRAINT profiles_user_type_check 
    CHECK (user_type IN ('customer', 'partner', 'admin', 'driver'));
END $$;

-- Create a trigger to update JWT claims when user_type changes
CREATE OR REPLACE FUNCTION public.handle_user_type_change()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.user_type IS DISTINCT FROM NEW.user_type THEN
    -- Log the role change
    INSERT INTO public.logs (event_type, details)
    VALUES ('user_role_changed', jsonb_build_object(
      'user_id', NEW.id, 
      'old_role', OLD.user_type, 
      'new_role', NEW.user_type
    ));
    
    -- Force refresh of JWT claims by updating auth.users
    UPDATE auth.users SET updated_at = now() WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_user_type_change ON public.profiles;

-- Create the trigger
CREATE TRIGGER on_user_type_change
  AFTER UPDATE ON public.profiles
  FOR EACH ROW
  WHEN (OLD.user_type IS DISTINCT FROM NEW.user_type)
  EXECUTE FUNCTION public.handle_user_type_change();
