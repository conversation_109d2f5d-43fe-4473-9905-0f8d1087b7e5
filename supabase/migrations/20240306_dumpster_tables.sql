-- Enable PostGIS extension for geographic data types
CREATE EXTENSION IF NOT EXISTS postgis;

-- Create partners table
CREATE TABLE IF NOT EXISTS partners (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES profiles(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  company_name TEXT NOT NULL,
  business_license TEXT,
  tax_id TEXT,
  service_areas JSONB DEFAULT '[]'::jsonb,
  rating DECIMAL DEFAULT 0,
  total_ratings INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT false,
  verification_date TIMESTAMP WITH TIME ZONE,
  status TEXT DEFAULT 'pending' CHECK (status IN ('active', 'pending', 'suspended')),
  commission_rate DECIMAL DEFAULT 0,
  bank_details JSONB,
  contact_person TEXT,
  contact_email TEXT,
  contact_phone TEXT,
  whatsapp_business_id TEXT
);

-- Create waste_tags table
CREATE TABLE IF NOT EXISTS waste_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create waste_types table (renamed from dumpster_types in CONTEXT.md)
CREATE TABLE IF NOT EXISTS waste_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  image_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create waste_type_tags junction table
CREATE TABLE IF NOT EXISTS waste_type_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  waste_type_id UUID NOT NULL REFERENCES waste_types(id) ON DELETE CASCADE,
  waste_tag_id UUID NOT NULL REFERENCES waste_tags(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(waste_type_id, waste_tag_id)
);

-- Create dumpster_types table (as per CONTEXT.md)
CREATE TABLE IF NOT EXISTS dumpster_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  dimensions JSONB DEFAULT '{}'::jsonb,
  capacity DECIMAL, -- cubic yards
  max_weight DECIMAL, -- tons
  image_url TEXT,
  suitable_waste_types TEXT[] DEFAULT '{}'::text[],
  notes TEXT
);

-- Create dumpster_sizes table (for user app)
CREATE TABLE IF NOT EXISTS dumpster_sizes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dumpster_type_id UUID REFERENCES dumpster_types(id),
  name TEXT NOT NULL UNIQUE,
  volume_cubic_yards NUMERIC NOT NULL,
  max_weight_pounds NUMERIC NOT NULL,
  length NUMERIC NOT NULL,
  width NUMERIC NOT NULL,
  height NUMERIC NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dumpsters table (inventory)
CREATE TABLE IF NOT EXISTS dumpsters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  partner_id UUID REFERENCES partners(id),
  dumpster_type_id UUID REFERENCES dumpster_types(id),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  image_url TEXT NOT NULL,
  serial_number TEXT,
  status TEXT DEFAULT 'available' CHECK (status IN ('available', 'in_use', 'maintenance', 'decommissioned')),
  current_location JSONB DEFAULT '{"latitude": 0, "longitude": 0}'::jsonb,
  last_maintenance_date TIMESTAMP WITH TIME ZONE,
  next_maintenance_date TIMESTAMP WITH TIME ZONE,
  purchase_date TIMESTAMP WITH TIME ZONE,
  purchase_cost DECIMAL,
  lifetime_revenue DECIMAL DEFAULT 0,
  lifetime_orders INTEGER DEFAULT 0,
  price_per_day NUMERIC NOT NULL,
  is_available BOOLEAN DEFAULT TRUE,
  next_available_date DATE,
  rating NUMERIC,
  review_count INTEGER DEFAULT 0
);

-- Create pricing_plans table
CREATE TABLE IF NOT EXISTS pricing_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  partner_id UUID REFERENCES partners(id),
  dumpster_type_id UUID REFERENCES dumpster_types(id),
  name TEXT NOT NULL,
  base_price DECIMAL NOT NULL,
  daily_rate DECIMAL NOT NULL,
  minimum_days INTEGER DEFAULT 1,
  maximum_days INTEGER,
  overage_fee_per_day DECIMAL,
  weight_limit DECIMAL,
  overage_fee_per_ton DECIMAL,
  is_active BOOLEAN DEFAULT true,
  special_instructions TEXT
);

-- Create dumpster_images table for additional images
CREATE TABLE IF NOT EXISTS dumpster_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dumpster_id UUID NOT NULL REFERENCES dumpsters(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dumpster_features table
CREATE TABLE IF NOT EXISTS dumpster_features (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dumpster_id UUID NOT NULL REFERENCES dumpsters(id) ON DELETE CASCADE,
  feature TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(dumpster_id, feature)
);

-- Create dumpster_size_options junction table
CREATE TABLE IF NOT EXISTS dumpster_size_options (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dumpster_id UUID NOT NULL REFERENCES dumpsters(id) ON DELETE CASCADE,
  dumpster_size_id UUID NOT NULL REFERENCES dumpster_sizes(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(dumpster_id, dumpster_size_id)
);

-- Create dumpster_waste_types junction table
CREATE TABLE IF NOT EXISTS dumpster_waste_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dumpster_id UUID NOT NULL REFERENCES dumpsters(id) ON DELETE CASCADE,
  waste_type_id UUID NOT NULL REFERENCES waste_types(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(dumpster_id, waste_type_id)
);

-- Create discounts table
CREATE TABLE IF NOT EXISTS discounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  partner_id UUID REFERENCES partners(id),
  code TEXT NOT NULL UNIQUE,
  description TEXT,
  discount_type TEXT CHECK (discount_type IN ('percentage', 'fixed_amount')),
  discount_value DECIMAL NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  max_uses INTEGER,
  current_uses INTEGER DEFAULT 0,
  min_order_value DECIMAL DEFAULT 0,
  applicable_dumpster_types UUID[],
  is_active BOOLEAN DEFAULT true
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  customer_id UUID REFERENCES profiles(id),
  partner_id UUID REFERENCES partners(id),
  dumpster_id UUID REFERENCES dumpsters(id),
  pricing_plan_id UUID REFERENCES pricing_plans(id),
  discount_id UUID REFERENCES discounts(id),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'delivered', 'in_use', 'pickup_scheduled', 'completed', 'cancelled')),
  delivery_address TEXT NOT NULL,
  delivery_coordinates JSONB DEFAULT '{"latitude": 0, "longitude": 0}'::jsonb,
  delivery_instructions TEXT,
  delivery_date TIMESTAMP WITH TIME ZONE,
  scheduled_pickup_date TIMESTAMP WITH TIME ZONE,
  actual_pickup_date TIMESTAMP WITH TIME ZONE,
  rental_duration_days INTEGER,
  waste_type TEXT,
  estimated_weight DECIMAL,
  actual_weight DECIMAL,
  base_price DECIMAL,
  discount_amount DECIMAL DEFAULT 0,
  additional_fees JSONB DEFAULT '{}'::jsonb,
  tax_amount DECIMAL DEFAULT 0,
  total_amount DECIMAL,
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'partial', 'paid', 'refunded')),
  payment_method TEXT CHECK (payment_method IN ('credit_card', 'cash', 'bank_transfer')),
  special_requirements TEXT,
  ai_conversation_id UUID
);

-- Create order_status_history table
CREATE TABLE IF NOT EXISTS order_status_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  status TEXT NOT NULL,
  notes TEXT,
  updated_by UUID REFERENCES profiles(id)
);

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  amount DECIMAL NOT NULL,
  payment_method TEXT CHECK (payment_method IN ('credit_card', 'cash', 'bank_transfer')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  transaction_id TEXT,
  payment_date TIMESTAMP WITH TIME ZONE,
  payment_details JSONB DEFAULT '{}'::jsonb,
  refund_amount DECIMAL DEFAULT 0,
  refund_date TIMESTAMP WITH TIME ZONE,
  refund_reason TEXT
);

-- Create reviews table
CREATE TABLE IF NOT EXISTS reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  order_id UUID REFERENCES orders(id) UNIQUE,
  customer_id UUID REFERENCES profiles(id),
  partner_id UUID REFERENCES partners(id),
  rating INTEGER CHECK (rating BETWEEN 1 AND 5),
  review_text TEXT,
  partner_response TEXT,
  partner_response_date TIMESTAMP WITH TIME ZONE,
  is_published BOOLEAN DEFAULT true
);

-- Create ai_conversations table
CREATE TABLE IF NOT EXISTS ai_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES profiles(id),
  conversation_data JSONB DEFAULT '{}'::jsonb,
  intent_classification TEXT,
  extracted_requirements JSONB DEFAULT '{}'::jsonb,
  recommended_dumpster_types UUID[],
  conversation_summary TEXT
);

-- Create ai_feedback table
CREATE TABLE IF NOT EXISTS ai_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  conversation_id UUID REFERENCES ai_conversations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id),
  feedback_type TEXT CHECK (feedback_type IN ('helpful', 'not_helpful', 'suggestion')),
  feedback_text TEXT,
  is_processed BOOLEAN DEFAULT false
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES profiles(id),
  type TEXT CHECK (type IN ('order_update', 'payment', 'system', 'promotion')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  related_entity_type TEXT,
  related_entity_id UUID,
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMP WITH TIME ZONE,
  delivery_channels TEXT[],
  delivery_status JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_partners_status ON partners(status);
CREATE INDEX IF NOT EXISTS idx_partners_profile_id ON partners(profile_id);

CREATE INDEX IF NOT EXISTS idx_waste_types_name ON waste_types(name);

CREATE INDEX IF NOT EXISTS idx_dumpster_types_name ON dumpster_types(name);

CREATE INDEX IF NOT EXISTS idx_dumpsters_partner_id ON dumpsters(partner_id);
CREATE INDEX IF NOT EXISTS idx_dumpsters_status ON dumpsters(status);
CREATE INDEX IF NOT EXISTS idx_dumpsters_name ON dumpsters(name);
CREATE INDEX IF NOT EXISTS idx_dumpsters_price ON dumpsters(price_per_day);
CREATE INDEX IF NOT EXISTS idx_dumpsters_availability ON dumpsters(is_available);
CREATE INDEX IF NOT EXISTS idx_dumpsters_rating ON dumpsters(rating);

CREATE INDEX IF NOT EXISTS idx_pricing_plans_partner_id ON pricing_plans(partner_id);
CREATE INDEX IF NOT EXISTS idx_pricing_plans_dumpster_type_id ON pricing_plans(dumpster_type_id);
CREATE INDEX IF NOT EXISTS idx_pricing_plans_is_active ON pricing_plans(is_active);

CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_partner_id ON orders(partner_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_delivery_date ON orders(delivery_date);

CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);

CREATE INDEX IF NOT EXISTS idx_reviews_partner_id ON reviews(partner_id);
CREATE INDEX IF NOT EXISTS idx_reviews_customer_id ON reviews(customer_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);

-- Enable RLS on all tables
ALTER TABLE partners ENABLE ROW LEVEL SECURITY;
ALTER TABLE waste_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE waste_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE waste_type_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE dumpster_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE dumpster_sizes ENABLE ROW LEVEL SECURITY;
ALTER TABLE dumpsters ENABLE ROW LEVEL SECURITY;
ALTER TABLE pricing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE dumpster_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE dumpster_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE dumpster_size_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE dumpster_waste_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE discounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY; 