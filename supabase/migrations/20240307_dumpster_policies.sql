-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow public read access for profiles" ON profiles;
DROP POLICY IF EXISTS "Allow users to manage their own profile" ON profiles;
DROP POLICY IF EXISTS "Allow public read access for partners" ON profiles;
DROP POLICY IF EXISTS "Allow public read access for waste_tags" ON waste_tags;
DROP POLICY IF EXISTS "Allow public read access for waste_types" ON waste_types;
DROP POLICY IF EXISTS "Allow public read access for waste_type_tags" ON waste_type_tags;
DROP POLICY IF EXISTS "Allow public read access for dumpster_types" ON dumpster_types;
DROP POLICY IF EXISTS "Allow public read access for dumpster_sizes" ON dumpster_sizes;
DROP POLICY IF EXISTS "Allow public read access for dumpsters" ON dumpsters;
DROP POLICY IF EXISTS "Allow public read access for pricing_plans" ON pricing_plans;
DROP POLICY IF EXISTS "Allow public read access for dumpster_images" ON dumpster_images;
DROP POLICY IF EXISTS "Allow public read access for dumpster_features" ON dumpster_features;
DROP POLICY IF EXISTS "Allow public read access for dumpster_size_options" ON dumpster_size_options;
DROP POLICY IF EXISTS "Allow public read access for dumpster_waste_types" ON dumpster_waste_types;
DROP POLICY IF EXISTS "Allow public read access for discounts" ON discounts;
DROP POLICY IF EXISTS "Allow public read access for reviews" ON reviews;
DROP POLICY IF EXISTS "Allow users to manage their own addresses" ON addresses;
DROP POLICY IF EXISTS "Allow admin access to all addresses" ON addresses;
DROP POLICY IF EXISTS "Allow users to manage their own settings" ON user_settings;
DROP POLICY IF EXISTS "Allow admin access to all user_settings" ON user_settings;

-- Create policies for public read access
CREATE POLICY "Allow public read access for profiles" ON profiles FOR SELECT USING (true);
CREATE POLICY "Allow public read access for partners" ON profiles FOR SELECT USING (true);
CREATE POLICY "Allow public read access for waste_tags" ON waste_tags FOR SELECT USING (true);
CREATE POLICY "Allow public read access for waste_types" ON waste_types FOR SELECT USING (true);
CREATE POLICY "Allow public read access for waste_type_tags" ON waste_type_tags FOR SELECT USING (true);
CREATE POLICY "Allow public read access for dumpster_types" ON dumpster_types FOR SELECT USING (true);
CREATE POLICY "Allow public read access for dumpster_sizes" ON dumpster_sizes FOR SELECT USING (true);
CREATE POLICY "Allow public read access for dumpsters" ON dumpsters FOR SELECT USING (true);
CREATE POLICY "Allow public read access for pricing_plans" ON pricing_plans FOR SELECT USING (true);
CREATE POLICY "Allow public read access for dumpster_images" ON dumpster_images FOR SELECT USING (true);
CREATE POLICY "Allow public read access for dumpster_features" ON dumpster_features FOR SELECT USING (true);
CREATE POLICY "Allow public read access for dumpster_size_options" ON dumpster_size_options FOR SELECT USING (true);
CREATE POLICY "Allow public read access for dumpster_waste_types" ON dumpster_waste_types FOR SELECT USING (true);
CREATE POLICY "Allow public read access for discounts" ON discounts FOR SELECT USING (true);
CREATE POLICY "Allow public read access for reviews" ON reviews FOR SELECT USING (is_published = true);

-- Drop existing user policies if they exist
DROP POLICY IF EXISTS "Allow users to manage their own profile" ON profiles;
DROP POLICY IF EXISTS "Allow users to view their own orders" ON orders;
DROP POLICY IF EXISTS "Allow users to create orders" ON orders;
DROP POLICY IF EXISTS "Allow users to update their own orders" ON orders;
DROP POLICY IF EXISTS "Allow users to view their own payments" ON payments;
DROP POLICY IF EXISTS "Allow users to view their own reviews" ON reviews;
DROP POLICY IF EXISTS "Allow users to create reviews" ON reviews;
DROP POLICY IF EXISTS "Allow users to update their own reviews" ON reviews;
DROP POLICY IF EXISTS "Allow users to view their own notifications" ON notifications;
DROP POLICY IF EXISTS "Allow users to update their own notifications" ON notifications;
DROP POLICY IF EXISTS "Allow users to view their own AI conversations" ON ai_conversations;
DROP POLICY IF EXISTS "Allow users to create AI feedback" ON ai_feedback;

-- Create policies for user access to own data
CREATE POLICY "Allow users to manage their own profile" ON profiles FOR ALL USING (auth.uid() = id);
CREATE POLICY "Allow users to view their own orders" ON orders FOR SELECT USING (auth.uid() = customer_id);
CREATE POLICY "Allow users to create orders" ON orders FOR INSERT WITH CHECK (auth.uid() = customer_id);
CREATE POLICY "Allow users to update their own orders" ON orders FOR UPDATE USING (auth.uid() = customer_id AND status IN ('pending', 'confirmed'));
CREATE POLICY "Allow users to view their own payments" ON payments FOR SELECT USING (auth.uid() = (SELECT customer_id FROM orders WHERE id = order_id));
CREATE POLICY "Allow users to view their own reviews" ON reviews FOR SELECT USING (auth.uid() = customer_id);
CREATE POLICY "Allow users to create reviews" ON reviews FOR INSERT WITH CHECK (auth.uid() = customer_id);
CREATE POLICY "Allow users to update their own reviews" ON reviews FOR UPDATE USING (auth.uid() = customer_id);
CREATE POLICY "Allow users to view their own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Allow users to update their own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Allow users to view their own AI conversations" ON ai_conversations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Allow users to create AI feedback" ON ai_feedback FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Allow users to manage their own addresses" ON addresses FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Allow users to manage their own settings" ON user_settings FOR ALL USING (auth.uid() = user_id);

-- Drop existing partner policies if they exist
DROP POLICY IF EXISTS "Allow partners to view their own data" ON partners;
DROP POLICY IF EXISTS "Allow partners to update their own data" ON partners;
DROP POLICY IF EXISTS "Allow partners to view their own dumpsters" ON dumpsters;
DROP POLICY IF EXISTS "Allow partners to manage their own dumpsters" ON dumpsters;
DROP POLICY IF EXISTS "Allow partners to manage their own pricing plans" ON pricing_plans;
DROP POLICY IF EXISTS "Allow partners to view their own orders" ON orders;
DROP POLICY IF EXISTS "Allow partners to update their own orders" ON orders;
DROP POLICY IF EXISTS "Allow partners to view their own payments" ON payments;
DROP POLICY IF EXISTS "Allow partners to respond to reviews" ON reviews;

-- Create policies for partner access
CREATE POLICY "Allow partners to view their own data" ON partners FOR SELECT USING (is_partner(auth.uid()) AND auth.uid() = profile_id);
CREATE POLICY "Allow partners to update their own data" ON partners FOR UPDATE USING (is_partner(auth.uid()) AND auth.uid() = profile_id);
CREATE POLICY "Allow partners to view their own dumpsters" ON dumpsters FOR SELECT USING (is_partner(auth.uid()) AND partner_id = (SELECT id FROM partners WHERE profile_id = auth.uid()));
CREATE POLICY "Allow partners to manage their own dumpsters" ON dumpsters FOR ALL USING (is_partner(auth.uid()) AND partner_id = (SELECT id FROM partners WHERE profile_id = auth.uid()));
CREATE POLICY "Allow partners to manage their own pricing plans" ON pricing_plans FOR ALL USING (is_partner(auth.uid()) AND partner_id = (SELECT id FROM partners WHERE profile_id = auth.uid()));
CREATE POLICY "Allow partners to view their own orders" ON orders FOR SELECT USING (is_partner(auth.uid()) AND partner_id = (SELECT id FROM partners WHERE profile_id = auth.uid()));
CREATE POLICY "Allow partners to update their own orders" ON orders FOR UPDATE USING (is_partner(auth.uid()) AND partner_id = (SELECT id FROM partners WHERE profile_id = auth.uid()));
CREATE POLICY "Allow partners to view their own payments" ON payments FOR SELECT USING (is_partner(auth.uid()) AND (SELECT partner_id FROM orders WHERE id = order_id) = (SELECT id FROM partners WHERE profile_id = auth.uid()));
CREATE POLICY "Allow partners to respond to reviews" ON reviews FOR UPDATE USING (is_partner(auth.uid()) AND partner_id = (SELECT id FROM partners WHERE profile_id = auth.uid()) AND partner_response IS NULL);

-- Drop existing admin policies if they exist
DROP POLICY IF EXISTS "Allow admin access to all profiles" ON profiles;
DROP POLICY IF EXISTS "Allow admin access to all partners" ON partners;
DROP POLICY IF EXISTS "Allow admin access to all waste_tags" ON waste_tags;
DROP POLICY IF EXISTS "Allow admin access to all waste_types" ON waste_types;
DROP POLICY IF EXISTS "Allow admin access to all waste_type_tags" ON waste_type_tags;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_types" ON dumpster_types;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_sizes" ON dumpster_sizes;
DROP POLICY IF EXISTS "Allow admin access to all dumpsters" ON dumpsters;
DROP POLICY IF EXISTS "Allow admin access to all pricing_plans" ON pricing_plans;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_images" ON dumpster_images;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_features" ON dumpster_features;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_size_options" ON dumpster_size_options;
DROP POLICY IF EXISTS "Allow admin access to all dumpster_waste_types" ON dumpster_waste_types;
DROP POLICY IF EXISTS "Allow admin access to all discounts" ON discounts;
DROP POLICY IF EXISTS "Allow admin access to all orders" ON orders;
DROP POLICY IF EXISTS "Allow admin access to all order_status_history" ON order_status_history;
DROP POLICY IF EXISTS "Allow admin access to all payments" ON payments;
DROP POLICY IF EXISTS "Allow admin access to all reviews" ON reviews;
DROP POLICY IF EXISTS "Allow admin access to all ai_conversations" ON ai_conversations;
DROP POLICY IF EXISTS "Allow admin access to all ai_feedback" ON ai_feedback;
DROP POLICY IF EXISTS "Allow admin access to all notifications" ON notifications;
DROP POLICY IF EXISTS "Allow admin access to all addresses" ON addresses;
DROP POLICY IF EXISTS "Allow admin access to all user_settings" ON user_settings;

-- Create policies for admin access
CREATE POLICY "Allow admin access to all profiles" ON profiles FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all partners" ON partners FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all waste_tags" ON waste_tags FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all waste_types" ON waste_types FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all waste_type_tags" ON waste_type_tags FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all dumpster_types" ON dumpster_types FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all dumpster_sizes" ON dumpster_sizes FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all dumpsters" ON dumpsters FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all pricing_plans" ON pricing_plans FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all dumpster_images" ON dumpster_images FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all dumpster_features" ON dumpster_features FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all dumpster_size_options" ON dumpster_size_options FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all dumpster_waste_types" ON dumpster_waste_types FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all discounts" ON discounts FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all orders" ON orders FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all order_status_history" ON order_status_history FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all payments" ON payments FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all reviews" ON reviews FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all ai_conversations" ON ai_conversations FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all ai_feedback" ON ai_feedback FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all notifications" ON notifications FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all addresses" ON addresses FOR ALL USING (is_admin(auth.uid()));
CREATE POLICY "Allow admin access to all user_settings" ON user_settings FOR ALL USING (is_admin(auth.uid())); 