# Login Timeout Fix Instructions

This guide will help you fix the email login timeout issues in the app.

## Step 1: Add SQL Functions to Supabase

Copy these SQL functions to your Supabase SQL Editor and execute them:

```sql
-- Function to get profile by any auth ID (main ID, email auth ID, or phone auth ID)
CREATE OR REPLACE FUNCTION public.get_profile_by_auth_id(auth_id uuid)
RETURNS TABLE (
  id uuid,
  email text,
  phone text,
  full_name text,
  avatar_url text,
  user_type text,
  created_at timestamptz,
  updated_at timestamptz,
  oauth_google_id text,
  email_auth_id uuid,
  phone_auth_id uuid
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT p.*
  FROM public.profiles p
  WHERE p.id = auth_id
     OR p.email_auth_id = auth_id
     OR p.phone_auth_id = auth_id;
END;
$$;

-- Function to link an auth ID to a profile
CREATE OR REPLACE FUNCTION public.link_auth_id(
  profile_id uuid,
  new_auth_id uuid,
  auth_type text -- 'email', 'phone', or 'unknown'
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  success boolean := false;
BEGIN
  -- Determine the auth type if 'unknown' is provided
  IF auth_type = 'unknown' THEN
    -- Check if this auth ID is associated with an email
    IF EXISTS (SELECT 1 FROM auth.users WHERE id = new_auth_id AND email IS NOT NULL) THEN
      auth_type := 'email';
    ELSE
      auth_type := 'phone';
    END IF;
  END IF;
  
  -- Update the appropriate auth ID field
  IF auth_type = 'email' THEN
    UPDATE public.profiles
    SET email_auth_id = new_auth_id
    WHERE id = profile_id
    AND (email_auth_id IS NULL OR email_auth_id <> new_auth_id);
    
    GET DIAGNOSTICS success = ROW_COUNT;
  ELSIF auth_type = 'phone' THEN
    UPDATE public.profiles
    SET phone_auth_id = new_auth_id
    WHERE id = profile_id
    AND (phone_auth_id IS NULL OR phone_auth_id <> new_auth_id);
    
    GET DIAGNOSTICS success = ROW_COUNT;
  END IF;
  
  -- Log the linking attempt
  INSERT INTO public.logs (event_type, details)
  VALUES (
    'auth_id_linking', 
    jsonb_build_object(
      'profile_id', profile_id,
      'auth_id', new_auth_id,
      'auth_type', auth_type,
      'success', success,
      'timestamp', now()
    )
  );
  
  RETURN success;
END;
$$;

-- Function to find profile by phone number
CREATE OR REPLACE FUNCTION public.find_profile_by_phone(phone_in text)
RETURNS TABLE (
  id uuid,
  user_type text,
  full_name text,
  email text,
  phone text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT p.id, p.user_type, p.full_name, p.email, p.phone
  FROM public.profiles p
  WHERE p.phone = phone_in;
END;
$$;
```

## Step 2: Fix Auth Context Issues

If you're seeing errors about useAuth being used outside AuthProvider, the app has an architectural issue. Make sure you're not using authentication hooks outside the AuthProvider context in _layout.tsx.

In your app/_layout.tsx, make sure the line:
```typescript
const { session, isAuthenticated, loading, isInitialized } = useAuth();
```
is removed from the RootLayout component, since it's outside the AuthProvider wrapper.

## Step 3: Clear AsyncStorage and Reset App State

If login issues persist, you can use the emergency reset feature by tapping the app logo 5 times, or manually clear the AsyncStorage items:

```typescript
await AsyncStorage.multiRemove([
  '@app:user_role',
  '@app:profile_id', 
  '@app:otp_verification_in_progress',
  '@app:verification_timestamp',
  '@app:verifying_otp_in_progress',
  '@app:last_auth_check',
  '@app:temp_user_type'
]);
await supabase.auth.signOut({ scope: 'global' });
```

## Step 4: Add Timeout Handling to API Calls

The key issue was likely RPC function calls that don't exist or timeout, causing login to get stuck. We've updated the code to:

1. Add timeouts to API calls
2. Handle missing functions gracefully
3. Reduce timeout durations
4. Provide fallback mechanisms

## Step 5: Test

After implementing these changes, test the login flow with both email and phone to ensure everything is working correctly.

If issues persist, check the console logs for specific error messages, particularly around the SQL functions or auth state handling.

## Note

The branch that was working (commit 7329924bfba07535876ab3b97db47374b96cd6a1) likely had simpler authentication logic that wasn't dependent on the custom SQL functions. The new version has more robust identity linking but requires the SQL functions to be present. 