# Real-Time Updates and Push Notifications

## Overview
This document outlines the setup and troubleshooting steps for implementing real-time updates and push notifications in the Dumpster on Demand user app.

## Setup

### Webhooks and Edge Functions
- **Webhooks** are configured to trigger on updates to the `orders` and `driver_assignments` tables.
- **Edge Function** `notify-order-update` is deployed to handle these webhook events and send push notifications.

### Push Notifications
- **Expo Push Notifications** are used to notify users of order updates.
- **APNs Credentials** are required for iOS devices and must be uploaded to Expo using EAS CLI.

## Troubleshooting

### Push Notification Issues
- **Error:** "Could not find APNs credentials for com.dumpstersondemand.userapp"
  - **Solution:** Generate an APNs Key from the Apple Developer account and upload it using `eas credentials`.

### Real-Time Updates
- **Issue:** Real-time UI updates not reflecting changes.
  - **Possible Cause:** Type mismatches between `app/orders.tsx` and `useOrderRealtime.ts`.
  - **Solution:** Ensure consistent type definitions across files.

## Next Steps
- Obtain a paid Apple Developer account to configure APNs credentials.
- Verify real-time updates by checking the console logs for subscription events.

## References
- [Expo Push Notifications](https://docs.expo.dev/push-notifications/overview/)
- [Supabase Realtime](https://supabase.com/docs/guides/realtime) 