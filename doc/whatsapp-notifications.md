# WhatsApp Notifications

## Overview

The Dumpster on Demand platform uses WhatsApp notifications to communicate with partners when new orders are created. This document outlines how the WhatsApp notification system works and how to configure it.

## Architecture

### Components

1. **Database Trigger**: A PostgreSQL trigger that fires when a new order is inserted into the `orders` table
2. **Edge Function**: A Supabase Edge Function that processes the trigger event and sends the WhatsApp message
3. **Authenticasa API**: The third-party service used to send WhatsApp messages

### Flow

1. User creates a new order in the app
2. Order is inserted into the `orders` table
3. Database trigger fires and calls the `send-partner-whatsapp` Edge Function
4. Edge Function retrieves partner details from the database
5. Edge Function formats the WhatsApp message with order details
6. Edge Function sends the message to the partner's phone number using the Authenticasa API

## Configuration

### Authenticasa API

The WhatsApp notification feature requires an Authenticasa API key and a registered WhatsApp Business account. To configure:

1. Register for an Authenticasa account at [https://portal.authentica.sa](https://portal.authentica.sa)
2. Set up a WhatsApp Business account and connect it to Authenticasa
3. Create a WhatsApp message template in the Authenticasa portal
4. Get your API key from the Authenticasa portal
5. Set the API key as an environment variable in Supabase:

```bash
supabase secrets set AUTHENTICASA_API_KEY=your_authenticasa_api_key
```

### WhatsApp Templates

WhatsApp requires pre-approved message templates for business communications. The current implementation uses a template with the following variables:

- Order ID
- Dumpster name
- Delivery address
- Delivery date
- Waste type

To modify the template:

1. Create a new template in the Authenticasa portal
2. Update the `template_id` in the Edge Function code
3. Redeploy the Edge Function

## Testing

To test the WhatsApp notification system:

1. Create a test order with a valid partner ID
2. Check the Edge Function logs to verify the message was sent
3. Verify the partner received the WhatsApp message

## Troubleshooting

Common issues and solutions:

### Message Not Sent

- Check the Edge Function logs for errors
- Verify the partner's phone number is in the correct format
- Ensure the Authenticasa API key is set correctly
- Verify the WhatsApp template ID is valid and approved

### Invalid Phone Number Format

The Edge Function includes a helper function to format phone numbers to the international format required by WhatsApp. If you're having issues with specific phone number formats, you may need to update the `formatPhoneNumber` function in the Edge Function code.

### Rate Limiting

Authenticasa and WhatsApp have rate limits on message sending. If you're sending a large volume of messages, you may need to implement rate limiting in the Edge Function.

## Future Enhancements

Planned enhancements for the WhatsApp notification system:

1. Add support for interactive buttons in WhatsApp messages
2. Implement two-way communication to allow partners to respond via WhatsApp
3. Add support for sending images and documents
4. Implement message delivery tracking and retry logic
