# Simplified Login Fix

Based on commit `7329924bfba07535876ab3b97db47374b96cd6a1`, here's a straightforward approach to fix the login issues without relying on complex RBAC functions.

## The Core Issue

The current implementation is trying to use custom SQL functions (`link_auth_id`, `get_profile_by_auth_id`, `find_profile_by_phone`) that don't exist in your database, causing timeouts and login failures.

## The Simple Solution

1. **Remove RPC Function Dependencies**

   Instead of calling these functions, we'll use direct database queries:

   ```typescript
   // INSTEAD OF THIS (which fails if function doesn't exist):
   const { data, error } = await supabase.rpc('link_auth_id', {
     profile_id: existingProfile.id,
     new_auth_id: verifyData.user.id,
     auth_type: 'phone'
   });
   
   // USE THIS (direct update that always works):
   const { error } = await supabase
     .from('profiles')
     .update({ phone_auth_id: authId })
     .eq('id', profileId);
   ```

2. **Simplify Authentication Flow**

   During login, simply:
   - Find the profile by email/phone
   - Store the profile ID in AsyncStorage and user metadata
   - Link the auth ID to the profile with a direct update

3. **Fix Timeout Handling**

   - Reduce timeouts from 15s to 5s for API operations
   - Add proper cleanup when timeouts happen

## Implementation Steps

1. **Email Login Fix** (app/(auth)/login.v2.tsx):
   ```typescript
   // Look up profile by email
   const { data: profile } = await supabase
     .from('profiles')
     .select('id, user_type')
     .eq('email', email.toLowerCase().trim())
     .single();
     
   if (profile) {
     // Store in AsyncStorage for immediate use
     await AsyncStorage.setItem('@app:profile_id', profile.id);
     await AsyncStorage.setItem('@app:user_role', profile.user_type);
     
     // Link this auth ID to the profile after authentication
     const { data } = await supabase.auth.signInWithPassword({ email, password });
     if (data?.user) {
       // Simple direct update to link the auth ID
       await supabase
         .from('profiles')
         .update({ email_auth_id: data.user.id })
         .eq('id', profile.id);
         
       // Update user metadata
       await supabase.auth.updateUser({
         data: { 
           profile_id: profile.id,
           user_type: profile.user_type
         }
       });
     }
   }
   ```

2. **Phone Login Fix** (app/(auth)/verify-otp.tsx):
   ```typescript
   // Simple direct query for profile by phone
   const { data: profiles } = await supabase
     .from('profiles')
     .select('id, user_type')
     .eq('phone', cleanPhoneNumber);
     
   if (profiles?.[0]) {
     const profile = profiles[0];
     
     // Store in AsyncStorage
     await AsyncStorage.setItem('@app:profile_id', profile.id);
     await AsyncStorage.setItem('@app:user_role', profile.user_type);
     
     // After OTP verification, link the auth ID
     const { data: verifyData } = await supabase.auth.verifyOtp({
       phone: phoneNumber,
       token: otp,
       type: 'sms',
     });
     
     if (verifyData?.user) {
       // Direct update to link auth ID
       await supabase
         .from('profiles')
         .update({ phone_auth_id: verifyData.user.id })
         .eq('id', profile.id);
         
       // Update user metadata
       await supabase.auth.updateUser({
         data: { 
           profile_id: profile.id,
           user_type: profile.user_type
         }
       });
     }
   }
   ```

3. **AuthContext Changes**:
   ```typescript
   // In session initialization, check if we have a profile ID 
   // in metadata or AsyncStorage and use that
   const profileId = session?.user?.user_metadata?.profile_id || 
                    await AsyncStorage.getItem('@app:profile_id');
                    
   if (profileId) {
     // Direct query to get profile details if needed
     const { data: profile } = await supabase
       .from('profiles')
       .select('*')
       .eq('id', profileId)
       .single();
       
     if (profile) {
       // Ensure storage and metadata are in sync
       await AsyncStorage.setItem('@app:profile_id', profile.id);
       await AsyncStorage.setItem('@app:user_role', profile.user_type);
     }
   }
   ```

## Why This Works

1. **No Dependency on Custom Functions**: Only uses standard Supabase features
2. **Direct Database Access**: Doesn't rely on RPC calls that can fail
3. **Simpler Error Paths**: Fewer places where things can go wrong
4. **Faster Operations**: Direct database queries typically complete in <1 second
5. **Still Maintains ID Linking**: We still link auth IDs to profiles but do it directly

This approach gives you the benefits of identity linking without the complexity and failure points of custom SQL functions. 