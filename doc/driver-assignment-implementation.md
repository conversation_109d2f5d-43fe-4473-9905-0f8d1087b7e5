# Driver Assignment Implementation for Dumpster Partner Portal

## Overview

This document explains how we implemented the driver assignment functionality in the Dumpster Partner Portal, focusing on how assigned driver information is displayed in the Order Details component. This implementation can be adapted for the user mobile app where users only need to view assigned drivers, not assign them.

## Database Structure

### Tables Involved

1. **orders**: Stores order information
2. **drivers**: Stores driver information
3. **driver_assignments**: Links drivers to orders
4. **vehicles**: Stores vehicle information
5. **profiles**: Stores user profile information

### Key Relationships

- `driver_assignments.driver_id` → `drivers.id`
- `driver_assignments.order_id` → `orders.id`
- `driver_assignments.vehicle_id` → `vehicles.id`
- `drivers.profile_id` → `profiles.id`

### Important Database Configurations

1. **Default ID Generation**: The `driver_assignments` table has a default value of `gen_random_uuid()` for the `id` column:
   ```sql
   ALTER TABLE driver_assignments ALTER COLUMN id SET DEFAULT gen_random_uuid()
   ```

2. **Row Level Security (RLS) Policies**: We created RLS policies to control access to the `driver_assignments` table:
   ```sql
   CREATE POLICY "Partners can insert driver assignments" ON public.driver_assignments
   FOR INSERT
   TO authenticated
   WITH CHECK (
     EXISTS (
       SELECT 1 FROM profiles p
       JOIN partners pa ON p.id = pa.profile_id
       JOIN orders o ON pa.id = o.partner_id
       WHERE (p.email_auth_id = auth.uid() OR p.phone_auth_id = auth.uid())
       AND o.id = order_id
     )
   );
   ```

## Frontend Implementation

### Order Details Component

The Order Details component displays the assigned driver's information:

```jsx
{/* Driver Information */}
{order.driver && (
  <div className="rounded-lg border border-shark-200 p-4 dark:border-shark-800">
    <div className="flex justify-between items-start mb-4">
      <h3 className="text-lg font-medium text-gray-800 dark:text-white/90">
        Driver Information
      </h3>
    </div>
    <div className="space-y-2">
      <p className="text-sm text-gray-600 dark:text-gray-400">
        <span className="font-medium">Name:</span> {order.driver.name}
      </p>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        <span className="font-medium">Phone:</span> {order.driver.phone}
      </p>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        <span className="font-medium">Vehicle:</span> {order.driver.vehicle}
      </p>
    </div>
  </div>
)}
```

### Data Fetching Logic

The key to displaying driver information is in the `getOrderById` function in the orders service:

```javascript
// Fetch driver details
let driverDetails = null;
try {
  const { data: driverAssignments, error: assignmentError } = await supabase
    .from('driver_assignments')
    .select('driver_id, vehicle_id')
    .eq('order_id', orderData.id)
    .maybeSingle();

  if (assignmentError) throw assignmentError;

  if (driverAssignments) {
    console.log('[OrderService] Found driver assignment:', driverAssignments);
    
    // First, get the driver record to get the profile_id
    const { data: driverData, error: driverError } = await supabase
      .from('drivers')
      .select('profile_id')
      .eq('id', driverAssignments.driver_id)
      .single();
    
    if (driverError) {
      console.error('[OrderService] Error fetching driver:', driverError);
      throw driverError;
    }
    
    console.log('[OrderService] Found driver data:', driverData);
    
    // Now get the profile data using the profile_id
    const { data: driverProfileData, error: driverProfileError } = await supabase
      .from('profiles') 
      .select('id, full_name, phone')
      .eq('id', driverData.profile_id) 
      .single();
         
    if (driverProfileError) {
      console.error('[OrderService] Error fetching driver profile:', driverProfileError);
      throw driverProfileError;
    }
    
    console.log('[OrderService] Found driver profile:', driverProfileData);

    // Get the vehicle data
    const { data: vehicleData, error: vehicleError } = await supabase
      .from('vehicles')
      .select('*')
      .eq('id', driverAssignments.vehicle_id)
      .single();

    if (vehicleError) {
      console.error('[OrderService] Error fetching vehicle:', vehicleError);
      throw vehicleError;
    }
    
    console.log('[OrderService] Found vehicle data:', vehicleData);
       
    driverDetails = {
      id: driverAssignments.driver_id,
      name: driverProfileData?.full_name || 'Unknown Driver',
      phone: driverProfileData?.phone || 'No Phone',
      vehicle: vehicleData ? `Vehicle #${vehicleData.vehicle_number || 'Unknown'} (${vehicleData.license_plate || 'No Plate'})` : 'Unknown Vehicle',
    };
    
    console.log('[OrderService] Created driver details:', driverDetails);
  }
} catch (driverFetchError) {
  console.error("[OrderService] Error fetching driver/vehicle details:", driverFetchError);
}
```

## Key Implementation Details

1. **Multi-Step Data Fetching**:
   - First, fetch the driver assignment for the order
   - Then, fetch the driver record to get the profile_id
   - Next, fetch the profile data using the profile_id
   - Finally, fetch the vehicle data

2. **Data Transformation**:
   - Transform the raw data into a format suitable for the UI
   - Create a `driverDetails` object with name, phone, and vehicle information

3. **Error Handling**:
   - Implement comprehensive error handling at each step
   - Log errors to help with debugging

4. **Conditional Rendering**:
   - Only display the driver information section if `order.driver` exists

## Adapting for User Mobile App

For the user mobile app where users only need to view assigned drivers:

1. **Simplified UI**:
   - Remove the action buttons (Replace, Unassign)
   - Focus on displaying driver information clearly

2. **Read-Only RLS Policies**:
   - Create RLS policies that allow users to read driver assignments for their orders
   - Example:
     ```sql
     CREATE POLICY "Users can view driver assignments for their orders" ON public.driver_assignments
     FOR SELECT
     TO authenticated
     USING (
       EXISTS (
         SELECT 1 FROM orders o
         WHERE o.id = order_id
         AND o.customer_id = auth.uid()
       )
     );
     ```

3. **Optimized Data Fetching**:
   - Consider using a single query with joins to reduce the number of API calls
   - Example:
     ```javascript
     const { data, error } = await supabase
       .from('orders')
       .select(`
         id,
         status,
         driver_assignments (
           driver:drivers (
             id,
             profile:profiles (
               full_name,
               phone
             )
           ),
           vehicle:vehicles (
             vehicle_number,
             license_plate
           )
         )
       `)
       .eq('id', orderId)
       .single();
     ```

4. **Real-Time Updates**:
   - Implement Supabase real-time subscriptions to update the UI when driver assignments change
   - Example:
     ```javascript
     const subscription = supabase
       .from(`driver_assignments:order_id=eq.${orderId}`)
       .on('*', (payload) => {
         // Update the UI with the new driver assignment
       })
       .subscribe();
     ```

## Conclusion

This implementation provides a robust solution for displaying assigned driver information in the Order Details component. By following the multi-step data fetching approach and implementing proper error handling, we ensure that users always see accurate and up-to-date driver information.

For the user mobile app, focus on simplifying the UI and optimizing data fetching to provide a smooth user experience. Consider implementing real-time updates to keep users informed about changes to their driver assignments.
