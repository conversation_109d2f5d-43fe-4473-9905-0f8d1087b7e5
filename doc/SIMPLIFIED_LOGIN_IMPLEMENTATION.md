# Simplified Login Implementation

Based on our analysis of commit `7329924bfba07535876ab3b97db47374b96cd6a1`, here's how to fix the login system without relying on complex RBAC functions:

## What's Been Fixed

We've simplified the authentication system to avoid dependency on custom SQL functions like `link_auth_id`, `get_profile_by_auth_id`, and `find_profile_by_phone`. Instead, we're using direct database queries which:

1. Are faster (typically under 1 second)
2. Don't fail when functions don't exist
3. Provide simpler error handling
4. Still maintain identity linking between auth IDs and profiles

## Implementation Details

### 1. Email Login (`login.v2.tsx`)

The email login flow now:
- Looks up the profile directly by email
- Stores the profile ID in AsyncStorage immediately
- Authenticates with Supabase
- Links the auth ID to the profile with a direct database update
- Updates user metadata with the profile ID

```typescript
// Direct database query instead of RPC function
const { data: profile } = await supabase
  .from('profiles')
  .select('id, user_type')
  .eq('email', email)
  .single();

// Store ID before authentication completes
await AsyncStorage.setItem('@app:profile_id', profile.id);

// After authentication, link the auth ID directly
await supabase
  .from('profiles')
  .update({ email_auth_id: user.id })
  .eq('id', profile.id);
```

### 2. Phone Verification (`verify-otp.tsx` and `login.v2.tsx`)

The phone login flow now:
- Queries the profile directly by phone number
- Stores the profile ID in AsyncStorage
- Verifies the OTP
- Links the auth ID to the profile with a direct database update
- Updates user metadata

```typescript
// Direct query instead of RPC function
const { data: profiles } = await supabase
  .from('profiles')
  .select('id, user_type')
  .eq('phone', phoneNumber);

// Direct update instead of RPC function
await supabase
  .from('profiles')
  .update({ phone_auth_id: verifyData.user.id })
  .eq('id', profile.id);
```

### 3. Auth Context Initialization

In `AuthContext.tsx`, we now:
- Check for profile ID in both metadata and AsyncStorage
- Use direct queries to get profile details
- Ensure storage and metadata stay in sync

### 4. Timeout Handling

We've also:
- Reduced timeouts from 15s to 5s for API operations
- Added proper cleanup when timeouts happen
- Simplified error paths

## Why This Works Better

1. **Direct Database Access**: Simple queries don't fail when functions are missing
2. **Predictable Performance**: Direct queries typically complete in under 1 second
3. **Simpler Error Handling**: Fewer places where things can go wrong
4. **Same Identity Linking**: We still link auth IDs to profiles, just more directly
5. **Automatic Recovery**: The system can recover from partial login states

## Testing

After implementation, verify both login paths:

1. Email login should complete within 3 seconds
2. Phone OTP verification should work seamlessly

If you encounter any issues, use the emergency reset feature by tapping the app logo 5 times or run the reset script.

## Next Steps

This approach provides a clean way forward without needing to maintain custom SQL functions. It aligns with the successful implementation in commit `7329924bfba07535876ab3b97db47374b96cd6a1` while preserving the identity linking benefits of the newer approach. 