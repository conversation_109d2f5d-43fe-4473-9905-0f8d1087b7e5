# Dumpsters on Demand

## Project Overview

Dumpsters on Demand is an AI-powered waste management platform that streamlines the dumpster rental process. Our solution connects users with waste management partners through an intuitive interface, enabling seamless dumpster rentals, tracking, and management.

## Technology Stack

### Mobile Application (User App)
- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Navigation**: Expo Router
- **UI Framework**: 
  - React Native Paper
  - NativeWind (TailwindCSS for React Native)
- **Build & Deployment**: Expo EAS
- **Mobile Features**:
  - Push Notifications (Expo Notifications)
  - Native device integrations
  - Cross-platform (iOS & Android)

### Web Application (Partner & Admin Portal)
- **Framework**: React
- **Language**: TypeScript
- **Navigation**: React Router
- **UI Framework**: 
  - TailwindCSS
  - Material-UI/Chakra UI
- **Build System**: Vite/Next.js
- **Deployment**: Vercel/Netlify

### Shared Infrastructure
- **Backend**: Supabase
  - PostgreSQL Database
  - Authentication & Authorization
  - File Storage
- **AI Integration**: OpenAI (ChatGPT)
- **Payment Processing**: Stripe/PayPal
- **Communication**: 
  - WhatsApp Business API
  - SMS APIs
  - Push Notifications

## Application Architecture

### 1. User Application

#### Onboarding Experience
- Three-step guided introduction to the platform
- Authentication options:
  - Phone number with OTP verification
  - Google single sign-on

#### Home Interface
- **Navigation**
  - Order history (top-left)
  - Profile and settings (top-right)
- **Central Experience**
  - AI-personalized welcome message
  - Input methods:
    - Text input (tap to write)
    - Voice commands (hold to speak)

#### Dumpster Selection
The AI analyzes user requirements and presents dumpster options based on:
- Size specifications
- Waste type compatibility
- Optimal use cases
- Partner ratings and reputation
- Pricing (including available discounts)

#### Checkout Process
- Order confirmation with detailed summary
- Payment options:
  - Immediate online payment
  - Cash on delivery (CoD)

#### Order Management
- Real-time order tracking
- Status updates and notifications
- Order history with detailed records

### 2. Partner Portal

#### Authentication & Orientation
- Login via phone (OTP) or email
- Interactive onboarding modal for new partners

#### Dashboard Features
- **Interactive Map**
  - Real-time visualization of all deployed dumpsters
- **Performance Metrics**
  - Active orders counter
  - Revenue tracking
  - Expense monitoring
  - Dumpster inventory status
- **Analytics**
  - 7-day performance graph (customizable timeframes)
- **Order Management Grid**
  - Status indicators (Ongoing, Empty Load)
  - Dumpster specifications
  - Customer information
  - Rental lifecycle status

#### Portal Navigation
- Dashboard overview
- Order management console
- Dumpster inventory control
- Account and settings configuration

#### Business Integration
- WhatsApp business integration for:
  - Order processing
  - Customer communication
  - Operational updates

### 3. Administrative Portal

The Admin Portal extends the Partner Portal with additional capabilities:

- **User Management**
  - Account creation and modification
  - Permission controls
  - Partner onboarding and deactivation
- **System-wide Analytics**
  - Comprehensive transaction reporting
  - User activity monitoring
  - Performance metrics across all partners

## Business Impact

Dumpsters on Demand transforms waste management by:

1. **Simplifying the rental process** for end users
2. **Optimizing operations** for waste management partners
3. **Reducing friction** in the dumpster rental marketplace
4. **Leveraging AI** to match supply with demand efficiently

## Database Schema

### Supabase Tables

#### Authentication Tables (Managed by Supabase Auth)
- **auth.users**: Core user authentication data
- **auth.identities**: External identity providers (Google, etc.)

#### Core Application Tables

##### User Management
- **profiles**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `updated_at`: timestamp
  - `email`: text
  - `phone`: text
  - `full_name`: text
  - `avatar_url`: text
  - `address`: text
  - `city`: text
  - `state`: text
  - `zip_code`: text
  - `preferred_language`: text
  - `notification_preferences`: jsonb
  - `user_type`: text

##### Dumpster Management
- **dumpsters**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `partner_id`: UUID
  - `dumpster_type_id`: UUID
  - `name`: text
  - `description`: text
  - `image_url`: text
  - `status`: text
  - `current_location`: point
  - `last_maintenance_date`: timestamp
  - `next_maintenance_date`: timestamp
  - `purchase_date`: timestamp
  - `purchase_cost`: numeric
  - `lifetime_revenue`: numeric
  - `lifetime_orders`: int4
  - `is_available`: bool

- **dumpster_types**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `name`: text
  - `description`: text
  - `dimensions`: jsonb
  - `capacity`: numeric
  - `max_weight`: numeric
  - `image_url`: text
  - `suitable_waste_types`: text[]
  - `notes`: text

- **waste_types**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `name`: text
  - `description`: text
  - `image_url`: text

##### Orders and Payments
- **orders**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `customer_id`: UUID
  - `partner_id`: UUID
  - `dumpster_id`: UUID
  - `pricing_plan_id`: UUID
  - `discount_id`: UUID
  - `status`: text
  - `delivery_address`: text
  - `delivery_coordinates`: point
  - `delivery_instructions`: text
  - `delivery_date`: timestamp
  - `scheduled_pickup_date`: timestamp
  - `actual_pickup_date`: timestamp
  - `rental_duration_days`: int4
  - `waste_type`: text
  - `estimated_weight`: numeric
  - `actual_weight`: numeric
  - `base_price`: numeric
  - `discount_amount`: numeric
  - `additional_fees`: jsonb
  - `tax_amount`: numeric
  - `total_amount`: numeric
  - `payment_status`: text
  - `payment_method`: text
  - `special_requirements`: text
  - `ai_conversation_id`: UUID

- **payments**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `order_id`: UUID
  - `amount`: numeric
  - `payment_method`: text
  - `status`: text
  - `transaction_id`: text
  - `payment_date`: timestamp
  - `payment_details`: jsonb
  - `refund_amount`: numeric
  - `refund_date`: timestamp
  - `refund_reason`: text

##### Partner Management
- **partners**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `updated_at`: timestamp
  - `profile_id`: UUID
  - `business_license`: text
  - `tax_id`: text
  - `service_areas`: jsonb
  - `rating`: numeric
  - `is_verified`: bool
  - `verification_date`: timestamp
  - `status`: text
  - `commission_rate`: numeric
  - `bank_details`: jsonb
  - `contact_person`: text
  - `contact_email`: text
  - `contact_phone`: text
  - `whatsapp_business_id`: text

##### Reviews and Communication
- **reviews**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `updated_at`: timestamp
  - `order_id`: UUID
  - `partner_id`: UUID
  - `rating`: int4
  - `review_text`: text
  - `partner_response`: text
  - `partner_response_date`: timestamp
  - `is_published`: bool

- **ai_conversations**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `user_id`: UUID
  - `conversation`: text
  - `conversation_data`: jsonb
  - `intent_classification`: text
  - `extracted_requirements`: jsonb
  - `conversation_summary`: text

- **notifications**
  - `id`: UUID (primary key)
  - `created_at`: timestamp
  - `type`: text
  - `title`: text
  - `message`: text
  - `related_entity_type`: text
  - `related_entity_id`: UUID
  - `is_read`: bool
  - `read_at`: timestamp
  - `delivery_channels`: text[]
  - `delivery_status`: jsonb

##### Driver Management & KPI Tracking
- **drivers**
  - `id`: UUID (primary key)
  - `partner_id`: UUID REFERENCES partners(id)
  - `profile_id`: UUID REFERENCES profiles(id)
  - `license_number`: text
  - `license_expiry`: date
  - `status`: text -- active, inactive, suspended
  - `current_vehicle_id`: UUID
  - `created_at`: timestamp
  - `updated_at`: timestamp

- **driver_performance_metrics**
  - `id`: UUID (primary key)
  - `driver_id`: UUID REFERENCES drivers(id)
  - `metric_type`: text -- delivery_efficiency, cash_handling, customer_service, operational_efficiency, safety
  - `metric_name`: text
  - `value`: numeric
  - `target`: numeric
  - `period_start`: timestamp
  - `period_end`: timestamp
  - `created_at`: timestamp

- **driver_cash_tracking**
  - `id`: UUID (primary key)
  - `driver_id`: UUID REFERENCES drivers(id)
  - `order_id`: UUID REFERENCES orders(id)
  - `amount_collected`: numeric
  - `collection_time`: timestamp
  - `reconciliation_time`: timestamp
  - `status`: text -- collected, reconciled, pending
  - `notes`: text
  - `created_at`: timestamp

- **driver_schedules**
  - `id`: UUID (primary key)
  - `driver_id`: UUID REFERENCES drivers(id)
  - `date`: date
  - `shift_start`: time
  - `shift_end`: time
  - `break_start`: time
  - `break_end`: time
  - `mwan_zone`: text
  - `status`: text -- scheduled, completed, cancelled
  - `created_at`: timestamp

- **driver_incidents**
  - `id`: UUID (primary key)
  - `driver_id`: UUID REFERENCES drivers(id)
  - `order_id`: UUID REFERENCES orders(id)
  - `incident_type`: text
  - `description`: text
  - `reported_at`: timestamp
  - `resolution`: text
  - `resolved_at`: timestamp
  - `status`: text -- reported, investigating, resolved
  - `created_at`: timestamp

- **driver_documents**
  - `id`: UUID (primary key)
  - `driver_id`: UUID REFERENCES drivers(id)
  - `document_type`: text -- license, insurance, training_cert
  - `document_url`: text
  - `expiry_date`: date
  - `status`: text -- active, expired, pending
  - `created_at`: timestamp

- **vehicle_assignments**
  - `id`: UUID (primary key)
  - `driver_id`: UUID REFERENCES drivers(id)
  - `vehicle_id`: UUID REFERENCES vehicles(id)
  - `assigned_at`: timestamp
  - `returned_at`: timestamp
  - `status`: text -- active, completed
  - `notes`: text
  - `created_at`: timestamp

- **mwan_rules**
  - `id`: UUID (primary key)
  - `zone_id`: text
  - `day_of_week`: text
  - `start_time`: time
  - `end_time`: time
  - `vehicle_type`: text
  - `operation_type`: text -- delivery, pickup, empty_load
  - `is_active`: boolean
  - `created_at`: timestamp
  - `updated_at`: timestamp

### Database Relationships
- One-to-one relationship between `auth.users` and `profiles`
- One-to-one relationship between `partners` and `profiles` (where user_type = 'partner')
- One-to-many relationship between `partners` and `dumpsters`
- One-to-many relationship between `partners` and `pricing_plans`
- One-to-many relationship between `dumpster_types` and `dumpsters`
- One-to-many relationship between `dumpster_types` and `pricing_plans`
- One-to-many relationship between `profiles` and `orders` (as customers)
- One-to-many relationship between `partners` and `orders`
- One-to-many relationship between `dumpsters` and `orders`
- One-to-many relationship between `orders` and `payments`
- One-to-many relationship between `orders` and `order_status_history`
- One-to-one relationship between `orders` and `reviews`
- One-to-many relationship between `profiles` and `ai_conversations`
- One-to-many relationship between `ai_conversations` and `ai_feedback`
- One-to-many relationship between `profiles` and `notifications`

### Database Indexes
- Index on `profiles.email` and `profiles.phone` for quick lookup
- Index on `dumpsters.status` and `dumpsters.partner_id` for inventory queries
- Index on `orders.customer_id`, `orders.partner_id`, and `orders.status` for filtering
- Spatial index on `dumpsters.current_location` for geographic queries
- Index on `payments.order_id` and `payments.status` for financial reporting
- Index on `notifications.user_id` and `notifications.is_read` for notification center

## Application Folder Structure

### User Application (React Native with Expo)

```
dumpster-user-app/
├── app/                           # Expo Router app directory
│   ├── (auth)/                    # Authentication routes
│   │   ├── login.tsx              # Login screen
│   │   ├── register.tsx           # Registration screen
│   │   └── forgot-password.tsx    # Password recovery
│   ├── (tabs)/                    # Main app tabs
│   │   ├── index.tsx              # Home screen with AI interface
│   │   ├── orders.tsx             # Order tracking and history
│   │   ├── profile.tsx            # User profile management
│   │   └── _layout.tsx            # Tab layout configuration
│   ├── dumpster-selection/        # Dumpster browsing and selection
│   ├── checkout/                  # Order placement and payment
│   ├── order-details/[id].tsx     # Order details screen
│   ├── notifications.tsx          # Notification center
│   └── _layout.tsx                # Root layout
├── src/
│   ├── components/                # Reusable components
│   │   ├── ui/                    # UI components
│   │   ├── forms/                 # Form components
│   │   └── modals/                # Modal components
│   ├── hooks/                     # Custom React hooks
│   ├── services/                  # API and service integrations
│   │   ├── supabase/              # Supabase client and queries
│   │   ├── ai/                    # OpenAI integration
│   │   ├── payments/              # Payment processing
│   │   └── notifications/         # Push notification services
│   ├── utils/                     # Utility functions
│   ├── context/                   # React context providers
│   │   ├── AuthContext.tsx        # Authentication context
│   │   ├── ThemeContext.tsx       # Theme context
│   │   └── OrderContext.tsx       # Order management context
│   ├── types/                     # TypeScript type definitions
│   └── constants/                 # App constants and configuration
├── assets/                        # Static assets
│   ├── images/                    # Image assets
│   ├── fonts/                     # Custom fonts
│   └── icons/                     # App icons
├── app.json                       # Expo configuration
├── babel.config.js                # Babel configuration
├── tailwind.config.js             # TailwindCSS/Nativewind configuration
├── tsconfig.json                  # TypeScript configuration
├── eas.json                       # Expo EAS build configuration
└── package.json                   # Dependencies and scripts
```

### Partner/Admin Portal (React Native Web)

```
dumpster-partner-portal/
├── app/                           # Expo Router app directory
│   ├── (auth)/                    # Authentication routes
│   ├── (dashboard)/               # Dashboard layout
│   │   ├── index.tsx              # Main dashboard
│   │   ├── orders/                # Order management
│   │   ├── inventory/             # Dumpster inventory
│   │   ├── map.tsx                # Interactive map
│   │   ├── analytics.tsx          # Business analytics
│   │   ├── settings/              # Account settings
│   │   └── admin/                 # Admin-only features
│   │       ├── users.tsx          # User administration
│   │       ├── partners.tsx       # Partner administration
│   │       └── system.tsx         # Global system settings
│   └── _layout.tsx                # Root layout
├── src/
│   ├── components/                # Reusable components
│   ├── hooks/                     # Custom React hooks
│   ├── services/                  # API and service integrations
│   ├── utils/                     # Utility functions
│   ├── context/                   # React context providers
│   ├── types/                     # TypeScript type definitions
│   └── constants/                 # App constants and configuration
├── assets/                        # Static assets
├── app.json                       # Expo configuration
├── babel.config.js                # Babel configuration
├── tailwind.config.js             # TailwindCSS/Nativewind configuration
├── tsconfig.json                  # TypeScript configuration
├── eas.json                       # Expo EAS build configuration
└── package.json                   # Dependencies and scripts
```

### Shared Backend (Supabase)

```
supabase/
├── functions/                      # Supabase Edge Functions
│   ├── ai-processing/              # OpenAI request processing
│   ├── notifications/              # Notification delivery
│   ├── order-management/           # Order lifecycle functions
│   ├── payment-processing/         # Payment handling
│   └── webhooks/                   # External service webhooks
├── migrations/                     # Database migrations
│   ├── 00-schema.sql               # Initial schema creation
│   └── [timestamp]-[description].sql  # Subsequent migrations
├── seed/                           # Seed data
│   ├── dumpster-types.sql          # Predefined dumpster types
│   └── admin-user.sql              # Initial admin account
└── triggers/                       # Database triggers
    ├── auth_triggers.sql           # Authentication-related triggers
    ├── order_triggers.sql          # Order-related triggers
    └── notification_triggers.sql   # Notification triggers
```

## AI Capabilities

Our OpenAI (ChatGPT) integration powers several key features:

- **Natural Language Processing**
  - Interprets user requests in conversational language
  - Extracts key requirements from unstructured input
- **Predictive Analytics**
  - Anticipates waste management needs based on context
  - Recommends optimal dumpster solutions
- **Learning System**
  - Improves recommendations through historical data analysis
  - Adapts to regional and seasonal patterns
- **Conversational Interface**
  - Provides human-like interaction for users
  - Handles complex queries with contextual understanding

---

**Notes**:  
- Ensure RTL support for Arabic UI  
- Partner ratings must update in real-time  
- Use FlatList with optimized rendering for order lists
- Ensure dark and light theme support with React Native Paper theming

*Built with React Native, Expo, and React Native Paper for frontend, Supabase for backend services, and OpenAI for intelligent data processing.* 