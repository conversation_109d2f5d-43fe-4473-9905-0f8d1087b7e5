# Dumpster Data Model v2 Migration Guide

This document outlines the migration process from the legacy dumpster data model to the new v2 model.

## Overview

The v2 data model offers several improvements:
- Fully aligned with the updated database schema
- Better multilingual support
- Improved type safety
- Optimized data fetching patterns
- Centralized query key management

## Directory Structure

All v2 code is segregated into its own directories:
- Types: `src/types/v2/`
- Hooks: `src/hooks/v2/`

## Migration Steps for Developers

### 1. Import from v2 Instead of Legacy

Change:
```typescript
import { Dumpster, WasteType } from '@/types/dumpster';
import { useDumpsters, useWasteTypes } from '@/hooks/useDumpsters';
```

To:
```typescript
import { Dumpster, WasteType } from '@/types/v2';
import { useDumpsters, useWasteTypes } from '@/hooks/v2';
```

### 2. Adjust Property Names

The v2 model uses consistent naming conventions:
- English: `nameEn` instead of `name_en` or `name`
- Arabic: `nameAr` instead of `name_ar`
- Price: `pricePerLoad` instead of `pricePerDay`
- Descriptions: Structured as `description.en` and `description.ar`

### 3. Updated Data Structures

Key differences:
- `Dumpster` now has a direct `sizeId` field instead of a sizes array
- `compatibleWasteTypes` contains IDs, with optional populated `wasteTypes` array
- Features are now objects with IDs, names, and icon names
- Additional images are directly in the `additionalImages` array

### 4. Filter Changes

The filter interface has changed:
```typescript
// New filter structure
const filters: DumpsterFilters = {
  sizeIds: ['size-1', 'size-2'],
  wasteTypeIds: ['waste-1'],
  priceRange: { min: 50, max: 200 },
  rating: 4,
  availability: true,
  searchTerm: 'construction'
};
```

### 5. New Capabilities

The v2 model adds:
- `useDumpsterSizes()` - Get all available dumpster sizes
- `useDumpsterFeatures()` - Get all available features
- Real-time updates with Supabase subscriptions
- Detailed recommendation system

## Example Usage

```typescript
import { useDumpsters, useDumpster, useWasteTypes } from '@/hooks/v2';
import { DumpsterFilters } from '@/types/v2';

// Component using v2 hooks
function DumpsterList() {
  const filters: DumpsterFilters = {
    availability: true,
    rating: 4
  };
  
  const { data: dumpsters, isLoading } = useDumpsters(filters);
  
  return (
    <div>
      {isLoading ? (
        <p>Loading dumpsters...</p>
      ) : (
        dumpsters?.map(dumpster => (
          <div key={dumpster.id}>
            <h3>{dumpster.nameEn}</h3>
            <p>{dumpster.description?.en}</p>
            <p>Price: {dumpster.pricePerLoad}</p>
            <p>Dimensions: {dumpster.length}x{dumpster.width}x{dumpster.height}</p>
          </div>
        ))
      )}
    </div>
  );
}
```

## Gradual Migration Strategy

1. Start using v2 in new components
2. Refactor existing components gradually
3. Eventually remove legacy code when all components are migrated

## Data Transformation

If needed during the transition, use this helper to convert between models:

```typescript
import { Dumpster as LegacyDumpster } from '@/types/dumpster';
import { Dumpster as V2Dumpster } from '@/types/v2';

export function convertToV2Dumpster(legacy: LegacyDumpster): V2Dumpster {
  return {
    id: legacy.id,
    nameEn: legacy.name,
    nameAr: legacy.name,
    description: {
      en: legacy.description || '',
      ar: legacy.description || ''
    },
    imageUrl: legacy.imageUrl,
    additionalImages: legacy.additionalImages || [],
    
    // Use first size or defaults
    length: legacy.size?.dimensions?.length || 10,
    width: legacy.size?.dimensions?.width || 8,
    height: legacy.size?.dimensions?.height || 6,
    capacity: legacy.size?.volumeCubicYards || 10,
    maxWeight: legacy.size?.maxWeightPounds || 2000,
    standingArea: (legacy.size?.dimensions?.length || 10) * (legacy.size?.dimensions?.width || 8),
    
    pricePerLoad: legacy.pricePerDay,
    rating: legacy.rating || 4.0,
    reviewCount: legacy.reviewCount || 0,
    isAvailable: legacy.availability?.isAvailable !== undefined ? legacy.availability.isAvailable : true,
    nextAvailableDate: legacy.availability?.nextAvailableDate,
    partnerId: legacy.partnerId,
    
    sizeId: legacy.size?.id || '',
    compatibleWasteTypes: legacy.compatibleWasteTypes || []
  };
}
```

## Rollback Plan

If issues arise, the v2 directories can be removed, and code can continue using the legacy implementation without disruption. 