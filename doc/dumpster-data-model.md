# Dumpster Data Model

This document provides a comprehensive overview of the dumpster data model in the application, explaining the database schema, type definitions, and how data flows through the system.

## Database Schema

The application uses several database tables to store dumpster-related data:

### Core Tables

1. **dumpsters**
   - Primary table for dumpster inventory
   - Contains fields like id, name_en, name_ar, description_en, description_ar, image_url
   - Tracks status, availability, rating, pricing, and partner relationships

2. **dumpster_types**
   - Defines different types/categories of dumpsters
   - Contains multilingual names (name_en, name_ar)
   - Stores default dimensions, capacity, and suitable waste types

3. **dumpster_sizes**
   - Defines standardized dumpster size options
   - Records volume_cubic_yards, max_weight_pounds, dimensions (length, width, height)
   - Links to dumpster_type_id

4. **waste_types**
   - Defines types of waste that can be disposed of
   - Contains multilingual names and descriptions
   - Includes image_url for visual representation

### Relationship Tables

5. **dumpster_waste_types**
   - Junction table linking dumpsters to compatible waste types
   - Contains dumpster_id and waste_type_id

6. **dumpster_features**
   - Stores features associated with specific dumpsters
   - Contains dumpster_id and feature text

7. **dumpster_images**
   - Additional images for dumpsters
   - Contains image_url and sort_order for display sequence

8. **dumpster_size_options**
   - Links dumpsters with available size options
   - Contains dumpster_id and dumpster_size_id

## Type Definitions

The application has two sets of type definitions for dumpsters:

### Legacy Types (`src/types/dumpster.ts`)

```typescript
export interface Dumpster {
  id: string;
  name: string;
  description?: string;
  imageUrl: string;
  additionalImages?: string[];
  sizes: DumpsterSize[];
  compatibleWasteTypes: string[]; // IDs of compatible waste types
  pricePerDay: number;
  availability: {
    isAvailable: boolean;
    nextAvailableDate?: string;
  };
  features?: string[];
  rating?: number;
  reviewCount?: number;
  partnerId?: string;
}

export interface DumpsterSize {
  id: string;
  name: string;
  volumeCubicYards: number;
  maxWeightPounds: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  description?: string;
}

export interface WasteType {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  tags?: string[];
}

export interface DumpsterFilter {
  typeId?: string;
  sizeIds?: string[];
  wasteTypeIds?: string[];
  priceRange?: {
    min?: number;
    max?: number;
  };
  searchTerm?: string;
}
```

### New Types (`src/types/new/dumpster.ts`)

```typescript
export interface Dumpster {
  id: string;
  nameEn: string;
  nameAr: string;
  imageUrl: string;
  partnerLogo?: string;
  length: number;
  width: number;
  height: number;
  pricePerLoad: number;
  rating: number;
  reviewCount: number;
  isAvailable: boolean;
  standingArea: number;
  description?: string;
}

export interface DumpsterSize {
  id: string;
  nameEn: string;
  nameAr: string;
  length: number;
  width: number;
  height: number;
  capacity: number;
}

export interface WasteType {
  id: string;
  name_en: string;
  name_ar: string;
  description_en: string;
  description_ar: string;
  imageUrl: string;
  tags?: string[];
}
```

## Data Flow

### Fetching Dumpster Data

1. **Hooks**:
   - `useDumpsters()`: Primary hook for fetching dumpster list with optional filtering
   - `useDumpster(id)`: Hook for fetching a single dumpster by ID
   - `useWasteTypes()`: Hook for fetching all waste types

2. **API Functions**:
   - `getDumpsters(filters?)`: Fetches dumpsters with optional filtering
   - `getDumpsterById(id)`: Fetches a single dumpster
   - `getWasteTypes()`: Fetches all waste types

### Adapters

Since the application is transitioning from an old data model to a new one, adapter functions are used to convert data:

```typescript
// Convert old dumpster format to new format
export function convertToNewDumpster(oldDumpster: OldDumpster): NewDumpster {
  // Conversion logic here
}

// Convert all dumpsters at once
export function convertAllDumpsters(oldDumpsters: OldDumpster[]): NewDumpster[] {
  return oldDumpsters
    .filter(dumpster => dumpster && dumpster.id)
    .map(convertToNewDumpster);
}
```

## Main Screens and Components

### Screens

1. **Dumpster Selection Screen** (`app/dumpster-selection/index.tsx`)
   - Main screen for browsing and selecting dumpsters
   - Includes AI-powered recommendation feature
   - Allows searching and filtering

2. **Dumpster Detail Screen** (`app/dumpster-selection/[id].tsx`)
   - Displays detailed information about a specific dumpster
   - Shows compatible waste types, dimensions, pricing
   - Enables selection of sizes and proceeding to checkout

3. **All Dumpsters Screen** (`app/all-dumpsters.tsx`)
   - Comprehensive view of all available dumpsters
   - Advanced filtering by waste type, size, rating
   - Sorting options for price, size, and rating

### Components

1. **DumpsterCard** (`src/components/dumpster/DumpsterCard.tsx`)
   - Reusable card component that displays basic dumpster info
   - Shows image, size, rating, price, and features

2. **WasteTypeCard** (`src/components/dumpster/WasteTypeCard.tsx`)
   - Displays waste type information
   - Used for selecting waste types in filters

3. **DumpsterFilters** (`src/components/dumpster/DumpsterFilters.tsx`)
   - Filter controls for dumpster selection
   - Includes waste type, price range, and other filters

## Data Processing Workflow

1. Data is fetched from the database using the `useDumpsters` or `useDumpster` hook
2. If needed, the data is transformed from the old format to the new format using `convertToNewDumpster`
3. The transformed data is passed to components like `DumpsterCard` for display
4. User interactions (filtering, sorting, selection) are handled by respective components
5. Selected dumpster data can be passed to the checkout flow

## Multilingual Support

The application supports both English and Arabic languages:
- Database tables store both `name_en`/`name_ar` and `description_en`/`description_ar`
- Components use the React i18n system to display the appropriate language
- RTL (Right-to-Left) support is available for Arabic language users

## AI Recommendation System

The application includes an AI-powered recommendation system:
- Users can describe their project needs
- AI processes this information and suggests appropriate dumpsters
- Recommendations are scored based on relevance to the user's needs
- Matching logic considers waste types, dumpster size, and availability

This data model provides the foundation for the dumpster rental application, enabling users to search, browse, and select appropriate dumpsters for their waste disposal needs.

## Updated Database Schema (Schema Changes)

The following SQL changes will update the database schema to match the desired data model:

```sql
-- Step 1: Create new features table
CREATE TABLE public.features (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    name_en text NOT NULL,
    name_ar text,
    icon_name text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Step 2: Create junction table for dumpster-feature many-to-many relationship
CREATE TABLE public.dumpster_feature_links (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    dumpster_id uuid NOT NULL REFERENCES public.dumpsters(id) ON DELETE CASCADE,
    feature_id uuid NOT NULL REFERENCES public.features(id) ON DELETE CASCADE,
    created_at timestamp with time zone DEFAULT now(),
    UNIQUE(dumpster_id, feature_id)
);

-- Step 3: Migrate existing feature data
-- First, insert unique features into the new features table
INSERT INTO public.features (name_en)
SELECT DISTINCT feature 
FROM public.dumpster_features;

-- Then link dumpsters to these features
INSERT INTO public.dumpster_feature_links (dumpster_id, feature_id)
SELECT df.dumpster_id, f.id
FROM public.dumpster_features df
JOIN public.features f ON f.name_en = df.feature;

-- Step 4: Modify dumpster_sizes table to remove dumpster_type_id
ALTER TABLE public.dumpster_sizes
DROP CONSTRAINT IF EXISTS dumpster_sizes_dumpster_type_id_fkey,
DROP COLUMN dumpster_type_id;

-- Step 5: Add direct size_id to dumpsters table
ALTER TABLE public.dumpsters
ADD COLUMN size_id uuid REFERENCES public.dumpster_sizes(id);

-- Step 6: Migrate existing size data (keep first size for each dumpster)
UPDATE public.dumpsters d
SET size_id = (
    SELECT dumpster_size_id 
    FROM public.dumpster_size_options 
    WHERE dumpster_id = d.id 
    LIMIT 1
);

-- Step 7: Remove dumpster_type_id from dumpsters
ALTER TABLE public.dumpsters
DROP CONSTRAINT IF EXISTS dumpsters_dumpster_type_id_fkey,
DROP COLUMN dumpster_type_id;

-- Step 8: Drop the junction table that's no longer needed
DROP TABLE IF EXISTS public.dumpster_size_options;

-- Step 9: Create appropriate indexes
CREATE INDEX idx_dumpsters_size_id ON public.dumpsters(size_id);
CREATE INDEX idx_dumpster_feature_links_dumpster_id ON public.dumpster_feature_links(dumpster_id);
CREATE INDEX idx_dumpster_feature_links_feature_id ON public.dumpster_feature_links(feature_id);

-- Step 10: Add RLS policies for the new tables
ALTER TABLE public.features ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow public read access for features" ON public.features FOR SELECT USING (true);
CREATE POLICY "Allow admin access to all features" ON public.features USING (public.authorize('admin'::text));

ALTER TABLE public.dumpster_feature_links ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow public read access for dumpster_feature_links" ON public.dumpster_feature_links FOR SELECT USING (true);
CREATE POLICY "Allow admin access to all dumpster_feature_links" ON public.dumpster_feature_links USING (public.authorize('admin'::text));
```

### Key Changes to Data Model

1. **Dumpster to Size** (One-to-Many):
   - Changed from many-to-many to one-to-many relationship
   - Each dumpster has exactly one size (size_id)
   - One size can be used by many dumpsters

2. **Dumpster to Feature** (Many-to-Many):
   - Created proper `features` table for reusable feature definitions
   - Added junction table `dumpster_feature_links` for many-to-many relationship
   - Replaces direct text storage in `dumpster_features`

3. **Removed Dumpster Types**:
   - Removed `dumpster_type_id` from both `dumpsters` and `dumpster_sizes`
   - Simplified the model by focusing on sizes rather than types

4. **Maintained Existing Relationships**:
   - Dumpster to Waste Type (Many-to-Many)
   - Dumpster to Image (One-to-Many)
   - Waste Type to Waste Tag (Many-to-Many)

This updated structure simplifies the relationships while maintaining the necessary functionality, making the data model more intuitive and aligned with the business requirements.

## Implementation Verification

The schema changes have been successfully implemented in the database, as verified by the schema dump (schema_dump-v10.sql). The following changes were confirmed:

### New Tables Created ✅
- `features` table was created with the proper structure for reusable feature definitions
- `dumpster_feature_links` junction table was created to link dumpsters to features

### New Indexes Created ✅
- `idx_dumpster_feature_links_dumpster_id` - For efficient querying by dumpster
- `idx_dumpster_feature_links_feature_id` - For efficient querying by feature
- `idx_dumpsters_size_id` - For efficient querying of dumpsters by size

### Structure Changes ✅
- `dumpster_sizes` table no longer has `dumpster_type_id` field
- `dumpsters` table now has a direct `size_id` reference
- `dumpster_type_id` has been removed from the `dumpsters` table

### Removed Tables ✅
- The `dumpster_size_options` junction table was successfully dropped
- All necessary data was migrated before removal

### Row-Level Security Policies ✅
- RLS policies properly implemented on the new tables
- Public read access granted to maintain consistent security model
- Admin access properly configured

The new schema now correctly represents the following relationships:
1. **Dumpster to Size**: One-to-many (direct reference via `size_id`)
2. **Dumpster to Feature**: Many-to-many (through `dumpster_feature_links`)
3. **Dumpster to Waste Type**: Many-to-many (through `dumpster_waste_types`)
4. **Dumpster to Image**: One-to-many (through `dumpster_images`)
5. **Waste Type to Tag**: Many-to-many (through `waste_type_tags`)

This implementation successfully completes the planned data model changes, providing a more intuitive structure while maintaining all necessary functionality.

## Code Implementation

The following code changes have been implemented to support the updated database schema:

### 1. Updated `transformDumpsterData` Function

The function has been updated to handle the new direct dumpster-to-size relationship and feature structure:

```typescript
// Transform raw database dumpster into our Dumpster interface
function transformDumpsterData(dumpster: any, locale: string = 'en'): Dumpster {
  // Extract waste type IDs from the relationship data
  const wasteTypeIds = dumpster.dumpster_waste_types
    ? dumpster.dumpster_waste_types
        .filter((dwt: any) => dwt && dwt.waste_type_id)
        .map((dwt: any) => dwt.waste_type_id)
    : [];
  
  // Extract feature IDs from the new dumpster_feature_links
  const featureIds = dumpster.dumpster_feature_links
    ? dumpster.dumpster_feature_links
        .filter((link: any) => link && link.feature_id)
        .map((link: any) => link.feature_id)
    : [];
  
  // Use locale-appropriate name and description
  const name = locale === 'ar' && dumpster.name_ar 
    ? dumpster.name_ar 
    : dumpster.name_en || 'Unnamed Dumpster';
    
  const description = locale === 'ar' && dumpster.description_ar 
    ? dumpster.description_ar 
    : dumpster.description_en || 'No description available';
  
  // Use image URL directly from database
  const imageUrl = typeof dumpster.image_url === 'string' && dumpster.image_url
    ? dumpster.image_url 
    : 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';
  
  // Default size when no size is associated
  const defaultSize: DumpsterSize = {
    id: `size-${dumpster.id}-default`,
    name: "Standard Size",
    volumeCubicYards: 10,
    maxWeightPounds: 2000,
    dimensions: { length: 10, width: 8, height: 6 },
    description: "Standard size dumpster"
  };
  
  const transformedDumpster: Dumpster = {
    id: dumpster.id,
    name: name,
    description: description,
    imageUrl: imageUrl,
    size: defaultSize, // Note: The actual size object is set separately after fetching the size data
    compatibleWasteTypes: wasteTypeIds,
    pricePerDay: dumpster.price_per_load || 75,
    availability: {
      isAvailable: dumpster.is_available !== undefined ? dumpster.is_available : true,
      nextAvailableDate: dumpster.next_available_date,
    },
    featureIds: featureIds,
    rating: dumpster.rating || 4.0,
    reviewCount: dumpster.review_count || 0,
    partnerId: dumpster.partner_id,
  };
  
  return transformedDumpster;
}
```

### 2. Updated `getDumpsterById` Function

The function has been modified to handle the direct size_id reference:

```typescript
export async function getDumpsterById(id: string): Promise<Dumpster | null> {
  if (!id) {
    console.error('No dumpster ID provided');
    return null;
  }
  
  if (shouldUseMockData(id)) {
    return mockDumpsters.find(d => d.id === id) || null;
  }

  try {
    // First fetch the dumpster basic info
    const { data, error } = await supabase
      .from('dumpsters')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url,
        price_per_load,
        rating,
        review_count,
        is_available,
        next_available_date,
        size_id,
        partner_id,
        dumpster_waste_types (
          waste_type_id
        ),
        dumpster_feature_links (
          feature_id
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      console.error('Error fetching dumpster:', error);
      throw error;
    }
    
    if (!data || !data.id) {
      console.error('Invalid dumpster data returned from database:', data);
      return null;
    }

    // Create the initial dumpster object without size
    const dumpster = transformDumpsterData(data);

    // Fetch the dumpster size if size_id exists
    if (data.size_id) {
      const { data: sizeData, error: sizeError } = await supabase
        .from('dumpster_sizes')
        .select('*')
        .eq('id', data.size_id)
        .single();
      
      if (sizeError && sizeError.code !== 'PGRST116') {
        console.error('Error fetching dumpster size:', sizeError);
        throw sizeError;
      }

      // Update the dumpster size if we found one
      if (sizeData) {
        dumpster.size = {
          id: sizeData.id,
          name: sizeData.name,
          volumeCubicYards: sizeData.volume_cubic_yards,
          maxWeightPounds: sizeData.max_weight_pounds,
          dimensions: {
            length: sizeData.length,
            width: sizeData.width,
            height: sizeData.height
          },
          description: sizeData.description
        };
      }
    }

    return dumpster;
  } catch (error) {
    console.error('Error in getDumpsterById:', error);
    return mockDumpsters.find(d => d.id === id) || null;
  }
}
```

### 3. Updated `convertToNewDumpster` Function

The adapter function has been updated to handle a single size object instead of a sizes array:

```typescript
export function convertToNewDumpster(oldDumpster: OldDumpster | null): NewDumpster {
  // Return default dumpster if null
  if (!oldDumpster || !oldDumpster.id) {
    return {
      id: 'default-id',
      nameEn: 'Unnamed Dumpster',
      nameAr: 'حاوية بدون اسم',
      imageUrl: getSafeImageUrl(),
      partnerLogo: '/assets/images/partners/default-logo.png',
      length: 10,
      width: 8,
      height: 6,
      pricePerLoad: 75,
      rating: 4.0,
      reviewCount: 0,
      isAvailable: true,
      standingArea: 80,
      description: 'No description available',
    };
  }

  // Get dimensions from the single size object
  const dimensions = getSafeDimensions(oldDumpster.size);
  
  // Get availability with fallbacks
  const isAvailable = oldDumpster.availability?.isAvailable !== undefined 
    ? oldDumpster.availability.isAvailable 
    : true;

  // Calculate standing area
  const standingArea = dimensions.length * dimensions.width;

  // Get a safe image URL
  const imageUrl = getSafeImageUrl(oldDumpster.imageUrl);

  return {
    id: oldDumpster.id,
    nameEn: oldDumpster.name || 'Unnamed Dumpster',
    nameAr: oldDumpster.name || 'حاوية بدون اسم', // Default to English if Arabic not available
    imageUrl: imageUrl,
    partnerLogo: '/assets/images/partners/default-logo.png', // Default logo
    length: dimensions.length,
    width: dimensions.width,
    height: dimensions.height,
    pricePerLoad: oldDumpster.pricePerDay || 75,
    rating: oldDumpster.rating || 4.0,
    reviewCount: oldDumpster.reviewCount || 0,
    isAvailable,
    standingArea,
    description: oldDumpster.description || '',
  };
}
```

### 4. Enhanced `convertAllDumpsters` Function

The function has been improved with better error handling:

```typescript
export function convertAllDumpsters(oldDumpsters: OldDumpster[]): NewDumpster[] {
  if (!oldDumpsters || !Array.isArray(oldDumpsters)) {
    console.error('Invalid dumpsters array passed to convertAllDumpsters:', oldDumpsters);
    return [];
  }
  
  // Filter out any null or undefined dumpsters first
  const validDumpsters = oldDumpsters.filter(dumpster => dumpster && dumpster.id);
  
  // Convert each dumpster with error handling
  return validDumpsters.map(dumpster => {
    try {
      return convertToNewDumpster(dumpster);
    } catch (error) {
      console.error(`Error converting dumpster ${dumpster.id}:`, error);
      // Return a default dumpster with the original ID if possible
      return {
        id: dumpster.id || 'error-id',
        nameEn: 'Error Loading Dumpster',
        nameAr: 'خطأ في تحميل الحاوية',
        imageUrl: getSafeImageUrl(),
        partnerLogo: '/assets/images/partners/default-logo.png',
        length: 10,
        width: 8,
        height: 6,
        pricePerLoad: 75,
        rating: 4.0,
        reviewCount: 0,
        isAvailable: false,
        standingArea: 80,
        description: 'There was an error loading this dumpster data.',
      };
    }
  });
}
```

### 5. Updated `getWasteTypes` Function

The function has been updated to return waste types in the new format with multilingual support:

```typescript
export async function getWasteTypes(): Promise<WasteType[]> {
  try {
    const { data, error } = await supabase
      .from('waste_types')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url
      `);

    if (error) throw error;

    return data.map(item => ({
      id: item.id,
      name_en: item.name_en,
      name_ar: item.name_ar,
      description_en: item.description_en,
      description_ar: item.description_ar,
      imageUrl: item.image_url,
    }));
  } catch (error) {
    console.error('Error fetching waste types:', error);
    // Convert mock waste types to new format
    return mockWasteTypes.map(wt => ({
      id: wt.id,
      name_en: wt.name,
      name_ar: wt.name, // Use English name as fallback for Arabic in mocks
      description_en: wt.description,
      description_ar: wt.description, // Use English description as fallback
      imageUrl: wt.imageUrl,
    }));
  }
}
```

### 6. Updated Sorting Logic in UI Components

The sorting logic in UI components has been updated to work with direct dimensions:

```typescript
// Apply Sorting
switch (sortOption) {
  case 'rating_desc':
    filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    break;
  case 'size_desc':
    // Use dimensions directly from the new dumpster model
    filtered.sort((a, b) => {
      const volumeDescA = (a.length || 0) * (a.width || 0) * (a.height || 0);
      const volumeDescB = (b.length || 0) * (b.width || 0) * (b.height || 0);
      return volumeDescB - volumeDescA;
    });
    break;
  case 'size_asc':
    // Use dimensions directly from the new dumpster model
    filtered.sort((a, b) => {
      const volumeAscA = (a.length || 0) * (a.width || 0) * (a.height || 0);
      const volumeAscB = (b.length || 0) * (b.width || 0) * (b.height || 0);
      return volumeAscA - volumeAscB;
    });
    break;
  case 'default':
  default:
    break;
}
```

### 7. Error Handling for Dumpster Conversion

Better error handling has been added to the dumpster conversion process:

```typescript
// Convert dumpsters when original data loads
useEffect(() => {
  try {
    if (originalDumpsters) {
      const converted = convertAllDumpsters(originalDumpsters);
      
      if (!converted || converted.length === 0) {
        console.error('No dumpsters were converted from original data');
        // Set empty arrays to avoid null reference errors
        setAllDumpsters([]);
        setDisplayedDumpsters([]);
        return;
      }
      
      setAllDumpsters(converted);
      setDisplayedDumpsters(converted);
    }
  } catch (error) {
    console.error('Error converting dumpsters:', error);
    // Set empty arrays to avoid null reference errors
    setAllDumpsters([]);
    setDisplayedDumpsters([]);
  }
}, [originalDumpsters]);
```

These code changes fully implement the database schema updates described in the schema changes section, ensuring compatibility with the new data structure while maintaining backward compatibility during the transition phase.
