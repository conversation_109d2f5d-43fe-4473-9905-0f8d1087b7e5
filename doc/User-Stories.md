---

# Dumpster-on-Demand App - User Stories & Flow

## 1. Introduction
This document outlines the detailed **user stories and app flow** for the **Dumpster-on-Demand** app, covering the **User App**, **Partner Portal**, and **Admin Portal**. It provides structured guidance for developers to implement the app efficiently.

---

## 2. User Application

### 2.1 Onboarding & Authentication

#### **User Story:**
- As a **new user**, I want to go through a **three-step onboarding**, so I can understand how the app works.
- As a **user**, I want to **sign up or log in** using **phone number (OTP) or Google authentication**, so I can securely access my account.

#### **Flow:**
1. User installs and opens the app.
2. The **onboarding flow** introduces the app in three steps:
   - Step 1: **How it works** (Brief overview of the rental process)
   - Step 2: **How to place an order** (Using AI and selecting the right dumpster)
   - Step 3: **How to track orders** (Order history and notifications)
3. User selects **Sign up / Log in**:
   - Enters **phone number** → **Receives OTP** → Logs in.
   - OR logs in using **Google authentication**.
4. User lands on the **Welcome Screen**.

---

### 2.2 Home & Dumpster Selection

#### **User Story:**
- As a **logged-in user**, I want to see a **welcome screen** with a **personalized AI prompt**, so I can quickly start ordering a dumpster.
- As a **user**, I want to **tap to type or hold to speak**, so I can easily describe my needs.

#### **Flow:**
1. The **Welcome Screen** has:
   - **History/Orders tab (top-left)**
   - **Profile/Settings tab (top-right)**
   - **AI onboarding message** (e.g., "Welcome [username], I want to revamp my bathroom...") 
   - **Action Button:** "Tap to write or hold to speak"
2. User **describes** the waste disposal need.
3. AI **analyzes** the input and suggests the best dumpster options:
   - Size, waste type, best use cases.
   - Partner name, rating, and **discounted price (if applicable)**.
4. User selects a dumpster and proceeds to **checkout**.

---

### 2.3 Checkout & Payment

#### **User Story:**
- As a **user**, I want to review my order details before confirming, so I can ensure I selected the right dumpster.
- As a **user**, I want to choose between **cash on delivery (CoD) or 30% upfront digital payment**, so I can pay conveniently.

#### **Flow:**
1. User reviews the **order summary**.
2. User selects a **payment method**:
   - **Pay Now (30% advance payment via Stripe/PayPal).**
   - **Cash on Delivery (Full payment upon delivery).**
3. User **confirms the order**.
4. Order is **created and appears in Order History**.
5. User gets a **notification with estimated delivery time**.

---

### 2.4 Order Tracking & Reload (Empty Load)

#### **User Story:**
- As a **user**, I want to track my **ongoing orders**, so I know when my dumpster will be delivered.
- As a **user**, I want to request an **empty load** when my dumpster is full, so I can continue using it.

#### **Flow:**
1. User goes to **Order History**.
2. User sees **active orders** with real-time status updates.
3. When the dumpster is full:
   - User selects **Request Empty Load**.
   - A request is sent to **partners** for scheduling an emptying service.
4. Partner confirms the service, and user gets notified.

---

## 3. Partner Portal

### 3.1 Onboarding & Authentication

#### **User Story:**
- As a **partner**, I want to receive a **notification when a new order is placed**, so I can fulfill it.
- As a **partner**, I want to **log in using phone OTP or email**, so I can access my dashboard securely.

#### **Flow:**
1. Partner **receives SMS/WhatsApp notification** for a new order.
2. Partner logs in via **phone OTP or email**.
3. Partner sees a **one-time onboarding modal** explaining:
   - Order handling.
   - WhatsApp business integration.
   - Payment and earnings tracking.
4. Partner lands on **Dashboard**.

### 3.2 Partner Dashboard & Order Management

#### **User Story:**
- As a **partner**, I want to see an **interactive map** with all rented dumpsters, so I can track my assets.
- As a **partner**, I want to have **performance insights** (orders, income, expenses, dumpsters in use), so I can optimize my operations.

#### **Flow:**
1. Dashboard shows:
   - **Map** of active dumpster locations.
   - **4 State Boxes**: Orders, Income, Expenses, Dumpsters.
   - **7-day performance graph**.
   - **Orders List** with status (Ongoing, Empty Load).
   - Customer details (Name, Phone, Dumpster Type, Status).
2. Partner can **accept or decline** orders.
3. Partner uses **WhatsApp integration** for direct communication.

### 3.3 Driver Management & Operations

#### **User Story:**
- As a **partner**, I want to **manage my drivers** and assign them to orders, so I can efficiently handle deliveries.
- As a **partner**, I want to **track driver performance** and cash handling, so I can ensure quality service.

#### **Flow:**
1. Partner can:
   - Add/edit driver profiles with required documentation
   - Assign drivers to orders
   - Monitor driver performance metrics
   - Track cash collections and reconciliations
2. Partner receives notifications for:
   - Driver document expiration
   - Cash collection status
   - Performance alerts
   - Incident reports

### 3.4 Driver Portal

#### **User Story:**
- As a **driver**, I want to see my **assigned orders** for the day, so I can plan my deliveries.
- As a **driver**, I want to **update order status** and **confirm cash collection**, so I can track my work.
- As a **driver**, I want to **report issues** during delivery, so I can get help when needed.

#### **Flow:**
1. Driver logs in to mobile web app
2. Views dashboard with:
   - Today's assigned orders
   - MWAN time windows
   - Cash collection status
   - Performance metrics
3. For each order:
   - Views delivery details and instructions
   - Updates status (picked up, in transit, delivered)
   - Confirms cash collection
   - Reports issues if any
4. End of day:
   - Reconciles cash collections
   - Reviews completed deliveries
   - Checks next day's schedule

---

## 4. Admin Portal

### 4.1 Features & User Management

#### **User Story:**
- As an **admin**, I want to **monitor all user and partner activities**, so I can manage the platform effectively.
- As an **admin**, I want to **add, edit, or deactivate users and partners**, so I can maintain control.

#### **Flow:**
1. Admin logs in and lands on the **dashboard** (similar to partner's dashboard but with **additional controls**).
2. Admin can:
   - Manage **users & partners** (add/edit/remove accounts).
   - Oversee **platform analytics**.
   - View **order trends and financial reports**.
   - Modify **commission rates and pricing rules**.

---

## 5. Summary & Next Steps

### ✅ Key Functionalities:
- **AI-powered dumpster selection.**
- **Seamless user authentication.**
- **Real-time order tracking.**
- **Partner dashboard with map & analytics.**
- **WhatsApp/SMS notifications.**
- **Admin control for user & partner management.**

### 🔜 Next Steps:
- Implement UI screens based on this document.
- Integrate AI to enhance user experience.
- Finalize payment and notification systems.

---

This document provides a structured guide for **developers and product teams** to build the Dumpster-on-Demand platform efficiently. 🚀
