# Custom RBAC Implementation Plan

## Current Issues
- JWT custom claims are causing authentication problems
- Complex hook functions are failing to update JWT tokens correctly
- Role validation dependent on JWT claims which are unreliable

## Solution Overview
We'll implement a lightweight RBAC approach by:
1. Removing dependency on JWT claims entirely
2. Using the database `profiles.user_type` field for role validation
3. Keeping Supabase Auth for authentication but handling roles separately
4. Adding client-side role checks without middleware

## Implementation Steps

### 1. Remove JWT Claim Dependencies
- Modify AuthContext to stop relying on JWT claims for role information
- Remove references to app_metadata and JWT claims for role validation
- Keep existing Supabase authentication flow

### 2. Implement Direct Database Role Checks
- After successful login, query the `profiles` table directly to get user_type
- Store user_type in local storage/context for client-side use
- Validate against this stored role instead of checking JWT claims

### 3. Update Authentication Guard
- Update existing AuthenticationGuard to validate roles directly from database/context
- Keep sign-in and sign-up components as is, only modify role checking
- Use simple error handling for mismatched roles (customer-only validation)

### 4. Non-Customer Error Handling
- Enhance error messages when non-customers try to sign in
- Direct users to use different credentials if their account is associated with other roles

### 5. Minimal UI Component Changes
- No UI changes needed for login/register screens
- Only modify internal validation logic and error messages

## Benefits
- Simplifies role management
- Removes dependency on problematic JWT hooks
- More reliable role-based access control
- Easier to debug and maintain

## Files to Modify
1. `context/AuthContext.tsx` - Update role validation
2. `app/_layout.tsx` - Modify authentication guard logic 
3. `app/(auth)/login.v2.tsx` - Enhance error handling for non-customers

## Files to Keep Unchanged
1. `app/(auth)/register.tsx` - No changes needed
2. `app/(auth)/_layout.tsx` - No changes needed 
3. `app/(auth)/language-select.tsx` - No changes needed

## Testing Plan
1. Test login with customer accounts
2. Test attempted logins with non-customer accounts
3. Verify error handling presents appropriate messages
4. Test role persistence across app restarts 