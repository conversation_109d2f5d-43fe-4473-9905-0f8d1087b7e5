FORMAT: 1A
HOST: https://api.authentica.sa/api/v1

# Authentica API Documentation

Authentica provides secure and flexible customer authentication through **OTP verification** via SMS, and WhatsApp. Additionally, it supports **Nafath** integration for identity verification.

Need to send Notification SMS messages to your clients? Just register your own SMS sender on Authentica and start messaging.

Our APIs are designed for easy integration, offering clear endpoints to help you implement robust authentication solutions in your applications.
* This manual is to describe Authentica API

* This manual is for developers 


## OTP Verification Workflow

To implement OTP verification, follow these steps:

###### 1- Send OTP
    * Use the Send OTP endpoint to generate and send an OTP to your customer via SMS or WhatsApp with a lot of options like: 
    * Custom OTP.
    * SMS delivery via your own sender name registered with Authentica.
    * Control fallback channel, templates with ids 1 and 2 will be used for the fallback OTP based on the language of the main template you used in the request.

###### 2- Verify OTP
    * Use the Verify OTP endpoint to validate the OTP entered by your customer.

## Nafath Verification Workflow

To implement Nafath verification, follow these steps:

###### 1- Initiate Verify By Nafath Process
    * Use the Verify By Nafath endpoint to initiate the verification process. This will provide a code that your customer must select in the Nafath application.

###### 2- Recive Webhook
    * Once the customer completes the selection, Authentica will call your application webhook with the verification process status.
Note: Contact [Authentica support team](https://wa.me/966550021781) to set up a webhook for your application before using this method.    
 
## Send Notification SMS Messages

To sned Notification messages, follow these steps:

###### 1- Register your SMS sender with Authentica

###### 2- Use Send SMS end point to comminucate with your customers
   

#Group Balance Monitoring

## Get Current Balance [/balance]

### Get Current Balance [GET]

This endpoint is to get the current balance.

#### Get Current Balance request headers
* X-Authorization : "YOUR_API_KEY"
    - This header is used for authentication, replace "YOUR_API_KEY" with  the actual API Key,
    which can be easily genrated from your Authentica account (https://portal.authentica.sa/apps/).
* Accept: application/json
* Content-Type: application/json  

#### curl for get current balance request

    
```
    curl --location 'api.authentica.sa/api/v1/balance' \
    --header 'X-Authorization: $2y$10XXXXXXXXXXXXXXXXXXXXX' \
    --header 'Accept: application/json' 
```


+ Request (application/json)

    + Headers

            Accept: application/json
            X-Authorization: api-key-provided-by-authentica

    + Body

+ Response 200 (application/json)

    + Body

            {
                "success": true,
                "data": {
                    "balance": 21934
                },
                "message": "Balance retrieved successfully"
            }
      


+ Response 401 (application/json)

    + Body

            {
                "errors": [
                  {
                     "message": "Unauthorized"
                  }
                ]
            }
            
#Group OTP Verification  

## Send OTP [/send-otp]

### Send OTP [POST]

This endpoint is used to send an OTP to user's mobile number.
    
#### Send OTP request headers
* X-Authorization : "YOUR_API_KEY"
    - This header is used for authentication, replace "YOUR_API_KEY" with  the actual API Key,
    which can be easily genrated from your Authentica account (https://portal.authentica.sa/apps/).
* Accept: application/json
* Content-Type: application/json  

#### Send OTP request body parameters
* phone : +96655XXXXXX
    - required if used method is sms or whatsapp. 
    - should be a valid international phone number.
* method: whatsapp
    - required.
    - valid options are [whatsapp, sms].
* template_id: 8
    - optional, default is 1.
    - you can send OTPs to your customers using various templates provided by Authentica. Each template is designed for a specific channel, so the selected method should align with the template's designated channel.
* otp_format : alphanumeric
    - optional, default is numeric.
    - valid options are [numeric, alphanumeric, alphabetic].
* number_of_digits : 6
    - optional, default is 4.
    - valid options are [4,5,6,7,8,9,10].
* is_fallback_on : false
    - optional, default is false
* fallback_method : sms
    - required if fallback is used.
    - Valid options are [whatsapp, sms].
    - fallback method and main method should be different.
 * fallback_phone : +96655XXXXXX
    - required if fallback method is sms or whatsapp. 
    - should be a valid international phone number.  
* otp : 123456
    - optional.
    - custom OTP to be sent to your customer, accpet only numbers.
* sender_name : your-registered-sender-name    
    - optional.
    - valid for SMS OTPs, sender name should be registerd with Authentica.
   
    
#### curl for send otp request

```` 
    curl --location 'api.authentica.sa/api/v1/send-otp' \
    --header 'Accept: application/json' \
    --header 'X-Authorization: $2y$10$XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' \
    --header 'Content-Type: application/json' \
    --data '{
        "phone":"+9665XXXXXXXXX",
        "method":"sms",
        "template_id":31,
        "otp_format":"alphabetic", 
        "number_of_digits":8,
        "is_fallback_on":false,
        "otp":"123456",
        "sender_name":"your-registered-sender-name"
    }'
```   
  
+ Request (application/json)

    + Headers

            Accept: application/json
            X-Authorization: api-key-provided-by-authentica

    + Body

            {
                "phone": "phone-number-of-the-recipient-in-international-format",
                "method": "sms-or-whatsapp",
                "template_id":"valid-template-id-from-Authentica",
                "otp_format":"alphabetic", 
                "number_of_digits":8,
                "is_fallback_on" : true,
                "otp":"123456",
                "sender_name":"your-registered-sender-name"
            }

+ Response 200 (application/json)

    + Body

            {
                 "success": true,
                 "data": null,
                 "message": "OTP send successfully"
            }
        
+ Response 401 (application/json)

    + Body

            {
                "errors": [
                  {
                     "message": "Unauthorized"
                  }
                ]
            }
            
            
## Verify OTP [/verify-otp]

### Verify OTP [POST]

This endpoint is used to verify the OTP for user authentication.


#### Verify OTP request headers
* X-Authorization : "YOUR_API_KEY"
    - This header is used for authentication, replace "YOUR_API_KEY" with  the actual API Key,
    which can be easily genrated from your Authentica account (https://portal.authentica.sa/apps/).
* Accept: application/json
* Content-Type: application/json  

#### Verify OTP request body parameters
* phone : +96655XXXXXX
    - required if the otp sent via sms or whatsapp.
    - should be a valid international phone number.
   
* otp : XXXXXX
    - required. 
    - the value to be validated against the OTP sent to your customer.

#### curl for verify otp request

```
    curl --location 'api.authentica.sa/api/v1/verify-otp' \
    --header 'X-Authorization: $XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' \
    --header 'Accept: application/json' \
    --header 'Content-Type: application/json' \
    --data '{
        "phone":"+9665XXXXXXXX",
        "otp":"OTP-ENTERED-BY-THE-USER"
    }'
```    

+ Request (application/json)

    + Headers

            Accept: application/json
            X-Authorization: api-key-provided-by-authentica

    + Body

            {
                "phone": "phone-number-of-the-recipient-in-international-format",
                "otp": "the-otp-entered-by-the-customer"
            }

+ Response 200 (application/json)

    + Body

            {
                "status": true,
                "message": "OTP verified successfully"
            }
            

+ Response 401 (application/json)

    + Body

            {
                "errors": [
                  {
                     "message": "Unauthorized"
                  }
                ]
            }

#Group Nafath Verification

            
## Verify By Nafath [/verify-by-nafath]

### Verify By Nafath [POST]

This endpoint is used to initiate verification process via Nafath.

#### Verify By Nafath request headers
* X-Authorization : "YOUR_API_KEY"
    - This header is used for authentication, replace "YOUR_API_KEY" with  the actual API Key,
    which can be easily genrated from your Authentica account (https://portal.authentica.sa/apps/).
* Accept: application/json
* Content-Type: application/json  

#### Verify By Nafath request body parameters
* national_id : 123XXXXXXX
    - required,valid national id number.

#### curl for verify by nafath request

```
    curl --location 'api.authentica.sa/api/v1/verify-by-nafath' \
    --header 'X-Authorization: $XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' \
    --header 'Accept: application/json' \
    --header 'Content-Type: application/json' \
    --data '{
        "national_id":"123XXXXXXX"
    }'
```    
        

+ Request (application/json)

    + Headers

            Accept: application/json
            X-Authorization: api-key-provided-by-authentica

    + Body

            {
                "national_id": "valid-national-id"
            }

+ Response 200 (application/json)

    + Body

            {
                "success": true,
                "data": {
                    "TransactionId": "unique-id-for-verification-process",
                    "Code": "code-must-be-selected-by-your-customer"
                }
            }
            

+ Response 401 (application/json)

    + Body

            {
                "errors": [
                  {
                     "message": "Unauthorized"
                  }
                ]
            }
            
#Group Webhook Notifications


Authenica sends a notification to the client’s registered webhook URL once NAFATH completes the verification.

### Webhook Request Details

* Method : POST
* URL: Client’s registered webhook URL.

Note: Contact [Authentica support team](https://wa.me/966550021781) to set up a webhook for your application to start receiving notifications.    

### Webhook Payload Fields
* TransactionId:
    * Type: String.
    * Description: Unique ID for the transaction
* NationalId:
    * Type: String.
    * Description: Nationl ID of your customer.
* Status:
    * Type: String.
    * Description: Verification status (COMPLETED or REJECTED.)
* Password:
    * Type: String.
    * Description: Verification password for request authenticity.

### Webhook Payload Example

```
    {
      "TransactionId": "6419747b-b0d7-4564-8755-a7f554d16b10",
      "NationalId": "1234567891",
      "Status": "COMPLETED",
      "Password": "your-secure-password"
    }
```

#### Webhook Security

Clients should verify the Password field to ensure the notification is from Authentica.

            

            
#Group Notification SMS Messages

## Send SMS [/send-sms]

### Send SMS [POST]

This endpoint is to send SMS messages to your customers.

#### Send SMS request headers
* X-Authorization : "YOUR_API_KEY"
    - This header is used for authentication, replace "YOUR_API_KEY" with  the actual API Key,
    which can be easily genrated from your Authentica account (https://portal.authentica.sa/apps/).
* Accept: application/json
* Content-Type: application/json  

#### curl for send SMS request

    
```
    curl --location 'api.authentica.sa/api/v1/send-sms' \
    --header 'Accept: application/json' \
    --header 'X-Authorization: $2y$1xxxxxxxxxxxxxxxxxxx' \
    --header 'Content-Type: application/json' \
    --data '{
        "phone":"+9665XXXXXXXX",
        "message":"your-custom-message",
        "sender_name":"your-registered-sender-name"
    }'
```


+ Request (application/json)

    + Headers

            Accept: application/json
            X-Authorization: api-key-provided-by-authentica

    + Body

+ Response 200 (application/json)

    + Body

            {
                "success": true,
                "message": "Message sent successfully."
            }
      


+ Response 401 (application/json)

    + Body

            {
                "errors": [
                  {
                     "message": "Unauthorized"
                  }
                ]
            }

             
            





