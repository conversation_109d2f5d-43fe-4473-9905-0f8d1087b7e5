require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Default Supabase client if not provided
let defaultSupabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqam5sbndpbnJtbndueXZ3bGhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjA2NzMsImV4cCI6MjA1NjU5NjY3M30.FaD3GMOSTCfpalOO6VgzWqnHLpWap5ypBLOo6lGm_Ao'
);

async function validateDumpsterData(supabase = defaultSupabase) {
  console.log('Starting enhanced database validation for dumpster selection flow...\n');

  try {
    // Check Partners
    const { data: partners, error: partnersError } = await supabase
      .from('partners')
      .select(`
        id,
        profile_id,
        company_name,
        service_areas,
        rating,
        status,
        commission_rate
      `);

    console.log('\n=== Partners ===');
    if (partnersError) {
      console.error(`Partners Error: ${partnersError.message}`);
    } else {
      console.log(`Found ${partners?.length || 0} partners`);
      if (partners && partners.length > 0) {
        console.log('Sample partner:', partners[0]);
      } else {
        console.log('WARNING: No partners found! Partners are required for dumpster assignments.');
      }
    }

    // Check Dumpster Types
    const { data: dumpsterTypes, error: typesError } = await supabase
      .from('dumpster_types')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        dimensions,
        capacity,
        max_weight,
        image_url,
        suitable_waste_types
      `);

    console.log('\n=== Dumpster Types ===');
    if (typesError) {
      console.error(`Dumpster Types Error: ${typesError.message}`);
    } else {
      console.log(`Found ${dumpsterTypes?.length || 0} dumpster types`);
      if (dumpsterTypes && dumpsterTypes.length > 0) {
        console.log('Sample dumpster type:', dumpsterTypes[0]);
        
        // Check multilingual support
        const typesWithoutArabic = dumpsterTypes.filter(t => !t.name_ar).length;
        console.log(`Dumpster types missing Arabic name: ${typesWithoutArabic}/${dumpsterTypes.length}`);
      } else {
        console.log('WARNING: No dumpster types found! Dumpster types are required for the application.');
      }
    }

    // Check Dumpsters with all details
    const { data: dumpsters, error: dumpstersError } = await supabase
      .from('dumpsters')
      .select(`
        id,
        partner_id,
        dumpster_type_id,
        image_url,
        status,
        price_per_load,
        is_available,
        next_available_date,
        rating,
        review_count,
        name_en,
        name_ar,
        description_en,
        description_ar
      `);

    console.log('\n=== All Dumpsters ===');
    if (dumpstersError) {
      console.error(`Dumpsters Error: ${dumpstersError.message}`);
    } else {
      console.log(`Found ${dumpsters?.length || 0} dumpsters`);
      
      if (dumpsters && dumpsters.length > 0) {
        console.log('Sample dumpster:', dumpsters[0]);
        
        // Count available dumpsters
        const availableDumpsters = dumpsters.filter(d => d.is_available).length;
        console.log(`Available dumpsters: ${availableDumpsters}/${dumpsters.length}`);
        
        // Check for partner assignments
        const withPartners = dumpsters.filter(d => d.partner_id).length;
        console.log(`Dumpsters with partner assignments: ${withPartners}/${dumpsters.length}`);
        
        // Check for type assignments
        const withTypes = dumpsters.filter(d => d.dumpster_type_id).length;
        console.log(`Dumpsters with type assignments: ${withTypes}/${dumpsters.length}`);
        
        // Check for images
        const withImages = dumpsters.filter(d => d.image_url).length;
        console.log(`Dumpsters with images: ${withImages}/${dumpsters.length}`);
        
        // Check multilingual support
        const withoutArabicName = dumpsters.filter(d => !d.name_ar).length;
        console.log(`Dumpsters missing Arabic name: ${withoutArabicName}/${dumpsters.length}`);
        
        // Check pricing
        const withoutPrice = dumpsters.filter(d => !d.price_per_load).length;
        console.log(`Dumpsters without price: ${withoutPrice}/${dumpsters.length}`);
        
        // Check pricing statistics
        if (dumpsters.some(d => d.price_per_load)) {
          const prices = dumpsters.filter(d => d.price_per_load).map(d => d.price_per_load);
          console.log('Price statistics:', {
            min: Math.min(...prices),
            max: Math.max(...prices),
            avg: prices.reduce((sum, p) => sum + p, 0) / prices.length
          });
        }
      } else {
        console.log('WARNING: No dumpsters found! Dumpsters are required for the application.');
      }
    }

    // Check Dumpster Sizes
    const { data: sizes, error: sizesError } = await supabase
      .from('dumpster_sizes')
      .select(`
        id,
        dumpster_type_id,
        name,
        volume_cubic_yards,
        max_weight_pounds,
        length,
        width,
        height,
        description
      `);

    console.log('\n=== Dumpster Sizes ===');
    if (sizesError) {
      console.error(`Dumpster Sizes Error: ${sizesError.message}`);
    } else {
      console.log(`Found ${sizes?.length || 0} dumpster sizes`);
      if (sizes && sizes.length > 0) {
        console.log('Sample size:', sizes[0]);
      } else {
        console.log('WARNING: No dumpster sizes found! Sizes are required for filtering.');
      }
    }

    // Check Dumpster Size Options (relationship between dumpsters and sizes)
    const { data: sizeOptions, error: sizeOptionsError } = await supabase
      .from('dumpster_size_options')
      .select('*');

    console.log('\n=== Dumpster Size Options ===');
    if (sizeOptionsError) {
      console.error(`Dumpster Size Options Error: ${sizeOptionsError.message}`);
    } else {
      console.log(`Found ${sizeOptions?.length || 0} dumpster size options`);
      
      if (dumpsters && dumpsters.length > 0 && sizeOptions && sizeOptions.length > 0) {
        const dumpstersWithSizes = new Set(sizeOptions.map(so => so.dumpster_id)).size;
        console.log(`Dumpsters with size assignments: ${dumpstersWithSizes}/${dumpsters.length}`);
      }
    }

    // Check Waste Types
    const { data: wasteTypes, error: wasteTypesError } = await supabase
      .from('waste_types')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url
      `);

    console.log('\n=== Waste Types ===');
    if (wasteTypesError) {
      console.error(`Waste Types Error: ${wasteTypesError.message}`);
    } else {
      console.log(`Found ${wasteTypes?.length || 0} waste types`);
      if (wasteTypes && wasteTypes.length > 0) {
        console.log('Sample waste type:', wasteTypes[0]);
        
        // Check multilingual support
        const withoutArabicName = wasteTypes.filter(wt => !wt.name_ar).length;
        console.log(`Waste types missing Arabic name: ${withoutArabicName}/${wasteTypes.length}`);
      } else {
        console.log('WARNING: No waste types found! Waste types are required for filtering.');
      }
    }

    // Check Dumpster-Waste Type Relationships
    const { data: dumpsterWasteTypes, error: dumpsterWasteTypesError } = await supabase
      .from('dumpster_waste_types')
      .select('*');

    console.log('\n=== Dumpster-Waste Type Relationships ===');
    if (dumpsterWasteTypesError) {
      console.error(`Dumpster-Waste Type Relationships Error: ${dumpsterWasteTypesError.message}`);
    } else {
      console.log(`Found ${dumpsterWasteTypes?.length || 0} dumpster-waste type relationships`);
      
      if (dumpsters && dumpsters.length > 0 && dumpsterWasteTypes && dumpsterWasteTypes.length > 0) {
        const dumpstersWithWasteTypes = new Set(dumpsterWasteTypes.map(dwt => dwt.dumpster_id)).size;
        console.log(`Dumpsters with waste type assignments: ${dumpstersWithWasteTypes}/${dumpsters.length}`);
      }
    }

    // Check Dumpster Features
    const { data: features, error: featuresError } = await supabase
      .from('dumpster_features')
      .select(`
        id,
        dumpster_id,
        feature
      `);

    console.log('\n=== Dumpster Features ===');
    if (featuresError) {
      console.error(`Dumpster Features Error: ${featuresError.message}`);
    } else {
      console.log(`Found ${features?.length || 0} dumpster features`);
      
      if (dumpsters && dumpsters.length > 0 && features && features.length > 0) {
        const dumpstersWithFeatures = new Set(features.map(f => f.dumpster_id)).size;
        console.log(`Dumpsters with feature assignments: ${dumpstersWithFeatures}/${dumpsters.length}`);
        
        if (features.length > 0) {
          console.log('Sample feature:', features[0]);
        }
      }
    }

    // Check Dumpster Images
    const { data: images, error: imagesError } = await supabase
      .from('dumpster_images')
      .select('*');

    console.log('\n=== Dumpster Additional Images ===');
    if (imagesError) {
      console.error(`Dumpster Images Error: ${imagesError.message}`);
    } else {
      console.log(`Found ${images?.length || 0} additional dumpster images`);
      
      if (dumpsters && dumpsters.length > 0 && images && images.length > 0) {
        const dumpstersWithAdditionalImages = new Set(images.map(img => img.dumpster_id)).size;
        console.log(`Dumpsters with additional images: ${dumpstersWithAdditionalImages}/${dumpsters.length}`);
      }
    }

    console.log('\n=== Database Status Summary ===');
    console.log('1. Partners:', partners?.length || 0);
    console.log('2. Dumpster Types:', dumpsterTypes?.length || 0);
    console.log('3. Dumpsters:', dumpsters?.length || 0);
    console.log('4. Dumpster Sizes:', sizes?.length || 0);
    console.log('5. Waste Types:', wasteTypes?.length || 0);
    console.log('6. Dumpster-Waste Type Relationships:', dumpsterWasteTypes?.length || 0);
    console.log('7. Dumpster Features:', features?.length || 0);

    if (dumpsters && dumpsters.length > 0) {
      console.log('\n=== Data Quality Assessment ===');
      
      // Check if we have enough data for the application to function
      const hasPartners = partners && partners.length > 0;
      const hasWasteTypes = wasteTypes && wasteTypes.length > 0;
      const hasSizes = sizes && sizes.length > 0;
      const hasAvailableDumpsters = dumpsters.some(d => d.is_available);
      const hasCompleteRelationships = dumpsters.every(d => 
        d.partner_id && d.dumpster_type_id && d.price_per_load
      );
      
      const requiredDataHealthy = hasPartners && hasWasteTypes && hasSizes && hasAvailableDumpsters && hasCompleteRelationships;
      
      console.log(`Data quality status: ${requiredDataHealthy ? 'GOOD' : 'NEEDS ATTENTION'}`);
      
      if (!requiredDataHealthy) {
        console.log('\nThe following issues need to be addressed:');
        if (!hasPartners) console.log('- Missing partners');
        if (!hasWasteTypes) console.log('- Missing waste types');
        if (!hasSizes) console.log('- Missing dumpster sizes');
        if (!hasAvailableDumpsters) console.log('- No available dumpsters');
        if (!hasCompleteRelationships) console.log('- Incomplete relationships (missing partner_id, dumpster_type_id, or price_per_load)');
      }
      
      // Recommendations
      console.log('\n=== Recommendations ===');
      
      const recommendations = [];
      
      if (!hasPartners) {
        recommendations.push('Create partner data before proceeding');
      }
      
      if (dumpsters.some(d => !d.partner_id)) {
        recommendations.push('Assign partners to all dumpsters');
      }
      
      if (dumpsters.some(d => !d.price_per_load)) {
        recommendations.push('Set prices for all dumpsters');
      }
      
      if (dumpsters.filter(d => d.is_available).length < 5) {
        recommendations.push('Mark more dumpsters as available for testing');
      }
      
      const dumpstersWithWasteTypes = dumpsterWasteTypes ? new Set(dumpsterWasteTypes.map(dwt => dwt.dumpster_id)).size : 0;
      if (dumpstersWithWasteTypes < dumpsters.length) {
        recommendations.push('Assign waste types to all dumpsters');
      }
      
      if (recommendations.length === 0) {
        console.log('Database appears ready for integration! No immediate actions required.');
      } else {
        console.log('Recommended actions before integration:');
        recommendations.forEach((rec, i) => console.log(`${i + 1}. ${rec}`));
      }
    }

    console.log('\nValidation complete!');

  } catch (error) {
    console.error('Validation failed:', error.message);
  }
}

// Export the function if being required by another file
module.exports = validateDumpsterData;

// If this script is run directly, execute the validation
if (require.main === module) {
  validateDumpsterData();
} 