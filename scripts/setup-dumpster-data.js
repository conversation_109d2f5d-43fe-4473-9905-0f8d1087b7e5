require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with Service Role key to bypass RLS
const supabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key-here' // ⚠️ Replace with actual key or use env var
);

// Import individual scripts
const validateDumpsterData = require('./validate-dumpster-data');
const seedPartnerData = require('./seed-partner-data');
const updateDumpsterData = require('./update-dumpster-data');
const createWasteTypeRelationships = require('./create-waste-type-relationships');
const createSizeOptions = require('./create-size-options');
const createDumpsterFeatures = require('./create-dumpster-features');

// Main setup function
async function setupDumpsterData() {
  console.log('🚀 Starting Dumpster Data Setup');
  console.log('================================\n');

  try {
    // Step 1: Initial Validation
    console.log('Step 1: Initial Database Validation');
    console.log('-----------------------------------');
    await validateDumpsterData(supabase);
    console.log('\n✅ Initial validation complete\n');

    // Step 2: Seed Partner Data
    console.log('Step 2: Creating Partner Data');
    console.log('-----------------------------');
    await seedPartnerData(supabase);
    console.log('\n✅ Partner data created\n');

    // Step 3: Update Dumpster Data (assign partners, set prices)
    console.log('Step 3: Updating Dumpster Data');
    console.log('------------------------------');
    await updateDumpsterData(supabase);
    console.log('\n✅ Dumpster data updated\n');

    // Step 4: Create Waste Type Relationships
    console.log('Step 4: Creating Waste Type Relationships');
    console.log('----------------------------------------');
    await createWasteTypeRelationships(supabase);
    console.log('\n✅ Waste type relationships created\n');

    // Step 5: Create Size Options
    console.log('Step 5: Creating Size Options');
    console.log('-----------------------------');
    await createSizeOptions(supabase);
    console.log('\n✅ Size options created\n');

    // Step 6: Create Dumpster Features
    console.log('Step 6: Creating Dumpster Features');
    console.log('----------------------------------');
    await createDumpsterFeatures(supabase);
    console.log('\n✅ Dumpster features created\n');

    // Step 7: Final Validation
    console.log('Step 7: Final Database Validation');
    console.log('---------------------------------');
    await validateDumpsterData(supabase);
    console.log('\n✅ Final validation complete\n');

    console.log('================================');
    console.log('🎉 All setup steps completed!');
    console.log('================================');
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
setupDumpsterData(); 