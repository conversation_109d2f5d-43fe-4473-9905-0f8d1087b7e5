require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Default Supabase client if not provided (using Service Role Key)
const defaultSupabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key-here' // ⚠️ Replace with actual key or use env var
);

/**
 * Creates relationships between dumpsters and waste types
 */
async function createWasteTypeRelationships(supabase = defaultSupabase) {
  console.log('Starting to create dumpster-waste type relationships...');
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };

  try {
    // Get all waste types
    const { data: wasteTypes, error: wasteTypesError } = await supabase
      .from('waste_types')
      .select('id, name_en');

    if (wasteTypesError) {
      console.error('Error fetching waste types:', wasteTypesError.message);
      return;
    }

    if (!wasteTypes || wasteTypes.length === 0) {
      console.error('No waste types found in the database.');
      return;
    }

    console.log(`Found ${wasteTypes.length} waste types to assign to dumpsters`);

    // Get all dumpsters
    const { data: dumpsters, error: dumpstersError } = await supabase
      .from('dumpsters')
      .select(`
        id, 
        name_en,
        dumpster_waste_types (
          id,
          waste_type_id
        )
      `);

    if (dumpstersError) {
      console.error('Error fetching dumpsters:', dumpstersError.message);
      return;
    }

    if (!dumpsters || dumpsters.length === 0) {
      console.error('No dumpsters found to assign waste types to.');
      return;
    }

    console.log(`Found ${dumpsters.length} dumpsters to assign waste types to`);

    // Calculate which dumpsters need waste type assignments
    const dumpstersNeedingRelationships = dumpsters.filter(
      d => !d.dumpster_waste_types || d.dumpster_waste_types.length === 0
    );

    console.log(`${dumpstersNeedingRelationships.length} dumpsters need waste type assignments`);

    // For each dumpster that needs waste types, assign some waste types
    for (const dumpster of dumpstersNeedingRelationships) {
      // Decide how many waste types to assign (2-4)
      const numWasteTypes = 2 + Math.floor(Math.random() * 3);
      
      // Randomly select waste types without duplicates
      const shuffledWasteTypes = [...wasteTypes].sort(() => 0.5 - Math.random());
      const selectedWasteTypes = shuffledWasteTypes.slice(0, numWasteTypes);
      
      console.log(`Assigning ${numWasteTypes} waste types to dumpster "${dumpster.name_en || dumpster.id}"`);
      
      // Create the relationships
      for (const wasteType of selectedWasteTypes) {
        const { data, error } = await supabase
          .from('dumpster_waste_types')
          .insert({
            dumpster_id: dumpster.id,
            waste_type_id: wasteType.id
          })
          .select();

        if (error) {
          console.error(`Error linking waste type ${wasteType.name_en} to dumpster ${dumpster.name_en || dumpster.id}:`, error.message);
          results.failed++;
          results.errors.push({
            dumpster: dumpster.name_en || dumpster.id,
            wasteType: wasteType.name_en,
            error: error.message
          });
        } else {
          results.success++;
        }
      }
      
      console.log(`✅ Added waste types to dumpster: ${dumpster.name_en || dumpster.id}`);
    }

    // Final summary
    console.log('\n=== Waste Type Relationship Summary ===');
    console.log(`Total dumpsters processed: ${dumpstersNeedingRelationships.length}`);
    console.log(`Successful relationships created: ${results.success}`);
    console.log(`Failed relationships: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('\nErrors:');
      results.errors.forEach((err, i) => {
        console.log(`${i+1}. ${err.dumpster} - ${err.wasteType}: ${err.error}`);
      });
    }

  } catch (error) {
    console.error('Unexpected error in createWasteTypeRelationships:', error);
  }
}

// Export the function if being required by another file
module.exports = createWasteTypeRelationships;

// If this script is run directly, execute it
if (require.main === module) {
  createWasteTypeRelationships()
    .then(() => {
      console.log('Waste type relationship creation complete.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Waste type relationship creation failed:', error);
      process.exit(1);
    });
} 