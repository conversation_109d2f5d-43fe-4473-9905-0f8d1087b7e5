require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Default Supabase client if not provided (using Service Role Key)
const defaultSupabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key-here' // ⚠️ Replace with actual key or use env var
);

// Sample partner data
const partnerData = [
  {
    company_name: 'EcoWaste Solutions',
    profile_id: null, // Will be auto-generated if not provided
    service_areas: ['Riyadh City Center', 'North Riyadh', 'East Riyadh'],
    status: 'active',
    rating: 4.8,
    commission_rate: 0.15,
    bio: 'Specializing in eco-friendly waste management for over 10 years.',
    website: 'https://ecowaste-solutions.example.com',
    phone: '+966-50-1234567',
    email: '<EMAIL>'
  },
  {
    company_name: 'Desert Dumpsters',
    profile_id: null,
    service_areas: ['Jeddah', 'Mecca', 'Medina'],
    status: 'active',
    rating: 4.5,
    commission_rate: 0.12,
    bio: 'Efficient waste management services across Western Saudi Arabia.',
    website: 'https://desert-dumpsters.example.com',
    phone: '+966-55-9876543',
    email: '<EMAIL>'
  },
  {
    company_name: 'Green Bin Services',
    profile_id: null,
    service_areas: ['Dammam', 'Khobar', 'Dhahran'],
    status: 'active',
    rating: 4.7,
    commission_rate: 0.14,
    bio: 'Promoting sustainability through responsible waste disposal since 2015.',
    website: 'https://greenbin.example.com',
    phone: '+966-58-1122334',
    email: '<EMAIL>'
  },
  {
    company_name: 'Royal Waste Management',
    profile_id: null,
    service_areas: ['Riyadh', 'Jeddah', 'Dammam', 'Tabuk'],
    status: 'active',
    rating: 4.9,
    commission_rate: 0.18,
    bio: 'Premium waste management solutions for residential and commercial clients.',
    website: 'https://royalwaste.example.com',
    phone: '+966-54-5566778',
    email: '<EMAIL>'
  },
  {
    company_name: 'Quick Dumpster Rentals',
    profile_id: null,
    service_areas: ['Riyadh South', 'Riyadh West', 'Al Kharj'],
    status: 'active',
    rating: 4.6,
    commission_rate: 0.13,
    bio: 'Fast and reliable dumpster rental services with same-day delivery.',
    website: 'https://quickdumpster.example.com',
    phone: '+966-56-8899001',
    email: '<EMAIL>'
  }
];

/**
 * Seeds the database with sample partner data
 */
async function seedPartnerData(supabase = defaultSupabase) {
  console.log('Starting to seed partner data...');
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };

  try {
    // Check if we already have partners to avoid duplicates
    const { data: existingPartners, error: checkError } = await supabase
      .from('partners')
      .select('id, company_name');

    if (checkError) {
      console.error('Error checking existing partners:', checkError.message);
      return;
    }

    if (existingPartners && existingPartners.length > 0) {
      console.log(`Found ${existingPartners.length} existing partners:`);
      existingPartners.forEach(partner => console.log(`- ${partner.company_name}`));
      
      // Ask if we should still proceed
      console.log('\nPartners already exist in the database. The script will only add missing partners.');
    }

    // Filter out partners that already exist
    const existingCompanyNames = existingPartners.map(p => p.company_name.toLowerCase());
    const partnersToAdd = partnerData.filter(p => 
      !existingCompanyNames.includes(p.company_name.toLowerCase())
    );

    console.log(`Adding ${partnersToAdd.length} new partners...`);

    // Add each partner
    for (const partner of partnersToAdd) {
      // Create a profile for the partner
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .insert([{ 
          full_name: partner.company_name,
          email: partner.email,
          phone: partner.phone,
          user_type: 'partner',
          created_at: new Date().toISOString()
        }])
        .select();

      if (profileError) {
        console.error(`Error creating profile for ${partner.company_name}:`, profileError.message);
        results.failed++;
        results.errors.push({
          company: partner.company_name,
          error: profileError.message
        });
        continue;
      }

      // Use the created profile_id for the partner
      const profileId = profile[0].id;
      
      const { data, error } = await supabase
        .from('partners')
        .insert([{
          ...partner,
          profile_id: profileId
        }])
        .select();

      if (error) {
        console.error(`Error creating partner ${partner.company_name}:`, error.message);
        results.failed++;
        results.errors.push({
          company: partner.company_name,
          error: error.message
        });
      } else {
        console.log(`✅ Created partner: ${partner.company_name}`);
        results.success++;
      }
    }

    // Final summary
    console.log('\n=== Partner Seeding Summary ===');
    console.log(`Total attempted: ${partnersToAdd.length}`);
    console.log(`Successfully created: ${results.success}`);
    console.log(`Failed: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('\nErrors:');
      results.errors.forEach((err, i) => {
        console.log(`${i+1}. ${err.company}: ${err.error}`);
      });
    }

  } catch (error) {
    console.error('Unexpected error in seedPartnerData:', error);
  }
}

// Export the function if being required by another file
module.exports = seedPartnerData;

// If this script is run directly, execute it
if (require.main === module) {
  seedPartnerData()
    .then(() => {
      console.log('Partner seeding complete.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Partner seeding failed:', error);
      process.exit(1);
    });
} 