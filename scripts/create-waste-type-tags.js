require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Default Supabase client with Service Role Key
const supabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key-here' // ⚠️ Replace with actual key or use env var
);

/**
 * Create relationships between waste types and tags
 */
async function createWasteTypeTags() {
  console.log('Starting to create waste type tag relationships...');
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };

  try {
    // Get all waste types
    const { data: wasteTypes, error: wasteTypesError } = await supabase
      .from('waste_types')
      .select('id, name_en');

    if (wasteTypesError) {
      console.error('Error fetching waste types:', wasteTypesError.message);
      return;
    }

    if (!wasteTypes || wasteTypes.length === 0) {
      console.error('No waste types found in the database.');
      return;
    }

    console.log(`Found ${wasteTypes.length} waste types`);

    // Get all waste tags
    const { data: wasteTags, error: wasteTagsError } = await supabase
      .from('waste_tags')
      .select('id, name_en');

    if (wasteTagsError) {
      console.error('Error fetching waste tags:', wasteTagsError.message);
      return;
    }

    if (!wasteTags || wasteTags.length === 0) {
      console.error('No waste tags found in the database.');
      return;
    }

    console.log(`Found ${wasteTags.length} waste tags`);

    // Get existing relationships to avoid duplicates
    const { data: existingRelationships, error: relationshipsError } = await supabase
      .from('waste_type_tags')
      .select('*');

    if (relationshipsError) {
      console.error('Error fetching existing relationships:', relationshipsError.message);
      return;
    }

    console.log(`Found ${existingRelationships?.length || 0} existing waste type tag relationships`);

    // Create a map of existing relationships for faster lookup
    const existingRelationshipsMap = new Map();
    if (existingRelationships) {
      existingRelationships.forEach(rel => {
        const key = `${rel.waste_type_id}:${rel.waste_tag_id}`;
        existingRelationshipsMap.set(key, true);
      });
    }

    // For each waste type, assign some tags
    for (const wasteType of wasteTypes) {
      // Decide how many tags to assign (2-4)
      const numTags = 2 + Math.floor(Math.random() * 3);
      
      // Randomly select tags without duplicates
      const shuffledTags = [...wasteTags].sort(() => 0.5 - Math.random());
      const selectedTags = shuffledTags.slice(0, numTags);
      
      console.log(`Assigning ${numTags} tags to waste type "${wasteType.name_en || wasteType.id}"`);
      
      // Create relationships
      for (const tag of selectedTags) {
        // Skip if relationship already exists
        const relationshipKey = `${wasteType.id}:${tag.id}`;
        if (existingRelationshipsMap.has(relationshipKey)) {
          console.log(`Relationship already exists for waste type "${wasteType.name_en}" and tag "${tag.name_en}"`);
          continue;
        }
        
        const { data, error } = await supabase
          .from('waste_type_tags')
          .insert({
            waste_type_id: wasteType.id,
            waste_tag_id: tag.id
          })
          .select();

        if (error) {
          console.error(`Error creating relationship for waste type "${wasteType.name_en}" and tag "${tag.name_en}":`, error.message);
          results.failed++;
          results.errors.push({
            wasteType: wasteType.name_en || wasteType.id,
            tag: tag.name_en || tag.id,
            error: error.message
          });
        } else {
          console.log(`✅ Created relationship for waste type "${wasteType.name_en}" and tag "${tag.name_en}"`);
          results.success++;
        }
      }
    }

    // Final summary
    console.log('\n=== Waste Type Tag Relationship Summary ===');
    console.log(`Successfully created: ${results.success}`);
    console.log(`Failed: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('\nErrors:');
      results.errors.forEach((err, i) => {
        console.log(`${i+1}. ${err.wasteType} - ${err.tag}: ${err.error}`);
      });
    }

  } catch (error) {
    console.error('Unexpected error in createWasteTypeTags:', error);
  }
}

// Run the function if script is executed directly
if (require.main === module) {
  createWasteTypeTags()
    .then(() => {
      console.log('Waste type tag creation complete.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Waste type tag creation failed:', error);
      process.exit(1);
    });
}

module.exports = createWasteTypeTags; 