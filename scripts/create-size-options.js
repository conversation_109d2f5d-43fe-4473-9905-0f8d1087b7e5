require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Default Supabase client if not provided (using Service Role Key)
const defaultSupabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key-here' // ⚠️ Replace with actual key or use env var
);

/**
 * Creates size options for dumpsters
 */
async function createSizeOptions(supabase = defaultSupabase) {
  console.log('Starting to create dumpster size options...');
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };

  try {
    // Get all dumpster sizes
    const { data: sizes, error: sizesError } = await supabase
      .from('dumpster_sizes')
      .select('id, name, volume_cubic_yards');

    if (sizesError) {
      console.error('Error fetching dumpster sizes:', sizesError.message);
      return;
    }

    if (!sizes || sizes.length === 0) {
      console.error('No dumpster sizes found in the database.');
      return;
    }

    console.log(`Found ${sizes.length} dumpster sizes`);

    // Get all dumpsters
    const { data: dumpsters, error: dumpstersError } = await supabase
      .from('dumpsters')
      .select(`
        id, 
        name_en,
        dumpster_size_options (
          dumpster_size_id
        )
      `);

    if (dumpstersError) {
      console.error('Error fetching dumpsters:', dumpstersError.message);
      return;
    }

    if (!dumpsters || dumpsters.length === 0) {
      console.error('No dumpsters found to assign sizes to.');
      return;
    }

    console.log(`Found ${dumpsters.length} dumpsters to assign sizes to`);

    // Check for existing size options
    const { data: existingSizeOptions, error: sizeOptionsError } = await supabase
      .from('dumpster_size_options')
      .select('*');

    if (sizeOptionsError) {
      console.error('Error checking existing size options:', sizeOptionsError.message);
      return;
    }

    console.log(`Found ${existingSizeOptions?.length || 0} existing size options`);

    // For each dumpster, assign at least one size option if it doesn't have any
    for (const dumpster of dumpsters) {
      // Skip if dumpster already has size options
      if (dumpster.dumpster_size_options && dumpster.dumpster_size_options.length > 0) {
        console.log(`Dumpster "${dumpster.name_en || dumpster.id}" already has size options`);
        continue;
      }

      // Decide how many sizes to assign (1-2)
      const numSizes = 1 + Math.floor(Math.random() * 2);
      
      // Randomly select sizes without duplicates
      const shuffledSizes = [...sizes].sort(() => 0.5 - Math.random());
      const selectedSizes = shuffledSizes.slice(0, numSizes);
      
      console.log(`Assigning ${numSizes} sizes to dumpster "${dumpster.name_en || dumpster.id}"`);
      
      // Create the size options
      for (const size of selectedSizes) {
        // Calculate prices based on size (but don't include in insert since the column doesn't exist)
        const basePrice = 75 + (size.volume_cubic_yards * 10);
        const price = Math.round(basePrice * (0.9 + Math.random() * 0.2)); // Add some variation
        console.log(`Would set price of $${price} for dumpster "${dumpster.name_en || dumpster.id}" and size "${size.name}"`);
        
        const { data, error } = await supabase
          .from('dumpster_size_options')
          .insert({
            dumpster_id: dumpster.id,
            dumpster_size_id: size.id
            // price field removed since it doesn't exist in the table
          })
          .select();

        if (error) {
          console.error(`Error creating size option for dumpster "${dumpster.name_en || dumpster.id}" and size "${size.name}":`, error);
          results.failed++;
          results.errors.push({
            dumpster: dumpster.name_en || dumpster.id,
            size: size.name,
            error: error.message
          });
        } else {
          console.log(`Created size option for dumpster "${dumpster.name_en || dumpster.id}" and size "${size.name}"`);
          results.success++;
        }
      }
    }

    // Final summary
    console.log('\n=== Size Options Creation Summary ===');
    console.log(`Successfully created: ${results.success}`);
    console.log(`Failed: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('\nErrors:');
      results.errors.forEach((err, i) => {
        console.log(`${i+1}. ${err.dumpster} - ${err.size}: ${err.error}`);
      });
    }

  } catch (error) {
    console.error('Unexpected error in createSizeOptions:', error);
  }
}

// Export the function if being required by another file
module.exports = createSizeOptions;

// If this script is run directly, execute it
if (require.main === module) {
  createSizeOptions()
    .then(() => {
      console.log('Size options creation complete.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Size options creation failed:', error);
      process.exit(1);
    });
} 