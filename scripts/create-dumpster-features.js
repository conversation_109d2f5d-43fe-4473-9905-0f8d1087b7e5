require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Default Supabase client if not provided (using Service Role Key)
const defaultSupabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key-here' // ⚠️ Replace with actual key or use env var
);

// Sample features to assign to dumpsters
const featureOptions = [
  'Easy Loading',
  'Weatherproof',
  'Roll-off design',
  'Forklift pockets',
  'High visibility markers',
  'Stackable design',
  'Eco-friendly materials',
  'Reinforced base',
  'Chemical resistant',
  'Easy transport',
  'Customizable colors',
  'Double doors',
  'Durable construction'
];

/**
 * Creates features for dumpsters
 */
async function createDumpsterFeatures(supabase = defaultSupabase) {
  console.log('Starting to create dumpster features...');
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };

  try {
    // Get all dumpsters
    const { data: dumpsters, error: dumpstersError } = await supabase
      .from('dumpsters')
      .select(`
        id, 
        name_en,
        dumpster_features (
          feature
        )
      `);

    if (dumpstersError) {
      console.error('Error fetching dumpsters:', dumpstersError.message);
      return;
    }

    if (!dumpsters || dumpsters.length === 0) {
      console.error('No dumpsters found to assign features to.');
      return;
    }

    console.log(`Found ${dumpsters.length} dumpsters to assign features to`);

    // Calculate which dumpsters need features
    const dumpstersNeedingFeatures = dumpsters.filter(
      d => !d.dumpster_features || d.dumpster_features.length === 0
    );

    console.log(`${dumpstersNeedingFeatures.length} dumpsters need feature assignments`);

    // For each dumpster that needs features, assign some features
    for (const dumpster of dumpstersNeedingFeatures) {
      // Decide how many features to assign (3-5)
      const numFeatures = 3 + Math.floor(Math.random() * 3);
      
      // Randomly select features without duplicates
      const shuffledFeatures = [...featureOptions].sort(() => 0.5 - Math.random());
      const selectedFeatures = shuffledFeatures.slice(0, numFeatures);
      
      console.log(`Assigning ${numFeatures} features to dumpster "${dumpster.name_en || dumpster.id}"`);
      
      // Create the features
      for (const feature of selectedFeatures) {
        const { data, error } = await supabase
          .from('dumpster_features')
          .insert({
            dumpster_id: dumpster.id,
            feature: feature
          })
          .select();

        if (error) {
          console.error(`Error creating feature "${feature}" for dumpster ${dumpster.name_en || dumpster.id}:`, error);
          results.failed++;
          results.errors.push({
            dumpster: dumpster.name_en || dumpster.id,
            feature: feature,
            error: error.message
          });
        } else {
          results.success++;
        }
      }
      
      console.log(`✅ Added features to dumpster: ${dumpster.name_en || dumpster.id}`);
    }

    // Final summary
    console.log('\n=== Feature Assignment Summary ===');
    console.log(`Total dumpsters processed: ${dumpstersNeedingFeatures.length}`);
    console.log(`Successful features created: ${results.success}`);
    console.log(`Failed features: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('\nErrors:');
      results.errors.forEach((err, i) => {
        console.log(`${i+1}. ${err.dumpster} - ${err.feature}: ${err.error}`);
      });
    }

  } catch (error) {
    console.error('Unexpected error in createDumpsterFeatures:', error);
  }
}

// Export the function if being required by another file
module.exports = createDumpsterFeatures;

// If this script is run directly, execute it
if (require.main === module) {
  createDumpsterFeatures()
    .then(() => {
      console.log('Dumpster features creation complete.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Dumpster features creation failed:', error);
      process.exit(1);
    });
} 