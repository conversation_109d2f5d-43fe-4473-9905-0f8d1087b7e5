const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://ejjnlnwinrmnwnyvwlhj.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqam5sbndpbnJtbndueXZ3bGhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjA2NzMsImV4cCI6MjA1NjU5NjY3M30.FaD3GMOSTCfpalOO6VgzWqnHLpWap5ypBLOo6lGm_Ao';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSizesAndDumpsters() {
  console.log('=== Checking Dumpster Sizes ===');
  
  // Get all dumpster sizes
  const { data: sizes, error: sizesError } = await supabase
    .from('dumpster_sizes')
    .select('*')
    .order('volume_cubic_yards');

  if (sizesError) {
    console.error('Error fetching sizes:', sizesError);
    return;
  }

  console.log(`Found ${sizes?.length || 0} dumpster sizes:`);
  sizes?.forEach(size => {
    console.log(`- ${size.name} (${size.volume_cubic_yards} cubic yards)`);
  });

  console.log('\n=== Checking Dumpsters ===');
  
  // Get all dumpsters with their size info
  const { data: dumpsters, error: dumpstersError } = await supabase
    .from('dumpsters')
    .select(`
      id,
      name_en,
      size_id,
      dumpster_sizes (
        name,
        volume_cubic_yards
      )
    `)
    .limit(20);

  if (dumpstersError) {
    console.error('Error fetching dumpsters:', dumpstersError);
    return;
  }

  console.log(`Found ${dumpsters?.length || 0} dumpsters:`);
  dumpsters?.forEach(dumpster => {
    const sizeName = dumpster.dumpster_sizes?.name || 'No size';
    console.log(`- ${dumpster.name_en} (Size: ${sizeName})`);
  });

  console.log('\n=== Grouping Analysis ===');
  
  // Group dumpsters by size
  const groupedBySizeId = {};
  dumpsters?.forEach(dumpster => {
    const sizeId = dumpster.size_id;
    if (!groupedBySizeId[sizeId]) {
      groupedBySizeId[sizeId] = {
        sizeInfo: dumpster.dumpster_sizes,
        dumpsters: []
      };
    }
    groupedBySizeId[sizeId].dumpsters.push(dumpster);
  });

  console.log('Dumpsters grouped by size:');
  Object.entries(groupedBySizeId).forEach(([sizeId, group]) => {
    const sizeName = group.sizeInfo?.name || 'Unknown size';
    console.log(`- ${sizeName}: ${group.dumpsters.length} dumpsters`);
  });
}

checkSizesAndDumpsters().catch(console.error);
