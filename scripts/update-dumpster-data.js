require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Default Supabase client if not provided (using Service Role Key)
const defaultSupabase = createClient(
  'https://ejjnlnwinrmnwnyvwlhj.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key-here' // ⚠️ Replace with actual key or use env var
);

/**
 * Updates dumpster data with partner associations
 */
async function updateDumpsterData(supabase = defaultSupabase) {
  console.log('Starting to update dumpster data with partner assignments...');
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };

  try {
    // Get all partners
    const { data: partners, error: partnersError } = await supabase
      .from('partners')
      .select('id, company_name');

    if (partnersError) {
      console.error('Error fetching partners:', partnersError.message);
      return;
    }

    if (!partners || partners.length === 0) {
      console.error('No partners found. Cannot assign partners to dumpsters.');
      return;
    }

    console.log(`Found ${partners.length} partners available for assignment`);

    // Get all dumpsters
    const { data: dumpsters, error: dumpstersError } = await supabase
      .from('dumpsters')
      .select(`
        id, 
        name_en, 
        partner_id, 
        price_per_load, 
        is_available,
        dumpster_type_id
      `);

    if (dumpstersError) {
      console.error('Error fetching dumpsters:', dumpstersError.message);
      return;
    }

    if (!dumpsters || dumpsters.length === 0) {
      console.error('No dumpsters found to update.');
      return;
    }

    console.log(`Found ${dumpsters.length} dumpsters to update`);
    
    // For all dumpsters, we'll assign the first available partner if they don't have one
    // and set a price if they don't have one
    const partnerId = partners[0].id;
    console.log(`Using partner "${partners[0].company_name}" (${partnerId}) for all dumpsters without partners`);
    
    let updateCount = 0;
    
    for (const dumpster of dumpsters) {
      const updates = {};
      let needsUpdate = false;

      // Assign the partner if missing
      if (!dumpster.partner_id) {
        updates.partner_id = partnerId;
        needsUpdate = true;
      }

      // Set a price if missing
      if (!dumpster.price_per_load) {
        // Price based on name/size (extract size from name if possible)
        const name = dumpster.name_en.toLowerCase();
        let basePrice = 100; // Default price
        
        if (name.includes('5 m³') || name.includes('5m³')) {
          basePrice = 75;
        } else if (name.includes('7 m³') || name.includes('7m³')) {
          basePrice = 110;
        } else if (name.includes('10 m³') || name.includes('10m³')) {
          basePrice = 150;
        } else if (name.includes('6.5 yd')) {
          basePrice = 125;
        }
        
        // Add some variation to make prices realistic
        const price = Math.round(basePrice * (0.9 + Math.random() * 0.2));
        updates.price_per_load = price;
        needsUpdate = true;
      }

      // Skip if no updates needed
      if (!needsUpdate) {
        console.log(`Dumpster "${dumpster.name_en}" already has partner and price set`);
        continue;
      }

      // Apply updates
      const { data, error } = await supabase
        .from('dumpsters')
        .update(updates)
        .eq('id', dumpster.id)
        .select();

      if (error) {
        console.error(`Error updating dumpster ${dumpster.name_en || dumpster.id}:`, error.message);
        results.failed++;
        results.errors.push({
          dumpster: dumpster.name_en || dumpster.id,
          error: error.message
        });
      } else {
        updateCount++;
        const partnerMsg = updates.partner_id ? `partner_id: ${updates.partner_id}` : '';
        const priceMsg = updates.price_per_load ? `price: ${updates.price_per_load}` : '';
        console.log(`✅ Updated dumpster: ${dumpster.name_en || dumpster.id} (${partnerMsg} ${priceMsg})`);
        results.success++;
      }
    }

    // Final summary
    console.log('\n=== Dumpster Update Summary ===');
    console.log(`Total dumpsters: ${dumpsters.length}`);
    console.log(`Dumpsters updated: ${updateCount}`);
    console.log(`Successfully updated: ${results.success}`);
    console.log(`Failed updates: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('\nErrors:');
      results.errors.forEach((err, i) => {
        console.log(`${i+1}. ${err.dumpster}: ${err.error}`);
      });
    }

  } catch (error) {
    console.error('Unexpected error in updateDumpsterData:', error);
  }
}

// Export the function if being required by another file
module.exports = updateDumpsterData;

// If this script is run directly, execute it
if (require.main === module) {
  updateDumpsterData()
    .then(() => {
      console.log('Dumpster data update complete.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Dumpster data update failed:', error);
      process.exit(1);
    });
} 