import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import countriesData from '../Countries.json';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// List of RTL language countries (ISO 3166-1 alpha-2 codes)
const RTL_COUNTRIES = [
  'SA', // Saudi Arabia
  'AE', // United Arab Emirates
  'YE', // Yemen
  'IR', // Iran
  'IQ', // Iraq
  'SY', // Syria
  'LB', // Lebanon
  'PS', // Palestine
  'IL', // Israel
  'JO', // Jordan
  'KW', // Kuwait
  'OM', // Oman
  'QA', // Qatar
  'BH', // Bahrain
  'EG', // Egypt
  'SD', // Sudan
  'LY', // Libya
  'DZ', // Algeria
  'MA', // Morocco
  'TN', // Tunisia
];

// Native names for some countries (focusing on RTL languages for now)
const NATIVE_NAMES: { [key: string]: string } = {
  'SA': 'المملكة العربية السعودية',
  'AE': 'الإمارات العربية المتحدة',
  'YE': 'اليمن',
  'IR': 'ایران',
  'IQ': 'العراق',
  'SY': 'سوريا',
  'LB': 'لبنان',
  'PS': 'فلسطين',
  'IL': 'ישראל',
  'JO': 'الأردن',
  'KW': 'الكويت',
  'OM': 'عمان',
  'QA': 'قطر',
  'BH': 'البحرين',
  'EG': 'مصر',
  'SD': 'السودان',
  'LY': 'ليبيا',
  'DZ': 'الجزائر',
  'MA': 'المغرب',
  'TN': 'تونس',
};

// Transform the countries data to match our database schema
const countries = countriesData.map(country => ({
  name: country.name,
  native_name: NATIVE_NAMES[country.code] || country.name, // Use native name if available, otherwise use English name
  code: country.code,
  dial_code: country.dial_code,
  flag_emoji: country.emoji,
  rtl: RTL_COUNTRIES.includes(country.code)
}));

async function populateCountries() {
  try {
    console.log('Starting to populate countries table...');
    let successCount = 0;
    let errorCount = 0;

    for (const country of countries) {
      // Generate flag URL using Flagpedia API
      const flagUrl = `https://flagcdn.com/w640/${country.code.toLowerCase()}.png`;

      const { data, error } = await supabase
        .from('countries')
        .upsert({
          name: country.name,
          native_name: country.native_name,
          code: country.code,
          dial_code: country.dial_code,
          flag_url: flagUrl,
          flag_emoji: country.flag_emoji,
          rtl: country.rtl
        }, {
          onConflict: 'code'
        });

      if (error) {
        console.error(`Error inserting ${country.name}:`, error);
        errorCount++;
      } else {
        console.log(`Successfully inserted/updated ${country.name}`);
        successCount++;
      }
    }

    console.log(`Finished populating countries table. Success: ${successCount}, Errors: ${errorCount}`);
  } catch (error) {
    console.error('Error in populateCountries:', error);
  }
}

populateCountries(); 