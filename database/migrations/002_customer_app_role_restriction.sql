-- Enhanced <PERSON>W<PERSON> Claims function to validate user roles
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := request->>'sub';
  user_role TEXT;
  user_status TEXT;
  app_metadata JSONB;
BEGIN
  -- Get the user's role from the profiles table
  SELECT user_type, 
         CASE 
           WHEN user_type = 'customer' THEN 'active'
           ELSE 'restricted'
         END as status
  INTO user_role, user_status
  FROM public.profiles 
  WHERE id = user_id;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
    user_status := 'active';
  END IF;
  
  -- Build app metadata
  app_metadata := jsonb_build_object(
    'user_role', user_role,
    'status', user_status,
    'can_access_customer_app', user_role = 'customer'
  );
  
  -- Return the custom claims to be added to the JWT
  RETURN jsonb_build_object(
    'role', user_role,
    'status', user_status,
    'app_metadata', app_metadata
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if a user can access the customer app
CREATE OR REPLACE FUNCTION public.can_access_customer_app(user_id UUID) 
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  RETURN COALESCE(user_role = 'customer', FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if a phone number is already linked to any profile
CREATE OR REPLACE FUNCTION public.get_phone_profile_type(phone_number TEXT)
RETURNS TEXT AS $$
DECLARE
  profile_type TEXT;
BEGIN
  SELECT user_type INTO profile_type
  FROM public.profiles
  WHERE phone = phone_number
  LIMIT 1;
  
  RETURN profile_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Modify the handle_new_user function to enforce customer role for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  existing_profile_id UUID;
  existing_profile_type TEXT;
  phone_value TEXT;
BEGIN
  -- Get phone number from the new user metadata if available
  SELECT raw_user_meta_data->>'phone' INTO phone_value FROM auth.users WHERE id = NEW.id;

  IF phone_value IS NOT NULL THEN
    -- Check if a profile with this phone already exists and get its type
    SELECT id, user_type 
    INTO existing_profile_id, existing_profile_type 
    FROM public.profiles 
    WHERE phone = phone_value 
    LIMIT 1;
    
    IF existing_profile_id IS NOT NULL THEN
      -- A profile already exists with this phone number
      RAISE LOG 'User % has the same phone number as existing profile % with role %', 
                NEW.id, existing_profile_id, existing_profile_type;
      
      -- If we're in a sign-up context, we'll handle this in the app UI
      -- Here we just create a record of the attempted signup
      INSERT INTO public.profile_conflicts (
        auth_user_id, 
        conflicting_profile_id, 
        conflict_type, 
        resolution_status,
        created_at
      ) VALUES (
        NEW.id, 
        existing_profile_id, 
        'phone_number', 
        'pending',
        NOW()
      );
    ELSE
      -- Create a new profile for this user as a customer
      INSERT INTO public.profiles (id, phone, user_type, created_at, updated_at)
      VALUES (NEW.id, phone_value, 'customer', NOW(), NOW());
    END IF;
  ELSE
    -- Handle users without a phone number (e.g., email signup)
    INSERT INTO public.profiles (id, email, user_type, created_at, updated_at)
    VALUES (NEW.id, NEW.email, 'customer', NOW(), NOW());
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create profile_conflicts table for tracking conflict resolution
CREATE TABLE IF NOT EXISTS public.profile_conflicts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  auth_user_id UUID NOT NULL REFERENCES auth.users(id),
  conflicting_profile_id UUID NOT NULL REFERENCES public.profiles(id),
  conflict_type TEXT NOT NULL CHECK (conflict_type IN ('phone_number', 'email')),
  resolution_status TEXT NOT NULL CHECK (resolution_status IN ('pending', 'merged', 'rejected')),
  resolved_by UUID REFERENCES auth.users(id),
  resolved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  notes TEXT
);

-- Index for faster lookups
CREATE INDEX IF NOT EXISTS idx_profile_conflicts_auth_user_id ON public.profile_conflicts(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_profile_conflicts_conflicting_profile_id ON public.profile_conflicts(conflicting_profile_id); 