-- Fix JWT Claims function to handle OTP verification edge cases
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := request->>'sub';
  user_role TEXT;
  user_status TEXT;
  app_metadata JSONB;
BEGIN
  -- If user_id is null, return basic claims with default role (for OTP flows)
  IF user_id IS NULL THEN
    RETURN jsonb_build_object(
      'role', 'customer',
      'status', 'active',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer',
        'status', 'active',
        'can_access_customer_app', true
      )
    );
  END IF;

  -- Get the user's role from the profiles table
  SELECT user_type, 
         CASE 
           WHEN user_type = 'customer' THEN 'active'
           ELSE 'restricted'
         END as status
  INTO user_role, user_status
  FROM public.profiles 
  WHERE id = user_id;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
    user_status := 'active';
  END IF;
  
  -- Build app metadata
  app_metadata := jsonb_build_object(
    'user_role', user_role,
    'status', user_status,
    'can_access_customer_app', user_role = 'customer'
  );
  
  -- Return the custom claims to be added to the JWT
  RETURN jsonb_build_object(
    'role', user_role,
    'status', user_status,
    'app_metadata', app_metadata
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure we don't get null values in the JWT during the claim creation
CREATE OR REPLACE FUNCTION auth.jwt() RETURNS JSONB AS $$
DECLARE
  result JSONB;
  claims JSONB;
BEGIN
  -- Get the base JWT claim
  result := current_setting('request.jwt.claim', true)::JSONB;
  
  -- If it's null, create an empty object
  IF result IS NULL THEN
    result := '{}'::JSONB;
  END IF;
  
  -- Get custom claims
  claims := auth.jwt_claim(result);
  
  -- If claims is null, use empty object with defaults
  IF claims IS NULL THEN
    claims := jsonb_build_object(
      'role', 'customer',
      'status', 'active',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer',
        'status', 'active',
        'can_access_customer_app', true
      )
    );
  END IF;
  
  -- Merge and return the result
  RETURN result || claims;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 