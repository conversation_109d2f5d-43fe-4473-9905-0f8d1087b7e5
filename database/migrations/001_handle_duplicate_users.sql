-- Function to handle profile creation or linking for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  existing_profile_id UUID;
  phone_value TEXT;
BEGIN
  -- Get phone number from the new user metadata if available
  SELECT raw_user_meta_data->>'phone' INTO phone_value FROM auth.users WHERE id = NEW.id;

  IF phone_value IS NOT NULL THEN
    -- Check if a profile with this phone already exists
    SELECT id INTO existing_profile_id FROM public.profiles WHERE phone = phone_value LIMIT 1;
    
    IF existing_profile_id IS NOT NULL THEN
      -- A profile already exists with this phone number
      -- Here you could implement merging logic or just log the situation
      RAISE NOTICE 'User % has the same phone number as existing profile %', NEW.id, existing_profile_id;
    ELSE
      -- Create a new profile for this user
      INSERT INTO public.profiles (id, phone, user_type, created_at, updated_at)
      VALUES (NEW.id, phone_value, 'customer', NOW(), NOW());
    END IF;
  ELSE
    -- Handle users without a phone number (e.g., email signup)
    INSERT INTO public.profiles (id, email, user_type, created_at, updated_at)
    VALUES (NEW.id, NEW.email, 'customer', NOW(), NOW());
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on auth.users table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user(); 