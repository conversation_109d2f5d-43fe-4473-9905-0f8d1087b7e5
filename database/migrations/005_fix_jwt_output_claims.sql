-- Fix the JWT claims issue causing "Expected: object, given: null" error
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID;
  user_role TEXT := 'customer';  -- Default role
  user_status TEXT := 'active';  -- Default status
  app_metadata JSONB;
BEGIN
  -- Always initialize app_metadata with default values
  app_metadata := jsonb_build_object(
    'user_role', user_role,
    'status', user_status,
    'can_access_customer_app', TRUE
  );

  -- Safely extract user_id, handling null request
  IF request IS NULL OR request = '{}'::JSONB THEN
    -- Return default values if request is null or empty
    RETURN jsonb_build_object(
      'role', user_role,
      'status', user_status,
      'app_metadata', app_metadata
    );
  END IF;

  -- Safely get user_id
  user_id := request->>'sub';

  -- If user_id is null, return default claims
  IF user_id IS NULL THEN
    RETURN jsonb_build_object(
      'role', user_role,
      'status', user_status,
      'app_metadata', app_metadata
    );
  END IF;

  -- Try to get user role from profiles table if user exists
  BEGIN
    SELECT user_type, 
           CASE WHEN user_type = 'customer' THEN 'active' ELSE 'restricted' END
    INTO user_role, user_status
    FROM public.profiles 
    WHERE id = user_id;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error getting user role: %', SQLERRM;
      -- Fall back to defaults (already set above)
  END;
  
  -- Always set defaults if we didn't get valid data
  IF user_role IS NULL THEN
    user_role := 'customer';
    user_status := 'active';
  END IF;
  
  -- Update app_metadata with actual values
  app_metadata := jsonb_build_object(
    'user_role', user_role,
    'status', user_status,
    'can_access_customer_app', user_role = 'customer'
  );
  
  -- Return complete claims object - never return NULL
  RETURN jsonb_build_object(
    'role', user_role,
    'status', user_status,
    'app_metadata', app_metadata
  );
EXCEPTION
  WHEN OTHERS THEN
    -- Catch any other errors and return safe defaults
    RAISE LOG 'Uncaught error in jwt_claim: %', SQLERRM;
    RETURN jsonb_build_object(
      'role', 'customer',
      'status', 'active',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer',
        'status', 'active',
        'can_access_customer_app', true
      )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a custom supabase auth function to set claims for sessions
CREATE OR REPLACE FUNCTION auth.claim_safe(event JSONB) 
RETURNS JSONB LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  claims JSONB;
BEGIN
  -- Ensure event is never null
  IF event IS NULL THEN
    event := '{}'::JSONB;
  END IF;
  
  -- Call the auth.jwt_claim function with proper error handling
  BEGIN
    claims := auth.jwt_claim(event);
    IF claims IS NULL THEN
      -- Fallback to default claims if still null
      claims := jsonb_build_object(
        'role', 'customer',
        'status', 'active',
        'app_metadata', jsonb_build_object(
          'user_role', 'customer',
          'status', 'active',
          'can_access_customer_app', true
        )
      );
    END IF;
  EXCEPTION WHEN OTHERS THEN
    -- Handle any unexpected error with safe defaults
    claims := jsonb_build_object(
      'role', 'customer',
      'status', 'active',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer',
        'status', 'active',
        'can_access_customer_app', true
      )
    );
  END;
  
  RETURN claims;
END;
$$;

-- Override JWT generation function
CREATE OR REPLACE FUNCTION auth.jwt() RETURNS JSONB AS $$
DECLARE
  result JSONB;
  custom_claims JSONB;
BEGIN
  -- Get the base JWT claim safely
  BEGIN
    result := COALESCE(current_setting('request.jwt.claim', true)::JSONB, '{}'::JSONB);
  EXCEPTION WHEN OTHERS THEN
    result := '{}'::JSONB;
  END;
  
  -- Get custom claims using our safe function
  custom_claims := auth.claim_safe(result);
  
  -- Return merged result
  RETURN result || custom_claims;
EXCEPTION
  WHEN OTHERS THEN
    -- Handle any errors in the JWT function itself
    RAISE LOG 'Error in JWT function: %', SQLERRM;
    -- Return a minimal valid JWT with default claims
    RETURN jsonb_build_object(
      'role', 'customer',
      'status', 'active',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer',
        'status', 'active',
        'can_access_customer_app', true
      )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 