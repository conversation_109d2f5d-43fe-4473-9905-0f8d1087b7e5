-- Create a table to track <PERSON><PERSON> attempts
CREATE TABLE IF NOT EXISTS auth.otp_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone TEXT NOT NULL,
  attempt_count INTEGER DEFAULT 1,
  last_attempt TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(phone)
);

-- Function to check and update rate limits for OTP
CREATE OR REPLACE FUNCTION auth.check_otp_rate_limit(phone_number TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  existing_record RECORD;
  max_attempts INTEGER := 5; -- Max attempts per time window
  cooldown_period INTERVAL := INTERVAL '30 minutes'; -- Cooldown period after max attempts
  rate_limit_window INTERVAL := INTERVAL '10 minutes'; -- Time window for rate limiting
BEGIN
  -- Look for existing record
  SELECT * INTO existing_record 
  FROM auth.otp_attempts 
  WHERE phone = phone_number;
  
  -- If no record exists, create one and allow
  IF existing_record IS NULL THEN
    INSERT INTO auth.otp_attempts (phone, attempt_count, last_attempt)
    VALUES (phone_number, 1, NOW());
    RETURN TRUE;
  END IF;
  
  -- Check if in cooldown period after max attempts
  IF existing_record.attempt_count >= max_attempts AND 
     NOW() < existing_record.last_attempt + cooldown_period THEN
    RETURN FALSE;
  END IF;
  
  -- If outside rate limit window, reset counter
  IF NOW() > existing_record.last_attempt + rate_limit_window THEN
    UPDATE auth.otp_attempts
    SET attempt_count = 1, last_attempt = NOW()
    WHERE phone = phone_number;
    RETURN TRUE;
  END IF;
  
  -- Update attempt count
  UPDATE auth.otp_attempts
  SET attempt_count = attempt_count + 1, last_attempt = NOW()
  WHERE phone = phone_number;
  
  -- Allow if under max attempts
  RETURN existing_record.attempt_count < max_attempts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset OTP attempts after successful verification
CREATE OR REPLACE FUNCTION auth.reset_otp_attempts(phone_number TEXT)
RETURNS VOID AS $$
BEGIN
  DELETE FROM auth.otp_attempts WHERE phone = phone_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Modify JWT function again to ensure it works with direct token auth
CREATE OR REPLACE FUNCTION auth.jwt() RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Get the base JWT claim
  result := current_setting('request.jwt.claim', true)::JSONB;
  
  -- If it's null, create an empty object with defaults
  IF result IS NULL OR result = '{}'::JSONB THEN
    result := jsonb_build_object(
      'role', 'customer', 
      'sub', NULL,
      'aud', 'authenticated'
    );
  END IF;
  
  -- Add custom claims
  RETURN result || auth.jwt_claim(result);
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error and return a safe default
    RAISE LOG 'Error in jwt function: %', SQLERRM;
    RETURN jsonb_build_object(
      'role', 'customer',
      'status', 'active',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer',
        'status', 'active',
        'can_access_customer_app', true
      )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Modify jwt_claim to handle more edge cases
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := request->>'sub';
  user_role TEXT := 'customer';  -- Default role
  user_status TEXT := 'active';  -- Default status
  app_metadata JSONB;
BEGIN
  -- Always return a valid object even if user_id is null
  IF user_id IS NULL THEN
    RETURN jsonb_build_object(
      'role', user_role,
      'status', user_status,
      'app_metadata', jsonb_build_object(
        'user_role', user_role,
        'status', user_status,
        'can_access_customer_app', TRUE
      )
    );
  END IF;

  -- Try to get user role from profiles table if user exists
  BEGIN
    SELECT user_type, 
           CASE WHEN user_type = 'customer' THEN 'active' ELSE 'restricted' END
    INTO user_role, user_status
    FROM public.profiles 
    WHERE id = user_id;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log error but continue with defaults
      RAISE LOG 'Error getting user role: %', SQLERRM;
  END;
  
  -- Always set defaults if we didn't get valid data
  IF user_role IS NULL THEN
    user_role := 'customer';
    user_status := 'active';
  END IF;
  
  -- Build app metadata with safe values
  app_metadata := jsonb_build_object(
    'user_role', user_role,
    'status', user_status,
    'can_access_customer_app', user_role = 'customer'
  );
  
  -- Return complete claims object
  RETURN jsonb_build_object(
    'role', user_role,
    'status', user_status,
    'app_metadata', app_metadata
  );
EXCEPTION
  WHEN OTHERS THEN
    -- Catch any other errors and return safe defaults
    RAISE LOG 'Uncaught error in jwt_claim: %', SQLERRM;
    RETURN jsonb_build_object(
      'role', 'customer',
      'status', 'active',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer',
        'status', 'active',
        'can_access_customer_app', true
      )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to check rate limits before OTP creation
CREATE OR REPLACE FUNCTION auth.before_sms_otp() RETURNS TRIGGER AS $$
DECLARE
  is_allowed BOOLEAN;
BEGIN
  -- Skip if not an SMS type
  IF NEW.factor_type != 'sms' THEN
    RETURN NEW;
  END IF;
  
  -- Check rate limit
  is_allowed := auth.check_otp_rate_limit(NEW.identity_data->>'phone');
  
  IF NOT is_allowed THEN
    RAISE EXCEPTION 'Rate limit exceeded for phone number. Please try again later.'
      USING HINT = 'Wait for a cooling period before requesting another OTP';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for rate limiting
DROP TRIGGER IF EXISTS check_sms_rate_limit ON auth.mfa_factors;
CREATE TRIGGER check_sms_rate_limit
BEFORE INSERT ON auth.mfa_factors
FOR EACH ROW
EXECUTE FUNCTION auth.before_sms_otp(); 