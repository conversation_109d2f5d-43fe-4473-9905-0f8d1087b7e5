// Reset authentication state in the app
// Run this script with: npx expo-cli run:web -- --web

import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function resetAuthState() {
  try {
    console.log('Clearing AsyncStorage auth items...');
    await AsyncStorage.multiRemove([
      '@app:user_role',
      '@app:profile_id', 
      '@app:otp_verification_in_progress',
      '@app:verification_timestamp',
      '@app:verifying_otp_in_progress',
      '@app:last_auth_check',
      '@app:temp_user_type'
    ]);
    
    console.log('Signing out from Supabase...');
    await supabase.auth.signOut({ scope: 'global' });
    
    console.log('Authentication state has been reset.');
  } catch (error) {
    console.error('Error resetting authentication state:', error);
  }
}

// Execute the reset function
resetAuthState(); 