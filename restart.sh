#!/bin/bash

echo "==============================================="
echo "  CLEAN RESTART AFTER JWT HOOK CONFIGURATION  "
echo "==============================================="

# Kill any running processes
echo "Killing any running processes..."
pkill -f "expo" 2>/dev/null
pkill -f "metro" 2>/dev/null
pkill -f "node" 2>/dev/null

# Clear all app caches
echo "Clearing all caches..."
rm -rf node_modules/.cache
rm -rf $TMPDIR/metro-* 2>/dev/null
rm -rf $HOME/.expo/cache
rm -rf .expo

# Clear AsyncStorage cache
echo "Attempting to clear AsyncStorage cache..."
if [ -d "$HOME/Library/Developer/CoreSimulator/Devices" ]; then
  find $HOME/Library/Developer/CoreSimulator/Devices -name "*.sqlite" -path "*expo*" -delete 2>/dev/null
  echo "Simulator AsyncStorage cleared."
fi

# Clear watchman watches
echo "Clearing watchman watches..."
watchman watch-del-all 2>/dev/null
watchman watch-del '/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app' 2>/dev/null
watchman watch-project '/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app' 2>/dev/null

# Delete node_modules and reinstall (optional - uncomment if needed)
# echo "Reinstalling node modules..."
# rm -rf node_modules
# npm install --legacy-peer-deps

# Start the app with a completely clean slate
echo "Starting the app with a clean slate..."
echo "Remember to configure the Custom Access Token Hook in the Supabase Dashboard!"
echo "==============================================="
npx expo start --clear 