{"name": "dumpster-user-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web", "startClear": "npx expo start --clear"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@gorhom/bottom-sheet": "^4.6.4", "@react-native-community/cli-platform-ios": "^18.0.0", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/slider": "4.5.5", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.67.1", "autoprefixer": "^10.4.20", "axios": "^1.8.4", "base64-arraybuffer": "^1.0.2", "buffer": "^6.0.3", "date-fns": "^4.1.0", "esm": "^3.2.25", "expo": "^52.0.16", "expo-asset": "^11.0.5", "expo-auth-session": "^6.0.3", "expo-background-fetch": "^13.0.6", "expo-blur": "^14.0.3", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.8", "expo-crypto": "^14.0.2", "expo-font": "^13.0.4", "expo-haptics": "^14.0.1", "expo-image": "~2.0.7", "expo-image-picker": "^16.0.6", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.0.2", "expo-location": "~18.0.10", "expo-notifications": "~0.29.14", "expo-router": "~4.0.19", "expo-splash-screen": "~0.29.7", "expo-status-bar": "~2.0.0", "expo-task-manager": "^12.0.6", "expo-updates": "~0.27.4", "expo-web-browser": "^14.0.2", "i18next": "^24.2.2", "nativewind": "2.0.11", "openai": "^4.89.1", "postcss": "8.4.23", "react": "18.3.1", "react-async-hook": "^4.0.0", "react-dom": "18.3.1", "react-i18next": "^15.4.1", "react-native": "0.76.9", "react-native-country-picker-modal": "^2.0.0", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "~1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-linear-gradient": "^2.8.3", "react-native-localization": "^2.3.2", "react-native-maps": "1.18.0", "react-native-modal": "^14.0.0-rc.1", "react-native-otp-entry": "^1.8.4", "react-native-paper": "^5.13.1", "react-native-phone-number-input": "^2.1.0", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "~3.16.1", "react-native-reanimated-carousel": "^4.0.2", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.6", "tailwindcss": "3.3.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli": "^17.0.0", "@supabase/supabase-js": "^2.49.1", "@types/color": "^4.2.0", "@types/node": "^22.14.0", "@types/react": "~18.3.12", "@types/react-native": "^0.73.0", "babel-plugin-module-resolver": "^5.0.2", "commander": "^13.1.0", "dotenv": "^16.4.7", "react-native-url-polyfill": "^2.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}