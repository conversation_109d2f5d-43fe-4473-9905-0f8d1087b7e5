-- First, create size records for the containers that are missing them
-- Use gen_random_uuid() to generate valid UUIDs
INSERT INTO dumpster_sizes (id, name, volume_cubic_yards, max_weight_pounds, length, width, height, description)
VALUES 
  (gen_random_uuid(), '15 yd', 15, 5000, 16, 8, 4, 'Standard 15 yard size'),
  (gen_random_uuid(), '20 yd', 20, 6000, 20, 8, 4.5, 'Standard 20 yard size'),
  (gen_random_uuid(), '30 yd', 30, 8000, 22, 8, 6, 'Large 30 yard size'),
  (gen_random_uuid(), '40 yd', 40, 10000, 24, 8, 8, 'Extra large 40 yard size')
RETURNING id, name;

-- Update the dumpsters with their appropriate sizes based on name
-- We need to store the UUIDs from the previous query and use them here
-- Since we can't easily do that in a script, let's do separate size queries

-- Get the ID of the 15yd size
WITH size_15yd AS (
  SELECT id FROM dumpster_sizes WHERE name = '15 yd' LIMIT 1
)
UPDATE dumpsters
SET size_id = (SELECT id FROM size_15yd)
WHERE name_en LIKE '%15%Yard%' AND size_id IS NULL;

-- Get the ID of the 20yd size
WITH size_20yd AS (
  SELECT id FROM dumpster_sizes WHERE name = '20 yd' LIMIT 1
)
UPDATE dumpsters
SET size_id = (SELECT id FROM size_20yd)
WHERE name_en LIKE '%20%Yard%' AND size_id IS NULL;

-- Get the ID of the 30yd size
WITH size_30yd AS (
  SELECT id FROM dumpster_sizes WHERE name = '30 yd' LIMIT 1
)
UPDATE dumpsters
SET size_id = (SELECT id FROM size_30yd)
WHERE name_en LIKE '%30%Yard%' AND size_id IS NULL;

-- Get the ID of the 40yd size
WITH size_40yd AS (
  SELECT id FROM dumpster_sizes WHERE name = '40 yd' LIMIT 1
)
UPDATE dumpsters
SET size_id = (SELECT id FROM size_40yd)
WHERE name_en LIKE '%40%Yard%' AND size_id IS NULL;

-- Add some default features for containers with null features
INSERT INTO features (id, name_en, name_ar)
VALUES 
  (gen_random_uuid(), 'Double doors', 'باب مزدوج'),
  (gen_random_uuid(), 'Heavy duty construction', 'تصميم قوي'),
  (gen_random_uuid(), 'Easy loading', 'سهولة التحميل'),
  (gen_random_uuid(), 'Roll-off design', 'تصميم دوار')
RETURNING id, name_en;

-- Link features to dumpsters that have null features
WITH dumpsters_missing_features AS (
  SELECT d.id
  FROM dumpsters d
  LEFT JOIN dumpster_feature_links dfl ON d.id = dfl.dumpster_id
  GROUP BY d.id
  HAVING COUNT(dfl.feature_id) = 0
),
features_to_add AS (
  SELECT id FROM features WHERE name_en IN ('Double doors', 'Heavy duty construction', 'Easy loading')
)
INSERT INTO dumpster_feature_links (dumpster_id, feature_id)
SELECT dmf.id, f.id
FROM dumpsters_missing_features dmf
CROSS JOIN features_to_add f
ON CONFLICT (dumpster_id, feature_id) DO NOTHING;

-- Add default waste types for containers without waste types
INSERT INTO waste_types (id, name_en, name_ar, description_en, image_url)
VALUES 
  (gen_random_uuid(), 'Mixed waste', 'نفايات مختلطة', 'General mixed waste', 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/waste_types/mixed_waste.png'),
  (gen_random_uuid(), 'Construction waste', 'نفايات البناء', 'Construction and demolition debris', 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/waste_types/construction_waste.png')
RETURNING id, name_en;

-- Link waste types to dumpsters that have null waste types
WITH dumpsters_missing_waste_types AS (
  SELECT d.id
  FROM dumpsters d
  LEFT JOIN dumpster_waste_types dwt ON d.id = dwt.dumpster_id
  GROUP BY d.id
  HAVING COUNT(dwt.waste_type_id) = 0
),
waste_types_to_add AS (
  SELECT id FROM waste_types WHERE name_en IN ('Mixed waste', 'Construction waste')
)
INSERT INTO dumpster_waste_types (dumpster_id, waste_type_id)
SELECT dmw.id, wt.id
FROM dumpsters_missing_waste_types dmw
CROSS JOIN waste_types_to_add wt
ON CONFLICT (dumpster_id, waste_type_id) DO NOTHING;
