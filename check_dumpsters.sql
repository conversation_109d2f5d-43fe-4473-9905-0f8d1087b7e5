SELECT 
    d.id, 
    d.name_en, 
    d.name_ar, 
    d.description_en, 
    d.description_ar, 
    d.image_url, 
    d.price_per_load, 
    d.rating, 
    d.review_count, 
    d.is_available, 
    d.next_available_date,
    d.partner_id,
    -- Size information
    s.id as size_id, 
    s.name as size_name, 
    s.volume_cubic_yards, 
    s.max_weight_pounds, 
    s.length, 
    s.width, 
    s.height, 
    s.description as size_description,
    -- Feature information
    ARRAY_AGG(DISTINCT f.name_en) as features,
    -- Waste types information
    ARRAY_AGG(DISTINCT wt.name_en) as waste_types
FROM 
    dumpsters d
LEFT JOIN 
    dumpster_sizes s ON d.size_id = s.id
LEFT JOIN 
    dumpster_feature_links dfl ON d.id = dfl.dumpster_id
LEFT JOIN 
    features f ON dfl.feature_id = f.id
LEFT JOIN 
    dumpster_waste_types dwt ON d.id = dwt.dumpster_id
LEFT JOIN 
    waste_types wt ON dwt.waste_type_id = wt.id
GROUP BY 
    d.id, s.id
ORDER BY 
    d.id;
