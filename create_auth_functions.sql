-- Function to get profile by any auth ID (main ID, email auth ID, or phone auth ID)
CREATE OR REPLACE FUNCTION public.get_profile_by_auth_id(auth_id uuid)
RETURNS TABLE (
  id uuid,
  email text,
  phone text,
  full_name text,
  avatar_url text,
  user_type text,
  created_at timestamptz,
  updated_at timestamptz,
  oauth_google_id text,
  email_auth_id uuid,
  phone_auth_id uuid
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT p.*
  FROM public.profiles p
  WHERE p.id = auth_id
     OR p.email_auth_id = auth_id
     OR p.phone_auth_id = auth_id;
END;
$$;

-- Function to link an auth ID to a profile
CREATE OR REPLACE FUNCTION public.link_auth_id(
  profile_id uuid,
  new_auth_id uuid,
  auth_type text -- 'email', 'phone', or 'unknown'
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  success boolean := false;
BEGIN
  -- Determine the auth type if 'unknown' is provided
  IF auth_type = 'unknown' THEN
    -- Check if this auth ID is associated with an email
    IF EXISTS (SELECT 1 FROM auth.users WHERE id = new_auth_id AND email IS NOT NULL) THEN
      auth_type := 'email';
    ELSE
      auth_type := 'phone';
    END IF;
  END IF;
  
  -- Update the appropriate auth ID field
  IF auth_type = 'email' THEN
    UPDATE public.profiles
    SET email_auth_id = new_auth_id
    WHERE id = profile_id
    AND (email_auth_id IS NULL OR email_auth_id <> new_auth_id);
    
    GET DIAGNOSTICS success = ROW_COUNT;
  ELSIF auth_type = 'phone' THEN
    UPDATE public.profiles
    SET phone_auth_id = new_auth_id
    WHERE id = profile_id
    AND (phone_auth_id IS NULL OR phone_auth_id <> new_auth_id);
    
    GET DIAGNOSTICS success = ROW_COUNT;
  END IF;
  
  -- Log the linking attempt
  INSERT INTO public.logs (event_type, details)
  VALUES (
    'auth_id_linking', 
    jsonb_build_object(
      'profile_id', profile_id,
      'auth_id', new_auth_id,
      'auth_type', auth_type,
      'success', success,
      'timestamp', now()
    )
  );
  
  RETURN success;
END;
$$;

-- Function to check for auth ID collisions (same auth ID linked to different profiles)
CREATE OR REPLACE FUNCTION public.check_auth_id_collision(user_id uuid)
RETURNS TABLE (
  profile_id uuid,
  user_type text,
  collision_type text -- 'email' or 'phone'
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check for email auth ID collisions
  RETURN QUERY
  SELECT p.id as profile_id, p.user_type, 'email' as collision_type
  FROM public.profiles p
  WHERE p.email_auth_id = user_id AND p.id <> user_id
  UNION ALL
  -- Check for phone auth ID collisions
  SELECT p.id as profile_id, p.user_type, 'phone' as collision_type
  FROM public.profiles p
  WHERE p.phone_auth_id = user_id AND p.id <> user_id;
END;
$$;

-- Function to find profile by phone number
CREATE OR REPLACE FUNCTION public.find_profile_by_phone(phone_in text)
RETURNS TABLE (
  id uuid,
  user_type text,
  full_name text,
  email text,
  phone text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT p.id, p.user_type, p.full_name, p.email, p.phone
  FROM public.profiles p
  WHERE p.phone = phone_in;
END;
$$; 