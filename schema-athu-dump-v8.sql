--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.12 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA auth;


--
-- Name: aal_level; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.aal_level AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


--
-- Name: code_challenge_method; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.code_challenge_method AS ENUM (
    's256',
    'plain'
);


--
-- Name: factor_status; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.factor_status AS ENUM (
    'unverified',
    'verified'
);


--
-- Name: factor_type; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.factor_type AS ENUM (
    'totp',
    'webauthn',
    'phone'
);


--
-- Name: one_time_token_type; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.one_time_token_type AS ENUM (
    'confirmation_token',
    'reauthentication_token',
    'recovery_token',
    'email_change_token_new',
    'email_change_token_current',
    'phone_change_token'
);


--
-- Name: claim_safe(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.claim_safe(event jsonb) RETURNS jsonb
    LANGUAGE sql STABLE SECURITY DEFINER
    AS $$
    SELECT auth.jwt_claim(event);
$$;


--
-- Name: custom_access_token_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.custom_access_token_hook(event jsonb) RETURNS jsonb
    LANGUAGE sql
    AS $$
    SELECT jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
$$;


--
-- Name: custom_jwt_claim(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.custom_jwt_claim(payload jsonb) RETURNS jsonb
    LANGUAGE sql STABLE SECURITY DEFINER
    AS $$
    SELECT auth.jwt_claim(payload);
$$;


--
-- Name: direct_string_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.direct_string_hook(event jsonb) RETURNS jsonb
    LANGUAGE sql
    AS $$
    SELECT '{"role":"authenticated"}'::jsonb;
$$;


--
-- Name: email(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.email() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


--
-- Name: FUNCTION email(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.email() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';


--
-- Name: fixed_jwt_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.fixed_jwt_hook(event jsonb) RETURNS jsonb
    LANGUAGE sql
    AS $$
    SELECT '{"role":"authenticated","app_metadata":{"user_role":"customer"}}'::jsonb;
$$;


--
-- Name: jwt(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt() RETURNS jsonb
    LANGUAGE sql STABLE SECURITY DEFINER
    AS $$
    -- Get base JWT and add our claims
    SELECT COALESCE(
        current_setting('request.jwt.claim', true)::JSONB,
        '{}'::JSONB
    ) || auth.jwt_claim('{}');
$$;


--
-- Name: jwt_claim(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt_claim(request jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  user_id UUID;
  user_role TEXT;
BEGIN
  -- Get the user_id from the request
  user_id := (request->>'sub')::UUID;
  
  -- Log request for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_claim_request', jsonb_build_object(
    'user_id', user_id,
    'timestamp', now(),
    'request', request
  ));
  
  -- Get the user's role from the profiles table if it exists
  BEGIN
    SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  EXCEPTION WHEN OTHERS THEN
    -- Default to customer if any error occurs
    user_role := 'customer';
  END;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Return JWT claims following the exact structure required by Supabase
  -- Required claims must include role at minimum
  RETURN jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
END;
$$;


--
-- Name: jwt_claims(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt_claims(event jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  result jsonb;
BEGIN
  -- Log the call
  INSERT INTO public.debug_logs (source, message, data) 
  VALUES ('jwt_claims', 'Hook called', event);
  
  -- Return a simple claims object
  result := jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
  
  -- Log the result
  INSERT INTO public.debug_logs (source, message, data) 
  VALUES ('jwt_claims', 'Returning result', result);
  
  RETURN result;
EXCEPTION 
  WHEN OTHERS THEN
    -- Log error
    INSERT INTO public.debug_logs (source, message, data) 
    VALUES ('jwt_claims_error', SQLERRM, event);
    
    -- Always return a valid object
    RETURN jsonb_build_object('role', 'authenticated');
END;
$$;


--
-- Name: jwt_generate_role(text); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt_generate_role(role text) RETURNS void
    LANGUAGE sql SECURITY DEFINER
    AS $$
    -- This is just a stub that does nothing but ensures the function exists
    SELECT NULL::void;
$$;


--
-- Name: FUNCTION jwt_generate_role(role text); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.jwt_generate_role(role text) IS 'Helper function to set the role claim in JWT tokens';


--
-- Name: jwt_hook_final(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt_hook_final(event jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    user_id text;
    result jsonb;
BEGIN
    -- Insert a log entry for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_called', jsonb_build_object('event', event));
    
    -- Get the user_id from the event
    user_id := event->>'user_id';
    
    -- Log the user_id for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_user_id', jsonb_build_object('user_id', user_id));
    
    -- Create a simple response with role and app_metadata
    result := jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object('user_role', 'customer')
    );
    
    -- Log the result for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_result', result);
    
    RETURN result;
EXCEPTION WHEN OTHERS THEN
    -- Log any errors
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_error', jsonb_build_object('error', SQLERRM, 'event', event));
    
    -- Return a default result even if there's an error
    RETURN jsonb_build_object('role', 'authenticated');
END;
$$;


--
-- Name: jwt_hook_function(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt_hook_function() RETURNS jsonb
    LANGUAGE sql STABLE SECURITY DEFINER
    AS $$
    SELECT auth.jwt_claim('{}');
$$;


--
-- Name: mimic_sms_jwt_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.mimic_sms_jwt_hook(event jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  user_id UUID;
  result jsonb;
BEGIN
  -- Extract user ID with null checks, just like SMS hook extracts phone
  user_id := (event->> 'sub')::uuid;
  
  IF user_id IS NULL THEN
    -- Log error and return a minimal object, similar to SMS hook
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_error', jsonb_build_object('error', 'User ID is null', 'event', event));
    
    -- Return an object in the same pattern as SMS hook
    RETURN jsonb_build_object(
      'role', 'authenticated'
    );
  END IF;
  
  -- Log the event just like SMS hook does
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_hook_called', jsonb_build_object('user_id', user_id));
  
  -- This section replaces the HTTP call in the SMS hook
  -- Just create a simple result
  result := jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
  
  -- Log the result like SMS hook logs the response
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_hook_result', result);
  
  -- Return success just like SMS hook
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error exactly like SMS hook
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_exception', jsonb_build_object('error', SQLERRM));
    
    -- Return a minimal object in case of error
    RETURN jsonb_build_object(
      'role', 'authenticated'
    );
END;
$$;


--
-- Name: minimal_jwt_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.minimal_jwt_hook(event jsonb) RETURNS jsonb
    LANGUAGE sql SECURITY DEFINER
    AS $$
    SELECT jsonb_build_object(
        'role', 'authenticated'
    );
$$;


--
-- Name: otp_sms_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.otp_sms_hook(event jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  phone TEXT;
  otp TEXT;
  response JSONB;
  api_key TEXT := '$2y$10$TK6W./k9tr/UFRQ0ue3igeYyd9UCeLXo0wukQBB2ojOIQ05atC.0i'; -- Your Authentica API key
BEGIN
  -- Extract phone and OTP with null checks
  phone := event->'user'->>'phone';
  otp := event->'sms'->>'otp';
  
  IF phone IS NULL THEN
    INSERT INTO public.logs (event_type, details)
    VALUES ('otp_sms_hook_error', jsonb_build_object('error', 'Phone is null', 'event', event));
    
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Missing phone'
    );
  END IF;
  
  -- Log the hook event for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('otp_sms_hook_called', jsonb_build_object('phone', phone, 'otp', otp));
  
  -- Format the phone number correctly if needed
  IF LEFT(phone, 1) != '+' THEN
    phone := '+' || phone;
  END IF;
  
  -- Make HTTP request to Authentica OTP API
  BEGIN
    SELECT
      content::jsonb INTO response
    FROM
      extensions.http((
        'POST',
        'https://api.authentica.sa/api/v1/send-otp',
        ARRAY[
          extensions.http_header('X-Authorization', api_key),
          extensions.http_header('Accept', 'application/json'),
          extensions.http_header('Content-Type', 'application/json')
        ],
        'application/json',
        jsonb_build_object(
          'phone', phone,
          'method', 'sms',
          'template_id', 1,  -- Using default template ID
          'otp', otp  -- Use the OTP provided by Supabase
        )::text
      ));
    
    -- Log the response for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('otp_sms_api_response', response);
    
    -- Check if the request was successful
    IF response->>'success' = 'true' THEN
      RETURN jsonb_build_object(
        'success', true
      );
    ELSE
      INSERT INTO public.logs (event_type, details)
      VALUES ('otp_sms_api_error', jsonb_build_object('error', 'API returned error', 'response', response));
      
      -- Return success anyway to allow the authentication flow to continue
      -- This is a workaround if SMS delivery is not critical
      RETURN jsonb_build_object(
        'success', true
      );
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error
      INSERT INTO public.logs (event_type, details)
      VALUES ('otp_sms_api_exception', jsonb_build_object('error', SQLERRM));
      
      -- Return success anyway to allow the authentication flow to continue
      -- This is a workaround if SMS delivery is not critical
      RETURN jsonb_build_object(
        'success', true
      );
  END;
END;
$_$;


--
-- Name: role(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.role() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  SELECT COALESCE(
    current_setting('request.jwt.claim.role', true),
    (SELECT user_type FROM public.profiles WHERE id = auth.uid()),
    'authenticated'
  )::text;
$$;


--
-- Name: FUNCTION role(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.role() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';


--
-- Name: send_sms_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.send_sms_hook(event jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  phone TEXT;
  otp TEXT;
  message TEXT;
  response JSONB;
  api_key TEXT := '$2y$10$TK6W./k9tr/UFRQ0ue3igeYyd9UCeLXo0wukQBB2ojOIQ05atC.0i'; -- Replace with your actual API key
  sender_name TEXT := 'DumpsterOD'; -- Replace with your registered sender name
BEGIN
  -- Add defensive coding to handle null values
  IF event IS NULL OR event->'user' IS NULL THEN
    INSERT INTO public.logs (event_type, details)
    VALUES ('sms_hook_error', jsonb_build_object('error', 'Event or user is null', 'event', event));
    
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Invalid event format'
    );
  END IF;

  -- Extract phone and OTP with null checks
  phone := event->'user'->>'phone';
  otp := event->'sms'->>'otp';
  
  IF phone IS NULL OR otp IS NULL THEN
    INSERT INTO public.logs (event_type, details)
    VALUES ('sms_hook_error', jsonb_build_object('error', 'Phone or OTP is null', 'phone', phone, 'otp_present', otp IS NOT NULL));
    
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Missing phone or OTP'
    );
  END IF;
  
  -- Log the hook event for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('sms_hook_called', jsonb_build_object('phone', phone, 'otp', otp));
  
  -- Create the message with the OTP
  message := 'Your verification code for Dumpster On Demand is: ' || otp;
  
  -- Format the phone number correctly if needed
  -- If the phone number doesn't start with '+', add it
  IF LEFT(phone, 1) != '+' THEN
    phone := '+' || phone;
  END IF;
  
  -- Log the formatted phone and message
  INSERT INTO public.logs (event_type, details)
  VALUES ('sms_sending', jsonb_build_object('phone', phone, 'message', message));
  
  -- Make HTTP request to Authentica API
  BEGIN
    SELECT
      content::jsonb INTO response
    FROM
      extensions.http((
        'POST',
        'https://api.authentica.sa/api/v1/send-sms',
        ARRAY[
          extensions.http_header('X-Authorization', api_key),
          extensions.http_header('Accept', 'application/json'),
          extensions.http_header('Content-Type', 'application/json')
        ],
        'application/json',
        jsonb_build_object(
          'phone', phone,
          'message', message,
          'sender_name', sender_name
        )::text
      ));
    
    -- Log the response for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('sms_api_response', response);
    
    -- Check if the request was successful
    IF response->>'success' = 'true' THEN
      RETURN jsonb_build_object(
        'success', true
      );
    ELSE
      INSERT INTO public.logs (event_type, details)
      VALUES ('sms_api_error', jsonb_build_object('error', 'API returned error', 'response', response));
      
      RETURN jsonb_build_object(
        'success', false,
        'error', 'API error: ' || (response->>'message')
      );
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error
      INSERT INTO public.logs (event_type, details)
      VALUES ('sms_api_exception', jsonb_build_object('error', SQLERRM));
      
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Exception: ' || SQLERRM
      );
  END;
END;
$_$;


--
-- Name: FUNCTION send_sms_hook(event jsonb); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.send_sms_hook(event jsonb) IS 'SMS authentication hook for Supabase Auth. 
Configure this in the Supabase Dashboard:
1. Go to Authentication > Hooks
2. For the "Send SMS" hook, enter: auth.send_sms_hook
3. Save the configuration';


--
-- Name: simple_sms_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.simple_sms_hook(event jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Log the event
  INSERT INTO public.logs (event_type, details)
  VALUES ('simple_sms_hook_called', event);
  
  -- Always return success
  RETURN jsonb_build_object(
    'success', true
  );
END;
$$;


--
-- Name: test_jwt_hook_formats(text); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.test_jwt_hook_formats(user_id_text text) RETURNS TABLE(format text, result_type text, result_value jsonb)
    LANGUAGE plpgsql
    AS $$
DECLARE
    user_uuid uuid;
    json_result json;
    jsonb_result jsonb;
BEGIN
    -- Convert user ID to UUID
    user_uuid := user_id_text::uuid;
    
    -- Get result from json version
    BEGIN
        SELECT auth.jwt_claim(json_build_object('sub', user_id_text))::jsonb 
        INTO jsonb_result;
        format := 'json';
        result_type := 'json';
        result_value := jsonb_result;
        RETURN NEXT;
    EXCEPTION WHEN others THEN
        format := 'json';
        result_type := 'error';
        result_value := jsonb_build_object('error', SQLERRM);
        RETURN NEXT;
    END;
    
    -- Get result from jsonb version
    BEGIN
        SELECT auth.jwt_claim(jsonb_build_object('sub', user_id_text)) 
        INTO jsonb_result;
        format := 'jsonb';
        result_type := 'jsonb';
        result_value := jsonb_result;
        RETURN NEXT;
    EXCEPTION WHEN others THEN
        format := 'jsonb';
        result_type := 'error';
        result_value := jsonb_build_object('error', SQLERRM);
        RETURN NEXT;
    END;
END;
$$;


--
-- Name: test_jwt_hook_formats(text, text, text, text); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.test_jwt_hook_formats(user_id_text text, format text, result_type text, result_value text) RETURNS TABLE(format text, result_type text, result_value jsonb)
    LANGUAGE sql
    AS $$
    -- This is just a stub
    SELECT 
        format::text, 
        result_type::text, 
        '{}'::jsonb AS result_value
    LIMIT 0;
$$;


--
-- Name: uid(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.uid() RETURNS uuid
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


--
-- Name: FUNCTION uid(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.uid() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';


--
-- Name: updated_sms_hook(jsonb); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.updated_sms_hook(event jsonb) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  phone TEXT;
  otp TEXT;
  message TEXT;
  response JSONB;
  api_key TEXT := '$2y$10$TK6W./k9tr/UFRQ0ue3igeYyd9UCeLXo0wukQBB2ojOIQ05atC.0i'; -- Your Authentica API key
BEGIN
  -- Extract phone and OTP with null checks
  phone := event->'user'->>'phone';
  otp := event->'sms'->>'otp';
  
  IF phone IS NULL OR otp IS NULL THEN
    INSERT INTO public.logs (event_type, details)
    VALUES ('updated_sms_hook_error', jsonb_build_object('error', 'Phone or OTP is null', 'phone', phone, 'otp_present', otp IS NOT NULL));
    
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Missing phone or OTP'
    );
  END IF;
  
  -- Log the hook event for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('updated_sms_hook_called', jsonb_build_object('phone', phone, 'otp', otp));
  
  -- Create the message with the OTP
  message := 'Your verification code for Dumpster On Demand is: ' || otp;
  
  -- Format the phone number correctly if needed
  IF LEFT(phone, 1) != '+' THEN
    phone := '+' || phone;
  END IF;
  
  -- Make HTTP request to Authentica API WITHOUT sender_name
  BEGIN
    SELECT
      content::jsonb INTO response
    FROM
      extensions.http((
        'POST',
        'https://api.authentica.sa/api/v1/send-sms',
        ARRAY[
          extensions.http_header('X-Authorization', api_key),
          extensions.http_header('Accept', 'application/json'),
          extensions.http_header('Content-Type', 'application/json')
        ],
        'application/json',
        jsonb_build_object(
          'phone', phone,
          'message', message
          -- Removed sender_name parameter
        )::text
      ));
    
    -- Log the response for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('updated_sms_api_response', response);
    
    -- Check if the request was successful
    IF response->>'success' = 'true' THEN
      RETURN jsonb_build_object(
        'success', true
      );
    ELSE
      INSERT INTO public.logs (event_type, details)
      VALUES ('updated_sms_api_error', jsonb_build_object('error', 'API returned error', 'response', response));
      
      -- Return success anyway to allow the authentication flow to continue
      -- This is a workaround if SMS delivery is not critical
      RETURN jsonb_build_object(
        'success', true
      );
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error
      INSERT INTO public.logs (event_type, details)
      VALUES ('updated_sms_api_exception', jsonb_build_object('error', SQLERRM));
      
      -- Return success anyway to allow the authentication flow to continue
      -- This is a workaround if SMS delivery is not critical
      RETURN jsonb_build_object(
        'success', true
      );
  END;
END;
$_$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: audit_log_entries; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL,
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) DEFAULT ''::character varying NOT NULL
);


--
-- Name: TABLE audit_log_entries; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.audit_log_entries IS 'Auth: Audit trail for user actions.';


--
-- Name: flow_state; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.flow_state (
    id uuid NOT NULL,
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone
);


--
-- Name: TABLE flow_state; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.flow_state IS 'stores metadata for pkce logins';


--
-- Name: identities; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: TABLE identities; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.identities IS 'Auth: Stores identities associated to a user.';


--
-- Name: COLUMN identities.email; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.identities.email IS 'Auth: Email is a generated column that references the optional email property in the identity_data';


--
-- Name: instances; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.instances (
    id uuid NOT NULL,
    uuid uuid,
    raw_base_config text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


--
-- Name: TABLE instances; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.instances IS 'Auth: Manages users across multiple sites.';


--
-- Name: mfa_amr_claims; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL
);


--
-- Name: TABLE mfa_amr_claims; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_amr_claims IS 'auth: stores authenticator method reference claims for multi factor authentication';


--
-- Name: mfa_challenges; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_challenges (
    id uuid NOT NULL,
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL,
    otp_code text,
    web_authn_session_data jsonb
);


--
-- Name: TABLE mfa_challenges; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_challenges IS 'auth: stores metadata about challenge requests made';


--
-- Name: mfa_factors; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_factors (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text,
    phone text,
    last_challenged_at timestamp with time zone,
    web_authn_credential jsonb,
    web_authn_aaguid uuid
);


--
-- Name: TABLE mfa_factors; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_factors IS 'auth: stores metadata about factors';


--
-- Name: one_time_tokens; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.one_time_tokens (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    token_type auth.one_time_token_type NOT NULL,
    token_hash text NOT NULL,
    relates_to text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    CONSTRAINT one_time_tokens_token_hash_check CHECK ((char_length(token_hash) > 0))
);


--
-- Name: refresh_tokens; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.refresh_tokens (
    instance_id uuid,
    id bigint NOT NULL,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid
);


--
-- Name: TABLE refresh_tokens; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.refresh_tokens IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE; Schema: auth; Owner: -
--

CREATE SEQUENCE auth.refresh_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: auth; Owner: -
--

ALTER SEQUENCE auth.refresh_tokens_id_seq OWNED BY auth.refresh_tokens.id;


--
-- Name: saml_providers; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.saml_providers (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    entity_id text NOT NULL,
    metadata_xml text NOT NULL,
    metadata_url text,
    attribute_mapping jsonb,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    name_id_format text,
    CONSTRAINT "entity_id not empty" CHECK ((char_length(entity_id) > 0)),
    CONSTRAINT "metadata_url not empty" CHECK (((metadata_url = NULL::text) OR (char_length(metadata_url) > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK ((char_length(metadata_xml) > 0))
);


--
-- Name: TABLE saml_providers; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.saml_providers IS 'Auth: Manages SAML Identity Provider connections.';


--
-- Name: saml_relay_states; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.saml_relay_states (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,
    CONSTRAINT "request_id not empty" CHECK ((char_length(request_id) > 0))
);


--
-- Name: TABLE saml_relay_states; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.saml_relay_states IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';


--
-- Name: schema_migrations; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.schema_migrations (
    version character varying(255) NOT NULL
);


--
-- Name: TABLE schema_migrations; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.schema_migrations IS 'Auth: Manages updates to the auth system.';


--
-- Name: sessions; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sessions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text
);


--
-- Name: TABLE sessions; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sessions IS 'Auth: Stores session data associated to a user.';


--
-- Name: COLUMN sessions.not_after; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.sessions.not_after IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';


--
-- Name: sso_domains; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sso_domains (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK ((char_length(domain) > 0))
);


--
-- Name: TABLE sso_domains; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sso_domains IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';


--
-- Name: sso_providers; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sso_providers (
    id uuid NOT NULL,
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK (((resource_id = NULL::text) OR (char_length(resource_id) > 0)))
);


--
-- Name: TABLE sso_providers; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sso_providers IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';


--
-- Name: COLUMN sso_providers.resource_id; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.sso_providers.resource_id IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';


--
-- Name: user_roles; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.user_roles (
    id uuid NOT NULL,
    role text DEFAULT 'customer'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: users; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.users (
    instance_id uuid,
    id uuid NOT NULL,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text DEFAULT NULL::character varying,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::character varying,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone,
    is_anonymous boolean DEFAULT false NOT NULL,
    CONSTRAINT users_email_change_confirm_status_check CHECK (((email_change_confirm_status >= 0) AND (email_change_confirm_status <= 2)))
);


--
-- Name: TABLE users; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.users IS 'Auth: Stores user login data within a secure schema.';


--
-- Name: COLUMN users.is_sso_user; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.users.is_sso_user IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';


--
-- Name: refresh_tokens id; Type: DEFAULT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens ALTER COLUMN id SET DEFAULT nextval('auth.refresh_tokens_id_seq'::regclass);


--
-- Name: mfa_amr_claims amr_id_pk; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT amr_id_pk PRIMARY KEY (id);


--
-- Name: audit_log_entries audit_log_entries_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.audit_log_entries
    ADD CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id);


--
-- Name: flow_state flow_state_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.flow_state
    ADD CONSTRAINT flow_state_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: identities identities_provider_id_provider_unique; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider);


--
-- Name: instances instances_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.instances
    ADD CONSTRAINT instances_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_authentication_method_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method);


--
-- Name: mfa_challenges mfa_challenges_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id);


--
-- Name: mfa_factors mfa_factors_last_challenged_at_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_last_challenged_at_key UNIQUE (last_challenged_at);


--
-- Name: mfa_factors mfa_factors_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_pkey PRIMARY KEY (id);


--
-- Name: one_time_tokens one_time_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_token_unique; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_token_unique UNIQUE (token);


--
-- Name: saml_providers saml_providers_entity_id_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_entity_id_key UNIQUE (entity_id);


--
-- Name: saml_providers saml_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_pkey PRIMARY KEY (id);


--
-- Name: saml_relay_states saml_relay_states_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: sso_domains sso_domains_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_pkey PRIMARY KEY (id);


--
-- Name: sso_providers sso_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_providers
    ADD CONSTRAINT sso_providers_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: audit_logs_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);


--
-- Name: confirmation_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_current_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_new_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text);


--
-- Name: factor_id_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);


--
-- Name: flow_state_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);


--
-- Name: identities_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops);


--
-- Name: INDEX identities_email_idx; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON INDEX auth.identities_email_idx IS 'Auth: Ensures indexed queries on the email column';


--
-- Name: identities_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id);


--
-- Name: idx_auth_code; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code);


--
-- Name: idx_user_id_auth_method; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);


--
-- Name: mfa_challenge_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);


--
-- Name: mfa_factors_user_friendly_name_unique; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);


--
-- Name: mfa_factors_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id);


--
-- Name: one_time_tokens_relates_to_hash_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to);


--
-- Name: one_time_tokens_token_hash_hash_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash);


--
-- Name: one_time_tokens_user_id_token_type_key; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type);


--
-- Name: reauthentication_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: recovery_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: refresh_tokens_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);


--
-- Name: refresh_tokens_instance_id_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);


--
-- Name: refresh_tokens_parent_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);


--
-- Name: refresh_tokens_session_id_revoked_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);


--
-- Name: refresh_tokens_updated_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC);


--
-- Name: saml_providers_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id);


--
-- Name: saml_relay_states_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC);


--
-- Name: saml_relay_states_for_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);


--
-- Name: saml_relay_states_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);


--
-- Name: sessions_not_after_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);


--
-- Name: sessions_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id);


--
-- Name: sso_domains_domain_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));


--
-- Name: sso_domains_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);


--
-- Name: sso_providers_resource_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id));


--
-- Name: unique_phone_factor_per_user; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone);


--
-- Name: user_id_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);


--
-- Name: user_roles_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX user_roles_id_idx ON auth.user_roles USING btree (id);


--
-- Name: users_email_partial_key; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false);


--
-- Name: INDEX users_email_partial_key; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON INDEX auth.users_email_partial_key IS 'Auth: A partial unique index that applies only when is_sso_user is false';


--
-- Name: users_instance_id_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));


--
-- Name: users_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id);


--
-- Name: users_is_anonymous_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous);


--
-- Name: users on_auth_user_created; Type: TRIGGER; Schema: auth; Owner: -
--

CREATE TRIGGER on_auth_user_created AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();


--
-- Name: identities identities_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: mfa_challenges mfa_challenges_auth_factor_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;


--
-- Name: mfa_factors mfa_factors_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: one_time_tokens one_time_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: refresh_tokens refresh_tokens_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: saml_providers saml_providers_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_flow_state_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_flow_state_id_fkey FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: sso_domains sso_domains_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.user_roles
    ADD CONSTRAINT user_roles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: audit_log_entries; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.audit_log_entries ENABLE ROW LEVEL SECURITY;

--
-- Name: flow_state; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.flow_state ENABLE ROW LEVEL SECURITY;

--
-- Name: identities; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.identities ENABLE ROW LEVEL SECURITY;

--
-- Name: instances; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.instances ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_amr_claims; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_amr_claims ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_challenges; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_challenges ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_factors; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_factors ENABLE ROW LEVEL SECURITY;

--
-- Name: one_time_tokens; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.one_time_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: refresh_tokens; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.refresh_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_providers; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.saml_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_relay_states; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.saml_relay_states ENABLE ROW LEVEL SECURITY;

--
-- Name: schema_migrations; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.schema_migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: sessions; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_domains; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sso_domains ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_providers; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sso_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: users; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

--
-- PostgreSQL database dump complete
--

