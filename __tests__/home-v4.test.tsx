import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { act } from 'react-test-renderer';
import HomeV4Screen from '../app/home-v4';
import { useQueryClient } from '@tanstack/react-query';
import { DumpsterKeys } from '../src/hooks/v2/useDumpsters';

// Mock the necessary dependencies
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('@/context/AuthContext', () => ({
  useAuth: () => ({}),
  GlobalUserIdentifier: { profileId: 'test-profile-id' },
}));

jest.mock('@/hooks/useProfile', () => ({
  useProfile: () => ({ profile: { id: 'test-profile-id', name: 'Test User' } }),
}));

jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'NewRTLText',
}));

jest.mock('@/hooks/v2/useDumpsters', () => ({
  useDumpsters: () => ({
    data: [
      {
        id: 'dumpster-1',
        nameEn: 'Test Dumpster 1',
        nameAr: 'اختبار حاوية 1',
        imageUrl: 'https://example.com/dumpster1.jpg',
        pricePerLoad: 100,
        isAvailable: true,
        sizeId: 'size-1',
      },
      {
        id: 'dumpster-2',
        nameEn: 'Test Dumpster 2',
        nameAr: 'اختبار حاوية 2',
        imageUrl: 'https://example.com/dumpster2.jpg',
        pricePerLoad: 150,
        isAvailable: true,
        sizeId: 'size-2',
      },
    ],
    isLoading: false,
  }),
  DumpsterKeys: {
    lists: () => ['dumpsters-v2', 'list'],
  },
}));

jest.mock('@tanstack/react-query', () => ({
  useQueryClient: jest.fn(() => ({
    invalidateQueries: jest.fn(),
  })),
}));

jest.mock('@/utils/user', () => ({
  getUserName: jest.fn(() => Promise.resolve('Yasmin')),
}));

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  
  // Mock the specific functions we need
  Reanimated.useSharedValue = jest.fn((initialValue) => ({ value: initialValue }));
  Reanimated.useAnimatedScrollHandler = jest.fn(() => jest.fn());
  Reanimated.useAnimatedStyle = jest.fn(() => ({}));
  Reanimated.interpolate = jest.fn((value, inputRange, outputRange) => outputRange[0]);
  Reanimated.Extrapolation = { CLAMP: 'clamp' };
  
  return {
    ...Reanimated,
    default: {
      ...Reanimated,
      View: 'Animated.View',
      ScrollView: 'Animated.ScrollView',
    },
  };
});

jest.mock('react-native-raw-bottom-sheet', () => {
  return jest.fn().mockImplementation(() => {
    return {
      open: jest.fn(),
      close: jest.fn(),
    };
  });
});

describe('HomeV4Screen', () => {
  it('renders correctly', async () => {
    const { getByText, getByTestId } = render(<HomeV4Screen />);
    
    // Wait for the component to fully render
    await waitFor(() => {
      expect(getByTestId('order-history-button')).toBeTruthy();
      expect(getByTestId('profile-button')).toBeTruthy();
      expect(getByTestId('city-selector')).toBeTruthy();
    });
  });

  it('shows the correct greeting based on time of day', async () => {
    // Mock the Date to return a specific time
    const originalDate = global.Date;
    
    // Test morning greeting
    global.Date = class extends originalDate {
      constructor() {
        super();
      }
      getHours() {
        return 9; // 9 AM
      }
    } as any;
    
    const { getByText: getByTextMorning } = render(<HomeV4Screen />);
    await waitFor(() => {
      expect(getByTextMorning('Good morning,')).toBeTruthy();
    });
    
    // Test afternoon greeting
    global.Date = class extends originalDate {
      constructor() {
        super();
      }
      getHours() {
        return 14; // 2 PM
      }
    } as any;
    
    const { getByText: getByTextAfternoon } = render(<HomeV4Screen />);
    await waitFor(() => {
      expect(getByTextAfternoon('Good afternoon,')).toBeTruthy();
    });
    
    // Test evening greeting
    global.Date = class extends originalDate {
      constructor() {
        super();
      }
      getHours() {
        return 20; // 8 PM
      }
    } as any;
    
    const { getByText: getByTextEvening } = render(<HomeV4Screen />);
    await waitFor(() => {
      expect(getByTextEvening('Good evening,')).toBeTruthy();
    });
    
    // Restore original Date
    global.Date = originalDate;
  });

  it('invalidates queries when city changes', async () => {
    const mockInvalidateQueries = jest.fn();
    (useQueryClient as jest.Mock).mockReturnValue({
      invalidateQueries: mockInvalidateQueries,
    });
    
    const { getByTestId } = render(<HomeV4Screen />);
    
    // Simulate city selection
    const citySelector = getByTestId('city-selector');
    fireEvent.press(citySelector);
    
    // Verify that the query invalidation was called
    expect(mockInvalidateQueries).toHaveBeenCalledWith({ 
      queryKey: DumpsterKeys.lists() 
    });
  });
});
