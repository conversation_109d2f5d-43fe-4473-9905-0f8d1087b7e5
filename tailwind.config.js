/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,jsx,ts,tsx}",
    "./src/**/*.{js,jsx,ts,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        // Brand colors
        primary: {
          DEFAULT: '#0bd8b6',
          50: '#e6faf7',
          100: '#ccf6ef',
          200: '#99ede0',
          300: '#66e3d0',
          400: '#33dac1',
          500: '#0bd8b6',
          600: '#08ad92',
          700: '#06826d',
          800: '#045749',
          900: '#022b24',
        },
        secondary: {
          DEFAULT: '#226d7a',
          50: '#e9f1f2',
          100: '#d2e3e6',
          200: '#a5c7cc',
          300: '#78abb3',
          400: '#4b8f99',
          500: '#226d7a',
          600: '#1b5762',
          700: '#144149',
          800: '#0e2b31',
          900: '#071518',
        },
        success: {
          DEFAULT: '#00810b',
          50: '#e6f2e7',
          100: '#cce6cf',
          200: '#99cc9f',
          300: '#66b36f',
          400: '#33993f',
          500: '#00810b',
          600: '#006709',
          700: '#004d07',
          800: '#003404',
          900: '#001a02',
        },
        warning: {
          DEFAULT: '#fddb43',
          50: '#fffaeb',
          100: '#fff5d7',
          200: '#ffebaf',
          300: '#ffe187',
          400: '#ffd75f',
          500: '#fddb43',
          600: '#caaf35',
          700: '#988328',
          800: '#65571a',
          900: '#332c0d',
        },
        danger: {
          DEFAULT: '#ff3869',
          50: '#ffebef',
          100: '#ffd7df',
          200: '#ffafbf',
          300: '#ff879f',
          400: '#ff5f7f',
          500: '#ff3869',
          600: '#cc2d54',
          700: '#99223f',
          800: '#66162a',
          900: '#330b15',
        },
        info: {
          DEFAULT: '#2b61de',
          50: '#e9eef9',
          100: '#d3ddf4',
          200: '#a7bbe9',
          300: '#7b99de',
          400: '#4f77d3',
          500: '#2b61de',
          600: '#224eb2',
          700: '#1a3a85',
          800: '#112759',
          900: '#09132c',
        },
        female: {
          DEFAULT: '#ef71c1',
          50: '#fcf0f7',
          100: '#f9e1ef',
          200: '#f4c3df',
          300: '#efa5cf',
          400: '#ea87bf',
          500: '#ef71c1',
          600: '#bf5a9a',
          700: '#8f4474',
          800: '#5f2d4d',
          900: '#301727',
        },
        male: {
          DEFAULT: '#7199ef',
          50: '#f0f3fc',
          100: '#e1e8f9',
          200: '#c3d1f4',
          300: '#a5baef',
          400: '#87a3ea',
          500: '#7199ef',
          600: '#5a7abf',
          700: '#445c8f',
          800: '#2d3d5f',
          900: '#171f30',
        },
        // Surface colors
        surface: {
          light: '#FFFFFF',
          dark: '#1A1A1A',
        },
        // Text colors
        text: {
          light: '#0e0909',
          dark: '#FFFFFF',
          secondary: {
            light: '#666666',
            dark: '#CCCCCC',
          },
          placeholder: {
            light: '#999999',
            dark: '#888888',
          },
        },
      },
      // Typography system
      fontFamily: {
        sans: ['System', 'sans-serif'],
        // Add custom fonts here when needed
      },
      fontSize: {
        // Mobile-first typography scale
        'xs': ['12px', { lineHeight: '16px' }],
        'sm': ['14px', { lineHeight: '20px' }],
        'base': ['16px', { lineHeight: '24px' }],
        'lg': ['18px', { lineHeight: '28px' }],
        'xl': ['20px', { lineHeight: '28px' }],
        '2xl': ['24px', { lineHeight: '32px' }],
        '3xl': ['30px', { lineHeight: '36px' }],
        '4xl': ['36px', { lineHeight: '40px' }],
      },
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      // Spacing system
      spacing: {
        // Base spacing unit: 4px
        '0': '0px',
        '0.5': '2px',
        '1': '4px',
        '2': '8px',
        '3': '12px',
        '4': '16px',
        '5': '20px',
        '6': '24px',
        '8': '32px',
        '10': '40px',
        '12': '48px',
        '16': '64px',
        '20': '80px',
        '24': '96px',
        // Component-specific spacing
        'button-y': '12px',
        'button-x': '24px',
        'card': '16px',
        'input': '16px',
        'section': '24px',
      },
      // Border radius
      borderRadius: {
        'none': '0',
        'sm': '4px',
        DEFAULT: '8px',
        'md': '12px',
        'lg': '16px',
        'xl': '24px',
        '2xl': '32px',
        'full': '9999px',
        // Component-specific radius
        'button': '8px',
        'card': '16px',
        'input': '8px',
      },
      // Simplified shadows for NativeWind v2 compatibility
      boxShadow: {
        // Empty object to disable NativeWind boxShadow - we'll use shadowStyles utility instead
        DEFAULT: 'none',
        'none': 'none',
      },
      // Z-index
      zIndex: {
        'behind': -1,
        'default': 1,
        'dropdown': 1000,
        'sticky': 1020,
        'fixed': 1030,
        'modal-backdrop': 1040,
        'modal': 1050,
        'popover': 1060,
        'tooltip': 1070,
      },
      // Animation
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale': 'scale 0.2s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        scale: {
          '0%': { transform: 'scale(0.95)' },
          '100%': { transform: 'scale(1)' },
        },
      },
      // Layout
      screens: {
        'xs': '360px',
        'sm': '480px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
      },
      container: {
        center: true,
        padding: {
          DEFAULT: '1rem',
          sm: '2rem',
          lg: '4rem',
          xl: '5rem',
        },
      },
    },
  },
  plugins: [],
} 