import { MD3DarkTheme, MD3LightTheme } from 'react-native-paper';

// Brand Colors
export const brandColors = {
  primary: {
    DEFAULT: '#0bd8b6',
    50: '#e6faf7',
    100: '#ccf6ef',
    200: '#99ede0',
    300: '#66e3d0',
    400: '#33dac1',
    500: '#0bd8b6',
    600: '#08ad92',
    700: '#06826d',
    800: '#045749',
    900: '#022b24',
  },
  secondary: {
    DEFAULT: '#226d7a',
    50: '#e9f1f2',
    100: '#d2e3e6',
    200: '#a5c7cc',
    300: '#78abb3',
    400: '#4b8f99',
    500: '#226d7a',
    600: '#1b5762',
    700: '#144149',
    800: '#0e2b31',
    900: '#071518',
  },
  success: {
    DEFAULT: '#00810b',
    50: '#e6f2e7',
    100: '#cce6cf',
    200: '#99cc9f',
    300: '#66b36f',
    400: '#33993f',
    500: '#00810b',
    600: '#006709',
    700: '#004d07',
    800: '#003404',
    900: '#001a02',
  },
  warning: {
    DEFAULT: '#fddb43',
    50: '#fffaeb',
    100: '#fff5d7',
    200: '#ffebaf',
    300: '#ffe187',
    400: '#ffd75f',
    500: '#fddb43',
    600: '#caaf35',
    700: '#988328',
    800: '#65571a',
    900: '#332c0d',
  },
  danger: {
    DEFAULT: '#ff3869',
    50: '#ffebef',
    100: '#ffd7df',
    200: '#ffafbf',
    300: '#ff879f',
    400: '#ff5f7f',
    500: '#ff3869',
    600: '#cc2d54',
    700: '#99223f',
    800: '#66162a',
    900: '#330b15',
  },
  info: {
    DEFAULT: '#2b61de',
    50: '#e9eef9',
    100: '#d3ddf4',
    200: '#a7bbe9',
    300: '#7b99de',
    400: '#4f77d3',
    500: '#2b61de',
    600: '#224eb2',
    700: '#1a3a85',
    800: '#112759',
    900: '#09132c',
  },
  female: {
    DEFAULT: '#ef71c1',
    50: '#fcf0f7',
    100: '#f9e1ef',
    200: '#f4c3df',
    300: '#efa5cf',
    400: '#ea87bf',
    500: '#ef71c1',
    600: '#bf5a9a',
    700: '#8f4474',
    800: '#5f2d4d',
    900: '#301727',
  },
  male: {
    DEFAULT: '#7199ef',
    50: '#f0f3fc',
    100: '#e1e8f9',
    200: '#c3d1f4',
    300: '#a5baef',
    400: '#87a3ea',
    500: '#7199ef',
    600: '#5a7abf',
    700: '#445c8f',
    800: '#2d3d5f',
    900: '#171f30',
  },
  grayScale: {
    DEFAULT: '#808080',
    50: '#f9f9f9',
    100: '#f3f3f3',
    200: '#e8e8e8',
    300: '#dcdcdc',
    400: '#c9c9c9',
    500: '#b3b3b3',
    600: '#9c9c9c',
    700: '#858585',
    800: '#666666',
    900: '#000000',
  },
};

// Surface Colors
export const surfaceColors = {
  light: '#f0f5f3',
  dark: '#141515',
  outline: {
    light: '#d7d7d7',
    dark: '#232323',
  },
  container: {
    light: '#FFFFFF',
    dark: '#222222',
  },
  onPrimaryContainer: {
    light: brandColors.primary[50],
    dark: brandColors.primary[900],
  },
  onSecondaryContainer: {
    light: brandColors.secondary[50],
    dark: brandColors.secondary[900],
  },
};

// Text Colors
export const textColors = {
  light: '#0e0909',
  dark: '#FFFFFF',
  secondary: {
    light: '#666666',
    dark: '#CCCCCC',
  },
  placeholder: {
    light: '#999999',
    dark: '#888888',
  },
};

// background colors
export const backgroundColors = {
  main: {
    light: '#f0f5f3',
    dark: '#141515',
  },
  white: {
    light: '#FFFFFF',
    dark: '#000000',
  },
  black: {
    light: '#000000',
    dark: '#FFFFFF',
  },
  outline: {
    light: '#e6e8e7',
    dark: '#232323',
  },
};
// Button Variants
export const buttonVariants = {
  gradient: {
    light: {
      default: brandColors.primary[500],
      hover: brandColors.secondary[500],
      active: brandColors.primary[700],
      disabled: brandColors.grayScale[300],
    },
    dark: {
      default: brandColors.primary[500],
      hover: brandColors.secondary[500],
      active: brandColors.primary[700],
      disabled: brandColors.grayScale[700],
    },
  },
  light: {
    light: {
      default: brandColors.primary[100],
      hover: brandColors.primary[200],
      active: brandColors.primary[300],
      disabled: brandColors.grayScale[300],
    },
    dark: {
      default: brandColors.primary[900],
      hover: brandColors.primary[800],
      active: brandColors.primary[700],
      disabled: brandColors.grayScale[700],
    },
  },
  solid: {
    light: {
      default: brandColors.primary[500],
      hover: brandColors.primary[600],
      active: brandColors.primary[700],
      disabled: brandColors.grayScale[300],
    },
    dark: {
      default: brandColors.primary[500],
      hover: brandColors.primary[600],
      active: brandColors.primary[600],
      disabled: brandColors.grayScale[700],
    },
  },
};



// Theme Configuration
export const theme = {
  colors: brandColors,
  surface: surfaceColors,
  text: textColors,
  buttons: buttonVariants,
  spacing: {
    '0': 0,
    '0.5': 2,
    '1': 4,
    '2': 8,
    '3': 12,
    '4': 16,
    '5': 20,
    '6': 24,
    '8': 32,
    '10': 40,
    '12': 48,
    '16': 64,
    '20': 80,
    '24': 96,
  },
  borderRadius: {
    none: 0,
    sm: 4,
    DEFAULT: 8,
    md: 12,
    lg: 16,
    xl: 24,
    '2xl': 32,
    full: 9999,
  },
};

// Light Theme
export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: brandColors.primary.DEFAULT,
    secondary: brandColors.secondary.DEFAULT,
    success: brandColors.success.DEFAULT,
    warning: brandColors.warning.DEFAULT,
    danger: brandColors.danger.DEFAULT,
    info: brandColors.info.DEFAULT,
    surface: surfaceColors.light,
    background: surfaceColors.light,
    text: textColors.light,
    'text-secondary': textColors.secondary.light,
    'text-placeholder': textColors.placeholder.light,
  },
};

// Dark Theme
export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: brandColors.primary.DEFAULT,
    secondary: brandColors.secondary.DEFAULT,
    success: brandColors.success.DEFAULT,
    warning: brandColors.warning.DEFAULT,
    danger: brandColors.danger.DEFAULT,
    info: brandColors.info.DEFAULT,
    surface: surfaceColors.dark,
    background: surfaceColors.dark,
    text: textColors.dark,
    'text-secondary': textColors.secondary.dark,
    'text-placeholder': textColors.placeholder.dark,
  },
};

export type CustomThemeColors = typeof lightTheme.colors;

// Helper function to get color with opacity
export const getColorWithOpacity = (color: string, opacity: number) => {
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const rgb = hexToRgb(color);
  return rgb ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})` : color;
}; 