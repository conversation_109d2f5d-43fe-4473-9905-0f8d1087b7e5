:root {
    --bs-primary: #0bd8b6;
    --bs-secondary: #226d7a;
    --bs-success: #4bc375;
    --bs-warning: #fddb43;
    --bs-danger: #ff3869;
    --bs-info: #2b61de;
    --bs-light: #d8f3eb;
    --bs-dark: #0e0909;
    --bs-female: #ef71c1;
    --bs-male: #7199ef;
  }
  
  :root,
  .light,
  .light-theme {
    --teal-1: #eaf2f0;
    --teal-2: #e1f0eb;
    --teal-3: #c0efe2;
    --teal-4: #9fecd8;
    --teal-5: #7ce5cb;
    --teal-6: #54d9bc;
    --teal-7: #00caa8;
    --teal-8: #00b090;
    --teal-9: #00e5bd;
    --teal-10: #00d9b3;
    --teal-11: #006a55;
    --teal-12: #004838;
  
    --teal-a1: #16888607;
    --teal-a2: #01a67410;
    --teal-a3: #00d79e33;
    --teal-a4: #03dba457;
    --teal-a5: #02d4a17c;
    --teal-a6: #00ca9ea6;
    --teal-a7: #00caa8;
    --teal-a8: #00b090;
    --teal-a9: #00e5bd;
    --teal-a10: #00d9b3;
    --teal-a11: #006a55;
    --teal-a12: #004838;
  
    --teal-contrast: #072821;
    --teal-surface: #ddefe9cc;
    --teal-indicator: #00e5bd;
    --teal-track: #00e5bd;
  }
  
  @supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
      :root,
      .light,
      .light-theme {
        --teal-1: oklch(95.6% 0.0091 175.7);
        --teal-2: oklch(94.2% 0.0174 175.7);
        --teal-3: oklch(91.7% 0.0517 175.7);
        --teal-4: oklch(88.8% 0.0812 175.7);
        --teal-5: oklch(85.1% 0.1052 175.7);
        --teal-6: oklch(80.5% 0.1243 175.7);
        --teal-7: oklch(74.5% 0.1467 175.7);
        --teal-8: oklch(66.7% 0.1843 175.7);
        --teal-9: oklch(81.3% 0.1726 175.7);
        --teal-10: oklch(78% 0.1725 175.7);
        --teal-11: oklch(45.8% 0.1642 175.7);
        --teal-12: oklch(35% 0.0874 175.7);
  
        --teal-a1: color(display-p3 0.0078 0.4902 0.4863 / 0.025);
        --teal-a2: color(display-p3 0.0039 0.6 0.3725 / 0.054);
        --teal-a3: color(display-p3 0.0039 0.7961 0.5529 / 0.166);
        --teal-a4: color(display-p3 0.0039 0.8039 0.5765 / 0.27);
        --teal-a5: color(display-p3 0.0039 0.7647 0.549 / 0.378);
        --teal-a6: color(display-p3 0.0039 0.7176 0.5255 / 0.494);
        --teal-a7: color(display-p3 0 0.6745 0.4863 / 0.623);
        --teal-a8: color(display-p3 0 0.5451 0.3922 / 0.677);
        --teal-a9: color(display-p3 0 0.8275 0.5961 / 0.569);
        --teal-a10: color(display-p3 0 0.7569 0.5412 / 0.594);
        --teal-a11: color(display-p3 0 0.2824 0.2 / 0.818);
        --teal-a12: color(display-p3 0 0.1922 0.1294 / 0.884);
  
        --teal-contrast: #072821;
        --teal-surface: color(display-p3 0.8784 0.9333 0.9137 / 0.8);
        --teal-indicator: oklch(81.3% 0.1726 175.7);
        --teal-track: oklch(81.3% 0.1726 175.7);
      }
    }
  }
  :root,
  .light,
  .light-theme {
    --gray-1: #ebf1f3;
    --gray-2: #e1eff2;
    --gray-3: #d2e7eb;
    --gray-4: #c3dfe5;
    --gray-5: #b5d9e1;
    --gray-6: #a6d2db;
    --gray-7: #90c9d4;
    --gray-8: #66b9c9;
    --gray-9: #328a9a;
    --gray-10: #277f8e;
    --gray-11: #0e606c;
    --gray-12: #00262e;
  
    --gray-a1: #1c4cf406;
    --gray-a2: #0196e410;
    --gray-a3: #0186b320;
    --gray-a4: #0181a930;
    --gray-a5: #0284ab3f;
    --gray-a6: #0185a64f;
    --gray-a7: #0087a666;
    --gray-a8: #008daa93;
    --gray-a9: #006e82ca;
    --gray-a10: #00697bd6;
    --gray-a11: #015865f1;
    --gray-a12: #00262e;
  
    --gray-contrast: #ffffff;
    --gray-surface: #ffffffcc;
    --gray-indicator: #328a9a;
    --gray-track: #328a9a;
  
    --bg-main-theme: #222222;
    --outline-color-dark: #ee0000;
    --outline-color-light: #d7d7d7;
  }
  
  @supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
      :root,
      .light,
      .light-theme {
        --gray-1: oklch(95.4% 0.0071 211.4);
        --gray-2: oklch(94.2% 0.0153 211.4);
        --gray-3: oklch(91.3% 0.0234 211.4);
        --gray-4: oklch(88.6% 0.0315 211.4);
        --gray-5: oklch(86.2% 0.0395 211.4);
        --gray-6: oklch(83.7% 0.0481 211.4);
        --gray-7: oklch(80% 0.0611 211.4);
        --gray-8: oklch(73.9% 0.0836 211.4);
        --gray-9: oklch(58.9% 0.0854 211.4);
        --gray-10: oklch(55.3% 0.084 211.4);
        --gray-11: oklch(44.9% 0.074 211.4);
        --gray-12: oklch(24.1% 0.0537 211.4);
  
        --gray-a1: color(display-p3 0.0118 0.2157 0.9569 / 0.021);
        --gray-a2: color(display-p3 0.0039 0.5255 0.8824 / 0.054);
        --gray-a3: color(display-p3 0.0118 0.4431 0.6392 / 0.113);
        --gray-a4: color(display-p3 0.0078 0.4353 0.6118 / 0.171);
        --gray-a5: color(display-p3 0.0039 0.4471 0.6118 / 0.22);
        --gray-a6: color(display-p3 0.0039 0.4471 0.5961 / 0.274);
        --gray-a7: color(display-p3 0.0039 0.4471 0.5882 / 0.353);
        --gray-a8: color(display-p3 0.0039 0.4706 0.6039 / 0.494);
        --gray-a9: color(display-p3 0 0.3373 0.4314 / 0.685);
        --gray-a10: color(display-p3 0 0.3137 0.4 / 0.727);
        --gray-a11: color(display-p3 0 0.2431 0.302 / 0.822);
        --gray-a12: color(display-p3 0 0.1059 0.1412 / 0.955);
  
        --gray-contrast: #ffffff;
        --gray-surface: color(display-p3 1 1 1 / 80%);
        --gray-indicator: oklch(58.9% 0.0854 211.4);
        --gray-track: oklch(58.9% 0.0854 211.4);
      }
    }
  }
  
  .dark,
  .dark-theme {
    --teal-1: #0f1715;
    --teal-2: #121d1a;
    --teal-3: #102e27;
    --teal-4: #093b31;
    --teal-5: #10483c;
    --teal-6: #1b574a;
    --teal-7: #23695a;
    --teal-8: #287f6c;
    --teal-9: #0bd8b6;
    --teal-10: #00cdab;
    --teal-11: #0bd8b6;
    --teal-12: #adf0de;
  
    --teal-a1: #00bf1503;
    --teal-a2: #00f8a309;
    --teal-a3: #00f9b91c;
    --teal-a4: #00fbbf2a;
    --teal-a5: #02fec738;
    --teal-a6: #2dffd148;
    --teal-a7: #3efed55c;
    --teal-a8: #40ffd574;
    --teal-a9: #0affd6d5;
    --teal-a10: #00ffd4c9;
    --teal-a11: #0affd6d5;
    --teal-a12: #b8ffecef;
  
    --teal-contrast: #072821;
    --teal-surface: #0929225c;
    --teal-indicator: #0bd8b6;
    --teal-track: #0bd8b6;
  
  }
  
  @supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
      .dark,
      .dark-theme {
        --teal-1: oklch(19.5% 0.0123 175.7);
        --teal-2: oklch(21.8% 0.0168 175.7);
        --teal-3: oklch(27.5% 0.0377 175.7);
        --teal-4: oklch(31.8% 0.0545 175.7);
        --teal-5: oklch(36.3% 0.06 175.7);
        --teal-6: oklch(41.4% 0.0652 175.7);
        --teal-7: oklch(47.3% 0.0738 175.7);
        --teal-8: oklch(53.9% 0.0865 175.7);
        --teal-9: oklch(78.9% 0.1467 175.7);
        --teal-10: oklch(75.5% 0.1467 175.7);
        --teal-11: oklch(78.9% 0.1467 175.7);
        --teal-12: oklch(90.5% 0.072 175.7);
  
        --teal-a1: color(display-p3 0 0.9569 0.0863 / 0.009);
        --teal-a2: color(display-p3 0.0784 0.9804 0.6431 / 0.035);
        --teal-a3: color(display-p3 0.2314 1 0.7686 / 0.103);
        --teal-a4: color(display-p3 0.2039 0.9961 0.7765 / 0.159);
        --teal-a5: color(display-p3 0.3373 0.9961 0.8118 / 0.214);
        --teal-a6: color(display-p3 0.4314 1 0.8471 / 0.278);
        --teal-a7: color(display-p3 0.4784 0.9961 0.8549 / 0.355);
        --teal-a8: color(display-p3 0.4863 1 0.8588 / 0.445);
        --teal-a9: color(display-p3 0.4549 1 0.8549 / 0.821);
        --teal-a10: color(display-p3 0.4471 1 0.8471 / 0.774);
        --teal-a11: color(display-p3 0.4549 1 0.8549 / 0.821);
        --teal-a12: color(display-p3 0.7843 0.9961 0.9333 / 0.928);
  
        --teal-contrast: #072821;
        --teal-surface: color(display-p3 0.0784 0.1333 0.1176 / 0.5);
        --teal-indicator: oklch(78.9% 0.1467 175.7);
        --teal-track: oklch(78.9% 0.1467 175.7);
      }
    }
  }
  
  .dark,
  .dark-theme {
    --gray-1: #0a171a;
    --gray-2: #111e21;
    --gray-3: #13272b;
    --gray-4: #132f34;
    --gray-5: #16363c;
    --gray-6: #174048;
    --gray-7: #1a4f59;
    --gray-8: #206b78;
    --gray-9: #347986;
    --gray-10: #488692;
    --gray-11: #90bcc5;
    --gray-12: #e5f1f3;
  
    --gray-a1: #006ae906;
    --gray-a2: #00b9f00e;
    --gray-a3: #0ad5ff18;
    --gray-a4: #0dd8fe22;
    --gray-a5: #20d9fd2b;
    --gray-a6: #22d9fe38;
    --gray-a7: #29dbfd4b;
    --gray-a8: #30e1ff6c;
    --gray-a9: #56e3fd7c;
    --gray-a10: #75e7fe89;
    --gray-a11: #b9f3ffc0;
    --gray-a12: #f0fdfff2;
  
    --gray-contrast: #ffffff;
    --gray-surface: #0a323a4b;
    --gray-indicator: #347986;
    --gray-track: #347986;
  
    --bg-main-theme: #141515;
    --outline-color: #1b1c1c;
  }
  

  @supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
      .dark,
      .dark-theme {
        --gray-1: oklch(19.5% 0.0187 211.4);
        --gray-2: oklch(22.4% 0.0192 211.4);
        --gray-3: oklch(25.9% 0.027 211.4);
        --gray-4: oklch(28.6% 0.0346 211.4);
        --gray-5: oklch(31.3% 0.0389 211.4);
        --gray-6: oklch(34.7% 0.0468 211.4);
        --gray-7: oklch(39.8% 0.0568 211.4);
        --gray-8: oklch(49% 0.074 211.4);
        --gray-9: oklch(53.7% 0.0716 211.4);
        --gray-10: oklch(58.3% 0.0668 211.4);
        --gray-11: oklch(76.8% 0.049 211.4);
        --gray-12: oklch(94.9% 0.0132 211.4);
  
        --gray-a1: color(display-p3 0 0.4392 0.9765 / 0.022);
        --gray-a2: color(display-p3 0.0784 0.7647 0.9882 / 0.052);
        --gray-a3: color(display-p3 0.2549 0.8235 1 / 0.09);
        --gray-a4: color(display-p3 0.2627 0.8431 0.9961 / 0.129);
        --gray-a5: color(display-p3 0.3216 0.851 0.9961 / 0.163);
        --gray-a6: color(display-p3 0.3373 0.851 0.9961 / 0.214);
        --gray-a7: color(display-p3 0.3686 0.8627 1 / 0.287);
        --gray-a8: color(display-p3 0.4078 0.8784 1 / 0.415);
        --gray-a9: color(display-p3 0.498 0.8941 1 / 0.475);
        --gray-a10: color(display-p3 0.5804 0.9098 1 / 0.526);
        --gray-a11: color(display-p3 0.7765 0.9529 1 / 0.748);
        --gray-a12: color(display-p3 0.949 0.9922 1 / 0.949);
  
        --gray-contrast: #ffffff;
        --gray-surface: color(display-p3 0 0 0 / 5%);
        --gray-indicator: oklch(53.7% 0.0716 211.4);
        --gray-track: oklch(53.7% 0.0716 211.4);
      }
    }
  }
  
  .dark,
  .dark-theme,
  :is(.dark, .dark-theme) :where(.radix-themes:not(.light, .light-theme)) {
    --color-background: var(--bg-main-theme);
    --body-color: $secondary;
  }
  
  :root,
  .light,
  .light-theme,
  .radix-themes {
    --color-background: var(--bg-main-theme);
    --body-color: $secondary;
  }
  
  // Import Bootstrap SCSS files with the tilde syntax to resolve from node_modules
  
  // ----------------------------------------
  // Bootstrap Variables Overrides for Radix Theme
  // ----------------------------------------
  
  // Update Bootstrap theme-colors to match Radix colors
  $theme-colors: (
    "primary": #0bd8b6,
    "secondary": #226d7a,
    "info": #2b61de,
    "success": #4bc375,
    "warning": #fddb43,
    "danger": #ff3869,
    "light": #d8f3eb,
    "dark": #0e0909,
    "gray-1": #e2e9ea,
    "gray-12": #00262e
  );
  
  // Border Radius
  $border-radius: 8px;
  $border-radius-lg: 16px;
  $border-radius-sm: 4px;
  $border-radius-pill: 50rem;
  $border-radius-xxl: 24px;
  // Shadows
  $box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.03);
  $box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.03);
  $box-shadow-lg:
    0 10px 15px rgba(0, 0, 0, 0.05),
    0 4px 6px rgba(0, 0, 0, 0.03);
  
  // Font Family
  $font-family-sans-serif: "Rubik", serif;
  $headings-font-family: $font-family-sans-serif;
  $font-family-base: $font-family-sans-serif;
  
  // Typography
  $font-size-base: 1rem; // 16px
  $font-size-lg: 1.125rem; // 18px
  $font-size-sm: 0.875rem; // 14px
  $line-height-base: 1.5;
  $line-height-lg: 1.75;
  $line-height-sm: 1.25;
  
  $white: #ffffff;
  $primary: map-get($theme-colors, "primary");
  $secondary: map-get($theme-colors, "secondary");
  $info: map-get($theme-colors, "info");
  $success: map-get($theme-colors, "success");
  $warning: map-get($theme-colors, "warning");
  $danger: map-get($theme-colors, "danger");
  $light: map-get($theme-colors, "light");
  $dark: map-get($theme-colors, "dark");
  
  $focus-ring-width: 0.25rem;
  $focus-ring-opacity: 0.25;
  $focus-ring-color: rgba($primary, $focus-ring-opacity);
  $focus-ring-blur: 0;
  $focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color;
  
  // Optional: Update Bootstrap Typography Variables
  $font-family-sans-serif: "Rubik", serif !default;
  $headings-font-family: $font-family-sans-serif;
  $display-font-family: $font-family-sans-serif;
  $font-family-base: $font-family-sans-serif;