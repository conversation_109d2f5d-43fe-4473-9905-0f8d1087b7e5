import { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/services/supabase/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { I18nManager, LogBox } from 'react-native';
import i18next from 'i18next';
import { useRTLContext } from '@/components/rtl/new-index';
import { getProfileId, syncProfileAuthIds } from '@/utils/user';
import { useRouter } from 'expo-router';

// Create a global object to store user identifiers
export const GlobalUserIdentifier = {
  authId: null as string | null,
  profileId: null as string | null,

  // Update both IDs
  updateIds(authId: string | null, profileId: string | null): void {
    this.authId = authId;
    this.profileId = profileId;
    console.log('Updated GlobalUserIdentifier:', { authId, profileId });
  },

  // Update just the auth ID
  updateAuthId(authId: string | null): void {
    this.authId = authId;
    console.log('Updated GlobalUserIdentifier.authId:', authId);
  },

  // Update just the profile ID
  updateProfileId(profileId: string | null): void {
    this.profileId = profileId;
    console.log('Updated GlobalUserIdentifier.profileId:', profileId);
  },

  // Clear all IDs (for logout)
  clear(): void {
    this.authId = null;
    this.profileId = null;
    console.log('Cleared GlobalUserIdentifier');
  }
};

type AuthContextType = {
  session: Session | null;
  loading: boolean;
  isAuthenticated: boolean;
  isInitialized: boolean;
  userIdentifier: typeof GlobalUserIdentifier;
  signInWithPhone: (phoneNumber: string) => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signUpWithPhone: (phoneNumber: string) => Promise<void>;
  signUpWithEmail: (email: string, password: string) => Promise<void>;
  signOut: (onSuccess?: () => void) => Promise<void>;
  refreshSession: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const LANGUAGE_KEY = '@app:language';
const RTL_APPLIED_KEY = '@app:rtl_applied';
const FORCE_RESTART_KEY = '@app:force_restart';

// Ignore specific warnings
LogBox.ignoreLogs([
  'Error fetching settings:',
  'Open debugger to view warnings.',
  '[Layout children]',
  'Failed to open debugger',
  'No route named "(tabs)"'
]);

// Helper function to execute RPC with timeout
const executeRpcWithTimeout = async (functionName: string, params: Record<string, any>, timeoutMs = 8000) => {
  // Create a promise that resolves with the result of the RPC call
  const rpcPromise = supabase.rpc(functionName, params);

  // Create a promise that rejects after the timeout
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`RPC call to ${functionName} timed out after ${timeoutMs}ms`)), timeoutMs);
  });

  // Race the RPC call against the timeout
  try {
    const result = await Promise.race([rpcPromise, timeoutPromise]);
    return result;
  } catch (error) {
    console.error(`Error or timeout in RPC call to ${functionName}:`, error);
    return { data: null, error };
  }
};

const resetLanguageSettings = async () => {
  try {
    // Set language to English
    await AsyncStorage.setItem(LANGUAGE_KEY, 'en');

    // Set RTL to false
    await AsyncStorage.setItem(RTL_APPLIED_KEY, 'false');
    await AsyncStorage.setItem(FORCE_RESTART_KEY, 'true');

    // Update i18next
    await i18next.changeLanguage('en');

    // Only update RTL if needed
    if (I18nManager.isRTL) {
      I18nManager.allowRTL(false);
      I18nManager.forceRTL(false);

      // Note: This will require a reload, but we'll let the app handle that
      console.log('RTL settings reset, app will need to reload');
    }
  } catch (error) {
    console.error('Failed to reset language settings:', error);
  }
};

const createOrUpdateUserSettings = async (session: Session) => {
  try {
    // First check if settings exists
    const { data: existingSettings, error: checkError } = await supabase
      .from('user_settings')
      .select('settings')
      .eq('user_id', GlobalUserIdentifier.profileId)
      .single();

    // Get the current language from AsyncStorage
    const currentLanguage = await AsyncStorage.getItem(LANGUAGE_KEY) || 'en';

    // If no settings exists
    if (checkError && checkError.code === 'PGRST116') {
      const defaultSettings = {
        theme: 'system',
        privacy: {
          terms_url: 'https://dumpster.app/terms',
          policy_url: 'https://dumpster.app/privacy'
        },
        language: 'en',
        notifications: {
          push_enabled: true
        },
        location_sharing: true,
        preferred_language: currentLanguage
      };

      const { data: newSettings, error: createError } = await supabase
        .from('user_settings')
        .insert([{
          user_id: GlobalUserIdentifier.profileId,
          settings: defaultSettings
        }])
        .select()
        .single();

      if (createError) {
        return null;
      }

      return newSettings;
    } else if (checkError) {
      return null;
    }

    // If settings exists but language differs from AsyncStorage, update it
    if (existingSettings.settings.preferred_language !== currentLanguage) {
      const updatedSettings = {
        ...existingSettings.settings,
        preferred_language: currentLanguage
      };

      const { error: updateError } = await supabase
        .from('user_settings')
        .update({
          settings: updatedSettings,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', GlobalUserIdentifier.profileId);

      if (updateError) {
        return existingSettings;
      }
    }

    return existingSettings;
  } catch (error) {
    return null;
  }
};

// Function to get user profile by auth ID with fallbacks
export const getProfileByAuthId = async (authId: string) => {
  if (!authId) return null;

  console.log('Fetching profile for auth ID:', authId);

  try {
    // Try using the RPC function first
    console.log('Trying RPC function get_profile_by_auth_id');
    const rpcResult = await executeRpcWithTimeout('get_profile_by_auth_id', {
      auth_id: authId
    });

    const { data: rpcProfile, error: rpcError } = rpcResult as { data: any, error: any };

    if (!rpcError && rpcProfile && Array.isArray(rpcProfile) && rpcProfile.length > 0) {
      console.log('Profile found via RPC:', rpcProfile[0]);

      // Store full profile data in AsyncStorage
      await AsyncStorage.setItem('@app:user_profile', JSON.stringify(rpcProfile[0]));
      if (rpcProfile[0].full_name) {
        await AsyncStorage.setItem('@app:user_name', rpcProfile[0].full_name);
      }
      if (rpcProfile[0].avatar_url) {
        await AsyncStorage.setItem('@app:user_avatar', rpcProfile[0].avatar_url);
      }
      if (rpcProfile[0].email) {
        await AsyncStorage.setItem('@app:user_email', rpcProfile[0].email);
      }
      if (rpcProfile[0].phone) {
        await AsyncStorage.setItem('@app:user_phone', rpcProfile[0].phone);
      }

      return rpcProfile[0];
    }

    if (rpcError) {
      console.error('Error fetching profile via RPC:', rpcError);
    }

    // Fallback to direct queries
    console.log('Falling back to direct DB queries');

    // Try main ID match
    const { data: mainIdMatches, error: mainIdError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authId)
      .limit(1);

    if (!mainIdError && mainIdMatches && mainIdMatches.length > 0) {
      console.log('Profile found via main ID match:', mainIdMatches[0]);

      // Store full profile data in AsyncStorage
      await AsyncStorage.setItem('@app:user_profile', JSON.stringify(mainIdMatches[0]));
      if (mainIdMatches[0].full_name) {
        await AsyncStorage.setItem('@app:user_name', mainIdMatches[0].full_name);
      }
      if (mainIdMatches[0].avatar_url) {
        await AsyncStorage.setItem('@app:user_avatar', mainIdMatches[0].avatar_url);
      }
      if (mainIdMatches[0].email) {
        await AsyncStorage.setItem('@app:user_email', mainIdMatches[0].email);
      }
      if (mainIdMatches[0].phone) {
        await AsyncStorage.setItem('@app:user_phone', mainIdMatches[0].phone);
      }

      return mainIdMatches[0];
    }

    // Try email auth ID match
    const { data: emailMatches, error: emailError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email_auth_id', authId)
      .limit(1);

    if (!emailError && emailMatches && emailMatches.length > 0) {
      console.log('Profile found via email auth ID match:', emailMatches[0]);

      // Store full profile data in AsyncStorage
      await AsyncStorage.setItem('@app:user_profile', JSON.stringify(emailMatches[0]));
      if (emailMatches[0].full_name) {
        await AsyncStorage.setItem('@app:user_name', emailMatches[0].full_name);
      }
      if (emailMatches[0].avatar_url) {
        await AsyncStorage.setItem('@app:user_avatar', emailMatches[0].avatar_url);
      }
      if (emailMatches[0].email) {
        await AsyncStorage.setItem('@app:user_email', emailMatches[0].email);
      }
      if (emailMatches[0].phone) {
        await AsyncStorage.setItem('@app:user_phone', emailMatches[0].phone);
      }

      return emailMatches[0];
    }

    // Try phone auth ID match
    const { data: phoneMatches, error: phoneError } = await supabase
      .from('profiles')
      .select('*')
      .eq('phone_auth_id', authId)
      .limit(1);

    if (!phoneError && phoneMatches && phoneMatches.length > 0) {
      console.log('Profile found via phone auth ID match:', phoneMatches[0]);

      // Store full profile data in AsyncStorage
      await AsyncStorage.setItem('@app:user_profile', JSON.stringify(phoneMatches[0]));
      if (phoneMatches[0].full_name) {
        await AsyncStorage.setItem('@app:user_name', phoneMatches[0].full_name);
      }
      if (phoneMatches[0].avatar_url) {
        await AsyncStorage.setItem('@app:user_avatar', phoneMatches[0].avatar_url);
      }
      if (phoneMatches[0].email) {
        await AsyncStorage.setItem('@app:user_email', phoneMatches[0].email);
      }
      if (phoneMatches[0].phone) {
        await AsyncStorage.setItem('@app:user_phone', phoneMatches[0].phone);
      }

      return phoneMatches[0];
    }

    console.log('No profile found for auth ID via any method');
    return null;
  } catch (error) {
    console.error('Unexpected error in getProfileByAuthId:', error);
    return null;
  }
};

const logFunctionCall = (functionName: string, params: Record<string, unknown>): void => {
  console.log(`Calling ${functionName} with params:`, params);
};

// Add this helper function near the beginning of the file, with the other helper functions
const resetAuthState = async () => {
  try {
    console.log('[AuthContext] Resetting auth state...');
    
    // Clear user-related AsyncStorage keys
    const keysToRemove = [
      '@app:user_role',
      '@app:profile_id', 
      '@app:otp_verification_in_progress',
      '@app:verification_timestamp',
      '@app:verifying_otp_in_progress',
      '@app:last_auth_check',
      '@app:user_profile',
      '@app:user_name',
      '@app:user_avatar',
      '@app:user_email',
      '@app:user_phone',
      '@app:login_success',
      '@app:force_restart',
      '@app:rtl_retry_count',
      '@app:rtl_applied'
    ];
    
    await AsyncStorage.multiRemove(keysToRemove);
    
    // Reset language to English if needed
    await resetLanguageSettings();
    
    // Clear global user identifier
    GlobalUserIdentifier.clear();
    
    console.log('[AuthContext] Auth state reset complete');
    return true;
  } catch (error) {
    console.error('[AuthContext] Error resetting auth state:', error);
    return false;
  }
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [userIdentifier, setUserIdentifier] = useState<typeof GlobalUserIdentifier>(GlobalUserIdentifier);
  const { setLanguage } = useRTLContext();
  const router = useRouter();

  // Function to update both local state and global singleton
  const updateUserIdentifier = async (authId: string | null, profileId: string | null): Promise<void> => {
    // Update the user identifier used by the context
    GlobalUserIdentifier.updateIds(authId, profileId);

    // Also update the state for components using the context
    setUserIdentifier({ ...GlobalUserIdentifier });
  };

  const refreshSession = async () => {
    try {
      const { data: { session: refreshedSession }, error } = await supabase.auth.refreshSession();
      if (error) throw error;
      if (refreshedSession) {
        setSession(refreshedSession);
        await updateUserIdentifier(refreshedSession.user.id, refreshedSession.user.id);
      }
    } catch (error) {
      console.error('Error refreshing session:', error);
      setSession(null);
    }
  };

  useEffect(() => {
    let mounted = true;
    let refreshTimer: NodeJS.Timeout;

    const initializeLanguage = async () => {
      const savedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
      if (savedLanguage && mounted) {
        await setLanguage(savedLanguage).catch(console.error);
      }
    };

    const initializeAuth = async () => {
      try {
        await initializeLanguage();

        const { data: { session: initialSession }, error } = await supabase.auth.getSession();
        if (error) throw error;

        if (mounted) {
          if (initialSession?.user?.id) {
            setSession(initialSession);
            const userSettings = await createOrUpdateUserSettings(initialSession).catch(() => null);

            if (userSettings?.settings?.preferred_language) {
              const currentLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
              if (currentLanguage !== userSettings.settings.preferred_language) {
                await AsyncStorage.setItem(LANGUAGE_KEY, userSettings.settings.preferred_language);
                await setLanguage(userSettings.settings.preferred_language).catch(console.error);
              }
            }

            // Check if user profile exists and sync data
            const profile = await getProfileByAuthId(initialSession.user.id);
            if (profile) {
              console.log('Found profile during initialization:', profile.id);
              // Only update local cache, don't sync with database during initialization
              await AsyncStorage.setItem('@app:profile_id', profile.id);
              await AsyncStorage.setItem('@app:user_role', profile.user_type || 'customer');
              await AsyncStorage.setItem('@app:user_profile', JSON.stringify(profile));

              // Don't update user metadata here in initialization - this can cause read-only transaction errors
              // Just set the global user identifier without sync
              GlobalUserIdentifier.updateIds(initialSession.user.id, profile.id);
              setUserIdentifier({ ...GlobalUserIdentifier });
            } else {
              console.warn('No profile found for authenticated user:', initialSession.user.id);
              // Still update with just the auth ID
              GlobalUserIdentifier.updateIds(initialSession.user.id, null);
              setUserIdentifier({ ...GlobalUserIdentifier });
            }

            // Set up automatic session refresh
            const expiresIn = initialSession.expires_at
              ? (initialSession.expires_at * 1000) - Date.now()
              : 3600 * 1000; // 1 hour default

            const refreshInterval = Math.max(Math.min(expiresIn - 60000, 3600000), 300000); // Between 5 minutes and 1 hour
            refreshTimer = setInterval(refreshSession, refreshInterval);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) setSession(null);
      } finally {
        if (mounted) {
          setLoading(false);
          setIsInitialized(true);
        }
      }
    };

    initializeAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, newSession) => {
      if (!mounted) return;

      console.log('Auth state changed:', event);

      try {
        if (newSession?.user?.id) {
          setSession(newSession);
          if (event === 'SIGNED_IN' || event === 'USER_UPDATED') {
            // Load user role from AsyncStorage as a fallback
            try {
              const savedProfileId = await AsyncStorage.getItem('@app:profile_id');
              const savedUserRole = await AsyncStorage.getItem('@app:user_role');

              if (savedProfileId && savedUserRole) {
                console.log('Using saved profile data:', { savedProfileId, savedUserRole });

                // Update user metadata with the saved profile information
                const { error: metadataError } = await supabase.auth.updateUser({
                  data: {
                    role: savedUserRole,
                    user_type: savedUserRole,
                    profile_id: savedProfileId
                  }
                });

                if (metadataError) {
                  console.error('Error updating user metadata from storage:', metadataError);
                }

                // Update the global user identifier with saved data
                await updateUserIdentifier(newSession.user.id, savedProfileId);
              } else {
                // Try to find profile by auth ID if we don't have it in storage
                const profile = await getProfileByAuthId(newSession.user.id);
                if (profile) {
                  console.log('Found profile during auth state change:', profile.id);
                  await AsyncStorage.setItem('@app:profile_id', profile.id);
                  await AsyncStorage.setItem('@app:user_role', profile.user_type || 'customer');
                  await AsyncStorage.setItem('@app:user_profile', JSON.stringify(profile));

                  // Don't update user metadata here - this can cause read-only transaction errors
                  // Just set the global user identifier without sync
                  GlobalUserIdentifier.updateIds(newSession.user.id, profile.id);
                  setUserIdentifier({ ...GlobalUserIdentifier });
                } else {
                  // Only update with auth ID if no profile found
                  GlobalUserIdentifier.updateIds(newSession.user.id, null);
                  setUserIdentifier({ ...GlobalUserIdentifier });
                }
              }
            } catch (storageError) {
              console.error('Error loading profile data from storage:', storageError);
              // Still update with just the auth ID
              await updateUserIdentifier(newSession.user.id, null);
            }

            // Try to create or update user settings
            await createOrUpdateUserSettings(newSession).catch(error => {
              console.error('Error updating user settings:', error);
            });
          }
        } else if (event === 'SIGNED_OUT') {
          setSession(null);

          // Reset language settings to default
          await resetLanguageSettings();

          // Just clear identifiers without trying to sync
          // Previously this was causing a write operation
          GlobalUserIdentifier.clear();
          setUserIdentifier({ ...GlobalUserIdentifier });

          // Clear any profile data from AsyncStorage
          await AsyncStorage.removeItem('@app:profile_id');
          await AsyncStorage.removeItem('@app:user_role');
          await AsyncStorage.removeItem('@app:user_profile');
          await AsyncStorage.removeItem('@app:user_name');
          await AsyncStorage.removeItem('@app:user_avatar');
          await AsyncStorage.removeItem('@app:user_email');
          await AsyncStorage.removeItem('@app:user_phone');
        }
      } catch (error) {
        console.error('Error handling auth state change:', error);
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
      if (refreshTimer) clearInterval(refreshTimer);
    };
  }, []);

  const signInWithPhone = async (phoneNumber: string) => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        phone: phoneNumber,
      });
      if (error) throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    setLoading(true);
    try {
      // Get the profile first - direct from the database
      try {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id, user_type')
          .eq('email', email.toLowerCase().trim())
          .single();

        if (profileError) {
          console.log('Profile lookup error or not found:', profileError);
          // Continue with normal sign in
        } else if (profile) {
          // Store the profile ID for later use
          await AsyncStorage.setItem('@app:profile_id', profile.id);
          await AsyncStorage.setItem('@app:user_role', profile.user_type || 'customer');
        }
      } catch (profileCheckError) {
        console.log('[authContext]Error checking profile before login:', profileCheckError);
        // Continue with normal sign in
      }

      // Now proceed with authentication
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUpWithPhone = async (phoneNumber: string) => {
    setLoading(true);
    try {
      await resetLanguageSettings();

      const { error } = await supabase.auth.signUp({
        phone: phoneNumber,
        password: Math.random().toString(36).slice(-8),
        options: {
          data: {
            phone: phoneNumber
          }
        }
      });
      if (error) throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUpWithEmail = async (email: string, password: string) => {
    setLoading(true);
    try {
      await resetLanguageSettings();

      const { error } = await supabase.auth.signUp({
        email,
        password,
      });
      if (error) throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async (onSuccess?: () => void) => { 
    setLoading(true);
    console.log('[AuthContext] Starting signOut...');
    let supabaseSignOutTimedOut = false;
    try {
      // 1. Attempt Supabase sign out FIRST with timeout and GLOBAL SCOPE
      console.log('[AuthContext] Calling supabase.auth.signOut() with GLOBAL SCOPE and 10s timeout...');
      const signOutPromise = supabase.auth.signOut({ 
        scope: 'global' 
      }); 
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Supabase signout timed out after 10 seconds')), 10000)
      );

      try {
        const { error: supabaseError } = await Promise.race([signOutPromise, timeoutPromise]) as { error: any };
        console.log('[AuthContext] supabase.auth.signOut() completed'); 

        if (supabaseError) {
          console.error('[AuthContext] Supabase signOut error:', supabaseError);
          // Don't throw immediately, proceed with local cleanup but log the error
        }
      } catch (timeoutError: any) {
         // This block catches the timeout rejection
         console.warn('[AuthContext] Supabase signout timed out:', timeoutError.message);
         supabaseSignOutTimedOut = true;
         
         // If timeout, make another attempt with just local scope as fallback
         try {
           console.log('[AuthContext] Attempting fallback signout with local scope...');
           await supabase.auth.signOut({ scope: 'global' });
         } catch (fallbackError) {
           console.error('[AuthContext] Fallback signout failed:', fallbackError);
         }
      }

      // 2. Clear AsyncStorage (Always attempt this)
      console.log('[AuthContext] Clearing auth state data...');
      try {
        // Use the centralized function for consistent cleanup
        await resetAuthState();
        console.log('[AuthContext] Cleared auth state data');
      } catch (storageError) {
        console.error('[AuthContext] Auth state cleanup error:', storageError);
        // Log and continue
      }

      // 3. Clear local state
      console.log('[AuthContext] Clearing local session state...');
      setSession(null);
      // GlobalUserIdentifier is already cleared in resetAuthState
      console.log('[AuthContext] Local session state cleared');

      // 4. If all else fails, try a different approach to kill any remaining sessions
      if (supabaseSignOutTimedOut) {
        try {
          console.log('[AuthContext] Making final attempt to clear auth state...');
          // Multiple approaches to ensure session is truly cleared
          
          // 1. Try to get current session and manipulate it
          const { data } = await supabase.auth.getSession();
          if (data.session) {
            try {
              // Update user with nonsense data - this often forces a session refresh
              await supabase.auth.updateUser({ data: { _forceRefresh: Date.now() } });
              console.log('[AuthContext] Updated user data to force refresh');
            } catch (updateError) {
              console.error('[AuthContext] User update failed:', updateError);
            }
            
            // 2. Force bad credentials to invalidate tokens (last resort)
            try {
              // This triggers auth failures which often helps clear stuck sessions
              await supabase.auth.refreshSession({ refresh_token: 'invalid_token' });
            } catch (refreshError) {
              // This error is expected and desired
              console.log('[AuthContext] Intentionally triggered auth error to clear session');
            }
          }
        } catch (finalError) {
          console.error('[AuthContext] Final session clearing failed:', finalError);
        }
      }

      // 5. Call the success callback (Always attempt this)
      if (onSuccess) {
        console.log('[AuthContext] Scheduling onSuccess callback execution...');
        // Use setTimeout to ensure state updates propagate before navigation
        setTimeout(() => {
           console.log('[AuthContext] Executing delayed onSuccess callback...');
           try {
             onSuccess();
           } catch (callbackError) {
             console.error('[AuthContext] Error within onSuccess callback:', callbackError);
           }
        }, 50); // Small delay (e.g., 50ms) to allow state propagation
      }

      if (supabaseSignOutTimedOut) {
          console.warn('[AuthContext] SignOut finished, but Supabase call may have timed out.');
      } else {
          console.log('[AuthContext] SignOut completed successfully.');
      }

    } catch (err) {
       // Catch any unexpected errors during the cleanup phases
       console.error('[AuthContext] Unexpected error during signOut process:', err);
       // Optionally re-throw or handle differently
    } finally {
      console.log('[AuthContext] SignOut finally block. Setting loading to false.');
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        loading,
        isAuthenticated: !!session,
        isInitialized,
        userIdentifier: GlobalUserIdentifier,
        signInWithPhone,
        signInWithEmail,
        signUpWithPhone,
        signUpWithEmail,
        signOut,
        refreshSession
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 
