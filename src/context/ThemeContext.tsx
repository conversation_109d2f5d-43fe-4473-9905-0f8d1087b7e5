import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme, Appearance, AppState, AppStateStatus } from 'react-native';
import { MD3DarkTheme, MD3LightTheme, Provider as PaperProvider } from 'react-native-paper';
import { useSettings } from '@/hooks/useSettings';
import * as colors from '@/theme/colors';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/services/supabase/client';
import { GlobalUserIdentifier } from './AuthContext';

const THEME_STORAGE_KEY = '@app:theme';

type ThemeType = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: ThemeType;
  isDarkMode: boolean;
  toggleTheme: () => Promise<void>;
  setTheme: (theme: ThemeType) => Promise<void>;
  initializeTheme: () => Promise<void>;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'system',
  isDarkMode: false,
  toggleTheme: async () => { },
  setTheme: async () => { },
  initializeTheme: async () => { },
});

export const useTheme = () => useContext(ThemeContext);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const { settings, updateSettings } = useSettings();
  const systemTheme = useColorScheme();
  const [systemColorScheme, setSystemColorScheme] = useState<'light' | 'dark'>(systemTheme || 'light');
  const [currentTheme, setCurrentTheme] = useState<ThemeType>('system');
  const [isInitialized, setIsInitialized] = useState(false);

  // Function to initialize theme from storage or system
  const initializeTheme = async () => {
    try {
      // First check if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        // Get user settings from Supabase
        const { data: userSettings } = await supabase
          .from('user_settings')
          .select('settings')
          .eq('user_id', GlobalUserIdentifier.profileId)
          .single();
          
        if (userSettings?.settings?.theme) {
          setCurrentTheme(userSettings.settings.theme);
          await AsyncStorage.setItem(THEME_STORAGE_KEY, userSettings.settings.theme);
          return;
        }
      }
      
      // If no user or no user settings, check AsyncStorage
      const storedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (storedTheme) {
        setCurrentTheme(storedTheme as ThemeType);
      } else {
        // Default to system theme if nothing is stored
        setCurrentTheme('system');
        await AsyncStorage.setItem(THEME_STORAGE_KEY, 'system');
      }
    } catch (error) {
      console.error('Failed to initialize theme:', error);
      // Default to system theme on error
      setCurrentTheme('system');
    } finally {
      setIsInitialized(true);
    }
  };

  // Listen for changes to the system theme
  useEffect(() => {
    const updateSystemColorScheme = (preferences: Appearance.AppearancePreferences) => {
      console.log('System color scheme changed:', preferences.colorScheme);
      setSystemColorScheme(preferences.colorScheme || 'light');
    };

    // Set initial system theme
    setSystemColorScheme(Appearance.getColorScheme() || 'light');

    const subscription = Appearance.addChangeListener(updateSystemColorScheme);
    const appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        setSystemColorScheme(Appearance.getColorScheme() || 'light');
      }
    });

    // Initialize theme on mount
    initializeTheme();

    return () => {
      subscription.remove();
      appStateSubscription.remove();
    };
  }, []);

  // Update system color scheme when systemTheme changes
  useEffect(() => {
    if (systemTheme) {
      setSystemColorScheme(systemTheme);
    }
  }, [systemTheme]);

  // Determine if dark mode should be active
  const isDarkMode = currentTheme === 'system' 
    ? systemColorScheme === 'dark' 
    : currentTheme === 'dark';

  const paperTheme = isDarkMode ? colors.darkTheme : colors.lightTheme;

  // Function to toggle theme
  const toggleTheme = async () => {
    const newTheme = isDarkMode ? 'light' : 'dark';
    await setTheme(newTheme);
  };

  // Function to set theme
  const setTheme = async (theme: ThemeType) => {
    try {
      setCurrentTheme(theme);
      await AsyncStorage.setItem(THEME_STORAGE_KEY, theme);
      
      // Update Supabase if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: settings } = await supabase
          .from('user_settings')
          .select('settings')
          .eq('user_id', GlobalUserIdentifier.profileId)
          .single();
          
        if (settings) {
          await supabase
            .from('user_settings')
            .update({
              settings: { ...settings.settings, theme },
              updated_at: new Date().toISOString()
            })
            .eq('user_id', GlobalUserIdentifier.profileId);
        } else {
          // Create new settings if they don't exist
          await supabase
            .from('user_settings')
            .insert({
              user_id: GlobalUserIdentifier.profileId,
              settings: { theme },
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
        }
      }
    } catch (error) {
      console.error('Failed to set theme:', error);
    }
  };

  if (!isInitialized) {
    return null;
  }

  return (
    <ThemeContext.Provider
      value={{
        theme: currentTheme,
        isDarkMode,
        toggleTheme,
        setTheme,
        initializeTheme,
      }}
    >
      <PaperProvider theme={paperTheme}>
        {children}
      </PaperProvider>
    </ThemeContext.Provider>
  );
} 