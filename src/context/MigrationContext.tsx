import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface MigrationContextType {
  useNewRTL: boolean;
  toggleRTLVersion: () => Promise<void>;
}

const MigrationContext = createContext<MigrationContextType>({
  useNewRTL: false,
  toggleRTLVersion: async () => {},
});

export const useMigration = () => useContext(MigrationContext);

export const MigrationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [useNewRTL, setUseNewRTL] = useState(false);

  useEffect(() => {
    // Load saved preference
    const loadMigrationState = async () => {
      try {
        const savedState = await AsyncStorage.getItem('@app:use_new_rtl');
        setUseNewRTL(savedState === 'true');
      } catch (error) {
        console.error('Failed to load RTL version preference:', error);
      }
    };

    loadMigrationState();
  }, []);

  const toggleRTLVersion = async () => {
    try {
      const newState = !useNewRTL;
      await AsyncStorage.setItem('@app:use_new_rtl', String(newState));
      setUseNewRTL(newState);
    } catch (error) {
      console.error('Failed to toggle RTL version:', error);
    }
  };

  return (
    <MigrationContext.Provider
      value={{
        useNewRTL,
        toggleRTLVersion,
      }}
    >
      {children}
    </MigrationContext.Provider>
  );
}; 