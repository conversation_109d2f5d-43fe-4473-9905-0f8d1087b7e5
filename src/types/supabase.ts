export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          full_name: string | null;
          avatar_url: string | null;
          phone: string | null;
          address: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          full_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          address?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          address?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      }
      waste_tags: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      waste_types: {
        Row: {
          id: string
          name: string
          description: string | null
          image_url: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          image_url: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          image_url?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      waste_type_tags: {
        Row: {
          id: string
          waste_type_id: string
          waste_tag_id: string
          created_at: string
        }
        Insert: {
          id?: string
          waste_type_id: string
          waste_tag_id: string
          created_at?: string
        }
        Update: {
          id?: string
          waste_type_id?: string
          waste_tag_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "waste_type_tags_waste_type_id_fkey"
            columns: ["waste_type_id"]
            referencedRelation: "waste_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "waste_type_tags_waste_tag_id_fkey"
            columns: ["waste_tag_id"]
            referencedRelation: "waste_tags"
            referencedColumns: ["id"]
          }
        ]
      }
      dumpster_sizes: {
        Row: {
          id: string
          name: string
          volume_cubic_yards: number
          max_weight_pounds: number
          length: number
          width: number
          height: number
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          volume_cubic_yards: number
          max_weight_pounds: number
          length: number
          width: number
          height: number
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          volume_cubic_yards?: number
          max_weight_pounds?: number
          length?: number
          width?: number
          height?: number
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      dumpsters: {
        Row: {
          id: string
          name: string
          description: string | null
          image_url: string
          price_per_day: number
          is_available: boolean
          next_available_date: string | null
          rating: number | null
          review_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          image_url: string
          price_per_day: number
          is_available?: boolean
          next_available_date?: string | null
          rating?: number | null
          review_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          image_url?: string
          price_per_day?: number
          is_available?: boolean
          next_available_date?: string | null
          rating?: number | null
          review_count?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      dumpster_images: {
        Row: {
          id: string
          dumpster_id: string
          image_url: string
          sort_order: number
          created_at: string
        }
        Insert: {
          id?: string
          dumpster_id: string
          image_url: string
          sort_order?: number
          created_at?: string
        }
        Update: {
          id?: string
          dumpster_id?: string
          image_url?: string
          sort_order?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "dumpster_images_dumpster_id_fkey"
            columns: ["dumpster_id"]
            referencedRelation: "dumpsters"
            referencedColumns: ["id"]
          }
        ]
      }
      dumpster_features: {
        Row: {
          id: string
          dumpster_id: string
          feature: string
          created_at: string
        }
        Insert: {
          id?: string
          dumpster_id: string
          feature: string
          created_at?: string
        }
        Update: {
          id?: string
          dumpster_id?: string
          feature?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "dumpster_features_dumpster_id_fkey"
            columns: ["dumpster_id"]
            referencedRelation: "dumpsters"
            referencedColumns: ["id"]
          }
        ]
      }
      dumpster_size_options: {
        Row: {
          id: string
          dumpster_id: string
          dumpster_size_id: string
          created_at: string
        }
        Insert: {
          id?: string
          dumpster_id: string
          dumpster_size_id: string
          created_at?: string
        }
        Update: {
          id?: string
          dumpster_id?: string
          dumpster_size_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "dumpster_size_options_dumpster_id_fkey"
            columns: ["dumpster_id"]
            referencedRelation: "dumpsters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dumpster_size_options_dumpster_size_id_fkey"
            columns: ["dumpster_size_id"]
            referencedRelation: "dumpster_sizes"
            referencedColumns: ["id"]
          }
        ]
      }
      dumpster_waste_types: {
        Row: {
          id: string
          dumpster_id: string
          waste_type_id: string
          created_at: string
        }
        Insert: {
          id?: string
          dumpster_id: string
          waste_type_id: string
          created_at?: string
        }
        Update: {
          id?: string
          dumpster_id?: string
          waste_type_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "dumpster_waste_types_dumpster_id_fkey"
            columns: ["dumpster_id"]
            referencedRelation: "dumpsters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dumpster_waste_types_waste_type_id_fkey"
            columns: ["waste_type_id"]
            referencedRelation: "waste_types"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [key: string]: {
        Row: Record<string, unknown>;
        Insert: Record<string, unknown>;
        Update: Record<string, unknown>;
      };
    }
    Functions: {
      [key: string]: {
        Args: Record<string, unknown>;
        Returns: unknown;
      };
    }
    Enums: {
      [key: string]: string[];
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export interface Order {
  id: string;
  created_at: string;
  status: 'pending' | 'confirmed' | 'delivered' | 'in_use' | 'pickup_scheduled' | 'completed' | 'cancelled';
  delivery_address: string;
  delivery_date: string;
  total_amount: number;
  partner_id: string | null;
  partner?: Partner[] | null;
  customer_id?: string;
  user_id?: string;
  payment_status: 'pending' | 'partial' | 'paid' | 'refunded';
  payment_method: 'credit_card' | 'cash' | 'bank_transfer' | null;
}

export interface DriverAssignment {
  id: string;
  order_id: string;
  driver_id: string | null;
  status: string;
}

export interface Partner {
  id: string;
  company_name: string;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
} 