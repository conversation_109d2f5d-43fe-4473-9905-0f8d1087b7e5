export interface WasteTag {
  id: string;
  name: string;
  description?: string;
}

export interface WasteType {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  tags?: string[];
}

export interface DumpsterSize {
  id: string;
  name: string;
  volumeCubicYards: number;
  maxWeightPounds: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  description?: string;
}

export interface Feature {
  id: string;
  name: string;
  name_ar?: string;
  icon_name?: string;
}

export interface Dumpster {
  [x: string]: any;
  id: string;
  name: string;
  description?: string;
  imageUrl: string;
  additionalImages?: string[];
  size: DumpsterSize;
  compatibleWasteTypes: string[]; // IDs of compatible waste types
  pricePerDay: number;
  availability?: {
    isAvailable: boolean;
    nextAvailableDate?: string;
  };
  featureIds?: string[]; // IDs of features instead of feature strings
  rating?: number;
  reviewCount?: number;
  partnerId?: string;
}

export interface DumpsterFilter {
  sizeId?: string;
  wasteTypeIds?: string[];
  priceRange?: {
    min?: number;
    max?: number;
  };
  searchTerm?: string;
}

export interface RecommendationType {
  dumpster: Dumpster | null;
  score: number;
  reasons: string[];
  aiResponse: string;
}

export interface AIRecommendationRequest {
  projectDescription?: string;
  wasteTypeIds?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  duration?: {
    startDate: string;
    endDate?: string;
  };
  budget?: {
    min?: number;
    max?: number;
  };
} 
