/// <reference types="jest" />

import { WasteType, WasteTypeTag } from '../waste';

describe('WasteType', () => {
  const mockTag: WasteTypeTag = {
    id: '1',
    nameEn: 'Recyclable',
    nameAr: 'قابل لإعادة التدوير',
  };

  const mockWasteType: WasteType = {
    id: '1',
    nameEn: 'Construction Waste',
    nameAr: 'نفايات البناء',
    descriptionEn: 'Waste generated from construction activities',
    descriptionAr: 'النفايات الناتجة عن أنشطة البناء',
    imageUrl: 'https://example.com/construction-waste.jpg',
    tags: [mockTag],
  };

  it('should have all required properties', () => {
    expect(mockWasteType).toHaveProperty('id');
    expect(mockWasteType).toHaveProperty('nameEn');
    expect(mockWasteType).toHaveProperty('nameAr');
    expect(mockWasteType).toHaveProperty('descriptionEn');
    expect(mockWasteType).toHaveProperty('descriptionAr');
    expect(mockWasteType).toHaveProperty('imageUrl');
    expect(mockWasteType).toHaveProperty('tags');
  });

  it('should have correct property types', () => {
    expect(typeof mockWasteType.id).toBe('string');
    expect(typeof mockWasteType.nameEn).toBe('string');
    expect(typeof mockWasteType.nameAr).toBe('string');
    expect(typeof mockWasteType.descriptionEn).toBe('string');
    expect(typeof mockWasteType.descriptionAr).toBe('string');
    expect(typeof mockWasteType.imageUrl).toBe('string');
    expect(Array.isArray(mockWasteType.tags)).toBe(true);
  });

  it('should have valid tag structure', () => {
    const tag = mockWasteType.tags[0];
    expect(tag).toHaveProperty('id');
    expect(tag).toHaveProperty('nameEn');
    expect(tag).toHaveProperty('nameAr');
    expect(typeof tag.id).toBe('string');
    expect(typeof tag.nameEn).toBe('string');
    expect(typeof tag.nameAr).toBe('string');
  });
}); 