/// <reference types="jest" />

import { OrderDetails, CreateOrderInput, OrderFilters } from '../order';
import { Dumpster } from '../dumpster';
import { WasteType } from '../waste';

describe('OrderDetails', () => {
  const mockDumpster: Dumpster = {
    id: '1',
    nameEn: '10 Yard Dumpster',
    nameAr: 'حاوية 10 يارد',
    description: 'Perfect for small renovation projects',
    imageUrl: 'https://example.com/10yd-dumpster.jpg',
    length: 12,
    width: 8,
    height: 6,
    standingArea: 96,
    pricePerLoad: 299,
    rating: 4.5,
    reviewCount: 10,
    isAvailable: true,
  };

  const mockWasteType: WasteType = {
    id: '1',
    nameEn: 'Construction Waste',
    nameAr: 'نفايات البناء',
    descriptionEn: 'Waste generated from construction activities',
    descriptionAr: 'النفايات الناتجة عن أنشطة البناء',
    imageUrl: 'https://example.com/construction-waste.jpg',
    tags: [],
  };

  const mockOrder: OrderDetails = {
    id: '1',
    dumpsterId: '1',
    dumpster: mockDumpster,
    wasteTypeId: '1',
    wasteType: mockWasteType,
    quantity: 1,
    deliveryDate: new Date('2024-03-20'),
    installationType: 'ground',
    location: {
      latitude: 25.2048,
      longitude: 55.2708,
      address: 'Dubai, UAE',
    },
    notes: 'Please deliver in the morning',
    status: 'pending',
    createdAt: new Date('2024-03-19'),
    updatedAt: new Date('2024-03-19'),
    totalPrice: 299,
    userId: 'user123',
  };

  it('should have all required properties', () => {
    expect(mockOrder).toHaveProperty('id');
    expect(mockOrder).toHaveProperty('dumpsterId');
    expect(mockOrder).toHaveProperty('wasteTypeId');
    expect(mockOrder).toHaveProperty('quantity');
    expect(mockOrder).toHaveProperty('deliveryDate');
    expect(mockOrder).toHaveProperty('installationType');
    expect(mockOrder).toHaveProperty('location');
    expect(mockOrder).toHaveProperty('status');
    expect(mockOrder).toHaveProperty('createdAt');
    expect(mockOrder).toHaveProperty('updatedAt');
    expect(mockOrder).toHaveProperty('totalPrice');
    expect(mockOrder).toHaveProperty('userId');
  });

  it('should have correct property types', () => {
    expect(typeof mockOrder.id).toBe('string');
    expect(typeof mockOrder.dumpsterId).toBe('string');
    expect(typeof mockOrder.wasteTypeId).toBe('string');
    expect(typeof mockOrder.quantity).toBe('number');
    expect(mockOrder.deliveryDate).toBeInstanceOf(Date);
    expect(['ground', 'truck']).toContain(mockOrder.installationType);
    expect(typeof mockOrder.location.latitude).toBe('number');
    expect(typeof mockOrder.location.longitude).toBe('number');
    expect(typeof mockOrder.location.address).toBe('string');
    expect(['pending', 'confirmed', 'in_progress', 'completed', 'cancelled']).toContain(mockOrder.status);
    expect(mockOrder.createdAt).toBeInstanceOf(Date);
    expect(mockOrder.updatedAt).toBeInstanceOf(Date);
    expect(typeof mockOrder.totalPrice).toBe('number');
    expect(typeof mockOrder.userId).toBe('string');
  });

  it('should have optional properties', () => {
    expect(mockOrder.notes).toBeDefined();
    expect(mockOrder.dumpster).toBeDefined();
    expect(mockOrder.wasteType).toBeDefined();
  });
});

describe('CreateOrderInput', () => {
  const mockInput: CreateOrderInput = {
    dumpsterId: '1',
    wasteTypeId: '1',
    quantity: 1,
    deliveryDate: new Date('2024-03-20'),
    installationType: 'ground',
    location: {
      latitude: 25.2048,
      longitude: 55.2708,
      address: 'Dubai, UAE',
    },
    notes: 'Please deliver in the morning',
  };

  it('should have all required properties', () => {
    expect(mockInput).toHaveProperty('dumpsterId');
    expect(mockInput).toHaveProperty('wasteTypeId');
    expect(mockInput).toHaveProperty('quantity');
    expect(mockInput).toHaveProperty('deliveryDate');
    expect(mockInput).toHaveProperty('installationType');
    expect(mockInput).toHaveProperty('location');
  });

  it('should have correct property types', () => {
    expect(typeof mockInput.dumpsterId).toBe('string');
    expect(typeof mockInput.wasteTypeId).toBe('string');
    expect(typeof mockInput.quantity).toBe('number');
    expect(mockInput.deliveryDate).toBeInstanceOf(Date);
    expect(['ground', 'truck']).toContain(mockInput.installationType);
    expect(typeof mockInput.location.latitude).toBe('number');
    expect(typeof mockInput.location.longitude).toBe('number');
    expect(typeof mockInput.location.address).toBe('string');
  });

  it('should have optional properties', () => {
    expect(mockInput.notes).toBeDefined();
  });
});

describe('OrderFilters', () => {
  const mockFilters: OrderFilters = {
    status: ['pending', 'confirmed'],
    startDate: new Date('2024-03-01'),
    endDate: new Date('2024-03-31'),
    search: 'construction',
  };

  it('should have all optional properties', () => {
    expect(mockFilters.status).toBeDefined();
    expect(mockFilters.startDate).toBeDefined();
    expect(mockFilters.endDate).toBeDefined();
    expect(mockFilters.search).toBeDefined();
  });

  it('should have correct property types', () => {
    expect(Array.isArray(mockFilters.status)).toBe(true);
    expect(mockFilters.startDate).toBeInstanceOf(Date);
    expect(mockFilters.endDate).toBeInstanceOf(Date);
    expect(typeof mockFilters.search).toBe('string');
  });
}); 