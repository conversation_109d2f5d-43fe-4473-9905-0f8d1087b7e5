export interface Dumpster {
  id: string;
  nameEn: string;
  nameAr: string;
  imageUrl: string;
  partnerLogo?: string;
  length: number;
  width: number;
  height: number;
  pricePerLoad: number;
  rating: number;
  reviewCount: number;
  isAvailable: boolean;
  standingArea: number;
  description?: string;
  size?: DumpsterSize;
  sizes?: DumpsterSize[];
  featureIds?: string[];
  compatibleWasteTypes?: string[];
}

export interface DumpsterWithRecommendation extends Dumpster {
  recommendationScore: number;
}

export interface DumpsterFeature {
  id: string;
  dumpsterId: string;
  nameEn: string;
  nameAr: string;
  iconName: string;
}

export interface DumpsterSize {
  id: string;
  nameEn: string;
  nameAr: string;
  length: number;
  width: number;
  height: number;
  capacity: number;
}

export interface DumpsterFilters {
  priceRange: [number, number];
  size: string[];
  rating: number | null;
  availability: boolean;
  wasteType: string | null;
}

export interface OrderDetails {
  dumpster_id: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  };
  waste_type_id: string;
  quantity: number;
  installation_type: 'Private Property' | 'Public Property';
  additional_notes?: string;
  delivery_date: Date;
} 

export interface WasteType {
  id: string;
  name_en: string;
  name_ar: string;
  description_en: string;
  description_ar: string;
  imageUrl: string;
  tags?: string[];
}