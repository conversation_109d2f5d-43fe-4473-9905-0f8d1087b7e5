import { <PERSON>mpster } from './dumpster';
import { WasteType } from './waste';

export interface Order {
  id: string;
  created_at: string;
  dumpster_id: string;
  user_id: string;
  status: 'pending' | 'confirmed' | 'delivered' | 'completed' | 'cancelled';
  delivery_date: string;
  delivery_location: {
    lat: number;
    lng: number;
    address: string;
  };
  quantity: number;
  total_price: number;
  payment_method: 'cash' | 'card';
  payment_status: 'pending' | 'paid';
}

export interface OrderDetails {
  id: string;
  dumpsterId: string;
  dumpster?: Dumpster;
  wasteTypeId: string;
  wasteType?: WasteType;
  quantity: number;
  deliveryDate: Date;
  installationType: 'ground' | 'truck';
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  notes?: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  totalPrice: number;
  userId: string;
}

export interface CreateOrderInput {
  dumpsterId: string;
  wasteTypeId: string;
  quantity: number;
  deliveryDate: Date;
  installationType: 'ground' | 'truck';
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  notes?: string;
}

export interface OrderFilters {
  status?: OrderDetails['status'][];
  startDate?: Date;
  endDate?: Date;
  search?: string;
} 