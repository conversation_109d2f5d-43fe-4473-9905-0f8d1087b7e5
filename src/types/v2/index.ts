/**
 * V2 Types Index
 * Export all types from the v2 directory
 * 
 * Migration Guide:
 * For full details on migrating from legacy to v2 data model, 
 * see the migration guide in doc/v2-migration.md
 * 
 * Key improvements:
 * - Fully aligned with updated database schema
 * - Better multilingual support
 * - Improved type safety
 * - Centralized query key management
 */

export * from './dumpster';
// Export from other type files as they are created 