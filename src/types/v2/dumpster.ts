/**
 * Dumpster Data Model - Version 2
 * Comprehensive types for the dumpster system, aligned with updated DB schema
 */

// Core Dumpster Type
export interface Dumpster {
  id: string;
  nameEn: string;
  nameAr: string;
  description?: {
    en: string;
    ar: string;
  };
  imageUrl: string;
  additionalImages?: string[];

  // Dimensions
  length: number;
  width: number;
  height: number;
  capacity: number; // In cubic yards
  maxWeight: number; // In pounds
  standingArea: number; // Calculated from length * width

  // Business details
  pricePerLoad: number;
  rating: number;
  reviewCount: number;
  isAvailable: boolean;
  nextAvailableDate?: string;
  partnerId?: string;
  partnerLogo?: string;

  // Relationships
  sizeId: string;
  size?: DumpsterSize;
  features?: DumpsterFeature[];
  compatibleWasteTypes?: string[]; // IDs of compatible waste types
  wasteTypes?: WasteType[]; // Populated waste types (optional)
}

// Size definitions
export interface DumpsterSize {
  id: string;
  nameEn: string;
  nameAr: string;
  length: number;
  width: number;
  height: number;
  capacity: number; // In cubic yards
  maxWeight: number; // In pounds
  description?: {
    en: string;
    ar: string;
  };
}

// Features
export interface DumpsterFeature {
  id: string;
  nameEn: string;
  nameAr: string;
  iconName?: string;
}

// Waste Types
export interface WasteType {
  id: string;
  nameEn: string;
  nameAr: string;
  descriptionEn: string;
  descriptionAr: string;
  imageUrl: string;
  tags?: string[];
}

// Search and Filter Types
export interface DumpsterFilters {
  sizeIds?: string[];
  wasteTypeIds?: string[];
  priceRange?: {
    min?: number;
    max?: number;
  };
  rating?: number;
  availability?: boolean;
  searchTerm?: string;
  cityId?: string; // Added for city-based filtering
  projectType?: string | null; // Added for project type filtering
  count?: string; // Added for count filtering
}

// Recommendation Types
export interface DumpsterWithRecommendation extends Dumpster {
  recommendationScore: number;
  matchReasons?: string[];
}

export interface RecommendationRequest {
  projectDescription?: string;
  wasteTypeIds?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  duration?: {
    startDate: string;
    endDate?: string;
  };
  budget?: {
    min?: number;
    max?: number;
  };
  sizePreference?: string[];
}

// Order Types
export interface DumpsterOrderDetails {
  dumpsterId: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  wasteTypeId: string;
  quantity: number;
  installationType: 'Private Property' | 'Public Property';
  additionalNotes?: string;
  deliveryDate: string; // ISO date string
  pickupDate?: string; // ISO date string
}