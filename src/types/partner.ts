export interface Partner {
  id: string;
  company_name: string;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
  profile_id: string;
  status: 'active' | 'pending' | 'suspended';
  is_verified: boolean;
  service_areas: string[];
  rating: number;
  profile?: {
    id: string;
    avatar_url: string;
    full_name: string;
    email: string;
    phone: string;
    user_type: 'customer' | 'partner' | 'admin';
  };
}

export interface Driver {
  id: string;
  partner_id: string;
  profile_id: string;
  license_number: string;
  license_expiry: Date;
  status: 'active' | 'inactive' | 'suspended';
  current_vehicle_id: string;
  profile?: {
    id: string;
    full_name: string;
    phone: string;
    avatar_url: string;
  };
}