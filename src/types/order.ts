interface OrderDetails {
  id: string;
  created_at: string;
  updated_at: string;
  status: string;
  customer_id: string;
  partner_id?: string;
  delivery_address: string;
  delivery_coordinates: string;
  delivery_instructions?: string;
  delivery_date: string;
  scheduled_pickup_date: string;
  actual_pickup_date?: string;
  rental_duration_days: number;
  waste_type: string;
  base_price: number;
  total_amount: number;
  payment_status: 'pending' | 'partial' | 'paid' | 'refunded';
  payment_method: string;
  special_requirements?: string;
  discount_amount?: number;
  dumpster?: {
    id: string;
    name_en: string;
    name_ar: string;
    image_url: string;
  };
  partner?: {
    id: string;
    company_name: string;
    contact_person: string;
    contact_phone: string;
    contact_email: string;
    profiles?: {
      id: string;
      avatar_url: string;
    }[];
  };
  driver?: {
    id: string;
    full_name: string;
    phone: string;
    avatar_url?: string;
  };
}
