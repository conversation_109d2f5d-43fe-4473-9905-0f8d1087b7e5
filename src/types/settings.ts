export type ThemeType = 'light' | 'dark' | 'system';

export interface UserSettingsPrivacy {
  policy_url: string;
  terms_url: string;
}

export interface UserSettingsNotifications {
  push_enabled: boolean;
}

export interface UserSettings {
  theme: ThemeType;
  notifications: UserSettingsNotifications;
  location_sharing: boolean;
  privacy: UserSettingsPrivacy;
}

export interface UserSettingsRow {
  id: string;
  user_id: string;
  settings: UserSettings;
  created_at: string;
  updated_at: string;
} 