export interface AIConversationMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface DumpsterRecommendation {
  dumpsterId: string;
  score: number;
  reasons: string[];
}

export interface DumpsterRecommendationResult {
  recommendedSize: {
    minSize: number;
    maxSize: number;
    idealSize: number;
  };
  wasteTypes: string[];
  reasoning: string;
  confidenceScore: number;
}

export const wasteTypeTranslations = {
  en: {
    construction: "construction",
    furniture: "furniture",
    yard: "yard",
    household: "household",
    cabinets: "cabinets",
    fixtures: "fixtures",
    demolition: "demolition"
  },
  ar: {
    construction: "مواد البناء",
    furniture: "أثاث",
    yard: "نفايات الحديقة",
    household: "نفايات منزلية",
    cabinets: "خزائن",
    fixtures: "تركيبات",
    demolition: "مخلفات الهدم"
  }
};
