/// <reference types="jest" />

import { api } from '../api';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock axios and AsyncStorage
jest.mock('axios');
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (mockedAsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
  });

  it('should create an axios instance with correct base URL', () => {
    expect(mockedAxios.create).toHaveBeenCalledWith({
      baseURL: expect.any(String),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });

  it('should add authorization header when token exists', async () => {
    const token = 'test-token';
    (mockedAsyncStorage.getItem as jest.Mock).mockResolvedValue(token);

    const config = {
      headers: {},
    };

    // @ts-ignore - accessing private property
    const interceptor = api.interceptors.request.handlers[0].fulfilled;
    const result = await interceptor(config);

    expect(result.headers.Authorization).toBe(`Bearer ${token}`);
  });

  it('should not add authorization header when token does not exist', async () => {
    const config = {
      headers: {},
    };

    // @ts-ignore - accessing private property
    const interceptor = api.interceptors.request.handlers[0].fulfilled;
    const result = await interceptor(config);

    expect(result.headers.Authorization).toBeUndefined();
  });

  it('should handle 401 unauthorized response', async () => {
    const error = {
      response: {
        status: 401,
      },
    };

    // @ts-ignore - accessing private property
    const interceptor = api.interceptors.response.handlers[0].rejected;
    await interceptor(error);

    expect(mockedAsyncStorage.removeItem).toHaveBeenCalledWith('token');
  });

  it('should not remove token on non-401 errors', async () => {
    const error = {
      response: {
        status: 500,
      },
    };

    // @ts-ignore - accessing private property
    const interceptor = api.interceptors.response.handlers[0].rejected;
    await interceptor(error);

    expect(mockedAsyncStorage.removeItem).not.toHaveBeenCalled();
  });
}); 