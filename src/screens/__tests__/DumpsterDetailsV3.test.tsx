import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DumpsterDetailsV3 from '../DumpsterDetailsV3';
import { Dumpster } from '@/types/v2/dumpster';
import { PartnerOffer } from '@/components/PartnerOfferList';

// Mock the context hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('DumpsterDetailsV3', () => {
  const mockDumpster: Dumpster = {
    id: '1',
    nameEn: '10 Yard Dumpster',
    nameAr: 'حاوية 10 ياردة',
    imageUrl: 'https://example.com/dumpster.jpg',
    additionalImages: ['https://example.com/dumpster2.jpg'],
    length: 10,
    width: 8,
    height: 4,
    capacity: 10,
    maxWeight: 2000,
    pricePerLoad: 250,
    rating: 4.5,
    reviewCount: 12,
    isAvailable: true,
    sizeId: 'size-1',
    description: {
      en: 'A great dumpster for construction projects',
      ar: 'حاوية رائعة لمشاريع البناء'
    },
    wasteTypes: [
      { id: 'wt1', nameEn: 'Construction', nameAr: 'بناء' },
      { id: 'wt2', nameEn: 'Household', nameAr: 'منزلي' }
    ],
    features: [
      { id: 'f1', nameEn: 'Easy loading', nameAr: 'تحميل سهل' },
      { id: 'f2', nameEn: 'Weatherproof', nameAr: 'مقاوم للطقس' }
    ]
  };

  const mockOffers: PartnerOffer[] = [
    {
      id: '1',
      partnerId: 'partner-1',
      partner: {
        id: 'partner-1',
        company_name: 'Waste Solutions Inc.',
        contact_person: 'John Doe',
        contact_phone: '************',
        contact_email: '<EMAIL>',
        profile_id: 'profile-1',
        status: 'active',
        is_verified: true,
        service_areas: ['Area 1'],
        rating: 4.5,
        profile: {
          id: 'profile-1',
          avatar_url: 'https://example.com/avatar.jpg',
          full_name: 'John Doe',
          email: '<EMAIL>',
          phone: '************',
          user_type: 'partner'
        }
      },
      price: 250,
      isAvailable: true
    }
  ];

  it('renders loading state correctly', () => {
    const { getByText } = render(
      <DumpsterDetailsV3
        dumpster={null}
        isLoading={true}
        partnerOffers={[]}
        isLoadingOffers={false}
        onSelectOffer={() => {}}
        onClose={() => {}}
      />
    );
    
    expect(getByText('loading_dumpster_details')).toBeTruthy();
  });

  it('renders dumpster details correctly', () => {
    const { getByText } = render(
      <DumpsterDetailsV3
        dumpster={mockDumpster}
        isLoading={false}
        partnerOffers={mockOffers}
        isLoadingOffers={false}
        onSelectOffer={() => {}}
        onClose={() => {}}
      />
    );
    
    expect(getByText('10 Yard Dumpster')).toBeTruthy();
    expect(getByText('A great dumpster for construction projects')).toBeTruthy();
    expect(getByText('specifications')).toBeTruthy();
    expect(getByText('Construction')).toBeTruthy();
    expect(getByText('Waste Solutions Inc.')).toBeTruthy();
  });

  it('calls onSelectOffer when an offer is pressed', () => {
    const onSelectOffer = jest.fn();
    const { getByTestId } = render(
      <DumpsterDetailsV3
        dumpster={mockDumpster}
        isLoading={false}
        partnerOffers={mockOffers}
        isLoadingOffers={false}
        onSelectOffer={onSelectOffer}
        onClose={() => {}}
      />
    );
    
    fireEvent.press(getByTestId('offer-1'));
    expect(onSelectOffer).toHaveBeenCalledWith(mockOffers[0]);
  });

  it('calls onClose when close button is pressed', () => {
    const onClose = jest.fn();
    const { getByTestId } = render(
      <DumpsterDetailsV3
        dumpster={mockDumpster}
        isLoading={false}
        partnerOffers={mockOffers}
        isLoadingOffers={false}
        onSelectOffer={() => {}}
        onClose={onClose}
      />
    );
    
    fireEvent.press(getByTestId('close-button'));
    expect(onClose).toHaveBeenCalled();
  });
});
