import React, { useState, useRef } from 'react';
import { View, Text, Image, ScrollView, ActivityIndicator, StyleSheet, Dimensions, TouchableOpacity, FlatList } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useRTLContext } from '@/components/rtl/new-index';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { Dumpster } from '@/types/v2/dumpster';
import { PartnerOffer } from '@/components/PartnerOfferList';
import Animated, { FadeIn } from 'react-native-reanimated';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledAnimatedView = styled(Animated.View);
const StyledFlatList = styled(FlatList);

interface DumpsterDetailsV3Props {
  dumpster: Dumpster | null;
  isLoading: boolean;
  partnerOffers: PartnerOffer[];
  isLoadingOffers: boolean;
  onSelectOffer: (offer: PartnerOffer) => void;
  onClose: () => void;
}

/**
 * DumpsterDetailsV3 - A screen for displaying detailed dumpster information and partner offers
 *
 * Features:
 * - Displays dumpster size, description, images, and specifications
 * - Shows a list of partner offers sorted by price and rating
 * - Supports RTL layouts and dark mode
 * - Shows loading state with shimmer effect
 * - Uses react-native-reanimated-carousel for image gallery
 */
export default function DumpsterDetailsV3({
  dumpster,
  isLoading,
  partnerOffers,
  isLoadingOffers,
  onSelectOffer,
  onClose,
}: DumpsterDetailsV3Props) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const screenWidth = Dimensions.get('window').width;
  const flatListRef = useRef<FlatList>(null);

  // Prepare images array
  const images = dumpster?.additionalImages?.length
    ? [dumpster.imageUrl, ...dumpster.additionalImages]
    : dumpster?.imageUrl
      ? [dumpster.imageUrl]
      : [];

  // Handle loading state
  if (isLoading || !dumpster) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-gray-900 justify-center items-center">
        <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
        <StyledText className="text-gray-600 dark:text-gray-400 mt-4">
          {t('loading_dumpster_details')}
        </StyledText>
      </StyledView>
    );
  }

  // Extract dumpster data
  const name = isRTL ? dumpster.nameAr : dumpster.nameEn;
  const description = isRTL
    ? dumpster.description?.ar
    : dumpster.description?.en;

  // Dimensions with fallbacks
  const length = dumpster.length || 0;
  const width = dumpster.width || 0;
  const height = dumpster.height || 0;
  const capacity = dumpster.capacity || 0;
  const maxWeight = dumpster.maxWeight || 0;

  return (
    <StyledView className="flex-1 bg-white dark:bg-gray-900">
      {/* Close button */}
      <StyledTouchableOpacity
        className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 rounded-full p-1"
        onPress={onClose}
        testID="close-button"
      >
        <MaterialIcons name="close" size={24} color="#fff" />
      </StyledTouchableOpacity>

      <StyledScrollView showsVerticalScrollIndicator={false}>
        {/* Image Carousel */}
        <StyledView className="w-full h-64 bg-gray-200 dark:bg-gray-800">
          {images.length > 0 ? (
            <>
              <StyledFlatList
                ref={flatListRef}
                data={images}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onMomentumScrollEnd={(e) => {
                  const contentOffset = e.nativeEvent.contentOffset;
                  const viewSize = e.nativeEvent.layoutMeasurement;
                  const newIndex = Math.floor(contentOffset.x / viewSize.width);
                  setActiveImageIndex(newIndex);
                }}
                renderItem={({ item }) => (
                  <Image
                    source={{ uri: item as string }}
                    style={{ width: screenWidth, height: 256 }}
                    resizeMode="cover"
                  />
                )}
                keyExtractor={(_, index) => index.toString()}
              />

              {/* Pagination dots */}
              {images.length > 1 && (
                <StyledView className="absolute bottom-4 left-0 right-0 flex-row justify-center">
                  {images.map((_, index) => (
                    <StyledTouchableOpacity
                      key={index}
                      className={`mx-1 rounded-full w-2 h-2 ${
                        index === activeImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                      }`}
                      onPress={() => {
                        setActiveImageIndex(index);
                        flatListRef.current?.scrollToIndex({ index, animated: true });
                      }}
                    />
                  ))}
                </StyledView>
              )}
            </>
          ) : (
            <StyledView className="w-full h-full justify-center items-center">
              <MaterialIcons name="image-not-supported" size={48} color={colors.textColors.secondary.light} />
            </StyledView>
          )}
        </StyledView>

        {/* Dumpster Details */}
        <StyledView className="p-4">
          <StyledAnimatedView entering={FadeIn.duration(300)}>
            {/* Title and Rating */}
            <StyledView className="flex-row justify-between items-start">
              <StyledView className="flex-1">
                <StyledText className="text-2xl font-bold text-gray-800 dark:text-white">
                  {name}
                </StyledText>

                {dumpster.rating > 0 && (
                  <StyledView className="flex-row items-center mt-1">
                    <MaterialIcons name="star" size={18} color="#FFD700" />
                    <StyledText className="text-gray-700 dark:text-gray-300 ml-1">
                      {dumpster.rating.toFixed(1)}
                    </StyledText>
                    {dumpster.reviewCount > 0 && (
                      <StyledText className="text-gray-500 dark:text-gray-400 ml-1">
                        ({dumpster.reviewCount})
                      </StyledText>
                    )}
                  </StyledView>
                )}
              </StyledView>

              {/* Price */}
              <StyledView className="bg-primary-50 dark:bg-primary-900 px-3 py-2 rounded-lg">
                <StyledText className="text-primary-600 dark:text-primary-300 font-bold">
                  {t('from')} ﷼{dumpster.pricePerLoad.toLocaleString()}
                </StyledText>
              </StyledView>
            </StyledView>

            {/* Description */}
            {description && (
              <StyledView className="mt-4">
                <StyledText className="text-base text-gray-700 dark:text-gray-300">
                  {description}
                </StyledText>
              </StyledView>
            )}

            {/* Specifications */}
            <StyledView className="mt-6">
              <StyledText className="text-lg font-bold text-gray-800 dark:text-white mb-2">
                {t('specifications')}
              </StyledText>

              <StyledView className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
                <StyledView className="flex-row justify-between mb-2">
                  <StyledText className="text-gray-600 dark:text-gray-400">
                    {t('dimensions')}
                  </StyledText>
                  <StyledText className="text-gray-800 dark:text-gray-200 font-medium">
                    {`${length}m × ${width}m × ${height}m`}
                  </StyledText>
                </StyledView>

                <StyledView className="flex-row justify-between mb-2">
                  <StyledText className="text-gray-600 dark:text-gray-400">
                    {t('capacity')}
                  </StyledText>
                  <StyledText className="text-gray-800 dark:text-gray-200 font-medium">
                    {`${capacity} ${t('cubic_yards')}`}
                  </StyledText>
                </StyledView>

                <StyledView className="flex-row justify-between">
                  <StyledText className="text-gray-600 dark:text-gray-400">
                    {t('max_weight')}
                  </StyledText>
                  <StyledText className="text-gray-800 dark:text-gray-200 font-medium">
                    {`${maxWeight} ${t('pounds')}`}
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>

            {/* Waste Types */}
            {dumpster.wasteTypes && dumpster.wasteTypes.length > 0 && (
              <StyledView className="mt-6">
                <StyledText className="text-lg font-bold text-gray-800 dark:text-white mb-2">
                  {t('suitable_for')}
                </StyledText>

                <StyledView className="flex-row flex-wrap">
                  {dumpster.wasteTypes.map((wasteType, index) => (
                    <StyledView
                      key={index}
                      className="bg-gray-100 dark:bg-gray-800 rounded-full px-3 py-1 mr-2 mb-2"
                    >
                      <StyledText className="text-gray-800 dark:text-gray-200">
                        {isRTL ? wasteType.nameAr : wasteType.nameEn}
                      </StyledText>
                    </StyledView>
                  ))}
                </StyledView>
              </StyledView>
            )}

            {/* Features */}
            {dumpster.features && dumpster.features.length > 0 && (
              <StyledView className="mt-6">
                <StyledText className="text-lg font-bold text-gray-800 dark:text-white mb-2">
                  {t('features')}
                </StyledText>

                {dumpster.features.map((feature, index) => (
                  <StyledView key={index} className="flex-row items-center mb-2">
                    <MaterialIcons name="check-circle" size={18} color={colors.brandColors.success[500]} />
                    <StyledText className="text-gray-700 dark:text-gray-300 ml-2">
                      {isRTL ? feature.nameAr : feature.nameEn}
                    </StyledText>
                  </StyledView>
                ))}
              </StyledView>
            )}
          </StyledAnimatedView>
        </StyledView>

        {/* Partner Offers Section */}
        <StyledView className="mt-4 pb-8">
          <StyledText className="text-xl font-bold text-gray-800 dark:text-white px-4 mb-2">
            {t('partner_offers')}
          </StyledText>

          {isLoadingOffers ? (
            <StyledView className="p-4">
              <ActivityIndicator size="small" color={colors.brandColors.primary[500]} />
              <StyledText className="text-center text-gray-600 dark:text-gray-400 mt-2">
                {t('loading_partner_offers')}
              </StyledText>
            </StyledView>
          ) : partnerOffers.length === 0 ? (
            <StyledView className="p-4 items-center">
              <MaterialIcons name="info" size={24} color={colors.textColors.secondary.light} />
              <StyledText className="text-center text-gray-600 dark:text-gray-400 mt-2">
                {t('no_partner_offers_available')}
              </StyledText>
            </StyledView>
          ) : (
            partnerOffers.map((offer) => (
              <StyledTouchableOpacity
                key={offer.id}
                className="bg-white dark:bg-gray-800 mx-4 mb-3 p-4 rounded-xl"
                style={styles.cardShadow}
                onPress={() => onSelectOffer(offer)}
                testID={`offer-${offer.id}`}
              >
                <StyledView className="flex-row items-center">
                  {/* Partner Image */}
                  <StyledView className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
                    {offer.partner.profile?.avatar_url ? (
                      <Image
                        source={{ uri: offer.partner.profile.avatar_url }}
                        style={{ width: '100%', height: '100%' }}
                        resizeMode="cover"
                      />
                    ) : (
                      <StyledView className="w-full h-full justify-center items-center">
                        <MaterialIcons name="business" size={20} color={colors.textColors.secondary.light} />
                      </StyledView>
                    )}
                  </StyledView>

                  {/* Partner Info */}
                  <StyledView className="ml-3 flex-1">
                    <StyledText className="text-base font-medium text-gray-800 dark:text-white">
                      {offer.partner.company_name}
                    </StyledText>

                    {offer.partner.rating > 0 && (
                      <StyledView className="flex-row items-center mt-1">
                        <MaterialIcons name="star" size={14} color="#FFD700" />
                        <StyledText className="text-sm text-gray-600 dark:text-gray-400 ml-1">
                          {offer.partner.rating.toFixed(1)}
                        </StyledText>
                      </StyledView>
                    )}
                  </StyledView>

                  {/* Price */}
                  <StyledView className="bg-primary-50 dark:bg-primary-900 px-3 py-2 rounded-lg">
                    <StyledText className="text-primary-600 dark:text-primary-300 font-bold">
                      ﷼{offer.price.toLocaleString()}
                    </StyledText>
                  </StyledView>
                </StyledView>
              </StyledTouchableOpacity>
            ))
          )}
        </StyledView>
      </StyledScrollView>
    </StyledView>
  );
}

const styles = StyleSheet.create({
  cardShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
});

// Jest test stub
/*
import { render, fireEvent } from '@testing-library/react-native';
import DumpsterDetailsV3 from '../DumpsterDetailsV3';
import { Dumpster } from '@/types/v2/dumpster';
import { PartnerOffer } from '@/components/PartnerOfferList';

// Mock the context hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('DumpsterDetailsV3', () => {
  const mockDumpster: Dumpster = {
    id: '1',
    nameEn: '10 Yard Dumpster',
    nameAr: 'حاوية 10 ياردة',
    imageUrl: 'https://example.com/dumpster.jpg',
    additionalImages: ['https://example.com/dumpster2.jpg'],
    length: 10,
    width: 8,
    height: 4,
    capacity: 10,
    maxWeight: 2000,
    pricePerLoad: 250,
    rating: 4.5,
    reviewCount: 12,
    isAvailable: true,
    sizeId: 'size-1',
    description: {
      en: 'A great dumpster for construction projects',
      ar: 'حاوية رائعة لمشاريع البناء'
    },
    wasteTypes: [
      { id: 'wt1', nameEn: 'Construction', nameAr: 'بناء' },
      { id: 'wt2', nameEn: 'Household', nameAr: 'منزلي' }
    ],
    features: [
      { id: 'f1', nameEn: 'Easy loading', nameAr: 'تحميل سهل' },
      { id: 'f2', nameEn: 'Weatherproof', nameAr: 'مقاوم للطقس' }
    ]
  };

  // Mock offers would be generated from real partner data in tests
  const mockOffers: PartnerOffer[] = [];

  it('renders loading state correctly', () => {
    const { getByText } = render(
      <DumpsterDetailsV3
        dumpster={null}
        isLoading={true}
        partnerOffers={[]}
        isLoadingOffers={false}
        onSelectOffer={() => {}}
        onClose={() => {}}
      />
    );

    expect(getByText('loading_dumpster_details')).toBeTruthy();
  });

  it('renders dumpster details correctly', () => {
    const { getByText, getByTestId } = render(
      <DumpsterDetailsV3
        dumpster={mockDumpster}
        isLoading={false}
        partnerOffers={mockOffers}
        isLoadingOffers={false}
        onSelectOffer={() => {}}
        onClose={() => {}}
      />
    );

    expect(getByText('10 Yard Dumpster')).toBeTruthy();
    expect(getByText('A great dumpster for construction projects')).toBeTruthy();
    expect(getByText('specifications')).toBeTruthy();
    expect(getByText('Construction')).toBeTruthy();
    expect(getByText('Waste Solutions Inc.')).toBeTruthy();
  });

  it('calls onSelectOffer when an offer is pressed', () => {
    const onSelectOffer = jest.fn();
    const { getByTestId } = render(
      <DumpsterDetailsV3
        dumpster={mockDumpster}
        isLoading={false}
        partnerOffers={mockOffers}
        isLoadingOffers={false}
        onSelectOffer={onSelectOffer}
        onClose={() => {}}
      />
    );

    fireEvent.press(getByTestId('offer-1'));
    expect(onSelectOffer).toHaveBeenCalledWith(mockOffers[0]);
  });

  it('calls onClose when close button is pressed', () => {
    const onClose = jest.fn();
    const { getByTestId } = render(
      <DumpsterDetailsV3
        dumpster={mockDumpster}
        isLoading={false}
        partnerOffers={mockOffers}
        isLoadingOffers={false}
        onSelectOffer={() => {}}
        onClose={onClose}
      />
    );

    fireEvent.press(getByTestId('close-button'));
    expect(onClose).toHaveBeenCalled();
  });
});
*/
