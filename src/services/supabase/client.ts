import 'react-native-url-polyfill/auto';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import { GlobalUserIdentifier } from '@/context/AuthContext';

if (!process.env.EXPO_PUBLIC_SUPABASE_URL) {
  console.warn('Missing SUPABASE_URL environment variable. Fallback to undefined.');
}

if (!process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY) {
  console.warn('Missing SUPABASE_ANON_KEY environment variable. Fallback to undefined.');
}

console.log('[clients]Supabase URL:', process.env.EXPO_PUBLIC_SUPABASE_URL ? 'Present' : 'Missing');
console.log('[clients]Supabase Anon Key:', process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ? 'Present' : 'Missing');

// Create the standard client with anon key (has RLS)
export const supabase = createClient<Database>(
  process.env.EXPO_PUBLIC_SUPABASE_URL!,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  }
);

// Create a service role client that can bypass RLS for admin operations
// WARNING: This should ONLY be used for read operations to avoid the read-only transaction errors
// Never expose this client directly to the user or use it for writes
// Uncomment adminSupabase creation
export const adminSupabase = process.env.SUPABASE_SERVICE_ROLE_KEY 
  ? createClient(
      process.env.EXPO_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY, 
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    )
  : supabase; // Fallback to regular client if no service key

// Function to safely read data without RLS to avoid read-only transaction errors
// Uncomment the entire safeReadData function
export const safeReadData = async (table: string, options: any = {}) => {
  try {
    console.log(`🔒 Performing safe read on table: ${table} with filters:`, options);

    if (table === 'orders') {
      const orderId = options.id;
      const profileId = options.customer_id;

      let query = adminSupabase.from('orders').select('*'); // Use adminSupabase again

      if (orderId) {
        console.log(`🔄 Explicitly querying order with id=${orderId}`);
        query = query.eq('id', orderId);
      } else if (profileId) {
        console.log(`🔄 Explicitly querying orders with customer_id=${profileId}`);
        query = query.eq('customer_id', profileId);
      } else {
        console.warn('⚠️ safeReadData for orders called without id or customer_id filter.');
        return { data: [], error: { message: 'Missing required filter for orders table (id or customer_id)', details: '', hint: '', code: 'SAFE_READ_FILTER_MISSING' } };
      }

      const { data, error } = await query;

      if (error) {
        console.log(`❌ Error reading orders: ${error.message}`);
        return { data: null, error };
      } else if (data && data.length > 0) {
        console.log(`✅ Successfully read ${data.length} orders with filter.`);
        return { data, error: null };
      } else {
        console.log(`🤷 No orders found with the provided filter.`);
        return { data: [], error: null };
      }

    } else if (table === 'addresses') {
      const userId = options.user_id;

      if (!userId) {
         console.warn('⚠️ safeReadData for addresses called without user_id filter.');
         return { data: [], error: { message: 'Missing required filter for addresses table (user_id)', details: '', hint: '', code: 'SAFE_READ_FILTER_MISSING' } };
      }

      console.log(`🔄 Explicitly querying addresses with user_id=${userId}`);
      const { data, error } = await adminSupabase // Use adminSupabase again
        .from('addresses')
        .select('*')
        .eq('user_id', userId);

      if (error) {
        console.log(`❌ Error reading addresses: ${error.message}`);
        return { data: null, error };
      } else if (data && data.length > 0) {
        console.log(`✅ Successfully read ${data.length} addresses with filter.`);
        return { data, error: null };
      } else {
        console.log(`🤷 No addresses found with the provided filter.`);
        return { data: [], error: null };
      }

    } else {
      console.warn(`⚠️ safeReadData called for unhandled table: ${table}. Returning empty.`);
      return { data: [], error: { message: `safeReadData not configured for table: ${table}`, details: '', hint: '', code: 'SAFE_READ_UNHANDLED_TABLE' } };
    }

  } catch (error: any) {
    console.error(`❌ Exception in safeReadData for ${table}:`, error);
    const errorMessage = error.message || 'An unexpected error occurred during safe read';
    return { data: [], error: { message: errorMessage, details: error.details || '', hint: error.hint || '', code: error.code || 'SAFE_READ_EXCEPTION' } };
  }
};

// Initialize session state
const initializeSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.log('[clients] Error getting initial session:', error);
      return;
    }

    if (session) {
      console.log('[clients]Initial session found:', {
        userId: GlobalUserIdentifier.profileId,
        role: session.user.role,
        email: session.user.email,
        aud: session.user.aud,
        exp: new Date(session.expires_at! * 1000).toISOString()
      });

      // Refresh session if needed
      const { error: refreshError } = await supabase.auth.refreshSession();
      if (refreshError) {
        console.log('[clients] Error refreshing session:', refreshError);
      } else {
        console.log('[clients]Session refreshed successfully');
      }
    } else {
      console.log('[clients]No initial session found');
    }
  } catch (error) {
    console.log('[clients] Error initializing session:', error);
  }
};

// Add session check and refresh
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log('[clients]Auth state changed:', event, session?.user?.id);
  
  if (session) {
    console.log('[clients]Session details:', {
      userId: GlobalUserIdentifier.profileId,
      role: session.user.role,
      email: session.user.email,
      aud: session.user.aud,
      exp: new Date(session.expires_at! * 1000).toISOString(),
      accessToken: session.access_token ? 'Present' : 'Missing'
    });

    // Refresh session on auth state change
    if (event === 'SIGNED_IN') {
      const { error: refreshError } = await supabase.auth.refreshSession();
      if (refreshError) {
        console.log('[clients] Error refreshing session on sign in:', refreshError);
      } else {
        console.log('[clients]Session refreshed on sign in');
      }
    }
  }
});

// Initialize the session
initializeSession(); 