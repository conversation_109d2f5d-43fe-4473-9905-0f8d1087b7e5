import { supabase } from '../supabase';
import { Dumpster, DumpsterFilter, WasteType } from '@/types/dumpster';

/**
 * Get all waste types
 */
export async function getWasteTypes(): Promise<WasteType[]> {
  try {
    const { data, error } = await supabase
      .from('waste_types')
      .select('*');

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching waste types:', error);
    throw error;
  }
}

/**
 * Get all dumpsters
 */
export async function getDumpsters(filter?: DumpsterFilter): Promise<Dumpster[]> {
  try {
    let query = supabase
      .from('dumpsters')
      .select('*');

    // Apply filters if provided
    if (filter) {
      // Filter by size
      if (filter.sizeIds && filter.sizeIds.length > 0) {
        query = query.contains('sizes', filter.sizeIds);
      }

      // Filter by waste type
      if (filter.wasteTypeIds && filter.wasteTypeIds.length > 0) {
        query = query.contains('compatible_waste_types', filter.wasteTypeIds);
      }

      // Filter by price range
      if (filter.priceRange) {
        if (filter.priceRange.min !== undefined) {
          query = query.gte('price_per_day', filter.priceRange.min);
        }
        if (filter.priceRange.max !== undefined) {
          query = query.lte('price_per_day', filter.priceRange.max);
        }
      }

      // Filter by search term
      if (filter.searchTerm) {
        query = query.ilike('name', `%${filter.searchTerm}%`);
      }

      // Apply sorting
      if (filter.sortBy) {
        const sortColumn = getSortColumn(filter.sortBy);
        const sortOrder = filter.sortOrder || 'asc';
        query = query.order(sortColumn, { ascending: sortOrder === 'asc' });
      }
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching dumpsters:', error);
    throw error;
  }
}

/**
 * Get a single dumpster by ID
 */
export async function getDumpsterById(id: string): Promise<Dumpster | null> {
  try {
    const { data, error } = await supabase
      .from('dumpsters')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Error fetching dumpster with ID ${id}:`, error);
    throw error;
  }
}

// Helper function to map sort options to database columns
function getSortColumn(sortBy: 'price' | 'size' | 'rating' | 'availability'): string {
  switch (sortBy) {
    case 'price':
      return 'price_per_day';
    case 'size':
      return 'sizes';
    case 'rating':
      return 'rating';
    case 'availability':
      return 'availability';
    default:
      return 'name';
  }
} 