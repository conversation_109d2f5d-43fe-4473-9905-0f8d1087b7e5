import { Dumpster, WasteTag, WasteType } from '@/types/dumpster';

// Map old IDs to new UUIDs for backwards compatibility
export const dumpsterIdMap = {
  'dump1': 'o5r5s8u5-14v6-2t89-7t1w-u3v5t4r676w9',
  'dump2': 'p6s6t9v6-25w7-3u90-8u2x-v4w6u5s787x0',
  'dump3': 'q7t7u0w7-36x8-4v01-9v3y-w5x7v6t898y1',
  'dump4': 'r8u8v1x8-47y9-5w12-0w4z-x6y8w7u909z2',
  'dump5': 's9v9w2y9-58z0-6x23-1x5a-y7z9x8v010a3',
  'dump6': 't0w0x3z0-69a1-7y34-2y6b-z8a0y9w121b4'
};

// Waste Tags
export const wasteTags: WasteTag[] = [
  { id: '60f7a9c7-36d8-4b11-9b3e-c5d7b69800e1', name: 'Recyclable' },
  { id: '70a8b1d8-47e9-5c12-0c4f-d6e8c7a900f2', name: 'Hazardous' },
  { id: '80b9c2e9-58f0-6d23-1d5g-e7f9d8b010g3', name: 'Organic' },
  { id: '90c0d3f0-69g1-7e34-2e6h-f8g0e9c121h4', name: 'Non-recyclable' },
  { id: 'a1d1e4g1-70h2-8f45-3f7i-g9h1f0d232i5', name: 'Heavy' },
  { id: 'b2e2f5h2-81i3-9g56-4g8j-h0i2g1e343j6', name: 'Bulky' },
  { id: 'c3f3g6i3-92j4-0h67-5h9k-i1j3h2f454k7', name: 'Liquid' },
  { id: 'd4g4h7j4-03k5-1i78-6i0l-j2k4i3g565l8', name: 'Solid' },
];

// Waste Types
export const wasteTypes: WasteType[] = [
  {
    id: 'e5h5i8k5-14l6-2j89-7j1m-k3l5j4h676m9',
    name: 'Mixed construction waste',
    description: 'General construction debris including wood, drywall, and other building materials',
    imageUrl: '/assets/images/waste-types/mixed-construction.jpg',
    tags: [wasteTags[5].name, wasteTags[7].name],
  },
  {
    id: 'f6i6j9l6-25m7-3k90-8k2n-l4m6k5i787n0',
    name: 'Reclaimed wood',
    description: 'Used lumber, wooden pallets, and other wood materials that can be recycled',
    imageUrl: '/assets/images/waste-types/reclaimed-wood.jpg',
    tags: [wasteTags[0].name, wasteTags[5].name],
  },
  {
    id: 'g7j7k0m7-36n8-4l01-9l3o-m5n7l6j898o1',
    name: 'Mixed waste',
    description: 'General household and commercial waste of various types',
    imageUrl: '/assets/images/waste-types/mixed-waste.jpg',
    tags: [wasteTags[3].name, wasteTags[7].name],
  },
  {
    id: 'h8k8l1n8-47o9-5m12-0m4p-n6o8m7k909p2',
    name: 'Construction waste',
    description: 'Specific construction materials including concrete, bricks, and tiles',
    imageUrl: '/assets/images/waste-types/construction-waste.jpg',
    tags: [wasteTags[4].name, wasteTags[5].name, wasteTags[7].name],
  },
  {
    id: 'i9l9m2o9-58p0-6n23-1n5q-o7p9n8l010q3',
    name: 'Soil excavation',
    description: 'Dirt, soil, and other excavated materials from construction sites',
    imageUrl: '/assets/images/waste-types/soil-excavation.jpg',
    tags: [wasteTags[4].name, wasteTags[7].name],
  },
  {
    id: 'j0m0n3p0-69q1-7o34-2o6r-p8q0o9m121r4',
    name: 'Green cuttings',
    description: 'Yard waste including grass clippings, branches, and other plant materials',
    imageUrl: '/assets/images/waste-types/green-cuttings.jpg',
    tags: [wasteTags[0].name, wasteTags[2].name],
  },
];

const dumpsterSizes = [
  {
    id: 'k1n1o4q1-70r2-8p45-3p7s-q9r1p0n232s5',
    name: '10 Yard',
    volumeCubicYards: 10,
    maxWeightPounds: 2000,
    dimensions: {
      length: 12,
      width: 8,
      height: 3.5,
    },
    description: 'Good for small renovation projects and yard cleanups',
  },
  {
    id: 'l2o2p5r2-81s3-9q56-4q8t-r0s2q1o343t6',
    name: '20 Yard',
    volumeCubicYards: 20,
    maxWeightPounds: 4000,
    dimensions: {
      length: 16,
      width: 8,
      height: 4,
    },
    description: 'Ideal for medium-sized projects and home renovations',
  },
  {
    id: 'm3p3q6s3-92t4-0r67-5r9u-s1t3r2p454u7',
    name: '30 Yard',
    volumeCubicYards: 30,
    maxWeightPounds: 6000,
    dimensions: {
      length: 20,
      width: 8,
      height: 5,
    },
    description: 'Perfect for large construction projects and major cleanouts',
  },
  {
    id: 'n4q4r7t4-03u5-1s78-6s0v-t2u4s3q565v8',
    name: '40 Yard',
    volumeCubicYards: 40,
    maxWeightPounds: 8000,
    dimensions: {
      length: 22,
      width: 8,
      height: 7,
    },
    description: 'Best for commercial projects and large-scale construction',
  },
];

// Dumpsters
export const dumpsters: Dumpster[] = [
  {
    id: 'o5r5s8u5-14v6-2t89-7t1w-u3v5t4r676w9',
    name: 'Standard Construction Dumpster',
    description: 'All-purpose dumpster for construction and renovation projects',
    imageUrl: '/assets/images/dumpsters/standard-construction.jpg',
    sizes: [dumpsterSizes[0], dumpsterSizes[1], dumpsterSizes[2]],
    compatibleWasteTypes: [wasteTypes[0].id, wasteTypes[3].id],
    pricePerDay: 75,
    availability: {
      isAvailable: true,
    },
    features: ['Roll-off design', 'Easy loading', 'Durable construction'],
    rating: 4.5,
    reviewCount: 120,
  },
  {
    id: 'p6s6t9v6-25w7-3u90-8u2x-v4w6u5s787x0',
    name: 'Heavy Duty Dumpster',
    description: 'Reinforced dumpster for heavy materials like concrete and soil',
    imageUrl: '/assets/images/dumpsters/heavy-duty.jpg',
    sizes: [dumpsterSizes[1], dumpsterSizes[2], dumpsterSizes[3]],
    compatibleWasteTypes: [wasteTypes[3].id, wasteTypes[4].id],
    pricePerDay: 95,
    availability: {
      isAvailable: true,
    },
    features: ['Reinforced base', 'High weight capacity', 'Industrial-grade steel'],
    rating: 4.7,
    reviewCount: 85,
  },
  {
    id: 'q7t7u0w7-36x8-4v01-9v3y-w5x7v6t898y1',
    name: 'Residential Cleanup Dumpster',
    description: 'Perfect for home cleanouts, renovations, and yard work',
    imageUrl: '/assets/images/dumpsters/residential.jpg',
    sizes: [dumpsterSizes[0], dumpsterSizes[1]],
    compatibleWasteTypes: [wasteTypes[2].id, wasteTypes[5].id],
    pricePerDay: 65,
    availability: {
      isAvailable: true,
    },
    features: ['Compact design', 'Residential-friendly', 'Low entry point'],
    rating: 4.3,
    reviewCount: 210,
  },
  {
    id: 'r8u8v1x8-47y9-5w12-0w4z-x6y8w7u909z2',
    name: 'Wood Recycling Dumpster',
    description: 'Specialized dumpster for wood waste and reclaimed materials',
    imageUrl: '/assets/images/dumpsters/wood-recycling.jpg',
    sizes: [dumpsterSizes[0], dumpsterSizes[1], dumpsterSizes[2]],
    compatibleWasteTypes: [wasteTypes[1].id],
    pricePerDay: 70,
    availability: {
      isAvailable: true,
    },
    features: ['Eco-friendly disposal', 'Recycling focused', 'Sustainable option'],
    rating: 4.6,
    reviewCount: 65,
  },
  {
    id: 's9v9w2y9-58z0-6x23-1x5a-y7z9x8v010a3',
    name: 'Commercial Construction Dumpster',
    description: 'Large capacity dumpster for major construction projects',
    imageUrl: '/assets/images/dumpsters/commercial-construction.jpg',
    sizes: [dumpsterSizes[2], dumpsterSizes[3]],
    compatibleWasteTypes: [wasteTypes[0].id, wasteTypes[3].id, wasteTypes[4].id],
    pricePerDay: 110,
    availability: {
      isAvailable: false,
      nextAvailableDate: '2023-06-15',
    },
    features: ['Extra-large capacity', 'Heavy-duty construction', 'Commercial grade'],
    rating: 4.8,
    reviewCount: 42,
  },
  {
    id: 't0w0x3z0-69a1-7y34-2y6b-z8a0y9w121b4',
    name: 'Mixed Waste Dumpster',
    description: 'General purpose dumpster for various waste types',
    imageUrl: '/assets/images/dumpsters/mixed-waste.jpg',
    sizes: [dumpsterSizes[0], dumpsterSizes[1], dumpsterSizes[2], dumpsterSizes[3]],
    compatibleWasteTypes: [wasteTypes[0].id, wasteTypes[1].id, wasteTypes[2].id, wasteTypes[5].id],
    pricePerDay: 85,
    availability: {
      isAvailable: true,
    },
    features: ['Versatile use', 'Multiple size options', 'General purpose'],
    rating: 4.4,
    reviewCount: 150,
  },
]; 