import OpenAI from 'openai';
import { AIConversationMessage } from '@/types/ai';
import i18next from 'i18next';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const ASSISTANT_ID = process.env.OPENAI_ASSISTANT_ID;

if (!ASSISTANT_ID) {
  throw new Error('OPENAI_ASSISTANT_ID is not configured in environment variables');
}

export async function chatWithAI(messages: AIConversationMessage[], userName?: string, threadId?: string) {
  try {
    console.log('Starting chatWithAI with messages:', messages);
    console.log('ThreadID:', threadId);

    // Create or reuse thread
    const thread = threadId ? 
      await openai.beta.threads.retrieve(threadId) : 
      await openai.beta.threads.create();
    
    console.log('Thread created/retrieved:', thread.id);

    // Add messages to thread
    for (const message of messages) {
      await openai.beta.threads.messages.create(thread.id, {
        role: message.role,
        content: message.content
      });
    }

    // If new thread, add language and user context first
    if (!threadId) {
      await openai.beta.threads.messages.create(thread.id, {
        role: "user",
        content: `Initial context:
Language: ${i18next.language}
User name: ${userName || ''}
Please maintain this context throughout the conversation.`
      });
    }

    const run = await openai.beta.threads.runs.create(thread.id, {
      assistant_id: ASSISTANT_ID as string
    });
    
    console.log('Run created:', run.id);

    const response = await waitForCompletion(thread.id, run.id);
    console.log('Full AI Response:', response);
    
    const latestMessage = response.data
      .filter(msg => msg.role === 'assistant')
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];

    if (!latestMessage) {
      throw new Error('No response from assistant');
    }

    if (latestMessage.content[0].type !== 'text') {
      throw new Error('Expected text response from assistant');
    }
    
    const content = latestMessage.content[0].text.value;
    console.log('Raw content:', content);
    
    const recommendationMatch = content.match(/\[RECOMMENDATION\](.*?)\[\/RECOMMENDATION\]/);
    console.log('Recommendation match:', recommendationMatch);
    
    return {
      reply: content.replace(/\[RECOMMENDATION\].*?\[\/RECOMMENDATION\]/g, '').trim(),
      ...(recommendationMatch && { recommendation: JSON.parse(recommendationMatch[1]) }),
      threadId: thread.id
    };

  } catch (error) {
    console.error('OpenAI Assistant API Error:', error);
    throw error;
  }
}

async function waitForCompletion(threadId: string, runId: string) {
  while (true) {
    const run = await openai.beta.threads.runs.retrieve(threadId, runId);
    if (run.status === 'completed') {
      return await openai.beta.threads.messages.list(threadId);
    } else if (run.status === 'failed' || run.status === 'cancelled') {
      throw new Error(`Run ${run.status}: ${run.last_error?.message || 'Unknown error'}`);
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}
