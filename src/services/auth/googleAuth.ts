import 'react-native-url-polyfill/auto';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';
import { supabase } from '../supabase';
import Constants from 'expo-constants';

WebBrowser.maybeCompleteAuthSession();

// Client IDs for different platforms
const WEB_CLIENT_ID = '604422821113-n51peofkgp6t5b8j55c7l4ncpmb95s6n.apps.googleusercontent.com';
const IOS_CLIENT_ID = '604422821113-7kruhkotv11tg6fqgfmja73gd9mc3r9o.apps.googleusercontent.com';
const ANDROID_CLIENT_ID = '604422821113-9ikdqbd7b3schqqai6vsf0sba99i2i6i.apps.googleusercontent.com';

export const useGoogleAuth = () => {
  const [request, response, promptAsync] = Google.useAuthRequest({
    webClientId: WEB_CLIENT_ID,
    iosClientId: IOS_CLIENT_ID,
    androidClientId: ANDROID_CLIENT_ID,
    responseType: 'id_token',
    scopes: ['openid', 'profile', 'email'],
    redirectUri: 'https://auth.expo.io/@modymok/dumpster-user-app'
  });

  const signInWithGoogle = async () => {
    try {
      console.log('Starting Google Sign-in...');
      const result = await promptAsync();
      console.log('Google Sign-in result:', result?.type);
      
      if (result?.type === 'success' && result.authentication?.idToken) {
        console.log('Got Google ID token, signing in with Supabase...');
        // Sign in with Supabase using the Google token
        const { data, error } = await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: result.authentication.idToken,
        });

        if (error) {
          console.error('Supabase sign-in error:', error);
          throw error;
        }
        console.log('Successfully signed in with Supabase:', data.user?.email);
        return data;
      } else {
        console.warn('Google sign-in failed or was cancelled');
        throw new Error('Google sign in was cancelled or failed');
      }
    } catch (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    }
  };

  return {
    signInWithGoogle,
    isLoading: !!request,
  };
}; 