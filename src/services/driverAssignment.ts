import { supabase } from '@/services/supabase';

/**
 * Assigns a driver to an order
 * @param orderId The ID of the order to assign the driver to
 * @param driverId The ID of the driver to assign
 * @param vehicleId Optional ID of the vehicle to use
 * @returns The created driver assignment or null if there was an error
 */
export async function assignDriverToOrder(
  orderId: string,
  driverId: string,
  vehicleId?: string
) {
  try {
    console.log(`[assignDriverToOrder] Assigning driver ${driverId} to order ${orderId}`);

    // Check if there's already an assignment for this order
    const { data: existingAssignments, error: checkError } = await supabase
      .from('driver_assignments')
      .select('*')
      .eq('order_id', orderId);

    if (checkError) {
      console.error('[assignDriverToOrder] Error checking existing assignments:', checkError);
      return null;
    }

    if (existingAssignments && existingAssignments.length > 0) {
      console.log(`[assignDriverToOrder] Order ${orderId} already has ${existingAssignments.length} assignments`);
      return existingAssignments[0];
    }

    // Generate a UUID for the assignment
    const assignmentId = crypto.randomUUID();

    // Create a new assignment with explicit ID
    const { data, error } = await supabase
      .from('driver_assignments')
      .insert({
        id: assignmentId,
        driver_id: driverId,
        order_id: orderId,
        vehicle_id: vehicleId || null,
        status: 'assigned',
        assigned_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('[assignDriverToOrder] Error creating driver assignment:', error);
      return null;
    }

    console.log('[assignDriverToOrder] Successfully created assignment:', data);
    return data;
  } catch (error) {
    console.error('[assignDriverToOrder] Unexpected error:', error);
    return null;
  }
}

/**
 * Updates the status of a driver assignment
 * @param assignmentId The ID of the assignment to update
 * @param status The new status
 * @returns The updated assignment or null if there was an error
 */
export async function updateAssignmentStatus(
  assignmentId: string,
  status: string
) {
  try {
    console.log(`[updateAssignmentStatus] Updating assignment ${assignmentId} to status ${status}`);

    const updates: any = {
      status
    };

    // If status is completed, add completed_at timestamp
    if (status === 'completed') {
      updates.completed_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('driver_assignments')
      .update(updates)
      .eq('id', assignmentId)
      .select()
      .single();

    if (error) {
      console.error('[updateAssignmentStatus] Error updating assignment status:', error);
      return null;
    }

    console.log('[updateAssignmentStatus] Successfully updated assignment:', data);
    return data;
  } catch (error) {
    console.error('[updateAssignmentStatus] Unexpected error:', error);
    return null;
  }
}
