import { supabase } from '@/services/supabase';

/**
 * Fetches driver details for a specific order
 * @param orderId The ID of the order
 * @returns Driver details or null if no driver is assigned
 */
export async function getDriverForOrder(orderId: string) {
  if (!orderId) return null;
  
  console.log(`[OrderService] Fetching driver for order: ${orderId}`);
  
  let driverDetails = null;
  try {
    // Step 1: Fetch the driver assignment for the order
    const { data: driverAssignments, error: assignmentError } = await supabase
      .from('driver_assignments')
      .select('driver_id, vehicle_id')
      .eq('order_id', orderId)
      .maybeSingle();

    if (assignmentError) {
      console.error('[OrderService] Error fetching driver assignment:', assignmentError);
      throw assignmentError;
    }

    if (driverAssignments) {
      console.log('[OrderService] Found driver assignment:', driverAssignments);
      
      // Step 2: Get the driver record to get the profile_id
      const { data: driverData, error: driverError } = await supabase
        .from('drivers')
        .select('*')
        .eq('id', driverAssignments.driver_id)
        .single();
      
      if (driverError) {
        console.error('[OrderService] Error fetching driver:', driverError);
        throw driverError;
      }
      
      console.log('[OrderService] Found driver data:', driverData);
      
      // Step 3: Get the profile data using the profile_id
      const { data: driverProfileData, error: driverProfileError } = await supabase
        .from('profiles') 
        .select('id, full_name, phone, avatar_url')
        .eq('id', driverData.profile_id) 
        .single();
           
      if (driverProfileError) {
        console.error('[OrderService] Error fetching driver profile:', driverProfileError);
        throw driverProfileError;
      }
      
      console.log('[OrderService] Found driver profile:', driverProfileData);

      // Step 4: Get the vehicle data if available
      let vehicleData = null;
      if (driverAssignments.vehicle_id) {
        const { data: vehicle, error: vehicleError } = await supabase
          .from('vehicles')
          .select('*')
          .eq('id', driverAssignments.vehicle_id)
          .single();

        if (!vehicleError) {
          vehicleData = vehicle;
          console.log('[OrderService] Found vehicle data:', vehicleData);
        } else {
          console.error('[OrderService] Error fetching vehicle:', vehicleError);
        }
      }
         
      // Step 5: Combine all the data
      return {
        ...driverData,
        profile: driverProfileData,
        vehicle: vehicleData,
        assignment: {
          driver_id: driverAssignments.driver_id,
          vehicle_id: driverAssignments.vehicle_id
        }
      };
    }
    
    return null;
  } catch (error) {
    console.error("[OrderService] Error fetching driver details:", error);
    return null;
  }
}
