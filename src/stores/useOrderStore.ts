import { create } from 'zustand';
import { Order, DumpsterSize } from './types';

interface OrderStore {
  orders: Order[];
  currentOrder: Order | null;
  selectedDumpster: DumpsterSize | null;
  setOrders: (orders: Order[]) => void;
  addOrder: (order: Order) => void;
  updateOrder: (order: Order) => void;
  setCurrentOrder: (order: Order | null) => void;
  setSelectedDumpster: (dumpster: DumpsterSize | null) => void;
  cancelOrder: (orderId: string) => void;
}

const useOrderStore = create<OrderStore>((set) => ({
  orders: [],
  currentOrder: null,
  selectedDumpster: null,

  setOrders: (orders) => set({ orders }),
  
  addOrder: (order) =>
    set((state) => ({
      orders: [...state.orders, order],
    })),
  
  updateOrder: (order) =>
    set((state) => ({
      orders: state.orders.map((o) => (o.id === order.id ? order : o)),
      currentOrder: state.currentOrder?.id === order.id ? order : state.currentOrder,
    })),
  
  setCurrentOrder: (order) => set({ currentOrder: order }),
  
  setSelectedDumpster: (dumpster) => set({ selectedDumpster: dumpster }),
  
  cancelOrder: (orderId) =>
    set((state) => ({
      orders: state.orders.map((order) =>
        order.id === orderId ? { ...order, status: 'cancelled' } : order
      ),
      currentOrder:
        state.currentOrder?.id === orderId
          ? { ...state.currentOrder, status: 'cancelled' }
          : state.currentOrder,
    })),
}));

export default useOrderStore; 