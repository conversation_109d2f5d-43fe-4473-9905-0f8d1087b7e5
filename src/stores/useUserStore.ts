import { create } from 'zustand';
import { User, Address } from './types';

interface UserStore {
  user: User | null;
  setUser: (user: User | null) => void;
  addAddress: (address: Address) => void;
  removeAddress: (addressId: string) => void;
  updateAddress: (address: Address) => void;
  setDefaultAddress: (addressId: string) => void;
  clearUser: () => void;
}

const useUserStore = create<UserStore>((set) => ({
  user: null,
  
  setUser: (user) => set({ user }),
  
  addAddress: (address) =>
    set((state) => ({
      user: state.user
        ? {
            ...state.user,
            addresses: [...state.user.addresses, address],
          }
        : null,
    })),
  
  removeAddress: (addressId) =>
    set((state) => ({
      user: state.user
        ? {
            ...state.user,
            addresses: state.user.addresses.filter((addr) => addr.id !== addressId),
          }
        : null,
    })),
  
  updateAddress: (address) =>
    set((state) => ({
      user: state.user
        ? {
            ...state.user,
            addresses: state.user.addresses.map((addr) =>
              addr.id === address.id ? address : addr
            ),
          }
        : null,
    })),
  
  setDefaultAddress: (addressId) =>
    set((state) => ({
      user: state.user
        ? {
            ...state.user,
            addresses: state.user.addresses.map((addr) => ({
              ...addr,
              isDefault: addr.id === addressId,
            })),
          }
        : null,
    })),
  
  clearUser: () => set({ user: null }),
}));

export default useUserStore; 