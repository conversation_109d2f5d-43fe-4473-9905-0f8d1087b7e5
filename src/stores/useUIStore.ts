import { create } from 'zustand';
import { UIState } from './types';

interface UIStore extends UIState {
  setTheme: (theme: 'light' | 'dark') => void;
  setLoading: (isLoading: boolean) => void;
  showToast: (message: string, type: 'success' | 'error' | 'info') => void;
  hideToast: () => void;
}

const useUIStore = create<UIStore>((set) => ({
  theme: 'light',
  isLoading: false,
  toast: undefined,

  setTheme: (theme) => set({ theme }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  showToast: (message, type) =>
    set({
      toast: {
        message,
        type,
      },
    }),
  
  hideToast: () => set({ toast: undefined }),
}));

export default useUIStore; 