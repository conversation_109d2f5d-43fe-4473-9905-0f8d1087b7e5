export interface User {
  id: string;
  phone: string;
  email?: string;
  name?: string;
  addresses: Address[];
}

export interface Address {
  id: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
}

export interface DumpsterSize {
  id: string;
  name: string;
  capacity: string;
  dimensions: string;
  price: number;
}

export interface Order {
  id: string;
  userId: string;
  dumpsterId: string;
  status: 'pending' | 'confirmed' | 'delivered' | 'completed' | 'cancelled';
  deliveryAddress: Address;
  deliveryDate: string;
  pickupDate: string;
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

export interface UIState {
  theme: 'light' | 'dark';
  isLoading: boolean;
  toast?: {
    message: string;
    type: 'success' | 'error' | 'info';
  };
} 