import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import { I18nManager, Platform, Alert, NativeModules, DevSettings } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Updates from 'expo-updates';
import en from './translations/en';
import ar from './translations/ar';


const LANGUAGE_KEY = '@app:language';
const RTL_APPLIED_KEY = '@app:rtl_applied';
const FORCE_RESTART_KEY = '@app:force_restart';
const RTL_RETRY_COUNT_KEY = '@app:rtl_retry_count';
const MAX_RTL_RETRIES = 1;

const resources = {
  en: {
    translation: en,
  },
  ar: {
    translation: ar,
  },
};

// Initialize i18next with default settings
i18next.use(initReactI18next).init({
  resources,
  lng: 'en', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
  // Add debug mode in development
  debug: __DEV__,
});

// Log initial i18next state
if (__DEV__) {
  console.log('i18next initialized with language:', i18next.language);
  console.log('Available languages:', Object.keys(resources));
  console.log('Current RTL state:', I18nManager.isRTL);
}

// Force RTL changes to be applied immediately
const forceRTL = async (enabled: boolean) => {
  console.log('Forcing RTL to:', enabled);
  
  try {
    // Apply RTL changes
    I18nManager.allowRTL(enabled);
    I18nManager.forceRTL(enabled);
    
    // On Android, we can use a more direct approach
    if (Platform.OS === 'android' && NativeModules.I18nManager) {
      try {
        NativeModules.I18nManager.allowRTL(enabled);
        NativeModules.I18nManager.forceRTL(enabled);
      } catch (androidError) {
        console.warn('Failed to apply RTL changes using NativeModules:', androidError);
      }
    }
    
    // Set a flag to force restart on next app load
    await AsyncStorage.setItem(FORCE_RESTART_KEY, 'true');
    
    // Check if RTL was actually applied
    if (I18nManager.isRTL !== enabled) {
      console.warn('RTL setting was not applied correctly!', {
        requested: enabled,
        actual: I18nManager.isRTL
      });
      
      // Mark that we need to apply RTL on next load
      await AsyncStorage.setItem(RTL_APPLIED_KEY, 'false');
      
      // On iOS, we sometimes need to "fake it" since RTL can be problematic
      if (Platform.OS === 'ios') {
        console.log('On iOS, proceeding despite RTL mismatch - retry mechanism will handle it');
        
        // Reset retry counter on language change - this is a new attempt
        // Only do this if it's a language change and not app start
        const retryCountStr = await AsyncStorage.getItem(RTL_RETRY_COUNT_KEY);
        if (!retryCountStr) {
          // No retries yet, this is likely a fresh language change
          await AsyncStorage.setItem(RTL_RETRY_COUNT_KEY, '0');
        }
        
        // Return true regardless of actual RTL state - let the retry limiting handle it
        return true;
      }
      
      return false;
    } else {
      console.log('RTL setting applied successfully:', I18nManager.isRTL);
      await AsyncStorage.setItem(RTL_APPLIED_KEY, 'true');
      
      // Clear retry counter since RTL applied correctly
      await AsyncStorage.removeItem(RTL_RETRY_COUNT_KEY);
      
      return true;
    }
  } catch (error) {
    console.error('Error applying RTL settings:', error);
    return false;
  }
};

// Initialize language settings before app starts
export const initializeLanguageSettings = async () => {
  try {
    console.log('Starting language initialization...');
    
    // Check if we need to force restart for RTL changes
    const forceRestart = await AsyncStorage.getItem(FORCE_RESTART_KEY);
    
    // Check if we're in a retry loop
    const retryCountStr = await AsyncStorage.getItem(RTL_RETRY_COUNT_KEY);
    const retryCount = retryCountStr ? parseInt(retryCountStr) : 0;
    
    if (forceRestart === 'true') {
      // Clear the force restart flag
      await AsyncStorage.setItem(FORCE_RESTART_KEY, 'false');
      
      // Check if we've hit the retry limit
      if (retryCount >= MAX_RTL_RETRIES) {
        console.log(`Reached maximum RTL reload attempts (${MAX_RTL_RETRIES}). Proceeding with current settings.`);
        // Clear retry counter
        await AsyncStorage.removeItem(RTL_RETRY_COUNT_KEY);
        // Continue without reloading again
        return false;
      }
      
      // In development, we can use DevSettings to reload
      if (__DEV__ && DevSettings && DevSettings.reload) {
        console.log('Forcing a reload to apply RTL changes...');
        // Increment retry counter
        await AsyncStorage.setItem(RTL_RETRY_COUNT_KEY, (retryCount + 1).toString());
        setTimeout(() => {
          DevSettings.reload();
        }, 100);
        return true;
      }
    }
    
    // Get saved language preference
    const savedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
    const currentLanguage = savedLanguage || 'en';
    
    console.log('Initializing with language:', currentLanguage);
    
    // Determine if RTL should be enabled
    const shouldBeRTL = currentLanguage === 'ar';
    const currentRTLState = I18nManager.isRTL;
    
    // Check if we've already applied RTL for this language
    const rtlApplied = await AsyncStorage.getItem(RTL_APPLIED_KEY);
    const isRTLApplied = rtlApplied === 'true';

    console.log('Language initialization data:', { 
      currentLanguage, 
      shouldBeRTL, 
      currentRTLState,
      isRTL: I18nManager.isRTL,
      isRTLApplied
    });

    // Update i18next language
    await i18next.changeLanguage(currentLanguage);
    console.log('i18next language set to:', i18next.language);
    
    // Force reload resources to ensure translations are loaded
    if (currentLanguage === 'ar' && i18next.language !== 'ar') {
      console.log('Forcing reload of Arabic resources');
      await i18next.reloadResources(['ar']);
      await i18next.changeLanguage('ar');
      console.log('i18next language after forced reload:', i18next.language);
    }
    
    // Check if RTL state needs to be updated
    if (shouldBeRTL !== currentRTLState) {
      console.log('RTL state needs to change:', { shouldBeRTL, currentRTL: I18nManager.isRTL });
      
      // Try to force RTL settings with our enhanced method
      const rtlApplied = await forceRTL(shouldBeRTL);
      
      if (!rtlApplied) {
        // Mark that RTL needs to be applied on next reload
        await AsyncStorage.setItem(RTL_APPLIED_KEY, 'false');
        
        // For initial app load, we need to reload to apply RTL correctly
        
        return true;
      } else {
        // Mark that RTL has been applied
        await AsyncStorage.setItem(RTL_APPLIED_KEY, 'true');
        
        // No need to reload again
        
        return false;
      }
    } else if (shouldBeRTL && !isRTLApplied) {
      // If RTL should be enabled but hasn't been fully applied yet
      console.log('RTL needs to be reapplied');
      
      // Force RTL settings to be applied again with our enhanced method
      const rtlApplied = await forceRTL(true);
      
      // Mark that RTL has been applied if successful
      if (rtlApplied) {
        await AsyncStorage.setItem(RTL_APPLIED_KEY, 'true');
      }
      
      // No need to reload again
      
      return false;
    }

    console.log('No RTL changes needed, continuing with current settings');
   
    return false; // No reload needed
  } catch (error) {
    console.error('Failed to initialize language settings:', error);
    return false;
  }
};

// Change language and handle RTL changes
export const changeLanguage = async (language: string) => {
  try {
    console.log('Changing language to:', language);
    
    
    // Determine if RTL should be enabled for the new language
    const isNewLanguageRTL = language === 'ar';
    const currentRTLState = I18nManager.isRTL;

    console.log('Language change details:', { 
      language, 
      isNewLanguageRTL, 
      currentRTLState,
      currentI18nRTL: I18nManager.isRTL
    });

    // Save the new language setting
    await AsyncStorage.setItem(LANGUAGE_KEY, language);
    
    // Update the language in i18next
    await i18next.changeLanguage(language);
    console.log('i18next language updated to:', i18next.language);
    
    // Force reload resources to ensure translations are loaded
    if (language === 'ar' && i18next.language !== 'ar') {
      console.log('Forcing reload of Arabic resources');
      await i18next.reloadResources(['ar']);
      await i18next.changeLanguage('ar');
      console.log('i18next language after forced reload:', i18next.language);
    }
    
    // Check if RTL changes are needed
    if (isNewLanguageRTL !== currentRTLState) {
      console.log('RTL change needed, updating settings...');
      
      // In development mode, we'll defer the RTL changes until after the modal is shown
      if (__DEV__) {
        // Just mark that RTL changes are needed, but don't apply them yet
        await AsyncStorage.setItem(RTL_APPLIED_KEY, 'false');
        await AsyncStorage.setItem(FORCE_RESTART_KEY, 'true');
        
        // Log RTL state before returning
        
        
        // Return true to indicate that a reload is needed
        // This will trigger the modal to be shown
        return true;
      } else {
        // In production, apply RTL changes immediately
        const rtlApplied = await forceRTL(isNewLanguageRTL);
        
        // Reset the RTL applied flag
        await AsyncStorage.setItem(RTL_APPLIED_KEY, rtlApplied ? 'true' : 'false');
        
        console.log('RTL settings updated:', {
          requested: isNewLanguageRTL,
          actual: I18nManager.isRTL,
          applied: rtlApplied
        });
        
        // In production, reload the app automatically
        try {
          await Updates.reloadAsync();
        } catch (error) {
          console.error('Failed to reload app:', error);
        }
        
        
        return true;
      }
    } else {
      console.log('No RTL change needed, language updated successfully');
        
      return false;
    }
  } catch (error) {
    console.error('Failed to change language:', error);
    
    throw error;
  }
};

export default i18next;