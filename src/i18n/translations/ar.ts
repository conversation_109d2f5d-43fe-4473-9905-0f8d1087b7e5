export default {
  common: {
    back: 'رجوع',
    cancel: 'إلغاء',
    save: 'حفظ',
    delete: 'حذف',
    edit: 'تعديل',
    loading: 'جاري التحميل...',
    error: 'حدث خطأ',
    retry: 'إعادة المحاولة',
    submit: 'إرسال',
    continue: 'متابعة',
    next: 'التالي',
    previous: 'السابق',
    done: 'تم',
    search: 'بحث',
    filter: 'تصفية',
    sort: 'ترتيب',
    notSet: 'غير محدد',
  },
  auth: {
    login: {
      title: 'تسجيل الدخول',
      phoneNumber: 'رقم الهاتف',
      password: 'كلمة المرور',
      forgotPassword: 'نسيت كلمة المرور؟',
      signIn: 'تسجيل الدخول',
      orContinueWith: 'أو المتابعة باستخدام',
      googleSignIn: 'تسجيل الدخول عبر جوجل',
      noAccount: 'ليس لديك حساب؟',
      createAccount: 'إنشاء حساب',
      emailDescription: 'أدخل بريدك الإلكتروني وكلمة المرور للمتابعة',
      phoneDescription: 'أدخل رقم هاتفك للمتابعة',
      emailMethod: 'البريد الإلكتروني',
      phoneMethod: 'الهاتف',
      emailPlaceholder: 'بريدك الإلكتروني',
      passwordPlaceholder: 'كلمة المرور',
      withPhone: 'برقم الهاتف',
      withEmail: 'بالبريد الإلكتروني',
      errors: {
        phoneRequired: 'الرجاء إدخال رقم الهاتف',
        emailPasswordRequired: 'الرجاء إدخال البريد الإلكتروني وكلمة المرور',
        failed: 'فشل تسجيل الدخول. الرجاء المحاولة مرة أخرى'
      }
    },
    register: {
      title: 'إنشاء حساب',
      phoneDescription: 'أدخل رقم هاتفك للمتابعة',
      emailDescription: 'أدخل بريدك الإلكتروني وكلمة المرور للمتابعة',
      name: 'الاسم الكامل',
      phoneNumber: 'رقم الهاتف',
      password: 'كلمة المرور',
      confirmPassword: 'تأكيد كلمة المرور',
      agreeToTerms: 'أوافق على الشروط والأحكام',
      createAccount: 'إنشاء حساب',
      haveAccount: 'لديك حساب بالفعل؟',
      signIn: 'تسجيل الدخول',
      phoneMethod: 'الهاتف',
      emailMethod: 'البريد الإلكتروني',
      emailPlaceholder: 'بريدك الإلكتروني',
      passwordPlaceholder: 'كلمة المرور',
      confirmPasswordPlaceholder: 'تأكيد كلمة المرور',
      enterOtp: 'أدخل رمز التحقق',
      verifyOtp: 'تحقق من الرمز',
      sendOtp: 'إرسال رمز التحقق',
      resendOtp: 'إعادة إرسال الرمز',
      resendOtpTimer: 'إعادة إرسال الرمز خلال {{seconds}} ثانية',
      otpSent: 'تم إرسال رمز التحقق بنجاح',
      success: 'تم التسجيل بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد حسابك.',
      errors: {
        otpFailed: 'فشل إرسال رمز التحقق',
        otpVerificationFailed: 'فشل التحقق من الرمز',
        otpVerificationNotImplemented: 'التحقق من الرمز غير متاح حال',
        passwordLength: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
        passwordMatch: 'كلمتا المرور غير متطابقتين',
        failed: 'فشل التسجيل. يرجى المحاولة مرة أخرى'
      }
    },
    forgotPassword: {
      title: 'نسيت كلمة المرور',
      description: 'أدخل رقم هاتفك لإعادة تعيين كلمة المرور',
      submit: 'إعادة تعيين كلمة المرور',
      backToLogin: 'العودة لتسجيل الدخول',
    },
    verification: {
      title: 'تأكيد رقم الهاتف',
      description: 'أدخل الرمز المرسل إلى هاتفك',
      resend: 'إعادة إرسال الرمز',
      verify: 'تأكيد',
    },
    terms: {
      description: 'بالمتابعة، أنت توافق على',
      terms: 'شروط الخدمة',
      and: 'و',
      privacy: 'سياسة الخصوصية'
    },
    countryPicker: {
      title: 'اختر الدولة',
      currentLocation: 'الموقع الحالي',
      searchPlaceholder: 'ابحث عن دولة',
    }
  },
  home: {
    title: 'الرئيسية',
    welcome: 'مرحباً',
    searchPlaceholder: 'ما نوع الحاوية التي تحتاجها؟',
    recentOrders: 'الطلبات الأخيرة',
    nearbyDumpsters: 'الحاويات القريبة',
    recommendations: 'مقترحات لك',
    viewAll: 'عرض الكل',
    greeting: {
      morning: 'صباح الخير',
      afternoon: 'مساء الخير',
      evening: 'مساء الخير',
      welcome: 'هلا و مرحبا'
    },
    inputPrompt: 'اضغط للكتابة • اضغط باستمرار للتحدث'
  },
  orders: {
    title: 'الطلبات',
    active: 'نشط',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    noOrders: 'لا توجد طلبات',
    processingOrder: 'جاري معالجة طلبك...',
    loadingDetails: 'جاري تحميل تفاصيل الطلب...',
    errorLoadingOrder: 'خطأ في تحميل الطلب',
    errorLoadingOrderDetails: 'حدث خطأ أثناء تحميل تفاصيل طلبك. يرجى المحاولة مرة أخرى.',
    currency: 'د.ك',
    status: {
      pending: 'قيد الانتظار',
      confirmed: 'مؤكد',
      inProgress: 'قيد التنفيذ',
      delivered: 'تم التوصيل',
      completed: 'مكتمل',
      cancelled: 'ملغي'
    },
    details: {
      title: 'تفاصيل الطلب',
      OrderStatus: 'حالة الطلب',
      orderId: 'رقم الطلب',
      status: 'الحالة',
      date: 'التاريخ',
      location: 'الموقع',
      dumpsterType: 'نوع الحاوية',
      duration: 'المدة',
      amount: 'المبلغ',
      paymentMethod: 'طريقة الدفع',
      PaymentStatus: 'حالة الدفع',
      PaymentMethod: 'طريقة الدفع',
      Total: 'المجموع',
      creditCard: 'بطاقة ائتمان',
      cash: 'الدفع عند التوصيل',
      bankTransfer: 'تحويل بنكي',
      pending: 'قيد الانتظار',
      partial: 'جزئي',
      paid: 'مدفوع',
      refunded: 'مرتجع',
      Discount: 'خصم',
      BasePrice: 'السعر الأساسي',
      PaymentDetails: 'تفاصيل الدفع',
      DriverDetails: 'تفاصيل السائق',
      PartnerDetails: 'تفاصيل الشريك',
      Instructions: 'تعليمات',
      DeliveryDate: 'تاريخ التوصيل',
      Address: 'العنوان',
      DeliveryDetails: 'تفاصيل التوصيل',
      DumpsterDetails: 'تفاصيل الحاوية',
      
    },
    loadingState: {
      title: 'جاري تحميل الطلبات',
      description: 'يرجى الانتظار بينما نجلب طلباتك'
    },
    emptyState: {
      title: 'لا توجد طلبات بعد',
      description: 'ستظهر طلباتك هنا بمجرد إنشائها'
    },
    backButton: 'رجوع',
    orderNumber: 'طلب رقم',
    deliveryAddress: 'عنوان التوصيل',
    deliveryDate: 'تاريخ التوصيل',
    totalAmount: 'المبلغ الإجمالي'
  },
  profile: {
    title: 'الملف الشخصي',
    personalInfo: 'المعلومات الشخصية',
    name: 'الاسم',
    phoneNumber: 'رقم الهاتف',
    email: 'البريد الإلكتروني',
    
    paymentMethods: 'طرق الدفع',
    orderHistory: 'سجل الطلبات',
    support: 'الدعم',
    logout: 'تسجيل الخروج',
    editName: 'تعديل الاسم',
    addresses:{
      title: 'عناويني',
      addNew: 'إضافة عنوان',
      addFirst: 'إضافة عنوانك الأول',
      tapAdd: 'اضغط لإضافة عنوان جديد',
      
    },
    payment: {
      title: 'طرق الدفع',
      protected: 'معلومات الدفع محمية',
      addCard: 'إضافة بطاقة جديدة',
    },
    help: {
      title: 'تحتاج المساعدة؟',
      contact: 'تواصل مع الدعم',
      rethink: 'تراجع البساطة',
      tired: 'متعب من عدد كبير من خدمات التوصيل والاستلام؟',
    },
  },
  addresses: {
    title: 'العناوين',
    addNew: 'إضافة عنوان جديد',
    edit: 'تعديل العنوان',
    delete: 'حذف العنوان',
    searchPlaceholder: 'ابحث عن عنوان على الخريطة',
    selectedLocation: 'العنوان المحدد',
    showSelectedLocation: 'عرض العنوان المحدد على الخريطة كعنوان موجز',
    name: 'تسمية العنوان',
    namePlaceholder: 'منزلي',
    type: 'نوع العنوان',
    home: 'المنزل',
    office: 'المكتب',
    other: 'آخر',
    setAsDefault: 'تعيين كعنوان افتراضي',
    save: 'حفظ العنوان',
    form: {
      label: 'تسمية العنوان',
      street: 'الشارع',
      building: 'المبنى/الشقة',
      city: 'المدينة',
      state: 'المنطقة',
      zipCode: 'الرمز البريدي',
      setAsDefault: 'تعيين كعنوان افتراضي',
    },
    types: {
      home: 'المنزل',
      work: 'العمل',
      other: 'آخر',
    },
  },
  dumpster: {
    types: {
      construction: 'بناء',
      household: 'منزلي',
      yard: 'نفايات الحدائق',
      commercial: 'تجاري',
    },
    sizes: {
      small: 'صغير (10 ياردة)',
      medium: 'متوسط (20 ياردة)',
      large: 'كبير (30 ياردة)',
      extraLarge: 'كبير (40 ياردة)',
    },
    filters: {
      title: 'التصفية',
      type: 'النوع',
      size: 'الحجم',
      availability: 'التوفر',
      price: 'نطاق السعر',
      distance: 'المسافة',
    },
    booking: {
      title: 'حجز حاوية',
      selectDate: 'اختر التاريخ',
      selectTime: 'اختر الوقت',
      duration: 'مدة الإيجار',
      location: 'موقع التوصيل',
      specialInstructions: 'تعليمات خاصة',
      summary: 'الملخص',
      total: 'المجموع',
      book: 'احجز الآن',
    },
  },
  settings: {
    title: 'الإعدادات',
    theme: {
      title: 'المظهر',
      label: 'مظهر التطبيق',
      light: 'المظهر الفاتح',
      dark: 'المظهر الداكن',
      system: 'تتبع مظهر النظام',
      chooseTheme: 'اختر المظهر',
    },
    language: {
      title: 'اللغة',
      label: 'لغة التطبيق',
      chooseLanguage: 'اختر اللغة',
      english: 'English',
      arabic: 'العربية',
      changing: 'جاري تغيير اللغة...',
      rtlChangeNotice: 'تم تغيير اللغة. قد تتطلب بعض تغييرات التخطيط إعادة تحميل للتأثير الكامل.',
      changeTitle: 'تم تغيير اللغة',
      changedToEnglish: 'تم تغيير اللغة إلى الإنجليزية.',
      changedToArabic: 'تم تغيير اللغة إلى العربية.',
      rtlChangeDescription: 'لتغيير اتجاه التخطيط بشكل صحيح، يحتاج التطبيق إلى إعادة التحميل. يمكنك إعادة التحميل الآن أو متابعة استخدام التطبيق مع بعض عدم اتساق التخطيط.',
      reloadNow: 'إعادة التحميل الآن',
      reloadLater: 'المتابعة بدون إعادة التحميل',
      manualReloadRequired: 'يرجى إغلاق التطبيق وإعادة فتحه لتطبيق تغييرات التخطيط بالكامل.',
      reloadError: 'فشل في إعادة تحميل التطبيق. يرجى إغلاقه وإعادة فتحه يدويًا.',
      appLanguage: 'لغة التطبيق',
      select: 'اختر اللغة',
    },
    designSystem: {
      title: 'النظام التصميمي',
      viewComponents: 'عرض المكونات',
      description: 'استكشف نظام التصميم',
    },
    notifications: {
      title: 'الإشعارات',
      push: 'إشعارات التنبيهات',
      email: 'إشعارات البريد الإلكتروني',
      sms: 'إشعارات الرسائل النصية',
      whatsapp: 'إشعارات واتساب',
      description: 'احصل على إشعارات عن طلباتك',
    },
    privacy: {
      title: 'الخصوصية',
      locationSharing: {
        title: 'مشاركة الموقع',
        description: 'السماح للتطبيق بالوصول إلى موقعك',
      },
      dataUsage: {
        title: 'استخدام البيانات',
        description: 'مشاركة بيانات الاستخدام لتحسين التطبيق',
      },
      marketing: {
        title: 'التسويق',
        description: 'تلقي رسائل تسويقية',
      },
    },
    defaults: {
      title: 'الإعدادات الافتراضية',
      address: {
        title: 'العنوان الافتراضي',
        notSet: 'غير محدد',
      },
      paymentMethod: {
        title: 'طريقة الدفع',
        notSet: 'غير محدد',
      },
      communicationChannel: {
        title: 'قناة التواصل',
        chooseChannel: 'قناة التواصل المفضلة',
      },
    },
    account: {
      title: 'الحساب',
      logout: 'تسجيل الخروج',
      logoutConfirmTitle: 'تسجيل الخروج',
      logoutConfirmMessage: 'هل أنت متأكد أنك تريد تسجيل الخروج؟',
    },
    signOut: 'تسجيل الخروج',
  },
  notifications: {
    title: 'الإشعارات',
    markAllRead: 'تحديد الكل كمقروء',
    clearAll: 'مسح الكل',
    types: {
      order: 'تحديثات الطلبات',
      promotion: 'العروض',
      system: 'النظام',
    },
    empty: 'لا توجد إشعارات',
  },
  payment: {
    title: 'الدفع',
    methods: {
      card: 'بطاقة ائتمان/خصم',
      cash: 'الدفع عند التوصيل',
      wallet: 'المحفظة',
    },
    addCard: {
      title: 'إضافة بطاقة',
      cardNumber: 'رقم البطاقة',
      expiryDate: 'تاريخ الانتهاء',
      cvv: 'رمز التحقق',
      nameOnCard: 'الاسم على البطاقة',
      saveCard: 'حفظ البطاقة للاستخدام المستقبلي',
    },
    wallet: {
      balance: 'رصيد المحفظة',
      addMoney: 'إضافة رصيد',
      transactions: 'المعاملات',
    },
  },
  dumpsterSelection: {
    title: 'اختر الحاوية المناسبة',
    initialPrompt: {
      title: 'أخبرنا عن مشروعك',
      description: 'صف ما تحتاج للتخلص منه، وسنوصي بأفضل حاوية تناسب احتياجاتك.',
      examplesTitle: 'جرب السؤال عن:',
      examples: {
        construction: 'أحتاج للتخلص من مخلفات البناء من تجديد الحمام',
        yard: 'لدي الكثير من مخلفات الحديقة من تنسيق المناظر الطبيعية',
        moving: 'أنا أنتقل وأحتاج للتخلص من الأثاث القديم',
        renovation: 'تجديد مطبخي، أحتاج للتخلص من الخزائن والأجهزة'
      }
    },
    input: {
      placeholder: 'صف ما تحتاج للتخلص منه...',
      voicePrompt: 'اضغط للتحدث',
      sending: 'جاري الإرسال...'
    },
    recommendations: {
      title: 'الحاويات الموصى بها',
      loading: 'جاري البحث عن أفضل الخيارات لك...',
      noResults: 'لم يتم العثور على حاويات مطابقة',
      matchScore: 'نسبة التطابق',
      whyThisMatch: 'لماذا هذا التطابق',
      viewDetails: 'عرض التفاصيل',
      bookNow: 'احجز الآن'
    },
    chat: {
      aiTyping: 'الذكاء الاصطناعي يكتب...',
      retryMessage: 'فشل إرسال الرسالة. اضغط لإعادة المحاولة',
      clearChat: 'مسح المحادثة'
    },
    showAll: 'عرض جميع الحاويات'
  },
}; 
