export default {
  common: {
    back: 'Back',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    loading: 'Loading...',
    error: 'An error occurred',
    retry: 'Retry',
    submit: 'Submit',
    continue: 'Continue',
    next: 'Next',
    previous: 'Previous',
    done: 'Done',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    notSet: 'Not set',
    goBackToHome: 'Go Back to Home',
  },
  auth: {
    login: {
      title: 'Login',
      phoneNumber: 'Phone Number',
      password: 'Password',
      forgotPassword: 'Forgot Password?',
      signIn: 'Sign In',
      orContinueWith: 'Or continue with',
      googleSignIn: 'Sign in with Google',
      noAccount: 'Don\'t have an account?',
      createAccount: 'Create Account',
      emailDescription: 'Enter your email and password to continue',
      phoneDescription: 'Enter your phone number to continue',
      emailMethod: 'Email',
      phoneMethod: 'Phone',
      emailPlaceholder: 'Your email',
      passwordPlaceholder: 'Your password',
      withPhone: 'with phone number',
      withEmail: 'with email',
      errors: {
        phoneRequired: 'Please enter your phone number',
        emailPasswordRequired: 'Please enter both email and password',
        failed: 'Login failed. Please try again.',
        nonCustomerAccount: 'This phone number is associated with a non-customer account. Only customers can log in to this app.',
        accessDenied: 'Access denied. Your account type doesn\'t have permission to use this app.'
      }
    },
    register: {
      title: 'Create Account',
      name: 'Full Name',
      phoneNumber: 'Phone Number',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      agreeToTerms: 'I agree to the Terms and Conditions',
      createAccount: 'Create Account',
      haveAccount: 'Already have an account?',
      signIn: 'Sign In',
      phoneMethod: 'Phone',
      emailMethod: 'Email',
      emailPlaceholder: 'Email',
      passwordPlaceholder: 'Password',
      confirmPasswordPlaceholder: 'Confirm Password',
      enterOtp: 'Enter OTP',
      verifyOtp: 'Verify OTP',
      sendOtp: 'Send OTP',
      resendOtp: 'Resend OTP',
      resendOtpTimer: 'Resend OTP in {{seconds}}s',
      otpSent: 'OTP sent successfully',
      success: 'Registration successful! Please check your email to verify your account.',
      errors: {
        otpFailed: 'Failed to send OTP',
        otpVerificationFailed: 'Failed to verify OTP',
        otpVerificationNotImplemented: 'OTP verification not implemented yet',
        passwordLength: 'Password must be at least 6 characters long',
        passwordMatch: 'Passwords do not match',
        failed: 'Failed to register'
      }
    },
    forgotPassword: {
      title: 'Forgot Password',
      description: 'Enter your phone number to reset your password',
      submit: 'Reset Password',
      backToLogin: 'Back to Login',
    },
    verification: {
      title: 'Verify Phone',
      description: 'Enter the code sent to your phone',
      resend: 'Resend Code',
      verify: 'Verify',
      newUser: {
        title: 'Welcome!',
        message: 'This is your first time logging in. Please complete your profile.'
      },
      incompleteProfile: {
        title: 'Complete Your Profile',
        message: 'Please complete your profile information.'
      },
      errors: {
        nonCustomerAccount: 'Your account type is {{type}}. Only customers can use this app.'
      }
    },
    terms: {
      description: 'By continuing, you agree to our',
      terms: 'Terms of Service',
      and: 'and',
      privacy: 'Privacy Policy'
    },
    countryPicker: {
      title: 'Select a Country',
      currentLocation: 'Current Location',
      searchPlaceholder: 'Search for a country',
    }
  },
  home: {
    title: 'Home',
    welcome: 'Welcome',
    searchPlaceholder: 'What type of dumpster do you need?',
    recentOrders: 'Recent Orders',
    nearbyDumpsters: 'Nearby Dumpsters',
    recommendations: 'Recommended for You',
    viewAll: 'View All',
    greeting: {
      morning: 'Good morning',
      afternoon: 'Good afternoon',
      evening: 'Good evening',
      welcome: 'Welcome'
    },
    inputPrompt: 'Tap to type • Hold to speak'
  },
  orders: {
    title: 'Orders',
    active: 'Active',
    completed: 'Completed',
    cancelled: 'Cancelled',
    noOrders: 'No orders found',
    processingOrder: 'Processing your order...',
    loadingDetails: 'Loading order details...',
    errorLoadingOrder: 'Error Loading Order',
    errorLoadingOrderDetails: 'There was an error loading your order details. Please try again.',
    currency: '$',
    status: {
      pending: 'Pending',
      confirmed: 'Confirmed',
      inProgress: 'In Progress',
      delivered: 'Delivered',
      completed: 'Completed',
      cancelled: 'Cancelled'
    },
    details: {
      title: 'Order Details',
      OrderStatus: 'Order Status',
      orderId: 'Order ID',
      status: 'Status',
      date: 'Date',
      location: 'Location',
      dumpsterType: 'Dumpster Type',
      duration: 'Duration',
      amount: 'Amount',
      paymentMethod: 'Payment Method',
      PaymentStatus: 'Payment Status',
      PaymentMethod: 'Payment Method',
      Total: 'Total',
      creditCard: 'Credit Card',
      cash: 'Cash',
      bankTransfer: 'Bank Transfer',
      pending: 'Pending',
      partial: 'Partial',
      paid: 'Paid',
      refunded: 'Refunded',
      Discount: 'Discount',
      BasePrice: 'Base Price',
      PaymentDetails: 'Payment Details',
      DriverDetails: 'Driver Details',
      PartnerDetails: 'Partner Details',
      Instructions: 'Instructions',
      DeliveryDate: 'Delivery Date',
      Address: 'Address',
      DeliveryDetails: 'Delivery Details',
      DumpsterDetails: 'Dumpster Details',
    },
    loadingState: {
      title: 'Loading your orders',
      description: 'Please wait while we fetch your orders'
    },
    emptyState: {
      title: 'No orders yet',
      description: 'Your orders will appear here once you place them'
    },
    backButton: 'Back',
    orderNumber: 'Order',
    deliveryAddress: 'Delivery Address',
    deliveryDate: 'Delivery Date',
    totalAmount: 'Total Amount'
  },
  profile: {
    title: 'Profile',
    personalInfo: 'Personal Information',
    name: 'Name',
    phoneNumber: 'Phone Number',
    email: 'Email',
    
    paymentMethods: 'Payment Methods',
    orderHistory: 'Order History',
    support: 'Support',
    logout: 'Logout',
    editName: 'Edit Name',
    addresses:{
      title: 'My addresses',
      addNew: 'Add New Address',
      addFirst: 'Add your first address',
      tapAdd: 'Tap to add an address',
      
    },
    payment: {
      title: 'My payment methods',
      protected: 'Payment information is protected',
      addCard: 'Add new credit card',
    },
    help: {
      title: 'Need help?',
      contact: 'Contact Support',
      rethink: 'Rethink simplicity',
      tired: 'Tired of too many different delivery and pickup services?',
      
    },
    create: {
      title: 'Complete Your Profile',
      subtitle: 'Please provide the following information to complete your profile'
    },
    fields: {
      fullName: 'Full Name',
      phone: 'Phone Number',
      email: 'Email Address'
    },
    placeholders: {
      fullName: 'Enter your full name',
      phone: 'Enter your phone number',
      email: 'Enter your email address'
    },
    hints: {
      phoneReadOnly: 'Phone number cannot be changed'
    },
    actions: {
      save: 'Save Profile',
      cancel: 'Cancel'
    },
    success: {
      title: 'Profile Updated',
      message: 'Your profile has been updated successfully.'
    },
    errors: {
      nameRequired: 'Please enter your full name',
      notLoggedIn: 'You must be logged in to save your profile',
      saveFailed: 'Failed to save profile. Please try again.'
    }
  },
  addresses: {
    title: 'Addresses',
    addNew: 'Add New Address',
    edit: 'Edit Address',
    delete: 'Delete Address',
    searchPlaceholder: 'Search the map',
    selectedLocation: 'Selected Location', 
    showSelectedLocation: 'Show the selected location on the map as long address',
    name: 'Address Label',
    namePlaceholder: 'My Home',
    type: 'Address Type',
    home: 'Home',
    office: 'Office',
    other: 'Other',
    setAsDefault: 'Set as Default Address',
    save: 'Save Address',
    form: {
      label: 'Address Label',
      street: 'Street Address',
      building: 'Building/Apartment',
      city: 'City',
      state: 'State',
      zipCode: 'ZIP Code',
      setAsDefault: 'Set as Default Address',
      
    },
    types: {
      home: 'Home',
      work: 'Work',
      other: 'Other',
    },
  },
  dumpster: {
    types: {
      construction: 'Construction',
      household: 'Household',
      yard: 'Yard Waste',
      commercial: 'Commercial',
    },
    sizes: {
      small: 'Small (10 yard)',
      medium: 'Medium (20 yard)',
      large: 'Large (30 yard)',
      extraLarge: 'Extra Large (40 yard)',
    },
    filters: {
      title: 'Filters',
      type: 'Type',
      size: 'Size',
      availability: 'Availability',
      price: 'Price Range',
      distance: 'Distance',
    },
    booking: {
      title: 'Book Dumpster',
      selectDate: 'Select Date',
      selectTime: 'Select Time',
      duration: 'Rental Duration',
      location: 'Delivery Location',
      specialInstructions: 'Special Instructions',
      summary: 'Summary',
      total: 'Total',
      book: 'Book Now',
    },
  },
  settings: {
    title: 'Settings',
    theme: {
      title: 'Theme',
      label: 'App Theme',
      light: 'Light theme',
      dark: 'Dark theme',
      system: 'Follow system appearance',
      chooseTheme: 'Choose Theme',
    },
    language: {
      title: 'Language',
      label: 'App Language',
      chooseLanguage: 'Choose Language',
      english: 'English',
      arabic: 'العربية',
      changing: 'Changing language...',
      rtlChangeNotice: 'Language changed. Some layout changes may require a reload for full effect.',
      changeTitle: 'Language Changed',
      changedToEnglish: 'Language has been changed to English.',
      changedToArabic: 'Language has been changed to Arabic.',
      rtlChangeDescription: 'For the layout direction to change properly, the app needs to reload. You can reload now or continue using the app with some layout inconsistencies.',
      reloadNow: 'Reload Now',
      reloadLater: 'Continue Without Reloading',
      manualReloadRequired: 'Please close and reopen the app to fully apply the layout changes.',
      reloadError: 'Failed to reload the app. Please close and reopen it manually.',
      appLanguage: 'App Language',
      select: 'Select Language',
    },
    designSystem: {
      title: 'Design System',
      viewComponents: 'View Components',
      description: 'Explore our design system',
    },
    notifications: {
      title: 'Notifications',
      push: 'Push Notifications',
      email: 'Email Notifications',
      sms: 'SMS Notifications',
      whatsapp: 'WhatsApp Notifications',
      description: 'Get notifications about your orders',
    },
    privacy: {
      title: 'Privacy',
      locationSharing: {
        title: 'Location Sharing',
        description: 'Allow app to access your location',
      },
      dataUsage: {
        title: 'Data Usage',
        description: 'Share usage data to improve the app',
      },
      marketing: {
        title: 'Marketing',
        description: 'Receive marketing communications',
      },
    },
    defaults: {
      title: 'Default Settings',
      address: {
        title: 'Default Address',
        notSet: 'Not set',
      },
      paymentMethod: {
        title: 'Payment Method',
        notSet: 'Not set',
      },
      communicationChannel: {
        title: 'Communication Channel',
        chooseChannel: 'Preferred Communication Channel',
      },
    },
    account: {
      title: 'Account',
      logout: 'Sign Out',
      logoutConfirmTitle: 'Sign Out',
      logoutConfirmMessage: 'Are you sure you want to sign out?',
    },
    signOut: 'Sign Out',
  },
  notifications: {
    title: 'Notifications',
    markAllRead: 'Mark All as Read',
    clearAll: 'Clear All',
    types: {
      order: 'Order Updates',
      promotion: 'Promotions',
      system: 'System',
    },
    empty: 'No notifications',
  },
  payment: {
    title: 'Payment',
    methods: {
      card: 'Credit/Debit Card',
      cash: 'Cash on Delivery',
      wallet: 'Wallet',
    },
    addCard: {
      title: 'Add Card',
      cardNumber: 'Card Number',
      expiryDate: 'Expiry Date',
      cvv: 'CVV',
      nameOnCard: 'Name on Card',
      saveCard: 'Save Card for Future Use',
    },
    wallet: {
      balance: 'Wallet Balance',
      addMoney: 'Add Money',
      transactions: 'Transactions',
    },
  },
  dumpsterSelection: {
    title: 'Find the Right Dumpster',
    initialPrompt: {
      title: 'Tell me about your project',
      description: "Describe what you need to dispose of, and I'll recommend the best dumpster for your needs.",
      examplesTitle: 'Try asking about:',
      examples: {
        construction: 'I need to dispose of construction debris from a bathroom remodel',
        yard: 'I have a lot of yard waste from landscaping',
        moving: "I'm moving and need to get rid of old furniture",
        renovation: 'Renovating my kitchen, need to dispose of cabinets and appliances'
      }
    },
    input: {
      placeholder: 'Describe what you need to dispose of...',
      voicePrompt: 'Hold to speak',
      sending: 'Sending...'
    },
    recommendations: {
      title: 'Recommended Dumpsters',
      loading: 'Finding the best options for you...',
      noResults: 'No matching dumpsters found',
      matchScore: 'Match Score',
      whyThisMatch: 'Why this matches',
      viewDetails: 'View Details',
      bookNow: 'Book Now'
    },
    chat: {
      aiTyping: 'AI is typing...',
      retryMessage: 'Failed to send message. Tap to retry',
      clearChat: 'Clear Chat'
    },
    showAll: 'Show All Dumpsters'
  },
  access: {
    denied: {
      title: 'Access Denied',
      roleMessage: 'Your account type ({{role}}) does not have access to this app.',
      explanation: 'This app is specifically designed for customers only. If you believe this is an error, please contact support.'
    }
  },
}; 
