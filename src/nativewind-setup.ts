import { styled } from "nativewind";
import { View as RNView, Text as RNText, TouchableOpacity as RNTouchableOpacity, ScrollView as RNScrollView, TextInput as RNTextInput } from 'react-native';

export const View = styled(RNView);
export const Text = styled(RNText);
export const TouchableOpacity = styled(RNTouchableOpacity);
export const ScrollView = styled(RNScrollView);
export const TextInput = styled(RNTextInput);

// Add more styled components as needed 