# React Native Paper Theme Colors Guide

## How to Access Theme Colors

React Native Paper provides a comprehensive theming system with a wide range of colors. Here's how to use them in your components:

### 1. Import the useTheme hook

```typescript
import { useTheme } from 'react-native-paper';
```

### 2. Use the hook in your component

```typescript
const MyComponent = () => {
  const theme = useTheme();
  
  return (
    <View style={{ backgroundColor: theme.colors.surface }}>
      <Text style={{ color: theme.colors.onSurface }}>
        This text uses theme colors
      </Text>
    </View>
  );
};
```

## Available Colors

React Native Paper v3 (MD3) provides these color categories:

### Basic Colors
- `primary` - Primary brand color
- `onPrimary` - Text/icons on primary color
- `primaryContainer` - Container with primary color
- `onPrimaryContainer` - Text/icons on primary container

- `secondary` - Secondary brand color
- `onSecondary` - Text/icons on secondary color
- `secondaryContainer` - Container with secondary color
- `onSecondaryContainer` - Text/icons on secondary container

- `tertiary` - Tertiary brand color
- `onTertiary` - Text/icons on tertiary color
- `tertiaryContainer` - Container with tertiary color
- `onTertiaryContainer` - Text/icons on tertiary container

### Surface Colors
- `surface` - Surface color for cards, sheets, etc.
- `onSurface` - Text/icons on surface
- `surfaceVariant` - Alternative surface color
- `onSurfaceVariant` - Text/icons on surface variant
- `surfaceDisabled` - Disabled surface color
- `onSurfaceDisabled` - Text/icons on disabled surface

### Background
- `background` - Background color
- `onBackground` - Text/icons on background

### Error Colors
- `error` - Error color
- `onError` - Text/icons on error color
- `errorContainer` - Container with error color
- `onErrorContainer` - Text/icons on error container

### Other Colors
- `outline` - Outline color
- `outlineVariant` - Alternative outline color
- `shadow` - Shadow color
- `scrim` - Scrim color for modals
- `backdrop` - Backdrop color
- `inverseSurface` - Inverse of surface color
- `inverseOnSurface` - Text/icons on inverse surface
- `inversePrimary` - Inverse of primary color

### Elevation Colors
- `elevation.level0` - No elevation
- `elevation.level1` - Low elevation
- `elevation.level2` - Medium-low elevation
- `elevation.level3` - Medium elevation
- `elevation.level4` - Medium-high elevation
- `elevation.level5` - High elevation

## Common Usage Patterns

### Text on Background
```typescript
<Text style={{ color: theme.colors.onBackground }}>
  Text on background
</Text>
```

### Cards/Surfaces
```typescript
<View style={{ 
  backgroundColor: theme.colors.surface,
  borderColor: theme.colors.outline,
  borderWidth: 1,
}}>
  <Text style={{ color: theme.colors.onSurface }}>
    Card content
  </Text>
</View>
```

### Buttons
```typescript
<TouchableOpacity
  style={{
    backgroundColor: theme.colors.primary,
    padding: 12,
    borderRadius: 8,
  }}
>
  <Text style={{ color: theme.colors.onPrimary }}>
    Button Text
  </Text>
</TouchableOpacity>
```

### Status Bar
```typescript
<StatusBar 
  barStyle={theme.dark ? 'light-content' : 'dark-content'}
  backgroundColor="transparent"
  translucent
/>
```

## Theme-Aware Components

Always pair background colors with their corresponding text colors:
- Use `onPrimary` for text on `primary` backgrounds
- Use `onSurface` for text on `surface` backgrounds
- Use `onBackground` for text on `background`

This ensures proper contrast and readability in both light and dark themes. 