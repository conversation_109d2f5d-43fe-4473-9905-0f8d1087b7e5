import React from 'react';
import { View, Platform, StatusBar, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface AndroidSafeAreaViewProps {
  children: React.ReactNode;
  style?: ViewStyle | ViewStyle[];
  edges?: ('top' | 'bottom' | 'left' | 'right')[];
}

/**
 * AndroidSafeAreaView - A wrapper component that provides proper safe area handling for Android
 * This component addresses the Android-specific issue where headers don't properly respect safe area insets
 */
export const AndroidSafeAreaView: React.FC<AndroidSafeAreaViewProps> = ({ 
  children, 
  style,
  edges = ['top', 'bottom', 'left', 'right']
}) => {
  if (Platform.OS === 'android') {
    // For Android, we need to manually handle the status bar height
    const statusBarHeight = StatusBar.currentHeight || 0;
    
    return (
      <SafeAreaView 
        style={[
          { flex: 1 },
          style
        ]}
        edges={edges}
      >
        <View style={{ paddingTop: statusBarHeight, flex: 1 }}>
          {children}
        </View>
      </SafeAreaView>
    );
  }
  
  // For iOS, use the standard SafeAreaView
  return (
    <SafeAreaView style={[{ flex: 1 }, style]} edges={edges}>
      {children}
    </SafeAreaView>
  );
};

export default AndroidSafeAreaView;
