import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle, TextStyle, StyleProp } from 'react-native';
import { Text } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useAppTheme } from '@/hooks/useAppTheme';
import { NewRTLView, NewRTLText } from '@/components/rtl/new-index';
import { brandColors, textColors } from '@/theme/colors';
export type ButtonVariant = 'gradient' | 'light' | 'solid';
export type ButtonState = 'default' | 'hover' | 'active' | 'disabled';

type ButtonProps = {
  variant?: ButtonVariant;
  onPress?: () => void;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  children: React.ReactNode;
  state?: ButtonState;
};

export default function Button({
  variant = 'solid',
  onPress,
  disabled = false,
  style,
  textStyle,
  children,
  state = 'default',
}: ButtonProps) {
  const theme = useAppTheme();
  const currentState = disabled ? 'disabled' : state;
  
  const getButtonStyle = () => {
    const baseStyle: ViewStyle = {
      backgroundColor: theme.getButtonStyle(variant, currentState, theme.isDarkMode ? 'dark' : 'light'),
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 24,
      alignItems: 'center',
      justifyContent: 'center',
    };

    if (variant === 'gradient') {
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
      };
    }

    return baseStyle;
  };

  const renderContent = () => {
    // If children is a string, wrap it in NewRTLText
    if (typeof children === 'string') {
      const textStyles = [
        styles.text,
        { color: variant === 'light' ? brandColors.primary[700] : textColors.dark } as TextStyle,
      ] as TextStyle[];
      
      if (disabled) {
        textStyles.push({ opacity: 0.5, color: textColors.placeholder } as unknown as TextStyle);
      }
      
      if (textStyle) {
        textStyles.push(textStyle as TextStyle);
      }

      return (
        <NewRTLText style={textStyles}>
          {children}
        </NewRTLText>
      );
    }

    // If children is not a string (e.g., an icon), wrap it in NewRTLView
    return (
      <NewRTLView style={styles.contentContainer}>
        {children}
      </NewRTLView>
    );
  };

  if (variant === 'gradient') {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        style={[styles.container, style]}
      >
        <LinearGradient
          colors={[
            theme.getButtonStyle(variant, currentState, theme.isDarkMode ? 'dark' : 'light'),
            theme.getButtonStyle(variant, 'hover', theme.isDarkMode ? 'dark' : 'light'),
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1.5 }}
          style={[getButtonStyle(), styles.gradient]}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[styles.container, getButtonStyle(), style]}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  gradient: {
    borderRadius: 8,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  disabledText: {
    opacity: 0.5,
  },
}); 