import React from 'react';
import { StatusBar, Platform } from 'react-native';
import { useTheme } from '@/context/ThemeContext';

/**
 * StatusBarManager component that handles the status bar appearance based on the current theme
 * This component should be included at the root level of your app to ensure the status bar
 * is properly styled throughout the application
 */
export default function StatusBarManager() {
  const { theme, isDarkMode } = useTheme();
  
  // Determine status bar style based on theme
  // For iOS, we use 'dark-content' for light theme and 'light-content' for dark theme
  // This controls the color of the status bar text and icons (clock, battery, etc.)
  const barStyle = isDarkMode ? 'light-content' : 'dark-content';
  
  // For Android, we can also set the background color of the status bar
  // This is handled via the translucent and backgroundColor props
  const translucent = Platform.OS === 'android';
  
  return (
    <StatusBar 
      barStyle={barStyle}
      translucent={translucent}
      backgroundColor="transparent"
    />
  );
} 