import React from 'react';
import { StyleSheet, TouchableOpacity, View, FlexAlignType } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { NewRTLView, NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import { RadioButton } from 'react-native-paper';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

import { brandColors, surfaceColors, textColors, backgroundColors } from '@/theme/colors';
import ThemeDebug from './ThemeDebug';

type ThemeType = 'light' | 'dark' | 'system';
type IconName = 'sun' | 'moon' | 'monitor';

interface ThemeOptionProps {
  title: string;
  description?: string;
  icon: IconName;
  isSelected: boolean;
  onSelect: () => void;
  showDivider?: boolean;
}

const ThemeOption = ({ 
  title, 
  description, 
  icon, 
  isSelected, 
  onSelect,
  showDivider = true 
}: ThemeOptionProps) => {
  const { isRTL } = useRTLContext();
  const { isDarkMode } = useTheme();
  
  return (
    <>
      <TouchableOpacity
        style={[
          styles.themeOption,
          { backgroundColor: isDarkMode ? surfaceColors.container.dark : surfaceColors.container.light }
        ]}
        onPress={onSelect}
      >
        <NewRTLView 
          style={[
            styles.optionContent,
            { flexDirection: isRTL ? 'row-reverse' : 'row' }
          ]}
        >
          <Feather 
            name={icon} 
            size={22} 
            color={isDarkMode ? textColors.dark : textColors.light}
          />
          <NewRTLText 
            style={[
              styles.optionTitle,
              { 
                color: isDarkMode ? textColors.dark : textColors.light,
                textAlign: isRTL ? 'right' : 'left'
              }
            ]}
          >
            {title}
          </NewRTLText>
          <RadioButton 
            value={title}
            status={isSelected ? 'checked' : 'unchecked'}
            onPress={onSelect}
            color={brandColors.primary.DEFAULT}
          />
        </NewRTLView>
      </TouchableOpacity>
      {showDivider && (
        <View 
          style={[
            styles.divider,
            { backgroundColor: isDarkMode ? backgroundColors.outline.dark : backgroundColors.outline.light }
          ]} 
        />
      )}
    </>
  );
};

export default function ThemeSelector() {
  const { t } = useTranslation();
  const { theme, setTheme, isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  return (
    <>
      <Animated.View 
        entering={FadeIn}
        exiting={FadeOut}
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? surfaceColors.dark : surfaceColors.light }
        ]}
      >
        <ThemeOption
          title={t('settings.theme.system', 'تتبع مظهر النظام')}
          icon="monitor"
          isSelected={theme === 'system'}
          onSelect={() => setTheme('system')}
        />
        <ThemeOption
          title={t('settings.theme.light', 'المظهر الفاتح')}
          icon="sun"
          isSelected={theme === 'light'}
          onSelect={() => setTheme('light')}
        />
        <ThemeOption
          title={t('settings.theme.dark', 'المظهر الداكن')}
          icon="moon"
          isSelected={theme === 'dark'}
          onSelect={() => setTheme('dark')}
          showDivider={false}
        />
      </Animated.View>
      
      {/* Add the debug component for testing */}
      {/* {__DEV__ && <ThemeDebug />} */}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    
  },
  themeOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    
  },
  optionContent: {
    flex: 1,
    alignItems: 'center' as FlexAlignType,
  },
  optionTitle: {
    flex: 1,
    fontSize: 17,
    fontWeight: '400',
    marginHorizontal: 12,
  },
  divider: {
    opacity: 0.3,
    borderBottomWidth: StyleSheet.hairlineWidth,
  }
}); 