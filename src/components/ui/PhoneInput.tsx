import React from 'react';
import { View, TextInput, Text, StyleSheet } from 'react-native';

interface PhoneInputProps {
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  placeholder?: string;
}

export function PhoneInput({ value, onChangeText, error, placeholder }: PhoneInputProps) {
  return (
    <View style={styles.container}>
      <View style={[styles.inputContainer, error && styles.inputError]}>
        <Text style={styles.prefix}>+966</Text>
        <TextInput
          style={styles.input}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          keyboardType="phone-pad"
          maxLength={9}
        />
      </View>
      {error ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : null}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%'
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    height: 50
  },
  inputError: {
    borderColor: '#dc3545'
  },
  prefix: {
    paddingHorizontal: 15,
    color: '#666',
    fontSize: 16,
    fontWeight: '500'
  },
  input: {
    flex: 1,
    height: '100%',
    fontSize: 16,
    color: '#333',
    paddingRight: 15
  },
  errorText: {
    color: '#dc3545',
    fontSize: 14,
    marginTop: 5
  }
}); 