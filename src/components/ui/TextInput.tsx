import { StyleSheet } from 'react-native';
import { TextInput as PaperTextInput, TextInputProps } from 'react-native-paper';
import { useTheme } from '../../context/ThemeContext';
import * as colors from '../../theme/colors';

export function TextInput({ style, ...props }: TextInputProps) {
  const { theme } = useTheme();

  return (
    <PaperTextInput
      mode="outlined"
      style={[styles.input, style]}
      outlineColor={colors.textColors.secondary.dark}
      activeOutlineColor={colors.brandColors.primary[500]}
      textColor={colors.textColors.dark}
      placeholderTextColor={colors.textColors.secondary.dark}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  input: {
    backgroundColor: 'transparent',
    marginVertical: 8,
  },
}); 