import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { Appearance } from 'react-native';

/**
 * A debug component to display current theme information
 */
export default function ThemeDebug() {
  const { theme, isDarkMode } = useTheme();
  const systemTheme = Appearance.getColorScheme();
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Theme Debug Info</Text>
      
      <View style={styles.infoRow}>
        <Text style={styles.label}>Selected Theme:</Text>
        <Text style={styles.value}>{theme}</Text>
      </View>
      
      <View style={styles.infoRow}>
        <Text style={styles.label}>System Theme:</Text>
        <Text style={styles.value}>{systemTheme || 'unknown'}</Text>
      </View>
      
      <View style={styles.infoRow}>
        <Text style={styles.label}>Is Dark Mode:</Text>
        <Text style={styles.value}>{isDarkMode ? 'Yes' : 'No'}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 8,
    flex: 1,
  },
  value: {
    flex: 1,
  },
}); 