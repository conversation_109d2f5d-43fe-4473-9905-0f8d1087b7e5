import React from 'react';
import { View, StyleSheet, ViewStyle, StyleProp } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import * as colors from '@/theme/colors';


type CardProps = {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  variant?: 'elevated' | 'outlined' | 'filled';
};

export default function Card({
  children,
  style,
  variant = 'elevated',
}: CardProps) {
  const theme = useAppTheme();

  const getCardStyle = () => {
    switch (variant) {
      case 'elevated':
        return {
          backgroundColor: theme.isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          borderRadius: 12,
          padding: 16,
          overflow: 'visible',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 6,
          },
          shadowOpacity: 0.37,
          shadowRadius: 7.49,
          elevation: 12,
        };
      case 'outlined':
        return {
          backgroundColor: "transparent",
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: theme.isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
        };
      case 'filled':
        return {
          backgroundColor: theme.isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          borderRadius: 12,
          padding: 16,
        };
      default:
        return {
          backgroundColor: theme.isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          borderRadius: 12,
          padding: 16,
        };
    }
  };

  return (
    <View style={[styles.container, getCardStyle() as ViewStyle, style]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
  },
}); 
