import React from 'react';
import { Animated, Easing } from 'react-native';
import { View } from '@/components/styled';
import { useTheme } from '@/context/ThemeContext';

interface SkeletonProps {
  type: 'location' | 'card' | 'text';
}

export function Skeleton({ type }: SkeletonProps) {
  const { isDarkMode } = useTheme();
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const renderLocationSkeleton = () => (
    <View className="space-y-2">
      <Animated.View 
        style={{ opacity }}
        className={`h-4 w-20 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} 
      />
      <Animated.View 
        style={{ opacity }}
        className={`h-40 w-full rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} 
      />
    </View>
  );

  const renderCardSkeleton = () => (
    <View className="space-y-4">
      <Animated.View 
        style={{ opacity }}
        className={`h-40 w-full rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} 
      />
      <View className="space-y-2">
        <Animated.View 
          style={{ opacity }}
          className={`h-6 w-3/4 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} 
        />
        <Animated.View 
          style={{ opacity }}
          className={`h-4 w-1/2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} 
        />
      </View>
    </View>
  );

  const renderTextSkeleton = () => (
    <Animated.View 
      style={{ opacity }}
      className={`h-4 w-full rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} 
    />
  );

  switch (type) {
    case 'location':
      return renderLocationSkeleton();
    case 'card':
      return renderCardSkeleton();
    case 'text':
      return renderTextSkeleton();
    default:
      return null;
  }
} 