import React, { useState } from 'react';
import { Platform } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/context/ThemeContext';
import RNDateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { MaterialIcons } from '@expo/vector-icons';
import { View, Text, TouchableOpacity } from '@/components/styled';
import { Skeleton } from '@/components/common/Skeleton';

interface DateTimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  isLoading?: boolean;
}

export default function DateTimePicker({ value, onChange, isLoading }: DateTimePickerProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const [show, setShow] = useState(false);

  const handleChange = (event: any, selectedDate?: Date) => {
    setShow(Platform.OS === 'ios');
    if (selectedDate) {
      onChange(selectedDate);
    }
  };

  if (isLoading) {
    return <Skeleton type="text" />;
  }

  return (
    <View>
      <TouchableOpacity
        onPress={() => setShow(true)}
        className={`
          flex-row items-center justify-between p-4 rounded-lg
          ${isDarkMode ? 'bg-gray-800' : 'bg-white'}
        `}
      >
        <Text className={isDarkMode ? 'text-white' : 'text-gray-800'}>
          {format(value, 'PPP')}
        </Text>
        <MaterialIcons 
          name="calendar-today" 
          size={20} 
          color={isDarkMode ? '#fff' : '#000'} 
        />
      </TouchableOpacity>

      {show && (
        <RNDateTimePicker
          value={value}
          mode="date"
          display="default"
          onChange={handleChange}
          minimumDate={new Date()}
          textColor={isDarkMode ? '#fff' : '#000'}
        />
      )}
    </View>
  );
} 