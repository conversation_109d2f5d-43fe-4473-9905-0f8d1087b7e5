import React from 'react';
import { Modal, View, StyleSheet, TouchableOpacity, Platform, DevSettings, I18nManager, NativeModules } from 'react-native';
import { Button } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import * as Updates from 'expo-updates';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NewRTLText, NewRTLView } from './rtl/new-index';


// Define constants for AsyncStorage keys to ensure consistency
const LANGUAGE_KEY = '@app:language';
const RTL_APPLIED_KEY = '@app:rtl_applied';
const FORCE_RESTART_KEY = '@app:force_restart';

interface LanguageChangeModalProps {
  visible: boolean;
  onDismiss: () => void;
  onReload: () => Promise<void>;
}

const LanguageChangeModal = ({ visible, onDismiss, onReload }: LanguageChangeModalProps) => {
  const { t } = useTranslation();
  
  const handleReload = async () => {
    try {
      // Log RTL state before reload
      
      
      // Dismiss the modal first to prevent any UI issues during reload
      onDismiss();
      
      // Call the provided onReload function
      await onReload();
      
      // Log RTL state after reload attempt
      
    } catch (error) {
      console.error('Failed to reload app:', error);
      
      
      alert(t('settings.language.reloadError'));
      onDismiss();
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onDismiss}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <NewRTLText style={styles.title}>{t('settings.language.changeTitle')}</NewRTLText>
          
          <NewRTLText style={styles.message}>
            {I18nManager.isRTL 
              ? t('settings.language.changedToArabic') 
              : t('settings.language.changedToEnglish')}
          </NewRTLText>
          
          <NewRTLText style={styles.description}>
            {t('settings.language.rtlChangeDescription')}
          </NewRTLText>
          
          <NewRTLView style={styles.buttonContainer} direction="column">
            <Button 
              mode="contained" 
              onPress={handleReload} 
              style={styles.reloadButton}
            >
              {t('settings.language.reloadNow')}
            </Button>
            
            <Button 
              mode="outlined" 
              onPress={onDismiss} 
              style={styles.laterButton}
            >
              {t('settings.language.reloadLater')}
            </Button>
          </NewRTLView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '85%',
    maxWidth: 400,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    marginBottom: 24,
    textAlign: 'center',
    color: '#666',
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  reloadButton: {
    width: '100%',
  },
  laterButton: {
    width: '100%',
  },
});

export default LanguageChangeModal; 