import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import * as Location from 'expo-location';
import MapView, { Marker, PROVIDER_DEFAULT, PROVIDER_GOOGLE } from 'react-native-maps';
import { Platform } from 'react-native';
import { View, Text, TouchableOpacity } from '@/components/styled';
import { darkMapStyle } from '@/theme/mapStyle';
import { Skeleton } from '@/components/common/Skeleton';

interface LocationPickerProps {
  value: {
    lat: number;
    lng: number;
    address: string;
  };
  onChange: (location: { lat: number; lng: number; address: string }) => void;
}

export default function LocationPicker({ value, onChange }: LocationPickerProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getCurrentLocation = async () => {
    setLoading(true);
    setError(null);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setError(t('Location permission denied'));
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const address = await reverseGeocode(
        location.coords.latitude,
        location.coords.longitude
      );

      onChange({
        lat: location.coords.latitude,
        lng: location.coords.longitude,
        address,
      });
    } catch (error) {
      console.error('Error getting location:', error);
      setError(t('Error getting location'));
    } finally {
      setLoading(false);
    }
  };

  const reverseGeocode = async (latitude: number, longitude: number) => {
    try {
      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      if (result[0]) {
        return `${result[0].street}, ${result[0].district}, ${result[0].city}, ${result[0].postalCode}`;
      }
      return '';
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return '';
    }
  };

  if (loading) {
    return <Skeleton type="location" />;
  }

  return (
    <View>
      <Text className={`text-base mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
        {t('Location')}
      </Text>
      
      <View className={`
        rounded-lg overflow-hidden
        ${isDarkMode ? 'bg-gray-800' : 'bg-white'}
      `}>
        {value.lat !== 0 && value.lng !== 0 && (
          <MapView
            provider={Platform.OS === 'android' ? PROVIDER_GOOGLE : PROVIDER_DEFAULT}
            style={{ height: 150, width: '100%' }}
            customMapStyle={isDarkMode ? darkMapStyle : []}
            initialRegion={{
              latitude: value.lat,
              longitude: value.lng,
              latitudeDelta: 0.005,
              longitudeDelta: 0.005,
            }}
          >
            <Marker
              coordinate={{
                latitude: value.lat,
                longitude: value.lng,
              }}
            />
          </MapView>
        )}

        <View className="p-4">
          {error ? (
            <Text className="text-red-500 mb-2">{error}</Text>
          ) : (
            <Text className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {value.address || t('Select location')}
            </Text>
          )}
          
          <TouchableOpacity
            onPress={getCurrentLocation}
            className="flex-row items-center mt-2"
            disabled={loading}
          >
            <MaterialIcons 
              name="my-location" 
              size={20} 
              color={isDarkMode ? '#fff' : '#000'} 
            />
            <Text className={`ml-2 ${isDarkMode ? 'text-white' : 'text-black'}`}>
              {loading ? t('Getting location...') : t('Use current location')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
} 