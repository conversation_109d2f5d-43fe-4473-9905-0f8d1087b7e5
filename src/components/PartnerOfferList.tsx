import React from 'react';
import { View, Text, Image, FlatList, TouchableOpacity, StyleSheet } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useRTLContext } from '@/components/rtl/new-index';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { Partner } from '@/types/partner';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledFlatList = styled(FlatList);

export interface PartnerOffer {
  id: string;
  partnerId: string;
  partner: Partner;
  price: number;
  isAvailable: boolean;
  deliveryTime?: string;
  imageUrl?: string;
}

interface PartnerOfferListProps {
  offers: PartnerOffer[];
  isLoading: boolean;
  onSelectOffer: (offer: PartnerOffer) => void;
  dumpsterName?: string;
}

/**
 * PartnerOfferList - A component for displaying partner offers for a dumpster
 * 
 * Features:
 * - Displays partner name, rating, live images, and price
 * - Sorts offers by ascending price then descending rating
 * - Supports RTL layouts and dark mode
 * - Shows loading state with shimmer effect
 * - Follows Skyscanner design system with elevation and color discipline
 */
export default function PartnerOfferList({
  offers,
  isLoading,
  onSelectOffer,
  dumpsterName,
}: PartnerOfferListProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  // Sort offers by price (ascending) then rating (descending)
  const sortedOffers = [...offers].sort((a, b) => {
    if (a.price !== b.price) {
      return a.price - b.price; // Sort by price ascending
    }
    return (b.partner.rating || 0) - (a.partner.rating || 0); // Then by rating descending
  });

  // Render loading skeleton
  if (isLoading) {
    return (
      <StyledView className="px-4">
        <StyledText className="text-lg font-bold text-gray-800 dark:text-white mb-4">
          {t('partner_offers')}
        </StyledText>
        {[1, 2, 3].map((i) => (
          <StyledView 
            key={i}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-3"
            style={styles.cardShadow}
          >
            <StyledView className="flex-row">
              <StyledView className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg" />
              <StyledView className="ml-3 flex-1">
                <StyledView className="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
                <StyledView className="w-1/2 h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
                <StyledView className="w-1/3 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
              </StyledView>
              <StyledView className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded" />
            </StyledView>
          </StyledView>
        ))}
      </StyledView>
    );
  }

  // Render empty state
  if (sortedOffers.length === 0) {
    return (
      <StyledView className="px-4 py-8 items-center">
        <MaterialIcons name="search-off" size={48} color={colors.brandColors.primary[400]} />
        <StyledText className="text-lg font-bold text-gray-800 dark:text-white mt-4">
          {t('no_partner_offers')}
        </StyledText>
        <StyledText className="text-gray-600 dark:text-gray-400 text-center mt-2">
          {t('no_partner_offers_description')}
        </StyledText>
      </StyledView>
    );
  }

  const renderOfferItem = ({ item }: { item: PartnerOffer }) => {
    const partnerName = item.partner.company_name || t('unknown_partner');
    const partnerRating = item.partner.rating || 0;
    const partnerImage = item.imageUrl || item.partner.profile?.avatar_url;
    
    return (
      <StyledTouchableOpacity
        className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-3"
        style={styles.cardShadow}
        onPress={() => onSelectOffer(item)}
        activeOpacity={0.7}
        testID={`partner-offer-${item.id}`}
      >
        <StyledView className="flex-row items-center">
          {/* Partner Image */}
          <StyledView className="w-16 h-16 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700">
            {partnerImage ? (
              <Image
                source={{ uri: partnerImage }}
                style={{ width: '100%', height: '100%' }}
                resizeMode="cover"
              />
            ) : (
              <StyledView className="w-full h-full justify-center items-center">
                <MaterialIcons name="business" size={24} color={colors.textColors.secondary.light} />
              </StyledView>
            )}
          </StyledView>
          
          {/* Partner Info */}
          <StyledView className="ml-3 flex-1">
            <StyledText className="text-base font-bold text-gray-800 dark:text-white" numberOfLines={1}>
              {partnerName}
            </StyledText>
            
            {/* Rating */}
            {partnerRating > 0 && (
              <StyledView className="flex-row items-center mt-1">
                <MaterialIcons name="star" size={16} color="#FFD700" />
                <StyledText className="text-sm text-gray-700 dark:text-gray-300 ml-1">
                  {partnerRating.toFixed(1)}
                </StyledText>
              </StyledView>
            )}
            
            {/* Delivery Time */}
            {item.deliveryTime && (
              <StyledView className="flex-row items-center mt-1">
                <MaterialIcons name="access-time" size={14} color={colors.textColors.secondary.light} />
                <StyledText className="text-xs text-gray-600 dark:text-gray-400 ml-1">
                  {item.deliveryTime}
                </StyledText>
              </StyledView>
            )}
          </StyledView>
          
          {/* Price */}
          <StyledView className="bg-primary-50 dark:bg-primary-900 px-3 py-2 rounded-lg">
            <StyledText className="text-primary-600 dark:text-primary-300 font-bold">
              ﷼{item.price.toLocaleString()}
            </StyledText>
          </StyledView>
        </StyledView>
      </StyledTouchableOpacity>
    );
  };

  return (
    <StyledView className="px-4">
      <StyledText className="text-lg font-bold text-gray-800 dark:text-white mb-4">
        {dumpsterName ? 
          t('partner_offers_for', { name: dumpsterName }) : 
          t('partner_offers')
        }
      </StyledText>
      
      {/* Best price badge */}
      {sortedOffers.length > 0 && (
        <StyledView className="flex-row mb-2">
          <StyledView className="bg-success-100 dark:bg-success-900 rounded-full px-3 py-1 flex-row items-center">
            <MaterialIcons name="local-offer" size={14} color={colors.brandColors.success[500]} />
            <StyledText className="text-xs text-success-700 dark:text-success-300 ml-1">
              {t('best_price_from', { price: sortedOffers[0].price.toLocaleString() })}
            </StyledText>
          </StyledView>
        </StyledView>
      )}
      
      <StyledFlatList
        data={sortedOffers}
        renderItem={renderOfferItem as any}
        keyExtractor={(item: any) => item.id}
        showsVerticalScrollIndicator={false}
        testID="partner-offers-list"
      />
    </StyledView>
  );
}

const styles = StyleSheet.create({
  cardShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
});

// Jest test stub
/*
import { render, fireEvent } from '@testing-library/react-native';
import PartnerOfferList, { PartnerOffer } from '../PartnerOfferList';

// Mock the context hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string, options?: any) => key }),
}));

describe('PartnerOfferList', () => {
  // Mock offers would be generated from real partner data in tests
  const mockOffers: PartnerOffer[] = [];

  it('renders correctly with offers', () => {
    const onSelectOffer = jest.fn();
    const { getByText, getByTestId } = render(
      <PartnerOfferList
        offers={mockOffers}
        isLoading={false}
        onSelectOffer={onSelectOffer}
        dumpsterName="10 Yard Dumpster"
      />
    );

    expect(getByText('partner_offers_for')).toBeTruthy();
    expect(getByTestId('partner-offers-list')).toBeTruthy();
  });

  it('shows loading state when isLoading is true', () => {
    const { queryByTestId } = render(
      <PartnerOfferList
        offers={mockOffers}
        isLoading={true}
        onSelectOffer={() => {}}
      />
    );

    expect(queryByTestId('partner-offers-list')).toBeNull();
  });
});
*/
