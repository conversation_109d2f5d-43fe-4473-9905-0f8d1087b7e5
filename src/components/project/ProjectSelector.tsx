import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { ProjectType, DEFAULT_PROJECTS } from '@/types/project';
import * as colors from '@/theme/colors';

interface ProjectSelectorProps {
  onSelect: (project: ProjectType) => void;
  selectedProject?: ProjectType;
}

export default function ProjectSelector({ onSelect, selectedProject }: ProjectSelectorProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  return (
    <View className="flex-1 px-4">
      <Text className={`text-lg font-medium mb-4 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
        {t('Want a dumpster for')}
      </Text>
      
      {DEFAULT_PROJECTS.map((project) => (
        <TouchableOpacity
          key={project.id}
          onPress={() => onSelect(project)}
          className={`
            flex-row items-center justify-between p-4 mb-2 rounded-lg
            ${isDarkMode ? 'bg-gray-800' : 'bg-white'}
            ${selectedProject?.id === project.id ? 'border-2 border-primary-500' : ''}
          `}
        >
          <View className="flex-1">
            <Text className={`text-base font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
              {isRTL ? project.name_ar : project.name_en}
            </Text>
            <Text className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {project.defaultSize}
            </Text>
          </View>
          
          {selectedProject?.id === project.id && (
            <MaterialIcons 
              name="check-circle" 
              size={24} 
              color={colors.brandColors.primary[500]} 
            />
          )}
        </TouchableOpacity>
      ))}

      <TouchableOpacity
        onPress={() => onSelect(DEFAULT_PROJECTS[DEFAULT_PROJECTS.length - 1])}
        className={`
          mt-4 p-4 rounded-lg
          ${isDarkMode ? 'bg-primary-600' : 'bg-primary-500'}
        `}
      >
        <Text className="text-center text-white font-medium">
          {t('Find dumpster')}
        </Text>
      </TouchableOpacity>
    </View>
  );
} 