import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useRTLContext } from '@/components/rtl/new-index';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { Dumpster } from '@/types/v2/dumpster';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

interface DumpsterCardV3Props {
  dumpster: Dumpster;
  onPress: (dumpster: Dumpster) => void;
  isLoading?: boolean;
  bestFor?: string[];
}

/**
 * DumpsterCardV3 - A Skyscanner-inspired card component for displaying dumpster information
 * 
 * Features:
 * - Displays dumpster size, lowest price, thumbnail, in-stock count, and "best-for" tags
 * - Supports RTL layouts and dark mode
 * - Shows loading state with shimmer effect
 * - Follows Skyscanner design system with elevation and color discipline
 */
export default function DumpsterCardV3({
  dumpster,
  onPress,
  isLoading = false,
  bestFor = [],
}: DumpsterCardV3Props) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Handle loading state
  if (isLoading) {
    return (
      <StyledView 
        className="rounded-xl overflow-hidden bg-white dark:bg-gray-800"
        style={styles.cardShadow}
        testID="dumpster-card-loading"
      >
        <StyledView className="h-40 justify-center items-center">
          <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
        </StyledView>
      </StyledView>
    );
  }

  // Extract dumpster data with fallbacks
  const name = isRTL ? dumpster.nameAr : dumpster.nameEn;
  const price = dumpster.pricePerLoad || 0;
  const rating = dumpster.rating || 0;
  const reviewCount = dumpster.reviewCount || 0;
  const isAvailable = dumpster.isAvailable !== undefined ? dumpster.isAvailable : true;
  const stockCount = isAvailable ? Math.floor(Math.random() * 10) + 1 : 0; // Mock stock count
  
  // Dimensions with fallbacks
  const length = dumpster.length || 0;
  const width = dumpster.width || 0;
  const height = dumpster.height || 0;
  
  // Image with fallback
  const imageUrl = dumpster.imageUrl || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';

  return (
    <StyledTouchableOpacity
      className="rounded-xl overflow-hidden bg-white dark:bg-gray-800 mb-3"
      style={styles.cardShadow}
      onPress={() => onPress(dumpster)}
      activeOpacity={0.7}
      testID="dumpster-card-v3"
    >
      <StyledView className="flex-row h-32">
        {/* Thumbnail */}
        <StyledView className="w-1/3 relative">
          {imageLoading && (
            <StyledView className="absolute inset-0 justify-center items-center bg-gray-200 dark:bg-gray-700 z-10">
              <ActivityIndicator size="small" color={colors.brandColors.primary[500]} />
            </StyledView>
          )}
          <Image
            source={{ uri: imageUrl }}
            className="w-full h-full"
            resizeMode="cover"
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
            onError={() => setImageError(true)}
          />
          
          {/* Rating badge */}
          {rating > 0 && (
            <StyledView className="absolute top-1 left-1 bg-yellow-500 rounded-md px-1 py-0.5 flex-row items-center">
              <MaterialIcons name="star" size={12} color="#fff" />
              <StyledText className="text-white text-xs ml-0.5 font-bold">{rating.toFixed(1)}</StyledText>
            </StyledView>
          )}
        </StyledView>
        
        {/* Content */}
        <StyledView className="flex-1 p-3 justify-between">
          {/* Title and size */}
          <StyledView>
            <StyledText className="text-base font-bold text-gray-800 dark:text-white" numberOfLines={1}>
              {name}
            </StyledText>
            <StyledText className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {`${length}m × ${width}m × ${height}m`}
            </StyledText>
          </StyledView>
          
          {/* Tags */}
          {bestFor.length > 0 && (
            <StyledView className="flex-row flex-wrap mt-1">
              {bestFor.slice(0, 2).map((tag, index) => (
                <StyledView 
                  key={index} 
                  className="bg-blue-100 dark:bg-blue-900 rounded-full px-2 py-0.5 mr-1 mb-1"
                >
                  <StyledText className="text-xs text-blue-800 dark:text-blue-100">
                    {tag}
                  </StyledText>
                </StyledView>
              ))}
            </StyledView>
          )}
          
          {/* Price and availability */}
          <StyledView className="flex-row justify-between items-center mt-1">
            <StyledText className="text-lg font-bold text-primary-600 dark:text-primary-400">
              ﷼{price.toLocaleString()}
            </StyledText>
            
            <StyledView className="flex-row items-center">
              {isAvailable ? (
                <>
                  <MaterialIcons name="check-circle" size={16} color={colors.brandColors.success[500]} />
                  <StyledText className="text-xs text-success-600 dark:text-success-400 ml-1">
                    {stockCount} {t('in_stock')}
                  </StyledText>
                </>
              ) : (
                <>
                  <MaterialIcons name="error" size={16} color={colors.brandColors.error[500]} />
                  <StyledText className="text-xs text-error-600 dark:text-error-400 ml-1">
                    {t('out_of_stock')}
                  </StyledText>
                </>
              )}
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledView>
    </StyledTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  cardShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});

// Jest test stub
/*
import { render, fireEvent } from '@testing-library/react-native';
import DumpsterCardV3 from '../DumpsterCardV3';
import { Dumpster } from '@/types/v2/dumpster';

// Mock the context hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('DumpsterCardV3', () => {
  const mockDumpster: Dumpster = {
    id: '1',
    nameEn: '10 Yard Dumpster',
    nameAr: 'حاوية 10 ياردة',
    imageUrl: 'https://example.com/dumpster.jpg',
    length: 10,
    width: 8,
    height: 4,
    pricePerLoad: 250,
    rating: 4.5,
    reviewCount: 12,
    isAvailable: true,
    sizeId: 'size-1',
  };

  it('renders correctly with all props', () => {
    const onPress = jest.fn();
    const { getByText, getByTestId } = render(
      <DumpsterCardV3 
        dumpster={mockDumpster} 
        onPress={onPress} 
        bestFor={['Construction', 'Renovation']} 
      />
    );
    
    expect(getByText('10 Yard Dumpster')).toBeTruthy();
    expect(getByText('10m × 8m × 4m')).toBeTruthy();
    expect(getByText('﷼250')).toBeTruthy();
    expect(getByText('Construction')).toBeTruthy();
    expect(getByTestId('dumpster-card-v3')).toBeTruthy();
  });

  it('shows loading state when isLoading is true', () => {
    const { getByTestId } = render(
      <DumpsterCardV3 dumpster={mockDumpster} onPress={() => {}} isLoading={true} />
    );
    
    expect(getByTestId('dumpster-card-loading')).toBeTruthy();
  });

  it('calls onPress with the dumpster when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <DumpsterCardV3 dumpster={mockDumpster} onPress={onPress} />
    );
    
    fireEvent.press(getByTestId('dumpster-card-v3'));
    expect(onPress).toHaveBeenCalledWith(mockDumpster);
  });
});
*/
