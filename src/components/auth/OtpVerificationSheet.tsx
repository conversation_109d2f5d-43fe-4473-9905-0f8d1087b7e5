import React, { useState, useEffect, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, TextStyle, ActivityIndicator, ScrollView, LogBox, Dimensions, KeyboardAvoidingView, Platform, Keyboard, ViewStyle } from 'react-native';
import { useTranslation } from 'react-i18next';
import { NewRTLView as RTLView, NewRTLText as RTLText } from '@/components/rtl/new-index';
import { Feather } from '@expo/vector-icons';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Modal from 'react-native-modal'; // Use react-native-modal for better bottom sheet behavior
import { OtpInput } from 'react-native-otp-entry'; // Import from the new library

// Get screen height
const screenHeight = Dimensions.get('window').height;

// --- Props Interface ---
interface OtpVerificationSheetProps {
  isVisible: boolean;
  phoneForVerification: string | null;
  isLoading: boolean;
  error: string | null;
  canResend: boolean;
  resendCountdown: number;
  onVerify: (otp: string) => Promise<void>;
  onResend: () => Promise<void>;
  onClose: () => void;
}

// --- Ref Interface for the new library ---
interface OtpInputRef {
  clear: () => void;
  focus: () => void;
  setValue: (value: string) => void;
}

const OtpVerificationSheet: React.FC<OtpVerificationSheetProps> = ({
  isVisible,
  phoneForVerification,
  isLoading,
  error,
  canResend,
  resendCountdown,
  onVerify,
  onResend,
  onClose,
}) => {
  LogBox.ignoreLogs(['visible']); // Ignore specific logs if needed
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const [otp, setOtp] = useState('');
  const otpInputRef = useRef<OtpInputRef>(null); // Use the specific ref type

  // Clear OTP when sheet becomes invisible
  useEffect(() => {
    if (!isVisible) {
      setOtp('');
    }
  }, [isVisible]);

  const handleInternalVerify = () => {
    if (!otp || otp.length !== 6) {
      // Maybe show a local error or rely on parent's error handling
      return;
    }
    // Dismiss keyboard before verifying
    Keyboard.dismiss(); 
    onVerify(otp);
  };

  // Clear OTP and focus input when modal becomes visible
  useEffect(() => {
    if (isVisible) {
      // Clear previous OTP using ref method
      otpInputRef.current?.clear(); 
      // Use timeout to ensure modal is fully visible before focusing
      setTimeout(() => otpInputRef.current?.focus(), 300);
    } else {
      setOtp(''); // Clear internal state as well
      Keyboard.dismiss(); // Dismiss keyboard when closing
    }
  }, [isVisible]);

  // Define theme styles based on dark mode
  const otpTheme: { [key: string]: ViewStyle | TextStyle } = {
    pinCodeContainerStyle: {
      ...styles.pinCodeContainerBase,
      backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
      borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
    },
    pinCodeTextStyle: {
      ...styles.pinCodeTextBase,
      color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
    },
    focusedPinCodeContainerStyle: {
      borderColor: colors.brandColors.primary[500], // Focused border color
    },
    containerStyle: styles.otpContainer // Style for the root container
    // Add other theme keys like focusStickStyle if needed
  };

  // Use Modal for better cross-platform bottom sheet behavior
  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose} // Close on backdrop tap
      onBackButtonPress={onClose} // Close on hardware back button
      style={styles.modal}
      avoidKeyboard // Automatically handles keyboard appearance
      backdropOpacity={0.5}
      swipeDirection={['down']} // Allow swiping down to close
      onSwipeComplete={onClose}
      propagateSwipe // Allow scrolling within the modal
    >
        <View style={[
          styles.sheetContainer,
          { backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light }
        ]}>
          <View style={styles.handleContainer}>
            <View style={[styles.handle, { backgroundColor: isDarkMode ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.2)' }]} />
          </View>

          <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
            <RTLText style={[styles.title, { color: isDarkMode ? colors.brandColors.secondary[400] : colors.brandColors.secondary[500] }]}>
              {t('auth.verify.title', 'Verify Your Phone')}
            </RTLText>

            <RTLText style={[styles.subtitle, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
              {t('auth.verify.description', 'Enter the verification code sent to your phone')} {phoneForVerification}
            </RTLText>

            <View style={styles.otpContainer}>
              <OtpInput
                ref={otpInputRef}
                numberOfDigits={6}
                onTextChange={setOtp}
                onFilled={handleInternalVerify}
                theme={otpTheme}
                autoFocus={false}
                disabled={isLoading}
                textInputProps={{
                  keyboardType: "number-pad",
                }}
              />
            </View>

            {error ? (
              <RTLText style={styles.errorText}>
                {error}
              </RTLText>
            ) : null}

            <TouchableOpacity
              style={[styles.button, { opacity: isLoading || otp.length !== 6 ? 0.7 : 1 }]}
              onPress={handleInternalVerify}
              disabled={isLoading || otp.length !== 6}
            >
              {isLoading ? (
                <ActivityIndicator color={isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600]} />
              ) : (
                <RTLText style={[styles.buttonText, { color: isDarkMode ? colors.textColors.light : colors.brandColors.secondary[600] }]}>
                  {t('auth.verify.verifyButton', 'Verify')}
                </RTLText>
              )}
            </TouchableOpacity>

            {/* Always display timer text container */}
            <View style={styles.timerContainer}>
              {/* Restore original logic */}
              <RTLText style={[styles.timerText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
                {resendCountdown > 0
                  ? t('auth.verify.resendOtpTimer', 'Resend code in {{seconds}}s', { seconds: resendCountdown })
                  : t('auth.verify.resendReady', 'You can now resend the code.')
                }
              </RTLText>
            </View>

            {/* Resend Button - Disabled state depends on canResend prop */}
            <TouchableOpacity
              style={styles.resendButton}
              onPress={onResend}
              disabled={isLoading || !canResend} // Correctly disabled based on parent state
            >
              <RTLText style={[
                styles.resendText,
                {
                  color: !canResend ? 
                    (isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light) : 
                    colors.brandColors.primary[500] 
                }
              ]}>
                 {t('auth.verify.resendCode', 'Resend Code')}
              </RTLText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.resendButton, { marginTop: 16 }]}
              onPress={onClose}
              disabled={isLoading}
            >
              <RTLText style={[styles.resendText, { color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }]}>
                {t('common.goBack', 'Go Back')}
              </RTLText>
            </TouchableOpacity>
          </ScrollView>
        </View>
    </Modal>
  );
};

// --- Styles ---
// Add styles relevant to the OTP sheet here
// Adapt styles from login.v2.tsx as needed
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0, // Remove default margins
  },
  sheetContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    paddingTop: 12, // Reduced top padding because of handle
    maxHeight: screenHeight * 0.9, // Limit height
    minHeight: 300, // Ensure minimum height
  },
  handleContainer: {
     alignItems: 'center',
     marginBottom: 16,
  },
  handle: {
     width: 40,
     height: 5,
     borderRadius: 3,
  },
  scrollContent: {
    paddingBottom: 20, // Add padding at the bottom for scrollable content
  },
  title: {
    fontSize: 22, // Slightly smaller for sheet
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 15, // Slightly smaller for sheet
    lineHeight: 21,
    textAlign: 'center',
    marginBottom: 24,
  },
  otpContainer: {
    alignSelf: 'center',
    marginBottom: 24,
  },
  pinCodeContainerBase: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pinCodeTextBase: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: colors.brandColors.primary[500],
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    color: colors.brandColors.danger[500],
    marginBottom: 16,
    fontSize: 14,
    textAlign: 'center',
  },
  resendButton: {
    marginTop: 5, // Reduced margin above button
    alignItems: 'center',
    paddingVertical: 8, 
  },
  resendText: {
    fontSize: 15, // Slightly smaller
    fontWeight: '500',
  },
  timerContainer: { 
    minHeight: 20, 
    marginBottom: 5, 
    alignItems: 'center', 
  },
  timerText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default OtpVerificationSheet; 