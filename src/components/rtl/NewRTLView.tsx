import React from 'react';
import { View, ViewStyle, StyleSheet, I18nManager } from 'react-native';
import { useRTLContext } from './RTLContext';

interface NewRTLViewProps {
  children: React.ReactNode;
  style?: ViewStyle | ViewStyle[];
  direction?: 'row' | 'column';
}

export const NewRTLView: React.FC<NewRTLViewProps> = ({ 
  children, 
  style,
  direction = 'row'
}) => {
  const { isRTL } = useRTLContext();
  
  const getRTLStyles = () => {
    // Base styles with RTL-aware flex direction
    const baseStyles: ViewStyle = {
      flexDirection: direction === 'column' 
        ? 'column' 
        : (isRTL ? 'row-reverse' : 'row'),
    };

    // Handle additional styles
    const flattenedStyle = StyleSheet.flatten(style || {});
    const rtlStyles: ViewStyle = {};
    
    if (isRTL) {
      // Handle alignItems
      if (flattenedStyle.alignItems === 'flex-start') {
        rtlStyles.alignItems = 'flex-end';
      } else if (flattenedStyle.alignItems === 'flex-end') {
        rtlStyles.alignItems = 'flex-start';
      }
      
      // Handle justifyContent for row direction
      if (direction === 'row') {
        if (flattenedStyle.justifyContent === 'flex-start') {
          rtlStyles.justifyContent = 'flex-end';
        } else if (flattenedStyle.justifyContent === 'flex-end') {
          rtlStyles.justifyContent = 'flex-start';
        }
      }
      
      // Handle margins
      if (flattenedStyle.marginLeft !== undefined || flattenedStyle.marginRight !== undefined) {
        const marginLeft = flattenedStyle.marginLeft || 0;
        const marginRight = flattenedStyle.marginRight || 0;
        rtlStyles.marginLeft = marginRight;
        rtlStyles.marginRight = marginLeft;
      }
      
      // Handle padding
      if (flattenedStyle.paddingLeft !== undefined || flattenedStyle.paddingRight !== undefined) {
        const paddingLeft = flattenedStyle.paddingLeft || 0;
        const paddingRight = flattenedStyle.paddingRight || 0;
        rtlStyles.paddingLeft = paddingRight;
        rtlStyles.paddingRight = paddingLeft;
      }

      // Handle position properties
      if (flattenedStyle.left !== undefined || flattenedStyle.right !== undefined) {
        const left = flattenedStyle.left || 0;
        const right = flattenedStyle.right || 0;
        rtlStyles.left = right;
        rtlStyles.right = left;
      }
    }

    return [baseStyles, rtlStyles];
  };

  return (
    <View style={[...getRTLStyles(), style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // Base styles if needed
  }
});

export default NewRTLView; 