import React from 'react';
import { Text, TextStyle, StyleSheet } from 'react-native';
import { useRTLContext } from './RTLContext';

interface NewRTLTextProps {
  children: React.ReactNode;
  style?: TextStyle | TextStyle[];
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
}

export const NewRTLText: React.FC<NewRTLTextProps> = ({ 
  children, 
  style,
  align = 'auto'
}) => {
  const { isRTL } = useRTLContext();
  
  const getRTLStyles = () => {
    // Handle text alignment first
    const textAlignment: TextStyle = {
      textAlign: align === 'auto' ? (isRTL ? 'right' : 'left') : align,
      writingDirection: isRTL ? 'rtl' : 'ltr',
    };

    // Handle additional text styles
    const flattenedStyle = StyleSheet.flatten(style || {});
    const rtlStyles: TextStyle = {};
    
    if (isRTL) {
      // Handle margins
      if (flattenedStyle.marginLeft !== undefined || flattenedStyle.marginRight !== undefined) {
        const marginLeft = flattenedStyle.marginLeft || 0;
        const marginRight = flattenedStyle.marginRight || 0;
        rtlStyles.marginLeft = marginRight;
        rtlStyles.marginRight = marginLeft;
      }
      
      // Handle padding
      if (flattenedStyle.paddingLeft !== undefined || flattenedStyle.paddingRight !== undefined) {
        const paddingLeft = flattenedStyle.paddingLeft || 0;
        const paddingRight = flattenedStyle.paddingRight || 0;
        rtlStyles.paddingLeft = paddingRight;
        rtlStyles.paddingRight = paddingLeft;
      }
    }

    return [textAlignment, rtlStyles];
  };

  // Apply styles in the correct order: base styles, RTL styles, and then custom styles
  const baseStyles = {
    textAlign: align === 'auto' ? (isRTL ? 'right' : 'left') : align,
  };

  return (
    <Text style={[baseStyles, ...getRTLStyles(), style]}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  // Base styles if needed
});

export default NewRTLText; 