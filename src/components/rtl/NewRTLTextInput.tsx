import React from 'react';
import { TextInput, TextInputProps, StyleSheet, TextStyle, I18nManager } from 'react-native';
import { useRTLContext } from './RTLContext';

interface NewRTLTextInputProps extends Omit<TextInputProps, 'style'> {
  style?: TextStyle | TextStyle[];
}

export const NewRTLTextInput: React.FC<NewRTLTextInputProps> = ({ 
  style,
  textAlign,
  ...props
}) => {
  const { isRTL } = useRTLContext();
  
  const getRTLStyles = () => {
    // Base RTL styles
    const baseStyles: TextStyle = {
      textAlign: textAlign || (isRTL ? 'right' : 'left'),
      writingDirection: isRTL ? 'rtl' : 'ltr',
    };

    // Handle additional styles
    const flattenedStyle = StyleSheet.flatten(style || {});
    const rtlStyles: TextStyle = {};
    
    if (isRTL) {
      // Handle margins
      if (flattenedStyle.marginLeft !== undefined || flattenedStyle.marginRight !== undefined) {
        const marginLeft = flattenedStyle.marginLeft || 0;
        const marginRight = flattenedStyle.marginRight || 0;
        rtlStyles.marginLeft = marginRight;
        rtlStyles.marginRight = marginLeft;
      }
      
      // Handle padding
      if (flattenedStyle.paddingLeft !== undefined || flattenedStyle.paddingRight !== undefined) {
        const paddingLeft = flattenedStyle.paddingLeft || 0;
        const paddingRight = flattenedStyle.paddingRight || 0;
        rtlStyles.paddingLeft = paddingRight;
        rtlStyles.paddingRight = paddingLeft;
      }
    }

    return [baseStyles, rtlStyles];
  };

  return (
    <TextInput 
      {...props}
      style={[...getRTLStyles(), style]}
      textAlign={textAlign || (isRTL ? 'right' : 'left')}
    />
  );
};

const styles = StyleSheet.create({
  // Base styles if needed
});

export default NewRTLTextInput; 