import React, { useEffect, useState } from 'react';
import { View, StyleSheet, I18nManager, ViewStyle } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface RTLIconProps {
  children: React.ReactNode;
  style?: ViewStyle;
  shouldFlip?: boolean; // Whether the icon should be flipped in RTL mode
  forceRTL?: boolean; // Force RTL regardless of system setting
}

/**
 * A wrapper component for icons that handles proper flipping in RTL mode.
 * Some icons should be flipped in RTL mode (like back/forward arrows),
 * while others should remain as is (like a settings gear icon).
 */
export default function RTLIcon({ 
  children, 
  style, 
  shouldFlip = true, 
  forceRTL = false 
}: RTLIconProps) {
  const [shouldUseRTL, setShouldUseRTL] = useState(forceRTL || I18nManager.isRTL);
  
  useEffect(() => {
    const checkRTLState = async () => {
      const language = await AsyncStorage.getItem('@app:language');
      if (language === 'ar' && !I18nManager.isRTL) {
        console.log('RTLIcon: Language is Arabic but RTL is not enabled, forcing RTL styles');
        setShouldUseRTL(true);
      }
    };
    
    checkRTLState();
  }, []);
  
  // Only apply transform if the icon should be flipped in RTL mode
  const rtlStyles = (shouldUseRTL && shouldFlip) ? styles.flippedIcon : {};
  
  return (
    <View style={[styles.container, rtlStyles, style]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  flippedIcon: {
    transform: [{ scaleX: -1 }],
  },
}); 