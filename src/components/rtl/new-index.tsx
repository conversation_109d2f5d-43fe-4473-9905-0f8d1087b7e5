import React from 'react';
import { View, Text, TextStyle, ViewStyle, StyleSheet, I18nManager, FlexAlignType } from 'react-native';
import { useRTLContext } from './RTLContext';

// Define types for RTL-aware components
type RTLViewProps = React.ComponentProps<typeof View> & {
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  align?: FlexAlignType;
  justify?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
};

type RTLTextProps = React.ComponentProps<typeof Text>;

/**
 * RTL-aware View component that automatically handles direction changes
 */
export const NewRTLView: React.FC<RTLViewProps> = ({ 
  children, 
  style, 
  direction = 'row',
  align,
  justify,
  ...props 
}) => {
  const { isRTL } = useRTLContext();
  
  // Create base styles
  const baseStyles: ViewStyle = {};
  
  // Handle direction
  if (direction === 'row') {
    baseStyles.flexDirection = isRTL ? 'row-reverse' : 'row';
  } else if (direction === 'row-reverse') {
    baseStyles.flexDirection = isRTL ? 'row' : 'row-reverse';
  } else {
    baseStyles.flexDirection = direction;
  }
  
  // Handle alignment
  if (align) {
    baseStyles.alignItems = align;
  }
  
  // Handle justification
  if (justify) {
    baseStyles.justifyContent = justify;
  }
  
  // Handle text alignment in style
  let modifiedStyle = style;
  if (style && typeof style === 'object') {
    const styleObj = StyleSheet.flatten(style) as any;
    if (styleObj.textAlign === 'left') {
      modifiedStyle = [
        style,
        { textAlign: isRTL ? 'right' : 'left' } as any
      ];
    } else if (styleObj.textAlign === 'right') {
      modifiedStyle = [
        style,
        { textAlign: isRTL ? 'left' : 'right' } as any
      ];
    }
  }

  return (
    <View style={[baseStyles, modifiedStyle]} {...props}>
      {children}
    </View>
  );
};

/**
 * RTL-aware Text component that automatically handles text alignment
 */
export const NewRTLText: React.FC<RTLTextProps> = ({ children, style, ...props }) => {
  const { isRTL } = useRTLContext();
  
  // Handle text alignment in style
  let textStyle: TextStyle = {};
  let modifiedStyle = style;
  
  // Set default text alignment based on RTL context
  textStyle.textAlign = isRTL ? 'right' : 'left';
  
  // Override with explicit alignment if provided
  if (style && typeof style === 'object') {
    const styleObj = StyleSheet.flatten(style);
    if (styleObj.textAlign === 'left') {
      textStyle.textAlign = isRTL ? 'right' : 'left';
    } else if (styleObj.textAlign === 'right') {
      textStyle.textAlign = isRTL ? 'left' : 'right';
    } else if (styleObj.textAlign === 'center') {
      textStyle.textAlign = 'center';
    } else if (styleObj.textAlign === 'auto') {
      // Use system default
      delete textStyle.textAlign;
    }
  }
  
  return (
    <Text style={[textStyle, modifiedStyle]} {...props}>
      {children}
    </Text>
  );
};

/**
 * Hook to get RTL-aware styles
 */
export const useRTLStyles = () => {
  const { isRTL } = useRTLContext();
  
  return {
    // Create RTL-aware margin
    createMargin: (
      start: number = 0, 
      top: number = 0, 
      end: number = 0, 
      bottom: number = 0
    ): ViewStyle => ({
      marginTop: top,
      marginBottom: bottom,
      marginLeft: isRTL ? end : start,
      marginRight: isRTL ? start : end,
    }),
    
    // Create RTL-aware padding
    createPadding: (
      start: number = 0, 
      top: number = 0, 
      end: number = 0, 
      bottom: number = 0
    ): ViewStyle => ({
      paddingTop: top,
      paddingBottom: bottom,
      paddingLeft: isRTL ? end : start,
      paddingRight: isRTL ? start : end,
    }),
    
    // Create RTL-aware position
    createPosition: (
      position: 'absolute' | 'relative',
      start: number | undefined = undefined, 
      top: number | undefined = undefined, 
      end: number | undefined = undefined, 
      bottom: number | undefined = undefined
    ): ViewStyle => {
      const positionStyle: ViewStyle = { position };
      
      if (top !== undefined) positionStyle.top = top;
      if (bottom !== undefined) positionStyle.bottom = bottom;
      
      if (start !== undefined) {
        if (isRTL) {
          positionStyle.right = start;
        } else {
          positionStyle.left = start;
        }
      }
      
      if (end !== undefined) {
        if (isRTL) {
          positionStyle.left = end;
        } else {
          positionStyle.right = end;
        }
      }
      
      return positionStyle;
    },
    
    // Get RTL-aware flex direction
    getFlexDirection: (direction: 'row' | 'row-reverse' = 'row'): ViewStyle['flexDirection'] => {
      if (direction === 'row') {
        return isRTL ? 'row-reverse' : 'row';
      }
      return isRTL ? 'row' : 'row-reverse';
    },
    
    // Get RTL-aware text alignment
    getTextAlign: (align: 'left' | 'right' | 'center' = 'left'): TextStyle['textAlign'] => {
      if (align === 'left') {
        return isRTL ? 'right' : 'left';
      } else if (align === 'right') {
        return isRTL ? 'left' : 'right';
      }
      return align;
    },
  };
};

// Re-export RTL context hook for convenience
export { useRTLContext } from './RTLContext'; 