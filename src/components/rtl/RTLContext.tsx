import React, { createContext, useContext, useEffect, useState } from 'react';
import { I18nManager, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/services/supabase/client';
import { GlobalUserIdentifier } from '@/context/AuthContext';

interface RTLContextType {
  isRTL: boolean;
  language: string;
  setLanguage: (lang: string) => Promise<boolean>;
  resetRTLSettings: () => Promise<boolean>;
}

const RTLContext = createContext<RTLContextType | undefined>(undefined);

const STORAGE_KEYS = {
  LANGUAGE: '@app:language',
  RTL_APPLIED: '@app:rtl_applied',
  FORCE_RESTART: '@app:force_restart',
};

export const RTLProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState('en');
  const [isRTL, setIsRTL] = useState(I18nManager.isRTL);
  const [isInitialized, setIsInitialized] = useState(false);

  // Function to reset RTL settings to a known good state
  const resetRTLSettings = async () => {
    try {
      // Get the stored language from Supabase if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      let storedLanguage = 'en';
      
      if (user) {
        // Try to get language from user_settings
        const { data: settingsData } = await supabase
          .from('user_settings')
          .select('settings')
          .eq('user_id', GlobalUserIdentifier.profileId)
          .single();
          
        if (settingsData?.settings?.preferred_language) {
          storedLanguage = settingsData.settings.preferred_language;
        }
      } else {
        // Fallback to AsyncStorage if not logged in
        storedLanguage = await AsyncStorage.getItem(STORAGE_KEYS.LANGUAGE) || 'en';
      }
      
      const shouldBeRTL = storedLanguage === 'ar';
      const currentRTLState = I18nManager.isRTL;

      // Reset all RTL-related storage
      await AsyncStorage.multiSet([
        [STORAGE_KEYS.RTL_APPLIED, 'true'],
        [STORAGE_KEYS.LANGUAGE, storedLanguage],
        [STORAGE_KEYS.FORCE_RESTART, 'false'],
      ]);

      // Only force RTL settings if they need to change
      if (shouldBeRTL !== currentRTLState) {
        // Android-specific RTL reset handling
        if (Platform.OS === 'android') {
          I18nManager.allowRTL(true); // Always allow RTL first on Android
          I18nManager.forceRTL(shouldBeRTL);
          if (shouldBeRTL) {
            I18nManager.swapLeftAndRightInRTL(true);
          }
        } else {
          I18nManager.allowRTL(shouldBeRTL);
          I18nManager.forceRTL(shouldBeRTL);
        }

        setIsRTL(shouldBeRTL);
        setLanguageState(storedLanguage);

        if (__DEV__) {
          console.log('RTL settings reset complete:', { shouldBeRTL, storedLanguage, platform: Platform.OS });
        }

        return true; // Return true only if RTL settings changed
      }

      // Update state without forcing RTL changes
      setIsRTL(shouldBeRTL);
      setLanguageState(storedLanguage);

      if (__DEV__) {
        console.log('RTL settings unchanged:', { shouldBeRTL, storedLanguage });
      }
      
      return false; // Return false if no RTL changes were needed
    } catch (error) {
      console.error('Failed to reset RTL settings:', error);
      alert('Failed to reset RTL settings: '+ error);
      return false;
    }
  };

  // Function to apply RTL settings
  const applyRTLSettings = async (shouldBeRTL: boolean) => {
    try {
      if (shouldBeRTL !== I18nManager.isRTL) {
        // Android-specific RTL handling
        if (Platform.OS === 'android') {
          // For Android, we need to be more explicit about RTL settings
          I18nManager.allowRTL(true); // Always allow RTL first
          I18nManager.forceRTL(shouldBeRTL);

          // Additional Android-specific RTL enforcement
          if (shouldBeRTL) {
            // Ensure RTL is properly applied on Android
            I18nManager.swapLeftAndRightInRTL(true);
          }
        } else {
          // iOS handling
          I18nManager.allowRTL(shouldBeRTL);
          I18nManager.forceRTL(shouldBeRTL);
        }

        // Update state
        setIsRTL(shouldBeRTL);

        // Set force restart flag
        await AsyncStorage.setItem(STORAGE_KEYS.FORCE_RESTART, 'true');
        await AsyncStorage.setItem(STORAGE_KEYS.RTL_APPLIED, 'true');

        if (__DEV__) {
          console.log('RTL settings applied:', { shouldBeRTL, platform: Platform.OS });
        }

        return true;
      } else {
        setIsRTL(shouldBeRTL);
        if (__DEV__) {
          console.log('RTL settings unchanged:', { shouldBeRTL });
        }
        return false;
      }
    } catch (error) {
      console.error('Failed to apply RTL settings:', error);
      return false;
    }
  };

  // Initialize RTL settings on mount
  useEffect(() => {
    const initializeRTL = async () => {
      try {
        // Force immediate RTL application based on current language
        const storedLanguage = await AsyncStorage.getItem(STORAGE_KEYS.LANGUAGE) || 'en';
        const shouldBeRTL = storedLanguage === 'ar';
        
        // Always force RTL settings to match the language immediately
        I18nManager.allowRTL(shouldBeRTL);
        I18nManager.forceRTL(shouldBeRTL);
        setIsRTL(shouldBeRTL);
        setLanguageState(storedLanguage);

        // Check if user is logged in
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          // Get language from user_settings
          const { data: settingsData } = await supabase
            .from('user_settings')
            .select('settings')
            .eq('user_id', GlobalUserIdentifier.profileId)
            .single();
            
          if (settingsData?.settings?.preferred_language) {
            const dbLanguage = settingsData.settings.preferred_language;
            const dbShouldBeRTL = dbLanguage === 'ar';
            
            // Update RTL settings if different from current
            if (dbShouldBeRTL !== shouldBeRTL) {
              I18nManager.allowRTL(dbShouldBeRTL);
              I18nManager.forceRTL(dbShouldBeRTL);
              setIsRTL(dbShouldBeRTL);
              setLanguageState(dbLanguage);
              
              // Update AsyncStorage to match DB
              await AsyncStorage.setItem(STORAGE_KEYS.LANGUAGE, dbLanguage);
            }
          }
        }
        
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize RTL settings:', error);
        // Fallback to AsyncStorage on error
        await fallbackToAsyncStorage();
        setIsInitialized(true);
      }
    };
    
    const fallbackToAsyncStorage = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem(STORAGE_KEYS.LANGUAGE) || 'en';
        setLanguageState(storedLanguage);
        
        const shouldBeRTL = storedLanguage === 'ar';
        await applyRTLSettings(shouldBeRTL);
      } catch (error) {
        console.error('Fallback to AsyncStorage failed:', error);
        // Last resort: reset to English
        setLanguageState('en');
        await applyRTLSettings(false);
      }
    };

    initializeRTL();
  }, []);

  const setLanguage = async (lang: string) => {
    try {
      const shouldBeRTL = lang === 'ar';
      
      // Update language state
      setLanguageState(lang);
      
      // Update AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEYS.LANGUAGE, lang);
      
      // Apply RTL settings
      const needsRestart = await applyRTLSettings(shouldBeRTL);
      
      // Update Supabase if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // Update user_settings
        const { data: settingsData } = await supabase
          .from('user_settings')
          .select('settings')
          .eq('user_id', GlobalUserIdentifier.profileId)
          .single();
          
        if (settingsData) {
          const updatedSettings = {
            ...settingsData.settings,
            preferred_language: lang
          };
          
          await supabase
            .from('user_settings')
            .update({
              settings: updatedSettings,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', GlobalUserIdentifier.profileId);
        }
      }
      
      return needsRestart;
    } catch (error) {
      console.error('Failed to set language:', error);
      return false;
    }
  };

  // Don't render children until RTL is initialized
  if (!isInitialized && Platform.OS !== 'web') {
    return null;
  }

  return (
    <RTLContext.Provider 
      value={{
        isRTL,
        language,
        setLanguage,
        resetRTLSettings,
      }}
    >
      {children}
    </RTLContext.Provider>
  );
};

export const useRTLContext = () => {
  const context = useContext(RTLContext);
  if (context === undefined) {
    throw new Error('useRTLContext must be used within an RTLProvider');
  }
  return context;
};

export default RTLContext; 