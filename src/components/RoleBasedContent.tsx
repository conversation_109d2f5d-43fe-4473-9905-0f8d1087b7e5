import React from 'react';
import { View, Text } from 'react-native';
import { useRBAC } from '@/hooks/useRBAC';
import { UserRole } from '@/utils/rbac';

/**
 * Component that displays content based on the user's role
 */
export function RoleBasedContent() {
  const { role, isAdmin, isPartner, isDriver, isCustomer } = useRBAC();
  
  return (
    <View style={{ padding: 16 }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 8 }}>
        Welcome, {role} user!
      </Text>
      
      {isAdmin && (
        <View style={{ marginTop: 8 }}>
          <Text style={{ fontSize: 16, color: '#4CAF50' }}>
            Admin Dashboard: You have full access to all features
          </Text>
          <Text>• Manage all users</Text>
          <Text>• View all orders</Text>
          <Text>• Configure system settings</Text>
        </View>
      )}
      
      {isPartner && (
        <View style={{ marginTop: 8 }}>
          <Text style={{ fontSize: 16, color: '#2196F3' }}>
            Partner Portal: Manage your dumpster business
          </Text>
          <Text>• View assigned orders</Text>
          <Text>• Manage your inventory</Text>
          <Text>• Track deliveries</Text>
        </View>
      )}
      
      {isDriver && (
        <View style={{ marginTop: 8 }}>
          <Text style={{ fontSize: 16, color: '#FF9800' }}>
            Driver App: Manage your deliveries
          </Text>
          <Text>• View assigned deliveries</Text>
          <Text>• Update delivery status</Text>
          <Text>• Navigate to customer locations</Text>
        </View>
      )}
      
      {isCustomer && (
        <View style={{ marginTop: 8 }}>
          <Text style={{ fontSize: 16, color: '#9C27B0' }}>
            Customer App: Order dumpsters on demand
          </Text>
          <Text>• Browse available dumpsters</Text>
          <Text>• Place new orders</Text>
          <Text>• Track your deliveries</Text>
        </View>
      )}
    </View>
  );
}

/**
 * Component that only renders its children if the user has the required roles
 */
export function RoleGuard({ 
  allowedRoles, 
  children, 
  fallback 
}: { 
  allowedRoles: UserRole[]; 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
}) {
  const { hasAnyRole } = useRBAC();
  
  if (hasAnyRole(allowedRoles)) {
    return <>{children}</>;
  }
  
  return fallback ? <>{fallback}</> : null;
}

/**
 * Example usage of RoleGuard
 */
export function AdminOnlyFeature() {
  return (
    <RoleGuard 
      allowedRoles={[UserRole.ADMIN]} 
      fallback={<Text>You don't have permission to view this content</Text>}
    >
      <Text>This content is only visible to admins</Text>
    </RoleGuard>
  );
}

export default RoleBasedContent;
