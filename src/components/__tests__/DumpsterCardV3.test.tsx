import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DumpsterCardV3 from '../DumpsterCardV3';
import { Dumpster } from '@/types/v2/dumpster';

// Mock the context hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('DumpsterCardV3', () => {
  const mockDumpster: Dumpster = {
    id: '1',
    nameEn: '10 Yard Dumpster',
    nameAr: 'حاوية 10 ياردة',
    imageUrl: 'https://example.com/dumpster.jpg',
    length: 10,
    width: 8,
    height: 4,
    pricePerLoad: 250,
    rating: 4.5,
    reviewCount: 12,
    isAvailable: true,
    sizeId: 'size-1',
  };

  it('renders correctly with all props', () => {
    const onPress = jest.fn();
    const { getByText, getByTestId } = render(
      <DumpsterCardV3 
        dumpster={mockDumpster} 
        onPress={onPress} 
        bestFor={['Construction', 'Renovation']} 
      />
    );
    
    expect(getByText('10 Yard Dumpster')).toBeTruthy();
    expect(getByText('10m × 8m × 4m')).toBeTruthy();
    expect(getByText('﷼250')).toBeTruthy();
    expect(getByText('Construction')).toBeTruthy();
    expect(getByTestId('dumpster-card-v3')).toBeTruthy();
  });

  it('shows loading state when isLoading is true', () => {
    const { getByTestId } = render(
      <DumpsterCardV3 dumpster={mockDumpster} onPress={() => {}} isLoading={true} />
    );
    
    expect(getByTestId('dumpster-card-loading')).toBeTruthy();
  });

  it('calls onPress with the dumpster when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <DumpsterCardV3 dumpster={mockDumpster} onPress={onPress} />
    );
    
    fireEvent.press(getByTestId('dumpster-card-v3'));
    expect(onPress).toHaveBeenCalledWith(mockDumpster);
  });

  it('displays availability status correctly', () => {
    // Available dumpster
    const { getByText, rerender } = render(
      <DumpsterCardV3 
        dumpster={mockDumpster} 
        onPress={() => {}} 
      />
    );
    
    expect(getByText(/in_stock/)).toBeTruthy();
    
    // Unavailable dumpster
    const unavailableDumpster = {
      ...mockDumpster,
      isAvailable: false
    };
    
    rerender(
      <DumpsterCardV3 
        dumpster={unavailableDumpster} 
        onPress={() => {}} 
      />
    );
    
    expect(getByText('out_of_stock')).toBeTruthy();
  });

  it('displays rating badge when rating is provided', () => {
    const { getByText } = render(
      <DumpsterCardV3 dumpster={mockDumpster} onPress={() => {}} />
    );
    
    expect(getByText('4.5')).toBeTruthy();
  });

  it('handles missing data gracefully', () => {
    const minimalDumpster: Dumpster = {
      id: '1',
      nameEn: 'Minimal Dumpster',
      nameAr: '',
      sizeId: 'size-1',
      pricePerLoad: 0,
      isAvailable: true,
    } as Dumpster;
    
    const { getByText } = render(
      <DumpsterCardV3 dumpster={minimalDumpster} onPress={() => {}} />
    );
    
    expect(getByText('Minimal Dumpster')).toBeTruthy();
    expect(getByText('0m × 0m × 0m')).toBeTruthy();
    expect(getByText('﷼0')).toBeTruthy();
  });
});
