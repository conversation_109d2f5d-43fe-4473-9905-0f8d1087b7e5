import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import PartnerOfferList, { PartnerOffer } from '../PartnerOfferList';

// Mock the context hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string, options?: any) => key }),
}));

describe('PartnerOfferList', () => {
  const mockOffers: PartnerOffer[] = [
    {
      id: '1',
      partnerId: 'partner-1',
      partner: {
        id: 'partner-1',
        company_name: 'Waste Solutions Inc.',
        contact_person: '<PERSON>',
        contact_phone: '************',
        contact_email: '<EMAIL>',
        profile_id: 'profile-1',
        status: 'active',
        is_verified: true,
        service_areas: ['Area 1', 'Area 2'],
        rating: 4.5,
        profile: {
          id: 'profile-1',
          avatar_url: 'https://example.com/avatar.jpg',
          full_name: 'John Doe',
          email: '<EMAIL>',
          phone: '************',
          user_type: 'partner'
        }
      },
      price: 250,
      isAvailable: true,
      deliveryTime: '24h',
      imageUrl: 'https://example.com/partner1.jpg'
    },
    {
      id: '2',
      partnerId: 'partner-2',
      partner: {
        id: 'partner-2',
        company_name: 'EcoWaste Management',
        contact_person: 'Jane Smith',
        contact_phone: '************',
        contact_email: '<EMAIL>',
        profile_id: 'profile-2',
        status: 'active',
        is_verified: true,
        service_areas: ['Area 3'],
        rating: 4.2,
        profile: {
          id: 'profile-2',
          avatar_url: 'https://example.com/avatar2.jpg',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '************',
          user_type: 'partner'
        }
      },
      price: 200,
      isAvailable: true,
      deliveryTime: '48h',
      imageUrl: 'https://example.com/partner2.jpg'
    }
  ];

  it('renders correctly with offers', () => {
    const onSelectOffer = jest.fn();
    const { getByText, getByTestId } = render(
      <PartnerOfferList 
        offers={mockOffers} 
        isLoading={false} 
        onSelectOffer={onSelectOffer} 
        dumpsterName="10 Yard Dumpster"
      />
    );
    
    expect(getByText('partner_offers_for')).toBeTruthy();
    expect(getByText('EcoWaste Management')).toBeTruthy(); // Should show the cheaper offer first
    expect(getByText('﷼200')).toBeTruthy();
    expect(getByTestId('partner-offers-list')).toBeTruthy();
  });

  it('shows loading state when isLoading is true', () => {
    const { queryByTestId } = render(
      <PartnerOfferList 
        offers={mockOffers} 
        isLoading={true} 
        onSelectOffer={() => {}} 
      />
    );
    
    expect(queryByTestId('partner-offers-list')).toBeNull();
  });

  it('calls onSelectOffer when an offer is pressed', () => {
    const onSelectOffer = jest.fn();
    const { getByTestId } = render(
      <PartnerOfferList 
        offers={mockOffers} 
        isLoading={false} 
        onSelectOffer={onSelectOffer} 
      />
    );
    
    fireEvent.press(getByTestId('partner-offer-2')); // Press the second offer
    expect(onSelectOffer).toHaveBeenCalledWith(mockOffers[1]);
  });

  it('sorts offers by price then rating', () => {
    const { getAllByTestId } = render(
      <PartnerOfferList 
        offers={mockOffers} 
        isLoading={false} 
        onSelectOffer={() => {}} 
      />
    );
    
    const offerElements = getAllByTestId(/partner-offer-/);
    
    // First offer should be the cheaper one (EcoWaste Management)
    expect(offerElements[0].props.testID).toBe('partner-offer-2');
    
    // Second offer should be the more expensive one (Waste Solutions Inc.)
    expect(offerElements[1].props.testID).toBe('partner-offer-1');
  });

  it('displays empty state when no offers are available', () => {
    const { getByText } = render(
      <PartnerOfferList 
        offers={[]} 
        isLoading={false} 
        onSelectOffer={() => {}} 
      />
    );
    
    expect(getByText('no_partner_offers')).toBeTruthy();
  });
});
