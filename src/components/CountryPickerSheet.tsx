import React, { useState, useEffect, useMemo, forwardRef, useRef, useImperativeHandle } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  ScrollView,
  Dimensions
} from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useTranslation } from 'react-i18next';
import { NewRTLView as RTLView, NewRTLText as RTLText, NewRTLTextInput as RTLTextInput, useRTLContext } from '@/components/rtl/new-index';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import * as colors from '@/theme/colors';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/services/supabase/client';
import type { CountryPickerItem } from '@/types/countries';

// Get screen height
const screenHeight = Dimensions.get('window').height;

// Type for the functions we want to expose via the ref
export interface CountryPickerSheetRef {
  open: () => void;
  close: () => void;
}

interface CountryPickerSheetProps {
  onSelectCountry: (item: CountryPickerItem) => void;
  initialState: CountryPickerItem;
}

// Note: The first type parameter of forwardRef is now our custom Ref type
const CountryPickerSheet = forwardRef<CountryPickerSheetRef, CountryPickerSheetProps>(({ onSelectCountry, initialState }, ref) => {
  // Use the component type for the internal ref
  const sheetRef = useRef<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [countries, setCountries] = useState<CountryPickerItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { isRTL } = useRTLContext();

  // Expose methods
  useImperativeHandle(ref, () => ({
    open: () => sheetRef.current?.open(),
    close: () => sheetRef.current?.close(),
  }));

  // Fetch countries when the component mounts or language changes
  useEffect(() => {
    async function fetchCountries() {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('countries')
          .select('code, name_en, name_ar, dial_code, flag_emoji')
          .order(isRTL ? 'name_ar' : 'name_en');

        if (error) {
          console.error('Error fetching countries:', error);
          return;
        }
        setCountries(data || []);
      } catch (error) {
        console.error('Error in fetchCountries:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchCountries();
  }, [isRTL]); // Refetch if RTL changes

  const filteredCountries = useMemo(() => {
    if (!searchQuery) {
      return countries;
    }
    return countries.filter(country =>
      country.name_en.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (country.name_ar && country.name_ar.toLowerCase().includes(searchQuery.toLowerCase())) ||
      country.dial_code.includes(searchQuery)
    );
  }, [searchQuery, countries]);

  const handleSelect = (item: CountryPickerItem) => {
    onSelectCountry(item);
    // Close the sheet using the internal ref
    sheetRef.current?.close();
  };

  const handleClose = () => {
    setSearchQuery('');
  };

  return (
    <RBSheet
      ref={sheetRef}
      height={screenHeight * 0.9} // 90% of screen height
      openDuration={450}
      closeDuration={400}
      closeOnPressMask={true}
      closeOnPressBack={true}
      onClose={handleClose} // Reset search on close
      customStyles={{
        wrapper: {
          backgroundColor: isDarkMode ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.3)',
        },
        container: {
          backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          padding: 16,
        },
        draggableIcon: {
          backgroundColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          width: 40,
        }
      }}
      draggable={true} // Enable drag to close
      dragOnContent={false} // Only drag the icon
    >
      {/* Header */}
      <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 10 }}>
         {/* Close button */}
         <TouchableOpacity onPress={() => sheetRef.current?.close()} style={{ padding: 8 }}>
            <MaterialIcons name="close" size={24} color={isDarkMode ? colors.textColors.dark : colors.textColors.light} />
          </TouchableOpacity>
        <RTLText style={{
          flex: 1,
          fontSize: 18,
          fontWeight: '600',
          color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
          textAlign: 'center',
          // Adjust margin to center title correctly with close button
          marginRight: isRTL ? 0 : 40,
          marginLeft: isRTL ? 40 : 0,
        }}>
          {t('auth.countryPicker.title')}
        </RTLText>
      </View>

      {/* Search Input */}
      <View style={{
        flexDirection: isRTL ? 'row-reverse' : 'row',
        alignItems: 'center',
        backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
        borderRadius: 8,
        paddingHorizontal: 12,
        marginBottom: 16
      }}>
        <Feather name="search" size={20} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
        <RTLTextInput
          style={{
            flex: 1,
            height: 40,
            marginHorizontal: 8,
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
            textAlign: isRTL ? 'right' : 'left'
          }}
          placeholder={t('auth.countryPicker.searchPlaceholder')}
          placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Current Location Button */}
      <View style={{ marginBottom: 16 }}>
        <RTLText style={{
          fontSize: 16,
          color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
          marginBottom: 8
        }}>
          {t('auth.countryPicker.currentLocation')}
        </RTLText>
        <TouchableOpacity
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            padding: 12,
            backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light,
            borderRadius: 8
          }}
          onPress={() => handleSelect(initialState)} // Use handleSelect
        >
          <RTLText style={{ fontSize: 24, marginHorizontal: 12 }}>{initialState.flag_emoji}</RTLText>
          <RTLText style={{
            flex: 1,
            fontSize: 16,
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light
          }}>
            {isRTL ? initialState.name_ar : initialState.name_en}
          </RTLText>
          {/* Check icon logic can be simplified or removed if needed */}
          {initialState.code === 'SA' && (
            <Feather name="check" size={20} color={colors.brandColors.primary[500]} />
          )}
        </TouchableOpacity>
      </View>

      {/* Scrollable List */}
      <ScrollView showsVerticalScrollIndicator={false}>
        {isLoading ? (
          <ActivityIndicator size="large" color={colors.brandColors.primary[500]} style={{ marginTop: 20 }}/>
        ) : (
          filteredCountries.map((country, index) => (
            <TouchableOpacity
              key={country.code}
              style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
                paddingVertical: 12,
                paddingHorizontal: 8,
                borderBottomWidth: index < filteredCountries.length - 1 ? 1 : 0,
                borderBottomColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light
              }}
              onPress={() => handleSelect(country)} // Use handleSelect
            >
              <RTLText style={{ fontSize: 24, marginHorizontal: 12 }}>{country.flag_emoji}</RTLText>
              <RTLText style={{
                flex: 1,
                fontSize: 16,
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light
              }}>
                {isRTL ? country.name_ar : country.name_en}
              </RTLText>
              {/* Check icon logic can be simplified or removed if needed */}
              {country.code === initialState.code && (
                 <Feather name="check" size={20} color={colors.brandColors.primary[500]} />
              )}
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </RBSheet>
  );
});

export default CountryPickerSheet;

// Define styles if needed, or rely on inline styles and theme colors
// const styles = StyleSheet.create({
//   // Add any specific styles here
// }); 