import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';

export interface City {
  id: string;
  nameEn: string;
  nameAr: string;
  region?: string;
}

interface CitySelectorProps {
  selectedCity: City;
  onPress: () => void;
  testID?: string;
}

/**
 * CitySelector - A component that displays the current city and allows the user to change it
 *
 * Features:
 * - Displays the current city with a dropdown icon
 * - Tapping opens a bottom sheet with city options
 * - Supports RTL layouts and dark mode
 */
export default function CitySelector({
  selectedCity,
  onPress,
  testID = 'city-selector',
}: CitySelectorProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.6}
      testID={testID}
      style={[
        styles.container,
        {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          borderColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[300],
        }
      ]}
    >
      <MaterialIcons
        name="location-on"
        size={16}
        color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600]}
        style={styles.icon}
      />
      <Text
        style={[
          styles.text,
          {
            color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
            marginLeft: isRTL ? 0 : 6,
            marginRight: isRTL ? 6 : 0,
            textAlign: isRTL ? 'right' : 'left',
          }
        ]}
        numberOfLines={1}
      >
        {isRTL ? selectedCity.nameAr : selectedCity.nameEn}
      </Text>
      <MaterialIcons
        name="keyboard-arrow-down"
        size={16}
        color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600]}
        style={styles.icon}
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    minWidth: 100,
    minHeight: 36,
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  icon: {
    marginHorizontal: 2,
  }
});

// List of Saudi Arabian cities
export const SAUDI_CITIES: City[] = [
  { id: 'riyadh', nameEn: 'Riyadh', nameAr: 'الرياض', region: 'Central' },
  { id: 'jeddah', nameEn: 'Jeddah', nameAr: 'جدة', region: 'Western' },
  { id: 'mecca', nameEn: 'Mecca', nameAr: 'مكة المكرمة', region: 'Western' },
  { id: 'medina', nameEn: 'Medina', nameAr: 'المدينة المنورة', region: 'Western' },
  { id: 'dammam', nameEn: 'Dammam', nameAr: 'الدمام', region: 'Eastern' },
  { id: 'khobar', nameEn: 'Khobar', nameAr: 'الخبر', region: 'Eastern' },
  { id: 'dhahran', nameEn: 'Dhahran', nameAr: 'الظهران', region: 'Eastern' },
  { id: 'tabuk', nameEn: 'Tabuk', nameAr: 'تبوك', region: 'Northern' },
  { id: 'abha', nameEn: 'Abha', nameAr: 'أبها', region: 'Southern' },
  { id: 'taif', nameEn: 'Taif', nameAr: 'الطائف', region: 'Western' },
  { id: 'buraidah', nameEn: 'Buraidah', nameAr: 'بريدة', region: 'Central' },
  { id: 'khamis-mushait', nameEn: 'Khamis Mushait', nameAr: 'خميس مشيط', region: 'Southern' },
  { id: 'yanbu', nameEn: 'Yanbu', nameAr: 'ينبع', region: 'Western' },
  { id: 'jubail', nameEn: 'Jubail', nameAr: 'الجبيل', region: 'Eastern' },
  { id: 'najran', nameEn: 'Najran', nameAr: 'نجران', region: 'Southern' },
  { id: 'hail', nameEn: 'Hail', nameAr: 'حائل', region: 'Northern' },
  { id: 'al-hofuf', nameEn: 'Al Hofuf', nameAr: 'الهفوف', region: 'Eastern' },
  { id: 'al-mubarraz', nameEn: 'Al Mubarraz', nameAr: 'المبرز', region: 'Eastern' },
];
