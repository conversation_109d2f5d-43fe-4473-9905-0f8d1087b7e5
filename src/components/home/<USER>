import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { City } from './CitySelector';

interface FloatingCitySelectorProps {
  selectedCity: City;
  onPress: () => void;
  testID?: string;
}

/**
 * FloatingCitySelector - A standalone city selector component that can be positioned absolutely
 * to ensure it receives touch events directly without interference from other components.
 */
export default function FloatingCitySelector({
  selectedCity,
  onPress,
  testID = 'floating-city-selector',
}: FloatingCitySelectorProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.6}
      testID={testID}
      style={[
        styles.container,
        {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          borderColor: isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[300],
        }
      ]}
    >
      <MaterialIcons
        name="location-on"
        size={16}
        color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600]}
        style={styles.icon}
      />
      <Text
        style={[
          styles.text,
          {
            color: isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600],
            marginLeft: isRTL ? 0 : 6,
            marginRight: isRTL ? 6 : 0,
            textAlign: isRTL ? 'right' : 'left',
          }
        ]}
        numberOfLines={1}
      >
        {isRTL ? selectedCity.nameAr : selectedCity.nameEn}
      </Text>
      <MaterialIcons
        name="keyboard-arrow-down"
        size={16}
        color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600]}
        style={styles.icon}
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    minWidth: 100,
    minHeight: 36,
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  icon: {
    marginHorizontal: 2,
  }
});
