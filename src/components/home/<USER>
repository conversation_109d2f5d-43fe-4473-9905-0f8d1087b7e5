import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { styled } from 'nativewind';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { LinearGradient } from 'expo-linear-gradient';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);

export interface HeroCardProps {
  imageUrl: string;
  headline: string;
  subtitle: string;
  onPress?: () => void;
  testID?: string;
}

/**
 * HeroCard - A full-width card featuring an image with a soft gradient overlay, headline, and subtitle
 * Used to highlight featured partners or deals on the home screen
 */
export default function HeroCard({
  imageUrl,
  headline,
  subtitle,
  onPress,
  testID = 'hero-card',
}: HeroCardProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenWidth = Dimensions.get('window').width;

  return (
    <StyledTouchableOpacity
      className="w-full rounded-xl overflow-hidden mb-4"
      style={{ height: 160 }}
      onPress={onPress}
      activeOpacity={0.9}
      testID={testID}
    >
      <Image
        source={{ uri: imageUrl }}
        style={{ width: screenWidth - 32, height: 160 }}
        resizeMode="cover"
      />
      
      {/* Gradient overlay */}
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)']}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0.5, y: 0 }}
        end={{ x: 0.5, y: 1 }}
      />
      
      {/* Content */}
      <StyledView 
        className="absolute bottom-0 left-0 right-0 p-4"
        style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
      >
        <StyledView className="flex-1">
          <NewRTLText
            style={{
              color: 'white',
              fontSize: 20,
              fontWeight: 'bold',
              marginBottom: 4,
              textAlign: isRTL ? 'right' : 'left',
            }}
          >
            {headline}
          </NewRTLText>
          <NewRTLText
            style={{
              color: 'rgba(255,255,255,0.9)',
              fontSize: 14,
              textAlign: isRTL ? 'right' : 'left',
            }}
          >
            {subtitle}
          </NewRTLText>
        </StyledView>
      </StyledView>
    </StyledTouchableOpacity>
  );
}
