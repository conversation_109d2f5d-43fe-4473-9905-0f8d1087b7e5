import React from 'react';
import { View, TouchableOpacity, StyleSheet, TextInput, Dimensions } from 'react-native';
import { styled } from 'nativewind';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { MaterialIcons, Feather } from '@expo/vector-icons';
import Animated, { useAnimatedStyle, interpolate, Extrapolation } from 'react-native-reanimated';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledTextInput = styled(TextInput);
const StyledAnimatedView = styled(Animated.View);

export interface FilterOption {
  id: string;
  label: string;
}

export interface StickySearchBarProps {
  searchValue: string;
  onSearchChange: (text: string) => void;
  onFilterPress: () => void;
  onSortPress: () => void;
  projectTypes: FilterOption[];
  selectedProjectType: string | null;
  onProjectTypeChange: (id: string) => void;
  countOptions: FilterOption[];
  selectedCount: string | null;
  onCountChange: (id: string) => void;
  scrollY: Animated.SharedValue<number>;
  stickyThreshold: number;
  testID?: string;
}

/**
 * StickySearchBar - A search and filter bar that sticks to the top of the screen when scrolling
 * Contains a search input, filter button, and two pickers for project type and count/size
 */
export default function StickySearchBar({
  searchValue,
  onSearchChange,
  onFilterPress,
  onSortPress,
  projectTypes,
  selectedProjectType,
  onProjectTypeChange,
  countOptions,
  selectedCount,
  onCountChange,
  scrollY,
  stickyThreshold,
  testID = 'sticky-search-bar',
}: StickySearchBarProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenWidth = Dimensions.get('window').width;

  // Animated styles for sticky behavior
  const animatedContainerStyle = useAnimatedStyle(() => {
    const elevation = interpolate(
      scrollY.value,
      [stickyThreshold - 10, stickyThreshold],
      [0, 4],
      Extrapolation.CLAMP
    );

    return {
      shadowOpacity: interpolate(
        scrollY.value,
        [stickyThreshold - 10, stickyThreshold],
        [0, 0.2],
        Extrapolation.CLAMP
      ),
      elevation,
      zIndex: 1020,
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      transform: [
        {
          translateY: interpolate(
            scrollY.value,
            [0, stickyThreshold],
            [0, 0],
            Extrapolation.CLAMP
          ),
        },
      ],
    };
  });

  return (
    <StyledAnimatedView
      style={[animatedContainerStyle]}
      className={`px-4 py-3 ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}
      testID={testID}
    >
      {/* Search Input */}
      <NewRTLView  style={{flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 12, borderRadius: 100 , paddingHorizontal: 12, paddingVertical: 8, backgroundColor: isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light}}>
        <Feather name="search" size={20} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
        <StyledTextInput
          className="flex-1 ml-2 text-base"
          placeholder="Search dumpsters..."
          placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
          value={searchValue}
          onChangeText={onSearchChange}
          style={{ 
            color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
            textAlign: isRTL ? 'right' : 'left',
          }}
        />
        <StyledTouchableOpacity onPress={onFilterPress}>
          <MaterialIcons name="filter-list" size={24} color={colors.brandColors.primary[500]} />
        </StyledTouchableOpacity>
      </NewRTLView>

      {/* Pickers Row */}
      <NewRTLView style={{flexDirection: isRTL ? 'row-reverse' : 'row', justifyContent: 'space-between', alignItems: 'center'}}>
        {/* Project Type Picker */}
        <StyledTouchableOpacity
          className="flex-row items-center justify-between rounded-lg border border-gray-300 dark:border-gray-700 px-3 py-2"
          style={{ width: '48%' }}
          onPress={() => {
            // Show project type picker
            if (projectTypes.length > 0) {
              const firstType = projectTypes[0].id;
              onProjectTypeChange(selectedProjectType === firstType ? '' : firstType);
            }
          }}
        >
          <NewRTLText
            style={{
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              fontSize: 14,
            }}
          >
            {selectedProjectType 
              ? projectTypes.find(t => t.id === selectedProjectType)?.label || 'Project type'
              : 'Project type'}
          </NewRTLText>
          <MaterialIcons name="arrow-drop-down" size={24} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
        </StyledTouchableOpacity>

        {/* Count/Size Picker */}
        <StyledTouchableOpacity
          className="flex-row items-center justify-between rounded-lg border border-gray-300 dark:border-gray-700 px-3 py-2"
          style={{ width: '48%' }}
          onPress={() => {
            // Show count picker
            if (countOptions.length > 0) {
              const firstCount = countOptions[0].id;
              onCountChange(selectedCount === firstCount ? '' : firstCount);
            }
          }}
        >
          <NewRTLText
            style={{
              color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
              fontSize: 14,
            }}
          >
            {selectedCount 
              ? countOptions.find(c => c.id === selectedCount)?.label || 'Count'
              : 'Count'}
          </NewRTLText>
          <MaterialIcons name="arrow-drop-down" size={24} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
        </StyledTouchableOpacity>

        {/* Sort Button */}
        <StyledTouchableOpacity
          className="ml-2"
          onPress={onSortPress}
        >
          <MaterialIcons name="sort" size={24} color={colors.brandColors.primary[500]} />
        </StyledTouchableOpacity>
      </NewRTLView>
    </StyledAnimatedView>
  );
}
