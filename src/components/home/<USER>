import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useRTLContext } from '@/components/rtl/new-index';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { DumpsterGroup } from '@/hooks/v2/useDumpsterGroups';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

interface DumpsterGroupCardProps {
  group?: DumpsterGroup;
  dumpster?: any; // Legacy support
  onPress: (group: DumpsterGroup | any) => void;
  isLoading?: boolean;
  bestFor?: string[];
  availableCount?: number;
  testID?: string;
}

/**
 * DumpsterGroupCard - A card component for displaying dumpster group information
 * 
 * Features:
 * - Displays dumpster image, name/size, lowest price, best use cases, and available count
 * - Supports RTL layouts and dark mode
 * - Shows loading state with shimmer effect
 */
export default function DumpsterGroupCard({
  group,
  dumpster, // Legacy support
  onPress,
  isLoading = false,
  bestFor = [],
  availableCount = 0,
  testID = 'dumpster-group-card',
}: DumpsterGroupCardProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Handle loading state
  if (isLoading) {
    return (
      <StyledView
        className="rounded-xl overflow-hidden bg-white dark:bg-gray-800 mb-3 mx-1"
        style={styles.cardShadow}
        testID={`${testID}-loading`}
      >
        <StyledView className="h-40 justify-center items-center">
          <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
        </StyledView>
      </StyledView>
    );
  }

  // Use group data if available, otherwise fall back to legacy dumpster data
  const data = group || dumpster;

  // Extract data with fallbacks
  const name = group ? (isRTL ? group.sizeNameAr : group.sizeName) : (isRTL ? data?.nameAr : data?.nameEn);
  const price = group ? group.minPrice : (data?.pricePerLoad || 0);
  const isAvailable = true; // Groups are always available if they have dumpsters
  const stockCount = group ? group.dumpsterCount : (availableCount > 0 ? availableCount : 1);
  const bestForTags = group ? group.bestFor : bestFor;

  // Image with fallback
  const imageUrl = group ? group.imageUrl : (data?.imageUrl || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png');

  return (
    <StyledTouchableOpacity
      className="rounded-xl overflow-hidden mb-3 mx-1"

      style={[styles.cardShadow, { backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }]}
      onPress={() => onPress(data)}
      activeOpacity={0.7}
      testID={testID}
    >
      <StyledView className="flex-col h-[200px]">
        {/* Thumbnail */}
        <StyledView className="h-1/2 relative">
          {imageLoading && (
            <StyledView className="justify-center items-center z-10" style={{ position: 'absolute', top: '50%', right: '50%' }}>
              <ActivityIndicator size="small" color={colors.brandColors.primary[500]} />
            </StyledView>
          )}
          <Image
            source={{ uri: imageUrl }}

            style={{ width: '100%', height: '100%' }}
            resizeMode="cover"
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
            onError={() => setImageError(true)}
          />
        </StyledView>

        {/* Content */}
        <StyledView className="flex-1 p-3 justify-between">
          {/* Title and size */}
          <StyledView>
            <StyledText className="text-base font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }} numberOfLines={1}>
              {name}
            </StyledText>
            <StyledText className="text-sm mt-1" style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }} numberOfLines={1}>
              best for small projects
            </StyledText>
          </StyledView>

          {/* Tags */}
          {bestForTags.length > 0 && (
            <StyledView className="flex-row flex-wrap mt-1">
              {bestForTags.slice(0, 2).map((tag, index) => (
                <StyledView
                  key={index}
                  className="bg-blue-100 dark:bg-blue-900 rounded-full px-2 py-0.5 mr-1 mb-1"
                >
                  <StyledText className="text-xs text-blue-800 dark:text-blue-100">
                    {tag}
                  </StyledText>
                </StyledView>
              ))}
            </StyledView>
          )}

          {/* Price and availability */}
          <StyledView className="flex-row justify-between items-center mt-1">
            <StyledText className="text-lg font-bold text-primary-600 dark:text-primary-400">
              <Image
                source={require('assets/images/currency/Saudi_Riyal_Symbol_primary.png')}
                style={{ width: 16, height: 16, marginRight: 2 }}
                resizeMode="contain"
              />
              {price.toLocaleString()}
            </StyledText>

            <StyledView className="flex-row items-center">
              <MaterialIcons name="check-circle" size={16} color={colors.brandColors.success[500]} />
              <StyledText className="text-xs text-success-600 dark:text-success-400 ml-1">
                {stockCount}
              </StyledText>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledView>
    </StyledTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  cardShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});
