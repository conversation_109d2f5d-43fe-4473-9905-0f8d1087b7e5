import React, { useState, useRef, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, TextInput, Dimensions } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { City } from './CitySelector';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useTranslation } from 'react-i18next';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledTextInput = styled(TextInput);
const StyledFlatList = styled(FlatList);

interface CityBottomSheetProps {
  cities: City[];
  selectedCity: City;
  onSelectCity: (city: City) => void;
  sheetRef: React.RefObject<any>;
}

/**
 * CityBottomSheet - A bottom sheet component for selecting a city
 *
 * Features:
 * - Displays a list of cities grouped by region
 * - Allows searching for cities
 * - Supports RTL layouts and dark mode
 */
export default function CityBottomSheet({
  cities,
  selectedCity,
  onSelectCity,
  sheetRef,
}: CityBottomSheetProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [searchQuery, setSearchQuery] = useState('');
  const screenHeight = Dimensions.get('window').height;

  // Filter cities based on search query
  const filteredCities = searchQuery.trim() === ''
    ? cities
    : cities.filter(city => {
        const query = searchQuery.toLowerCase();
        return (
          city.nameEn.toLowerCase().includes(query) ||
          city.nameAr.includes(query) ||
          (city.region && city.region.toLowerCase().includes(query))
        );
      });

  // Group cities by region
  const groupedCities = filteredCities.reduce((acc, city) => {
    const region = city.region || 'Other';
    if (!acc[region]) {
      acc[region] = [];
    }
    acc[region].push(city);
    return acc;
  }, {} as Record<string, City[]>);

  // Sort regions alphabetically
  const sortedRegions = Object.keys(groupedCities).sort();

  // Flatten grouped cities for rendering
  const sections = sortedRegions.map(region => ({
    region,
    data: groupedCities[region],
  }));

  // Make sure the selected city is highlighted
  useEffect(() => {
    // If the bottom sheet is opened, scroll to the selected city
    const timer = setTimeout(() => {
      try {
        const selectedRegion = selectedCity.region || 'Other';
        const regionIndex = sortedRegions.indexOf(selectedRegion);
        if (regionIndex >= 0) {
          // Scroll to the region section
          // This is a simple approximation - in a real app you'd calculate exact positions
          const sectionHeight = 40; // Approximate height of section header
          const itemHeight = 56; // Approximate height of city item

          // Calculate items before the selected region
          let itemsBeforeRegion = 0;
          for (let i = 0; i < regionIndex; i++) {
            itemsBeforeRegion += groupedCities[sortedRegions[i]].length;
          }

          // Calculate position
          const position = (regionIndex * sectionHeight) + (itemsBeforeRegion * itemHeight);

          // Scroll to position with a slight delay to ensure the list is rendered
          setTimeout(() => {
            // This would be implemented with a proper ref to the FlatList
          }, 100);
        }
      } catch (error) {
        console.log('Error scrolling to selected city:', error);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const renderCityItem = ({ item }: { item: City }) => {
    const isSelected = item.id === selectedCity.id;

    return (
      <StyledTouchableOpacity
        className={`p-4 flex-row justify-between items-center ${
          isSelected ? 'bg-primary-50 dark:bg-primary-900' : ''
        }`}
        onPress={() => {
          onSelectCity(item);
          sheetRef.current?.close();
        }}
        testID={`city-item-${item.id}`}
      >
        <NewRTLText
          style={{
            fontSize: 16,
            fontWeight: isSelected ? '600' : '400',
            color: isSelected
              ? (isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600])
              : (isDarkMode ? colors.textColors.dark : colors.textColors.light),
          }}
        >
          {isRTL ? item.nameAr : item.nameEn}
        </NewRTLText>

        {isSelected && (
          <MaterialIcons
            name="check"
            size={20}
            color={isDarkMode ? colors.brandColors.primary[300] : colors.brandColors.primary[600]}
          />
        )}
      </StyledTouchableOpacity>
    );
  };

  const renderSectionHeader = ({ region }: { region: string }) => (
    <StyledView className="px-4 py-2 bg-gray-100 dark:bg-gray-800">
      <StyledText className="text-sm font-medium text-gray-600 dark:text-gray-400">
        {region}
      </StyledText>
    </StyledView>
  );

  return (
    <RBSheet
      ref={sheetRef}
      closeOnDragDown={true}
      closeOnPressMask={true}
      height={screenHeight * 0.7}
      customStyles={{
        wrapper: {
          backgroundColor: 'rgba(0,0,0,0.5)',
        },
        container: {
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
          paddingTop: 10,
        },
        draggableIcon: {
          backgroundColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          width: 60,
          height: 6,
          marginBottom: 10,
        },
      }}
      animationType="slide"
    >
      <StyledView className="flex-1">
        {/* Header */}
        <StyledView className="px-4 pt-2 pb-4">
          <StyledText className="text-xl font-bold text-gray-800 dark:text-white">
            {t('select_city')}
          </StyledText>

          {/* Search Input */}
          <NewRTLView style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            alignItems: 'center',
            marginTop: 12,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 8,
            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}>
            <MaterialIcons
              name="search"
              size={20}
              color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
            />
            <StyledTextInput
              className="flex-1 ml-2 text-base"
              placeholder={t('search_cities')}
              placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
              value={searchQuery}
              onChangeText={setSearchQuery}
              style={{
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                textAlign: isRTL ? 'right' : 'left',
                marginLeft: isRTL ? 0 : 8,
                marginRight: isRTL ? 8 : 0,
              }}
            />
            {searchQuery.length > 0 && (
              <StyledTouchableOpacity onPress={() => setSearchQuery('')}>
                <MaterialIcons
                  name="close"
                  size={20}
                  color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
                />
              </StyledTouchableOpacity>
            )}
          </NewRTLView>
        </StyledView>

        {/* City List */}
        <StyledFlatList
          data={sections}
          keyExtractor={(item) => item.region}
          renderItem={({ item }) => (
            <StyledView>
              {renderSectionHeader(item)}
              {item.data.map((city) => (
                <React.Fragment key={city.id}>
                  {renderCityItem({ item: city })}
                </React.Fragment>
              ))}
            </StyledView>
          )}
          showsVerticalScrollIndicator={false}
        />
      </StyledView>
    </RBSheet>
  );
}
