import React from 'react';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useTranslation } from 'react-i18next';
import { SORT_OPTIONS, SortOption } from './FilterBottomSheet';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

export interface SortBottomSheetProps {
  sheetRef: React.RefObject<any>;
  selectedSortOption: string | null;
  onSortOptionChange: (sortOption: string) => void;
}

/**
 * SortBottomSheet - A bottom sheet component for sorting dumpsters
 *
 * Features:
 * - Sort by price (low to high, high to low)
 * - Sort by size (small to large, large to small)
 */
export default function SortBottomSheet({
  sheetRef,
  selectedSortOption,
  onSortOptionChange,
}: SortBottomSheetProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenHeight = Dimensions.get('window').height;

  // Handle sort option selection
  const handleSortOptionSelect = (sortOptionId: string) => {
    onSortOptionChange(sortOptionId);
    if (sheetRef.current) {
      sheetRef.current.close();
    }
  };

  return (
    <RBSheet
      ref={sheetRef}
      closeOnDragDown={true}
      closeOnPressMask={true}
      height={screenHeight * 0.4}
      customStyles={{
        wrapper: {
          backgroundColor: 'rgba(0,0,0,0.5)',
        },
        container: {
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
          paddingTop: 10,
        },
        draggableIcon: {
          backgroundColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          width: 60,
          height: 6,
          marginBottom: 10,
        },
      }}
      animationType="slide"
    >
      <StyledView className="flex-1 px-4">
        {/* Header */}
        <StyledText className="text-xl font-bold py-2" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
          {t('sortBy', 'Sort By')}
        </StyledText>

        {/* Sort Options */}
        <StyledView className="mt-2">
          {SORT_OPTIONS.map((sortOption: SortOption) => (
            <StyledTouchableOpacity
              key={sortOption.id}
              className="py-4 border-b"
              style={{
                borderBottomColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
              }}
              onPress={() => handleSortOptionSelect(sortOption.id)}
            >
              <NewRTLView style={{
                flexDirection: isRTL ? 'row-reverse' : 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
                <NewRTLText style={{
                  fontSize: 16,
                  color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                }}>
                  {isRTL ? sortOption.nameAr : sortOption.nameEn}
                </NewRTLText>
                {selectedSortOption === sortOption.id && (
                  <MaterialIcons
                    name="check"
                    size={24}
                    color={colors.brandColors.primary[500]}
                  />
                )}
              </NewRTLView>
            </StyledTouchableOpacity>
          ))}
        </StyledView>
      </StyledView>
    </RBSheet>
  );
}
