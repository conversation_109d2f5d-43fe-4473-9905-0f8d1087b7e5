import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import { styled } from 'nativewind';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useRTLContext } from '@/components/rtl/new-index';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';
import { Dumpster } from '@/types/v2/dumpster';
import { Surface } from 'react-native-paper';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledSurface = styled(Surface);

interface DumpsterTileProps {
  dumpster: Dumpster;
  onPress: (dumpster: Dumpster) => void;
  isLoading?: boolean;
  testID?: string;
}

/**
 * DumpsterTile - A card component for displaying dumpster information in a grid
 * 
 * Features:
 * - Displays dumpster thumbnail, title, and secondary text
 * - Shows loading state with placeholder
 * - Supports RTL layouts and dark mode
 */
export default function DumpsterTile({
  dumpster,
  onPress,
  isLoading = false,
  testID = 'dumpster-tile',
}: DumpsterTileProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  if (isLoading) {
    return (
      <StyledSurface
        className="rounded-xl overflow-hidden bg-gray-100 dark:bg-gray-800 mb-3 mx-1"
        elevation={1}
        testID={`${testID}-loading`}
      >
        <StyledView className="h-32 bg-gray-200 dark:bg-gray-700 justify-center items-center">
          <ActivityIndicator size="small" color={colors.brandColors.primary[500]} />
        </StyledView>
        <StyledView className="p-2">
          <StyledView className="h-4 w-3/4 bg-gray-300 dark:bg-gray-600 rounded mb-1" />
          <StyledView className="h-3 w-1/2 bg-gray-300 dark:bg-gray-600 rounded" />
        </StyledView>
      </StyledSurface>
    );
  }

  // Extract dumpster data with fallbacks
  const name = isRTL ? dumpster.nameAr : dumpster.nameEn;
  const price = dumpster.pricePerLoad || 0;
  
  // Dimensions with fallbacks
  const length = dumpster.length || 0;
  const width = dumpster.width || 0;
  const height = dumpster.height || 0;
  
  // Image with fallback
  const imageUrl = dumpster.imageUrl || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';

  return (
    <StyledTouchableOpacity
      className="rounded-xl overflow-hidden bg-white dark:bg-gray-800 mb-3 mx-1"
      style={styles.cardShadow}
      onPress={() => onPress(dumpster)}
      activeOpacity={0.7}
      testID={testID}
    >
      {/* Thumbnail */}
      <StyledView className="h-32 relative">
        {imageLoading && (
          <StyledView className="absolute inset-0 justify-center items-center bg-gray-200 dark:bg-gray-700 z-10">
            <ActivityIndicator size="small" color={colors.brandColors.primary[500]} />
          </StyledView>
        )}
        
        {imageError ? (
          <StyledView className="h-full justify-center items-center bg-gray-200 dark:bg-gray-700">
            <MaterialCommunityIcons name="image-off" size={32} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
          </StyledView>
        ) : (
          <Image
            source={{ uri: imageUrl }}
            className="w-full h-full"
            resizeMode="cover"
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
            onError={() => setImageError(true)}
          />
        )}
      </StyledView>
      
      {/* Footer */}
      <StyledView className="p-2">
        <StyledText 
          className="text-sm font-medium"
          style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}
          numberOfLines={1}
        >
          {name}
        </StyledText>
        <StyledText 
          className="text-xs"
          style={{ color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}
          numberOfLines={1}
        >
          {`${length}m × ${width}m × ${height}m • ﷼${price}`}
        </StyledText>
      </StyledView>
    </StyledTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  cardShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
});
