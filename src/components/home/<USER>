import React, { useEffect, useRef, useState } from 'react';
import { View, Image, Dimensions, FlatList, TouchableOpacity, StyleSheet } from 'react-native';
import { styled } from 'nativewind';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { useTranslation } from 'react-i18next';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);

export interface BannerItem {
  id: string;
  imageUrl: string;
  title: string;
  subtitle?: string;
  actionUrl?: string;
}

interface MarketingCarouselProps {
  banners: BannerItem[];
  autoSlideInterval?: number; // in milliseconds
  onBannerPress?: (banner: BannerItem) => void;
}

export default function MarketingCarousel({
  banners,
  autoSlideInterval = 5000, // Default to 5 seconds
  onBannerPress,
}: MarketingCarouselProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const { t } = useTranslation();
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const screenWidth = Dimensions.get('window').width;
  const autoSlideTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-slide functionality
  useEffect(() => {
    if (banners.length <= 1) return;

    const startAutoSlide = () => {
      autoSlideTimerRef.current = setInterval(() => {
        const nextIndex = (activeIndex + 1) % banners.length;
        setActiveIndex(nextIndex);
        flatListRef.current?.scrollToIndex({
          index: nextIndex,
          animated: true,
        });
      }, autoSlideInterval);
    };

    startAutoSlide();

    return () => {
      if (autoSlideTimerRef.current) {
        clearInterval(autoSlideTimerRef.current);
      }
    };
  }, [activeIndex, autoSlideInterval, banners.length]);

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffsetX / screenWidth);
    if (newIndex !== activeIndex) {
      setActiveIndex(newIndex);
    }
  };

  const handleDotPress = (index: number) => {
    setActiveIndex(index);
    flatListRef.current?.scrollToIndex({
      index,
      animated: true,
    });
  };

  const handleBannerPress = (banner: BannerItem) => {
    if (onBannerPress) {
      onBannerPress(banner);
    }
  };

  const renderItem = ({ item }: { item: BannerItem }) => (
    <StyledTouchableOpacity
      className="w-full"
      style={{ width: screenWidth , borderRadius: 12 }}
      onPress={() => handleBannerPress(item)}
      activeOpacity={0.9}
      testID={`banner-${item.id}`}
    >
      <Image
        source={{ uri: item.imageUrl }}
        style={{ width: screenWidth, height: 180 }}
        resizeMode="cover"
        accessibilityLabel={item.title}
      />
      <StyledView
        className="absolute bottom-0 left-0 right-0 p-4"
        style={{
          backgroundColor: 'rgba(0,0,0,0.4)',
        }}
      >
        <NewRTLText
          style={{
            color: colors.textColors.dark,
            fontSize: 16,
            fontWeight: 'bold',
            textAlign: isRTL ? 'right' : 'left',
          }}
        >
          {item.title}
        </NewRTLText>
        {item.subtitle && (
          <NewRTLText
            style={{
              color: colors.textColors.dark,
              fontSize: 14,
              textAlign: isRTL ? 'right' : 'left',
            }}
          >
            {item.subtitle}
          </NewRTLText>
        )}
      </StyledView>
    </StyledTouchableOpacity>
  );

  // If no banners, don't render anything
  if (!banners || banners.length === 0) {
    return null;
  }

  return (
    <StyledView className="mb-4">
      <FlatList
        ref={flatListRef}
        data={banners}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        testID="marketing-carousel"
        style={{ borderRadius: 12 }}
      />
      
      {/* Pagination dots */}
      {banners.length > 1 && (
        <StyledView className="flex-row justify-center mt-2">
          {banners.map((_, index) => (
            <StyledTouchableOpacity
              key={index}
              className={`mx-1 rounded-full w-2 h-2 ${
                index === activeIndex ? 'bg-primary-500' : isDarkMode ? 'bg-gray-600' : 'bg-gray-300'
              }`}
              onPress={() => handleDotPress(index)}
              testID={`dot-${index}`}
            />
          ))}
        </StyledView>
      )}
    </StyledView>
  );
}

// Jest test stub
/* 
import { render, fireEvent } from '@testing-library/react-native';
import MarketingCarousel, { BannerItem } from '../MarketingCarousel';

const mockBanners: BannerItem[] = [
  {
    id: '1',
    imageUrl: 'https://example.com/banner1.jpg',
    title: 'Banner 1',
    subtitle: 'Subtitle 1',
  },
  {
    id: '2',
    imageUrl: 'https://example.com/banner2.jpg',
    title: 'Banner 2',
  },
];

describe('MarketingCarousel', () => {
  it('renders correctly with banners', () => {
    const { getByTestId } = render(<MarketingCarousel banners={mockBanners} />);
    expect(getByTestId('marketing-carousel')).toBeTruthy();
    expect(getByTestId('banner-1')).toBeTruthy();
  });

  it('handles banner press', () => {
    const onBannerPress = jest.fn();
    const { getByTestId } = render(
      <MarketingCarousel banners={mockBanners} onBannerPress={onBannerPress} />
    );
    fireEvent.press(getByTestId('banner-1'));
    expect(onBannerPress).toHaveBeenCalledWith(mockBanners[0]);
  });
});
*/
