import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons, Feather } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useTranslation } from 'react-i18next';
import { Chip, Divider } from 'react-native-paper';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);
const StyledChip = styled(Chip);
const StyledDivider = styled(Divider);

// Waste types
export interface WasteType {
  id: string;
  nameEn: string;
  nameAr: string;
}

// Mock waste types - in production, these would come from an API
export const WASTE_TYPES: WasteType[] = [
  { id: 'construction', nameEn: 'Construction', nameAr: 'بناء' },
  { id: 'household', nameEn: 'Household', nameAr: 'منزلي' },
  { id: 'green', nameEn: 'Green Waste', nameAr: 'نفايات خضراء' },
  { id: 'metal', nameEn: 'Metal', nameAr: 'معدن' },
  { id: 'wood', nameEn: 'Wood', nameAr: 'خشب' },
  { id: 'concrete', nameEn: 'Concrete', nameAr: 'خرسانة' },
  { id: 'plastic', nameEn: 'Plastic', nameAr: 'بلاستيك' },
  { id: 'mixed', nameEn: 'Mixed', nameAr: 'مختلط' },
];

// Project types
export interface ProjectType {
  id: string;
  nameEn: string;
  nameAr: string;
  description: string;
}

// Mock project types
export const PROJECT_TYPES: ProjectType[] = [
  { 
    id: 'very-small', 
    nameEn: 'Very Small Project', 
    nameAr: 'مشروع صغير جدا',
    description: 'Bathroom renovation, small repairs'
  },
  { 
    id: 'small', 
    nameEn: 'Small Project', 
    nameAr: 'مشروع صغير',
    description: 'Room renovation, small landscaping'
  },
  { 
    id: 'medium', 
    nameEn: 'Medium Project', 
    nameAr: 'مشروع متوسط',
    description: 'House or villa renovation'
  },
  { 
    id: 'large', 
    nameEn: 'Large Project', 
    nameAr: 'مشروع كبير',
    description: 'Demolition, 3-5 floor building renovation'
  },
  { 
    id: 'business', 
    nameEn: 'Business', 
    nameAr: 'أعمال',
    description: 'Restaurant, supermarket, commercial'
  },
];

// Count options
export interface CountOption {
  id: string;
  value: number;
  nameEn: string;
  nameAr: string;
}

// Mock count options
export const COUNT_OPTIONS: CountOption[] = [
  { id: 'count-1', value: 1, nameEn: '1 Dumpster', nameAr: 'حاوية واحدة' },
  { id: 'count-2', value: 2, nameEn: '2 Dumpsters', nameAr: 'حاويتان' },
  { id: 'count-3', value: 3, nameEn: '3 Dumpsters', nameAr: '3 حاويات' },
];

// Sort options
export interface SortOption {
  id: string;
  nameEn: string;
  nameAr: string;
}

// Mock sort options
export const SORT_OPTIONS: SortOption[] = [
  { id: 'price-asc', nameEn: 'Price: Low to High', nameAr: 'السعر: من الأقل إلى الأعلى' },
  { id: 'price-desc', nameEn: 'Price: High to Low', nameAr: 'السعر: من الأعلى إلى الأقل' },
  { id: 'size-asc', nameEn: 'Size: Small to Large', nameAr: 'الحجم: من الصغير إلى الكبير' },
  { id: 'size-desc', nameEn: 'Size: Large to Small', nameAr: 'الحجم: من الكبير إلى الصغير' },
];

export interface FilterBottomSheetProps {
  sheetRef: React.RefObject<any>;
  selectedWasteTypes: string[];
  onWasteTypeChange: (wasteTypes: string[]) => void;
  selectedProjectType: string | null;
  onProjectTypeChange: (projectType: string | null) => void;
  selectedCount: string | null;
  onCountChange: (count: string | null) => void;
  onApplyFilters: () => void;
  onResetFilters: () => void;
}

/**
 * FilterBottomSheet - A bottom sheet component for filtering dumpsters
 *
 * Features:
 * - Waste type selection
 * - Project type selection
 * - Count selection
 * - Apply and reset filters
 */
export default function FilterBottomSheet({
  sheetRef,
  selectedWasteTypes,
  onWasteTypeChange,
  selectedProjectType,
  onProjectTypeChange,
  selectedCount,
  onCountChange,
  onApplyFilters,
  onResetFilters,
}: FilterBottomSheetProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const screenHeight = Dimensions.get('window').height;

  // Toggle waste type selection
  const toggleWasteType = (wasteTypeId: string) => {
    if (selectedWasteTypes.includes(wasteTypeId)) {
      onWasteTypeChange(selectedWasteTypes.filter(id => id !== wasteTypeId));
    } else {
      onWasteTypeChange([...selectedWasteTypes, wasteTypeId]);
    }
  };

  // Handle apply filters
  const handleApplyFilters = () => {
    onApplyFilters();
    if (sheetRef.current) {
      sheetRef.current.close();
    }
  };

  // Handle reset filters
  const handleResetFilters = () => {
    onResetFilters();
  };

  return (
    <RBSheet
      ref={sheetRef}
      closeOnDragDown={true}
      closeOnPressMask={true}
      height={screenHeight * 0.9}
      customStyles={{
        wrapper: {
          backgroundColor: 'rgba(0,0,0,0.5)',
        },
        container: {
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          paddingTop: 10,
        },
        draggableIcon: {
          backgroundColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
          width: 60,
          height: 6,
          marginBottom: 10,
        },
      }}
      animationType="slide"
    >
      <StyledView className="flex-1 px-4">
        {/* Header */}
        <StyledView className="flex-row justify-between items-center py-2">
          <StyledText className="text-xl font-bold" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
            {t('filters', 'Filters')}
          </StyledText>
          <StyledTouchableOpacity onPress={handleResetFilters}>
            <StyledText style={{ color: colors.brandColors.primary[500] }}>
              {t('reset', 'Reset')}
            </StyledText>
          </StyledTouchableOpacity>
        </StyledView>

        <StyledScrollView showsVerticalScrollIndicator={false} className="flex-1">
          {/* Waste Types Section */}
          <StyledView className="mt-4">
            <StyledText className="text-lg font-semibold mb-2" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
              {t('wasteTypes', 'Waste Types')}
            </StyledText>
            <StyledView className="flex-row flex-wrap">
              {WASTE_TYPES.map((wasteType) => (
                <StyledChip
                  key={wasteType.id}
                  selected={selectedWasteTypes.includes(wasteType.id)}
                  onPress={() => toggleWasteType(wasteType.id)}
                  style={{
                    margin: 4,
                    backgroundColor: selectedWasteTypes.includes(wasteType.id)
                      ? colors.brandColors.primary[100]
                      : isDarkMode ? colors.surfaceColors.dark : colors.surfaceColors.light,
                  }}
                  textStyle={{
                    color: selectedWasteTypes.includes(wasteType.id)
                      ? colors.brandColors.primary[700]
                      : isDarkMode ? colors.textColors.dark : colors.textColors.light,
                  }}
                  
                >
                  {isRTL ? wasteType.nameAr : wasteType.nameEn}
                </StyledChip>
              ))}
            </StyledView>
          </StyledView>

          <StyledDivider className="my-4" />

          {/* Project Types Section */}
          <StyledView className="mt-2">
            <StyledText className="text-lg font-semibold mb-2" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
              {t('projectType', 'Project Type')}
            </StyledText>
            <StyledView>
              {PROJECT_TYPES.map((projectType) => (
                <StyledTouchableOpacity
                  key={projectType.id}
                  className="py-3"
                  onPress={() => onProjectTypeChange(
                    selectedProjectType === projectType.id ? null : projectType.id
                  )}
                >
                  <NewRTLView style={{
                    flexDirection: isRTL ? 'row-reverse' : 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                    <StyledView>
                      <NewRTLText style={{
                        fontSize: 16,
                        fontWeight: '500',
                        color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                      }}>
                        {isRTL ? projectType.nameAr : projectType.nameEn}
                      </NewRTLText>
                      <NewRTLText style={{
                        fontSize: 14,
                        color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light,
                      }}>
                        {projectType.description}
                      </NewRTLText>
                    </StyledView>
                    {selectedProjectType === projectType.id && (
                      <MaterialIcons
                        name="check-circle"
                        size={24}
                        color={colors.brandColors.primary[500]}
                      />
                    )}
                  </NewRTLView>
                </StyledTouchableOpacity>
              ))}
            </StyledView>
          </StyledView>

          <StyledDivider className="my-4" />

          {/* Count Section */}
          {/* <StyledView className="mt-2">
            <StyledText className="text-lg font-semibold mb-2" style={{ color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
              {t('count', 'Number of Dumpsters')}
            </StyledText>
            <StyledView className="flex-row justify-between">
              {COUNT_OPTIONS.map((countOption) => (
                <StyledTouchableOpacity
                  key={countOption.id}
                  className="py-2 px-4 rounded-full"
                  style={{
                    backgroundColor: selectedCount === countOption.id
                      ? colors.brandColors.primary[500]
                      : isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
                    borderWidth: 1,
                    borderColor: selectedCount === countOption.id
                      ? colors.brandColors.primary[500]
                      : isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
                  }}
                  onPress={() => onCountChange(
                    selectedCount === countOption.id ? null : countOption.id
                  )}
                >
                  <StyledText
                    style={{
                      color: selectedCount === countOption.id
                        ? 'white'
                        : isDarkMode ? colors.textColors.dark : colors.textColors.light,
                      fontWeight: selectedCount === countOption.id ? 'bold' : 'normal',
                    }}
                  >
                    {isRTL ? countOption.nameAr : countOption.nameEn}
                  </StyledText>
                </StyledTouchableOpacity>
              ))}
            </StyledView>
          </StyledView> */}
        </StyledScrollView>

        {/* Apply Button */}
        <StyledView className="py-4 mb-6">
          <StyledTouchableOpacity 
            disabled={selectedWasteTypes.length === 0 && !selectedProjectType && !selectedCount}
            className="py-3 rounded-full"
            style={{
              backgroundColor: (selectedWasteTypes.length === 0 && !selectedProjectType && !selectedCount)
                ? colors.brandColors.primary[100]
                : colors.brandColors.primary[500],
            }}
            onPress={handleApplyFilters}
          >
            <StyledText className="text-center text-white font-bold">
              {t('applyFilters', 'Apply Filters')}
            </StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      </StyledView>
    </RBSheet>
  );
}
