import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MarketingCarousel, { BannerItem } from '../MarketingCarousel';

// Mock the context hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

const mockBanners: BannerItem[] = [
  {
    id: '1',
    imageUrl: 'https://example.com/banner1.jpg',
    title: 'Banner 1',
    subtitle: 'Subtitle 1',
  },
  {
    id: '2',
    imageUrl: 'https://example.com/banner2.jpg',
    title: 'Banner 2',
  },
];

describe('MarketingCarousel', () => {
  it('renders correctly with banners', () => {
    const { getByTestId } = render(<MarketingCarousel banners={mockBanners} />);
    expect(getByTestId('marketing-carousel')).toBeTruthy();
    expect(getByTestId('banner-1')).toBeTruthy();
  });

  it('handles banner press', () => {
    const onBannerPress = jest.fn();
    const { getByTestId } = render(
      <MarketingCarousel banners={mockBanners} onBannerPress={onBannerPress} />
    );
    fireEvent.press(getByTestId('banner-1'));
    expect(onBannerPress).toHaveBeenCalledWith(mockBanners[0]);
  });

  it('renders pagination dots for multiple banners', () => {
    const { getAllByTestId } = render(<MarketingCarousel banners={mockBanners} />);
    // Should have 2 dots for 2 banners
    expect(getAllByTestId(/dot-\d+/).length).toBe(2);
  });

  it('does not render pagination dots for single banner', () => {
    const { queryAllByTestId } = render(<MarketingCarousel banners={[mockBanners[0]]} />);
    // Should not have any dots for 1 banner
    expect(queryAllByTestId(/dot-\d+/).length).toBe(0);
  });

  it('does not render anything when banners array is empty', () => {
    const { container } = render(<MarketingCarousel banners={[]} />);
    expect(container.children.length).toBe(0);
  });
});
