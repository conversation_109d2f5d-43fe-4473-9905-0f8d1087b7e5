import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import HeroCard from '../HeroCard';

// Mock the hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
}));

jest.mock('expo-linear-gradient', () => 'LinearGradient');

describe('HeroCard', () => {
  const defaultProps = {
    imageUrl: 'https://example.com/hero.jpg',
    headline: 'Test Headline',
    subtitle: 'Test Subtitle',
  };

  it('renders correctly with all props', () => {
    const { getByText, getByTestId } = render(
      <HeroCard {...defaultProps} />
    );
    
    expect(getByText('Test Headline')).toBeTruthy();
    expect(getByText('Test Subtitle')).toBeTruthy();
    expect(getByTestId('hero-card')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <HeroCard {...defaultProps} onPress={onPress} />
    );
    
    fireEvent.press(getByTestId('hero-card'));
    
    expect(onPress).toHaveBeenCalled();
  });

  it('uses custom testID when provided', () => {
    const { getByTestId } = render(
      <HeroCard {...defaultProps} testID="custom-hero-card" />
    );
    
    expect(getByTestId('custom-hero-card')).toBeTruthy();
  });

  it('renders image with correct source', () => {
    const { getByTestId } = render(
      <HeroCard {...defaultProps} />
    );
    
    const heroCard = getByTestId('hero-card');
    const image = heroCard.findByType('Image');
    
    expect(image.props.source.uri).toBe('https://example.com/hero.jpg');
  });

  it('applies gradient overlay', () => {
    const { getByTestId } = render(
      <HeroCard {...defaultProps} />
    );
    
    const heroCard = getByTestId('hero-card');
    const gradient = heroCard.findByType('LinearGradient');
    
    expect(gradient).toBeTruthy();
    expect(gradient.props.colors).toEqual(['transparent', 'rgba(0,0,0,0.7)']);
  });
});
