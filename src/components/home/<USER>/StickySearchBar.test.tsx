import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import StickySearchBar, { FilterOption } from '../StickySearchBar';
import { useSharedValue } from 'react-native-reanimated';

// Mock the hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
  NewRTLView: 'View',
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

jest.mock('react-native-reanimated', () => {
  const Reanimated = jest.requireActual('react-native-reanimated/mock');
  
  // Add useAnimatedStyle mock
  Reanimated.useAnimatedStyle = jest.fn(() => ({}));
  
  // Add interpolate mock
  Reanimated.interpolate = jest.fn((value, inputRange, outputRange) => outputRange[0]);
  
  // Add useSharedValue mock
  Reanimated.useSharedValue = jest.fn((initialValue) => ({
    value: initialValue,
  }));
  
  // Add Extrapolation mock
  Reanimated.Extrapolation = {
    CLAMP: 'clamp',
  };
  
  return Reanimated;
});

const mockProjectTypes: FilterOption[] = [
  { id: 'construction', label: 'Construction' },
  { id: 'renovation', label: 'Renovation' },
];

const mockCountOptions: FilterOption[] = [
  { id: 'small', label: 'Small' },
  { id: 'medium', label: 'Medium' },
];

describe('StickySearchBar', () => {
  const defaultProps = {
    searchValue: '',
    onSearchChange: jest.fn(),
    onFilterPress: jest.fn(),
    onSortPress: jest.fn(),
    projectTypes: mockProjectTypes,
    selectedProjectType: null,
    onProjectTypeChange: jest.fn(),
    countOptions: mockCountOptions,
    selectedCount: null,
    onCountChange: jest.fn(),
    scrollY: { value: 0 },
    stickyThreshold: 200,
  };

  it('renders correctly', () => {
    const { getByTestId, getByPlaceholderText } = render(
      <StickySearchBar {...defaultProps} />
    );
    
    expect(getByTestId('sticky-search-bar')).toBeTruthy();
    expect(getByPlaceholderText('Search dumpsters...')).toBeTruthy();
  });

  it('calls onSearchChange when text input changes', () => {
    const onSearchChange = jest.fn();
    const { getByPlaceholderText } = render(
      <StickySearchBar {...defaultProps} onSearchChange={onSearchChange} />
    );
    
    const searchInput = getByPlaceholderText('Search dumpsters...');
    fireEvent.changeText(searchInput, 'test search');
    
    expect(onSearchChange).toHaveBeenCalledWith('test search');
  });

  it('calls onFilterPress when filter button is pressed', () => {
    const onFilterPress = jest.fn();
    const { getByTestId } = render(
      <StickySearchBar {...defaultProps} onFilterPress={onFilterPress} testID="test-search-bar" />
    );
    
    // Find the filter button by its icon and press it
    const filterButton = getByTestId('test-search-bar').findByProps({ name: 'filter-list' });
    fireEvent.press(filterButton);
    
    expect(onFilterPress).toHaveBeenCalled();
  });

  it('displays selected project type when available', () => {
    const { getByText } = render(
      <StickySearchBar 
        {...defaultProps} 
        selectedProjectType="construction"
      />
    );
    
    expect(getByText('Construction')).toBeTruthy();
  });

  it('displays selected count when available', () => {
    const { getByText } = render(
      <StickySearchBar 
        {...defaultProps} 
        selectedCount="medium"
      />
    );
    
    expect(getByText('Medium')).toBeTruthy();
  });
});
