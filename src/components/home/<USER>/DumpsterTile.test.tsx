import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DumpsterTile from '../DumpsterTile';

// Mock the hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

// Mock dumpster data
const mockDumpster = {
  id: '1',
  nameEn: '10 Yard Dumpster',
  nameAr: 'حاوية 10 ياردات',
  imageUrl: 'https://example.com/dumpster.jpg',
  length: 10,
  width: 8,
  height: 4,
  pricePerLoad: 250,
  rating: 4.5,
  reviewCount: 10,
  isAvailable: true,
  capacity: 10,
  maxWeight: 2000,
  standingArea: 80,
  sizeId: 'size-1',
};

describe('DumpsterTile', () => {
  it('renders correctly with all props', () => {
    const onPress = jest.fn();
    const { getByText, getByTestId } = render(
      <DumpsterTile 
        dumpster={mockDumpster} 
        onPress={onPress} 
      />
    );
    
    expect(getByText('10 Yard Dumpster')).toBeTruthy();
    expect(getByText('10m × 8m × 4m • ﷼250')).toBeTruthy();
    expect(getByTestId('dumpster-tile')).toBeTruthy();
  });

  it('shows loading state when isLoading is true', () => {
    const { getByTestId } = render(
      <DumpsterTile dumpster={mockDumpster} onPress={() => {}} isLoading={true} />
    );
    
    expect(getByTestId('dumpster-tile-loading')).toBeTruthy();
  });

  it('calls onPress with dumpster data when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <DumpsterTile dumpster={mockDumpster} onPress={onPress} />
    );
    
    fireEvent.press(getByTestId('dumpster-tile'));
    
    expect(onPress).toHaveBeenCalledWith(mockDumpster);
  });

  it('handles image loading states correctly', () => {
    const { getByTestId } = render(
      <DumpsterTile dumpster={mockDumpster} onPress={() => {}} />
    );
    
    const tile = getByTestId('dumpster-tile');
    const image = tile.findByType('Image');
    
    // Test image loading start
    fireEvent(image, 'loadStart');
    expect(tile.findAllByType('ActivityIndicator').length).toBe(1);
    
    // Test image loading end
    fireEvent(image, 'loadEnd');
    expect(tile.findAllByType('ActivityIndicator').length).toBe(0);
    
    // Test image error
    fireEvent(image, 'error');
    expect(tile.findByProps({ name: 'image-off' })).toBeTruthy();
  });
});
