import { View as RNView, Text as RNText, TouchableOpacity as RNTouchableOpacity, ScrollView as RNScrollView } from 'react-native';
import { styled } from 'nativewind';

export const View = styled(RNView);
export const Text = styled(RNText);
export const TouchableOpacity = styled(RNTouchableOpacity);
export const ScrollView = styled(RNScrollView);

// Re-export native components that don't need styling
export { Image, Platform, Animated } from 'react-native';
export { Button } from 'react-native-paper'; 