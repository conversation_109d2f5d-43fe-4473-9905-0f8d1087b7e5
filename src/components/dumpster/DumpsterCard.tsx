import React, { useState } from 'react';
import { View, Text, Image, StyleSheet, Pressable, ActivityIndicator } from 'react-native';
import { Dumps<PERSON> } from '@/types/dumpster';
import { useTranslation } from 'react-i18next';
import { Card, Chip, Badge, IconButton, ProgressBar } from 'react-native-paper';
import { styled } from "nativewind";
import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledCard = styled(Card);
const StyledCardContent = styled(Card.Content);
const StyledChip = styled(Chip);
const StyledProgressBar = styled(ProgressBar);

interface DumpsterCardProps {
  dumpster: Dumpster;
  onPress?: () => void;
  recommended?: boolean;
  recommendationScore?: number;
}

export const DumpsterCard: React.FC<DumpsterCardProps> = ({
  dumpster,
  onPress,
  recommended = false,
  recommendationScore,
}) => {
  const { t } = useTranslation();
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Get the largest size for prominent display
  const largestSize = dumpster.sizes.reduce((max, size) => 
    size.volumeCubicYards > max.volumeCubicYards ? size : max
  , dumpster.sizes[0]);
  
  return (
    <StyledCard
      className="mb-4 overflow-hidden"
      onPress={onPress}
    >
      <StyledView className="relative">
        {imageLoading && (
          <StyledView className="absolute inset-0 bg-gray-200 justify-center items-center">
            <ActivityIndicator size="large" color="#0000ff" />
          </StyledView>
        )}
        
        <Image
          source={{ uri: dumpster.imageUrl }}
          style={styles.image}
          resizeMode="cover"
          onLoadStart={() => setImageLoading(true)}
          onLoadEnd={() => setImageLoading(false)}
          onError={() => {
            setImageLoading(false);
            setImageError(true);
          }}
        />
        
        {imageError && (
          <StyledView className="absolute inset-0 bg-gray-200 justify-center items-center">
            <MaterialCommunityIcons name="image-off" size={48} color="#666" />
            <StyledText className="text-gray-600 mt-2">{t('Image not available')}</StyledText>
          </StyledView>
        )}
        
        {recommended && (
          <StyledView className="absolute top-2 right-2 bg-primary px-3 py-2 rounded-lg">
            <StyledText className="text-white font-bold text-base">
              {recommendationScore ? `${Math.round(recommendationScore)}% Match` : t('Recommended')}
            </StyledText>
          </StyledView>
        )}
        
        {!dumpster.availability.isAvailable && (
          <StyledView className="absolute bottom-2 left-2 bg-red-500 px-3 py-2 rounded-lg">
            <StyledText className="text-white font-bold">
              {dumpster.availability.nextAvailableDate 
                ? t('Available from {{date}}', { date: new Date(dumpster.availability.nextAvailableDate).toLocaleDateString() })
                : t('Not Available')}
            </StyledText>
          </StyledView>
        )}
      </StyledView>
      
      <StyledCardContent>
        {/* Size and Rating Row */}
        <StyledView className="flex-row justify-between items-center mb-3 bg-gray-100 p-3 rounded-lg">
          <StyledView>
            <StyledText className="text-sm text-gray-600">{t('Size')}</StyledText>
            <StyledText className="text-xl font-bold">
              {largestSize.volumeCubicYards} {t('cubic yards')}
            </StyledText>
            <StyledText className="text-xs text-gray-500">
              {largestSize.dimensions.length}' x {largestSize.dimensions.width}' x {largestSize.dimensions.height}'
            </StyledText>
          </StyledView>
          
          <StyledView className="items-end">
            {dumpster.rating && (
              <>
                <StyledView className="flex-row items-center mb-1">
                  <AntDesign name="star" size={20} color="#FFD700" />
                  <StyledText className="ml-1 text-lg font-bold">
                    {dumpster.rating}
                  </StyledText>
                </StyledView>
                <StyledText className="text-xs text-gray-500">
                  {dumpster.reviewCount} {t('reviews')}
                </StyledText>
              </>
            )}
          </StyledView>
        </StyledView>

        {/* Name and Description */}
        <StyledText className="text-lg font-bold mb-1">{dumpster.name}</StyledText>
        {dumpster.description && (
          <StyledText className="text-sm text-gray-600 mb-2" numberOfLines={2}>
            {dumpster.description}
          </StyledText>
        )}
        
        {/* Price and Features */}
        <StyledView className="flex-row justify-between items-center mb-2">
          <StyledText className="text-lg font-bold text-primary">
            ${dumpster.pricePerDay}/day
          </StyledText>
          
          <StyledView className="flex-row">
            {dumpster.features?.slice(0, 2).map((feature, index) => (
              <StyledChip
                key={index}
                className="ml-2"
                compact
                mode="outlined"
              >
                {feature}
              </StyledChip>
            ))}
            {dumpster.features && dumpster.features.length > 2 && (
              <StyledChip compact mode="outlined" className="ml-2">
                +{dumpster.features.length - 2}
              </StyledChip>
            )}
          </StyledView>
        </StyledView>
        
        {/* Waste Types */}
        <StyledView className="flex-row flex-wrap mt-2">
          {dumpster.compatibleWasteTypes.map((wasteType, index) => (
            <StyledText key={index} className="text-xs text-gray-500 mr-2 mb-1">
              • {wasteType}
            </StyledText>
          ))}
        </StyledView>
      </StyledCardContent>
    </StyledCard>
  );
};

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: 200, // Increased height for better visibility
    backgroundColor: '#f0f0f0', // Placeholder color while loading
  },
});

export default DumpsterCard; 