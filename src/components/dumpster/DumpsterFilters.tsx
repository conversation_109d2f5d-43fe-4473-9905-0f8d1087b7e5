import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Button, Chip, Portal, Modal, TextInput, IconButton } from 'react-native-paper';
import { cssInterop } from "nativewind";
import { DumpsterFilter, WasteType } from '@/types/dumpster';

const StyledView = cssInterop(View, { className: 'style' });
const StyledText = cssInterop(Text, { className: 'style' });
const StyledScrollView = cssInterop(ScrollView, { className: 'style' });
const StyledChip = cssInterop(Chip, { className: 'style' });
const StyledButton = cssInterop(Button, { className: 'style' });
const StyledTextInput = cssInterop(TextInput, { className: 'style' });

interface DumpsterFiltersProps {
  filter: DumpsterFilter;
  onFilterChange: (filter: DumpsterFilter) => void;
  wasteTypes: WasteType[];
}

export const DumpsterFilters: React.FC<DumpsterFiltersProps> = ({
  filter,
  onFilterChange,
  wasteTypes,
}) => {
  const { t } = useTranslation();
  const [showPriceModal, setShowPriceModal] = useState(false);
  
  // Handle waste type selection
  const toggleWasteType = (wasteTypeId: string) => {
    const wasteTypeIds = filter.wasteTypeIds || [];
    const newWasteTypeIds = wasteTypeIds.includes(wasteTypeId)
      ? wasteTypeIds.filter(id => id !== wasteTypeId)
      : [...wasteTypeIds, wasteTypeId];
    
    onFilterChange({
      ...filter,
      wasteTypeIds: newWasteTypeIds,
    });
  };
  
  // Handle price range changes
  const handlePriceChange = (type: 'min' | 'max', value: string) => {
    const numValue = value ? parseFloat(value) : undefined;
    onFilterChange({
      ...filter,
      priceRange: {
        ...filter.priceRange,
        [type]: numValue,
      },
    });
  };
  
  // Clear all filters
  const clearFilters = () => {
    onFilterChange({});
  };
  
  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (filter.wasteTypeIds?.length) count++;
    if (filter.priceRange?.min || filter.priceRange?.max) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();
  
  return (
    <>
      <StyledView>
        {/* Waste Types Section */}
        <StyledView className="mb-4">
          <StyledView className="flex-row justify-between items-center mb-2">
            <StyledText className="text-base font-semibold text-gray-800">
              {t('Waste Types')}
            </StyledText>
            {filter.wasteTypeIds?.length ? (
              <StyledButton
                mode="text"
                compact
                onPress={() => onFilterChange({ ...filter, wasteTypeIds: [] })}
              >
                {t('Clear')}
              </StyledButton>
            ) : null}
          </StyledView>
          
          <StyledScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="flex-row"
          >
            {wasteTypes.map((wasteType) => (
              <StyledChip
                key={wasteType.id}
                selected={(filter.wasteTypeIds || []).includes(wasteType.id)}
                onPress={() => toggleWasteType(wasteType.id)}
                className="mr-2"
                mode="outlined"
                style={{
                  backgroundColor: (filter.wasteTypeIds || []).includes(wasteType.id)
                    ? '#E8F5E9'  // Light green background when selected
                    : 'white',
                }}
              >
                {wasteType.name}
              </StyledChip>
            ))}
          </StyledScrollView>
        </StyledView>

        {/* Price Range Button */}
        <StyledView className="mb-4">
          <StyledButton
            mode="outlined"
            onPress={() => setShowPriceModal(true)}
            icon={filter.priceRange?.min || filter.priceRange?.max ? 'check' : 'currency-usd'}
            contentStyle={{ justifyContent: 'flex-start' }}
            className="bg-white"
          >
            {filter.priceRange?.min || filter.priceRange?.max
              ? t('Price: ${{min}} - ${{max}}', {
                  min: filter.priceRange.min || '0',
                  max: filter.priceRange.max || '∞',
                })
              : t('Set Price Range')}
          </StyledButton>
        </StyledView>

        {/* Active Filters Summary */}
        {activeFilterCount > 0 && (
          <StyledView className="flex-row justify-between items-center mt-2">
            <StyledText className="text-sm text-gray-600">
              {t('{{count}} filters applied', { count: activeFilterCount })}
            </StyledText>
            <StyledButton
              mode="text"
              compact
              onPress={clearFilters}
              className="text-red-500"
            >
              {t('Clear All')}
            </StyledButton>
          </StyledView>
        )}
      </StyledView>

      {/* Price Range Modal */}
      <Portal>
        <Modal
          visible={showPriceModal}
          onDismiss={() => setShowPriceModal(false)}
          contentContainerStyle={{
            backgroundColor: 'white',
            padding: 20,
            margin: 20,
            borderRadius: 8,
          }}
        >
          <StyledView>
            <StyledView className="flex-row justify-between items-center mb-4">
              <StyledText className="text-lg font-bold">
                {t('Set Price Range')}
              </StyledText>
              <IconButton
                icon="close"
                size={20}
                onPress={() => setShowPriceModal(false)}
              />
            </StyledView>
            
            <StyledView className="mb-4">
              <StyledTextInput
                label={t('Minimum Price ($)')}
                keyboardType="numeric"
                value={filter.priceRange?.min?.toString() || ''}
                onChangeText={(text) => handlePriceChange('min', text)}
                className="mb-2"
              />
              
              <StyledTextInput
                label={t('Maximum Price ($)')}
                keyboardType="numeric"
                value={filter.priceRange?.max?.toString() || ''}
                onChangeText={(text) => handlePriceChange('max', text)}
              />
            </StyledView>
            
            <StyledView className="flex-row justify-end">
              <StyledButton
                mode="text"
                onPress={() => {
                  onFilterChange({
                    ...filter,
                    priceRange: undefined,
                  });
                  setShowPriceModal(false);
                }}
                className="mr-2"
              >
                {t('Clear')}
              </StyledButton>
              
              <StyledButton
                mode="contained"
                onPress={() => setShowPriceModal(false)}
              >
                {t('Apply')}
              </StyledButton>
            </StyledView>
          </StyledView>
        </Modal>
      </Portal>
    </>
  );
};

export default DumpsterFilters; 