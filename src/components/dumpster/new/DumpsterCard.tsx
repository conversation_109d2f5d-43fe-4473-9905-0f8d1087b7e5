import React, { useState } from 'react';
import { View, TouchableOpacity, Image, Text, ActivityIndicator, FlatList } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { NewRTLText, useRTLContext } from '@/components/rtl/new-index';
import { Dumpster } from '@/types/new/dumpster';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { styled } from 'nativewind';
import * as colors from '@/theme/colors';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledImage = styled(Image);
const StyledText = styled(Text);
const StyledLinearGradient = styled(LinearGradient);

interface DumpsterCardProps {
  dumpster: Dumpster;
  onPress: () => void;
  isLoading?: boolean;
  showRecommendation?: boolean;
  recommendationScore?: number;
  recommendationReasons?: string[];
}

export default function DumpsterCard({
  dumpster,
  onPress,
  isLoading = false,
  showRecommendation = false,
  recommendationScore = 0,
  recommendationReasons = []
}: DumpsterCardProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  if (isLoading) {
    return (
      <StyledView className={`
        rounded-lg overflow-hidden h-64 justify-center items-center
        ${isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light}
      `}>
        <ActivityIndicator testID="loading-indicator" size="large" color={colors.brandColors.primary[500]} />
      </StyledView>
    );
  }

  // Verify we have a valid dumpster object, default early if not
  if (!dumpster || typeof dumpster !== 'object') {
    console.error('Invalid dumpster object:', dumpster);
    return (
      <StyledView className={`
        rounded-lg overflow-hidden shadow-md p-4
        ${isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light}
      `}>
        <StyledText className={`text-center ${isDarkMode ? colors.textColors.light : colors.textColors.dark}`}>
          Dumpster data unavailable
        </StyledText>
      </StyledView>
    );
  }

  // Handle possible undefined values with defaults
  const displayName = isRTL
    ? (dumpster.nameAr || dumpster.nameEn || 'Unnamed Dumpster')
    : (dumpster.nameEn || 'Unnamed Dumpster');

  const displayRating = dumpster.rating || 4.0;
  const displayReviewCount = dumpster.reviewCount || 0;

  // Default dimensions if not provided
  const length = dumpster.length || 10;
  const width = dumpster.width || 8;
  const height = dumpster.height || 6;

  // Default availability if not provided
  const isAvailable = dumpster.isAvailable !== undefined ? dumpster.isAvailable : true;

  // Ensure we have a valid image URL
  const imageUrl = typeof dumpster.imageUrl === 'string' && dumpster.imageUrl
    ? dumpster.imageUrl
    : 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';

  return (
    <StyledTouchableOpacity
      testID="dumpster-card"
      onPress={onPress}
      className={`
        rounded-lg overflow-hidden shadow-md
        ${isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light}
      `}
    >
      <StyledView className="relative" style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}>
        {imageLoading && (
          <StyledView style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }} className="absolute inset-0 justify-center items-center z-10 w-full h-32">
            <ActivityIndicator size="small" color={colors.brandColors.primary[500]} />
          </StyledView>
        )}

        {!imageError ? (
          <StyledImage
            source={{ uri: imageUrl }}
            className="w-full h-32"
            resizeMode="cover"
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
            onError={() => {
              console.error('Error loading image:', imageUrl);
              setImageLoading(false);
              setImageError(true);
            }}
          />
        ) : (
          <StyledView style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }} className="w-full h-32 justify-center items-center">
            <MaterialIcons name="broken-image" size={24} color={isDarkMode ? colors.textColors.light : colors.textColors.dark} />
            <StyledText className=" text-xs mt-1" style={{ color: isDarkMode ? colors.textColors.light : colors.textColors.dark }}>Image unavailable</StyledText>
          </StyledView>
        )}

        {/* Rating Badge */}
        <StyledView className="absolute top-2 left-2 rounded-full px-2 py-1 flex-row items-center" style={{ backgroundColor: isDarkMode ? `${colors.surfaceColors.container.dark}80` : `${colors.surfaceColors.container.light}80` }}>
          <MaterialIcons name="star" size={16} color={isDarkMode ? colors.brandColors.warning[400] : colors.brandColors.warning[500]} />
          <NewRTLText style={{ fontSize: 12, marginLeft: 4, color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>{`${Number(displayRating).toFixed(1)}`}</NewRTLText>
          <NewRTLText style={{ fontSize: 12, marginLeft: 4, color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>{` (${displayReviewCount})`}</NewRTLText>
        </StyledView>

        {/* Recommendation Score */}
        {showRecommendation && recommendationScore > 0 && (
          <StyledLinearGradient
            colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.7)']}
            className="absolute bottom-0 left-0 right-0 p-2"
          >
            <StyledView className="flex-row items-center">
              <MaterialIcons name="thumb-up" size={16} color="#4CAF50" />
              <NewRTLText style={{ fontSize: 12, marginLeft: 4, color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
                {`${recommendationScore.toFixed(0)}% Match`}
              </NewRTLText>
            </StyledView>
          </StyledLinearGradient>
        )}
      </StyledView>

      <StyledView className="p-3" style={{ backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light }}>
        <NewRTLText style={{ fontSize: 16, fontWeight: 'bold', color: isDarkMode ? colors.textColors.dark : colors.textColors.light }}>
          {displayName}
        </NewRTLText>

        {/* Dimensions */}
        <NewRTLText style={{ fontSize: 12, marginTop: 4, color: isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light }}>
          {`${length}m × ${width}m × ${height}m`}
        </NewRTLText>

        <StyledView className="flex-row items-center justify-between mt-2">
          <StyledView className="flex-col items-start">
            <StyledView className="flex-row items-center">
              {isDarkMode ? (<Image
                source={require('assets/images/currency/Saudi_Riyal_Symbol_white.png')}
                style={{ width: 16, height: 16, marginRight: 2 }}
                resizeMode="contain"
              />) : (<Image
                source={require('assets/images/currency/Saudi_Riyal_Symbol.png')}
                style={{ width: 16, height: 16, marginRight: 2 }}
                resizeMode="contain"
              />)}
              <StyledText className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {dumpster.pricePerLoad}
              </StyledText>

            </StyledView>
            {isAvailable ? (
              <StyledText className="text-green-500 text-sm">Available</StyledText>
            ) : (
              <StyledText className="text-red-500 text-sm">Unavailable</StyledText>
            )}
          </StyledView>

          {typeof dumpster.partnerLogo === 'string' && dumpster.partnerLogo ? (
            <StyledImage
              source={{ uri: dumpster.partnerLogo }}
              className="w-8 h-8 rounded-full"
            />
          ) : null}
        </StyledView>
      </StyledView>
    </StyledTouchableOpacity>
  );
} 