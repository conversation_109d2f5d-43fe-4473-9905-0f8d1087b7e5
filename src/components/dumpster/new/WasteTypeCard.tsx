import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { WasteType } from '@/types/new/dumpster';
import { MaterialIcons } from '@expo/vector-icons';

interface WasteTypeCardProps {
  wasteType: WasteType;
  selected?: boolean;
  onPress?: () => void;
}

export default function WasteTypeCard({ 
  wasteType, 
  selected = false, 
  onPress 
}: WasteTypeCardProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  return (
    <TouchableOpacity
      testID="waste-type-card"
      onPress={onPress}
      className={`
        rounded-lg overflow-hidden mb-2
        ${selected ? 'border-2 border-primary' : ''}
        ${isDarkMode ? 'bg-gray-800' : 'bg-white'}
      `}
    >
      <View className="flex-row">
        <Image
          testID="waste-type-image"
          source={{ uri: wasteType.imageUrl }}
          className="w-24 h-24"
          resizeMode="cover"
        />
        <View className="flex-1 p-3">
          <Text className={`
            text-lg font-bold mb-1
            ${isDarkMode ? 'text-white' : 'text-gray-800'}
          `}>
            {isRTL ? wasteType.nameAr : wasteType.nameEn}
          </Text>
          
          {wasteType.description && (
            <Text 
              className={`
                text-sm mb-2
                ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}
              `}
              numberOfLines={2}
            >
              {isRTL ? wasteType.descriptionAr : wasteType.descriptionEn}
            </Text>
          )}
          
          <View className="flex-row flex-wrap">
            {wasteType.tags.map((tag) => (
              <View
                key={tag.id}
                className={`
                  mr-1 mb-1 px-2 py-1 rounded-full
                  ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}
                `}
              >
                <Text className={`
                  text-xs
                  ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}
                `}>
                  {isRTL ? tag.nameAr : tag.nameEn}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
} 