import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { WasteType } from '@/types/new/dumpster';
import { MaterialIcons } from '@expo/vector-icons';

interface WasteTypeSelectorProps {
  wasteTypes: WasteType[];
  selectedId: string | null;
  onSelect: (id: string) => void;
}

export default function WasteTypeSelector({ 
  wasteTypes, 
  selectedId, 
  onSelect 
}: WasteTypeSelectorProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();

  return (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      className="flex-row"
    >
      {wasteTypes.map((type) => (
        <TouchableOpacity
          key={type.id}
          testID={`waste-type-${type.id}`}
          onPress={() => onSelect(type.id)}
          className={`
            mr-3 p-3 rounded-lg
            ${selectedId === type.id 
              ? 'bg-primary-500' 
              : isDarkMode ? 'bg-gray-800' : 'bg-white'}
          `}
        >
          <View className="items-center">
            <MaterialIcons 
              name="delete" 
              size={24} 
              color={selectedId === type.id ? '#fff' : isDarkMode ? '#9CA3AF' : '#6B7280'} 
            />
            <Text 
              className={`
                mt-1 text-sm
                ${selectedId === type.id 
                  ? 'text-white' 
                  : isDarkMode ? 'text-gray-300' : 'text-gray-600'}
              `}
            >
              {isRTL ? type.nameAr : type.nameEn}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
} 