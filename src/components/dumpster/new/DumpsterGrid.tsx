import React from 'react';
import { View, FlatList, Dimensions, Text, ActivityIndicator } from 'react-native';
import { Dumpster } from '@/types/new/dumpster';
import DumpsterCard from './DumpsterCard';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/context/ThemeContext';

interface DumpsterGridProps {
  dumpsters: Dumpster[];
  onSelect: (dumpster: Dumpster) => void;
  loading?: boolean;
  recommendations?: { [key: string]: number };
}

export default function DumpsterGrid({ 
  dumpsters, 
  onSelect, 
  loading = false,
  recommendations = {}
}: DumpsterGridProps) {
  const { t } = useTranslation();
  const { colors } = useTheme();
  const numColumns = 2;
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - 48) / numColumns; // 48 = padding (16) * 3

  const renderItem = ({ item }: { item: Dumpster }) => (
    <View style={{ width: cardWidth, padding: 8 }}>
      <DumpsterCard
        dumpster={item}
        onPress={() => onSelect(item)}
        isLoading={loading}
        showRecommendation={!!recommendations[item.id]}
        recommendationScore={recommendations[item.id] || 0}
      />
    </View>
  );

  return (
    <FlatList
      data={dumpsters}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      numColumns={numColumns}
      contentContainerStyle={{ padding: 8 }}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={
        loading ? (
          <View className="flex-1 justify-center items-center py-8">
            <ActivityIndicator size="large" color={colors.brandColors.primary[500]} />
          </View>
        ) : (
          <View className="flex-1 justify-center items-center py-8">
            <Text className="text-gray-500">{t('No dumpsters found')}</Text>
          </View>
        )
      }
    />
  );
} 