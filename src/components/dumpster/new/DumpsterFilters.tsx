import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { MaterialIcons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { DumpsterFilters } from '@/types/dumpster';
import { useTheme } from '@/context/ThemeContext';
import * as colors from '@/theme/colors';

interface FilterProps {
  filters: DumpsterFilters;
  onFiltersChange: (filters: DumpsterFilters) => void;
  wasteTypes: { id: string; name_en: string; name_ar: string; }[];
  dumpsterSizes: string[];
}

export default function DumpsterFiltersComponent({ 
  filters, 
  onFiltersChange,
  wasteTypes,
  dumpsterSizes
}: FilterProps) {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <View className={`
      rounded-t-xl p-4
      ${isDarkMode ? 'bg-gray-900' : 'bg-white'}
    `}>
      <View className="flex-row justify-between items-center mb-4">
        <Text className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
          {t('Filters')}
        </Text>
        <TouchableOpacity 
          onPress={() => onFiltersChange({
            priceRange: [0, 1000],
            size: [],
            rating: null,
            availability: true,
            wasteType: null
          })}
        >
          <Text className="text-primary-500">{t('Reset')}</Text>
        </TouchableOpacity>
      </View>

      {/* Price Range Slider */}
      <View className="mb-6">
        <Text className={`text-base mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {t('Price per load')}
        </Text>
        <Slider
          value={filters.priceRange[1]}
          minimumValue={0}
          maximumValue={1000}
          step={50}
          onValueChange={(value) => onFiltersChange({
            ...filters,
            priceRange: [0, value]
          })}
          minimumTrackTintColor={colors.brandColors.primary[500]}
          maximumTrackTintColor={isDarkMode ? colors.brandColors.primary[700] : colors.brandColors.primary[200]}
        />
        <Text className={`text-right ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
          ﷼{filters.priceRange[1]}
        </Text>
      </View>

      {/* Size Selection */}
      <View className="mb-6">
        <Text className={`text-base mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {t('Dumpster size')}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {dumpsterSizes.map((size) => (
            <TouchableOpacity
              key={size}
              onPress={() => {
                const newSizes = filters.size.includes(size)
                  ? filters.size.filter(s => s !== size)
                  : [...filters.size, size];
                onFiltersChange({ ...filters, size: newSizes });
              }}
              className={`
                mr-2 px-4 py-2 rounded-full
                ${filters.size.includes(size) 
                  ? 'bg-primary-500' 
                  : isDarkMode ? 'bg-gray-800' : 'bg-gray-200'}
              `}
            >
              <Text className={filters.size.includes(size) ? 'text-white' : isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                {size}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Rating Filter */}
      <View className="mb-6">
        <Text className={`text-base mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {t('Minimum rating')}
        </Text>
        <View className="flex-row">
          {[4, 4.5, 5].map((rating) => (
            <TouchableOpacity
              key={rating}
              onPress={() => onFiltersChange({ ...filters, rating })}
              className={`
                flex-row items-center mr-4 px-3 py-2 rounded-full
                ${filters.rating === rating 
                  ? 'bg-primary-500' 
                  : isDarkMode ? 'bg-gray-800' : 'bg-gray-200'}
              `}
            >
              <MaterialIcons name="star" size={16} color={filters.rating === rating ? '#fff' : '#FFD700'} />
              <Text className={`ml-1 ${filters.rating === rating ? 'text-white' : isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {rating}+
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Availability Toggle */}
      <TouchableOpacity
        onPress={() => onFiltersChange({ ...filters, availability: !filters.availability })}
        className="flex-row items-center justify-between mb-4"
      >
        <Text className={`text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {t('Show available only')}
        </Text>
        <View className={`
          w-6 h-6 rounded-full
          ${filters.availability ? 'bg-primary-500' : isDarkMode ? 'bg-gray-800' : 'bg-gray-200'}
        `}>
          {filters.availability && (
            <MaterialIcons name="check" size={18} color="#fff" />
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
} 