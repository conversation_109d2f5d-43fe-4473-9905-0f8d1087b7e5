import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import WasteTypeCard from '../WasteTypeCard';
import { ThemeProvider } from '@/context/ThemeContext';
import { RTLProvider } from '@/components/rtl/new-index';

const mockWasteType = {
  id: '1',
  nameEn: 'Construction Waste',
  nameAr: 'نفايات البناء',
  descriptionEn: 'Materials from construction and demolition projects',
  descriptionAr: 'مواد من مشاريع البناء والهدم',
  imageUrl: 'https://example.com/construction.jpg',
  tags: [
    {
      id: '1',
      nameEn: 'Concrete',
      nameAr: 'خرسانة'
    },
    {
      id: '2',
      nameEn: 'Bricks',
      nameAr: 'طوب'
    }
  ]
};

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <ThemeProvider>
      <RTLProvider>
        {component}
      </RTLProvider>
    </ThemeProvider>
  );
};

describe('WasteTypeCard', () => {
  it('renders correctly with basic props', () => {
    const onPress = jest.fn();
    const { getByText, getByTestId } = renderWithProviders(
      <WasteTypeCard wasteType={mockWasteType} onPress={onPress} />
    );

    expect(getByText('Construction Waste')).toBeTruthy();
    expect(getByText('Materials from construction and demolition projects')).toBeTruthy();
    expect(getByText('Concrete')).toBeTruthy();
    expect(getByText('Bricks')).toBeTruthy();
    expect(getByTestId('waste-type-image')).toBeTruthy();
  });

  it('shows selected state when selected prop is true', () => {
    const { getByTestId } = renderWithProviders(
      <WasteTypeCard wasteType={mockWasteType} selected={true} onPress={() => {}} />
    );

    const card = getByTestId('waste-type-card');
    expect(card.props.className).toContain('border-2 border-primary');
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = renderWithProviders(
      <WasteTypeCard wasteType={mockWasteType} onPress={onPress} />
    );

    fireEvent.press(getByTestId('waste-type-card'));
    expect(onPress).toHaveBeenCalled();
  });

  it('displays RTL text when RTL is enabled', () => {
    const { getByText } = renderWithProviders(
      <WasteTypeCard wasteType={mockWasteType} onPress={() => {}} />
    );

    expect(getByText('نفايات البناء')).toBeTruthy();
    expect(getByText('مواد من مشاريع البناء والهدم')).toBeTruthy();
    expect(getByText('خرسانة')).toBeTruthy();
    expect(getByText('طوب')).toBeTruthy();
  });

  it('limits description to 2 lines', () => {
    const longDescriptionWasteType = {
      ...mockWasteType,
      descriptionEn: 'This is a very long description that should be truncated after two lines because it contains a lot of text that would make the card too tall if it were to display all of it. We want to keep the card compact and readable.',
      descriptionAr: 'هذا وصف طويل جداً يجب اقتطاعه بعد سطرين لأنه يحتوي على الكثير من النص الذي سيجعل البطاقة طويلة جداً إذا تم عرض كل ذلك. نريد أن نبقي البطاقة مدمجة وقابلة للقراءة.'
    };

    const { getByText } = renderWithProviders(
      <WasteTypeCard wasteType={longDescriptionWasteType} onPress={() => {}} />
    );

    const description = getByText('This is a very long description that should be truncated after two lines because it contains a lot of text that would make the card too tall if it were to display all of it. We want to keep the card compact and readable.');
    expect(description.props.numberOfLines).toBe(2);
  });
}); 