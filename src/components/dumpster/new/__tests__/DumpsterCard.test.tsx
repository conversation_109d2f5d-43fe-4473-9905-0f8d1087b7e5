import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DumpsterCard from '../DumpsterCard';
import { ThemeProvider } from '@/context/ThemeContext';
import { RTLProvider } from '@/components/rtl/new-index';

const mockDumpster = {
  id: '1',
  nameEn: '5m³ Dumpster',
  nameAr: 'حاوية 5م³',
  imageUrl: 'https://example.com/dumpster.jpg',
  partnerLogo: 'https://example.com/partner.png',
  length: 2.5,
  width: 1.5,
  height: 1.2,
  pricePerLoad: 100,
  rating: 4.5,
  reviewCount: 12,
  isAvailable: true,
  standingArea: 3.75,
  description: 'Perfect for small renovations'
};

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <ThemeProvider>
      <RTLProvider>
        {component}
      </RTLProvider>
    </ThemeProvider>
  );
};

describe('DumpsterCard', () => {
  it('renders correctly with basic props', () => {
    const onPress = jest.fn();
    const { getByText, getByTestId } = renderWithProviders(
      <DumpsterCard dumpster={mockDumpster} onPress={onPress} />
    );

    expect(getByText('5m³ Dumpster')).toBeTruthy();
    expect(getByText('2.5m × 1.5m × 1.2m')).toBeTruthy();
    expect(getByText('﷼100')).toBeTruthy();
    expect(getByText('Available')).toBeTruthy();
    expect(getByText('4.5')).toBeTruthy();
    expect(getByText('(12)')).toBeTruthy();
  });

  it('shows loading state when isLoading is true', () => {
    const { getByTestId } = renderWithProviders(
      <DumpsterCard dumpster={mockDumpster} onPress={() => {}} isLoading={true} />
    );

    expect(getByTestId('loading-indicator')).toBeTruthy();
  });

  it('shows recommendation score when provided', () => {
    const { getByText } = renderWithProviders(
      <DumpsterCard 
        dumpster={mockDumpster} 
        onPress={() => {}} 
        showRecommendation={true}
        recommendationScore={85}
      />
    );

    expect(getByText('85% Match')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = renderWithProviders(
      <DumpsterCard dumpster={mockDumpster} onPress={onPress} />
    );

    fireEvent.press(getByTestId('dumpster-card'));
    expect(onPress).toHaveBeenCalled();
  });

  it('shows unavailable status when isAvailable is false', () => {
    const unavailableDumpster = { ...mockDumpster, isAvailable: false };
    const { getByText } = renderWithProviders(
      <DumpsterCard dumpster={unavailableDumpster} onPress={() => {}} />
    );

    expect(getByText('Unavailable')).toBeTruthy();
  });

  it('displays RTL text when RTL is enabled', () => {
    const { getByText } = renderWithProviders(
      <DumpsterCard dumpster={mockDumpster} onPress={() => {}} />
    );

    expect(getByText('حاوية 5م³')).toBeTruthy();
  });
}); 