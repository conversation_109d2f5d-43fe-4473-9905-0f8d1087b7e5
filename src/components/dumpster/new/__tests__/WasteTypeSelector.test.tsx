import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import WasteTypeSelector from '../WasteTypeSelector';
import { ThemeProvider } from '@/context/ThemeContext';
import { RTLProvider } from '@/components/rtl/new-index';

const mockWasteTypes = [
  {
    id: '1',
    nameEn: 'Construction Waste',
    nameAr: 'نفايات البناء',
    descriptionEn: 'Materials from construction and demolition projects',
    descriptionAr: 'مواد من مشاريع البناء والهدم',
    imageUrl: 'https://example.com/construction.jpg',
    tags: []
  },
  {
    id: '2',
    nameEn: 'Household Waste',
    nameAr: 'نفايات منزلية',
    descriptionEn: 'General household waste and garbage',
    descriptionAr: 'النفايات المنزلية العامة والقمامة',
    imageUrl: 'https://example.com/household.jpg',
    tags: []
  }
];

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <ThemeProvider>
      <RTLProvider>
        {component}
      </RTLProvider>
    </ThemeProvider>
  );
};

describe('WasteTypeSelector', () => {
  it('renders all waste types', () => {
    const onSelect = jest.fn();
    const { getByText, getByTestId } = renderWithProviders(
      <WasteTypeSelector 
        wasteTypes={mockWasteTypes} 
        selectedId={null} 
        onSelect={onSelect} 
      />
    );

    expect(getByText('Construction Waste')).toBeTruthy();
    expect(getByText('Household Waste')).toBeTruthy();
    expect(getByTestId('waste-type-1')).toBeTruthy();
    expect(getByTestId('waste-type-2')).toBeTruthy();
  });

  it('shows selected state correctly', () => {
    const onSelect = jest.fn();
    const { getByTestId } = renderWithProviders(
      <WasteTypeSelector 
        wasteTypes={mockWasteTypes} 
        selectedId="1" 
        onSelect={onSelect} 
      />
    );

    const selectedType = getByTestId('waste-type-1');
    expect(selectedType.props.className).toContain('bg-primary-500');
  });

  it('calls onSelect when a type is pressed', () => {
    const onSelect = jest.fn();
    const { getByTestId } = renderWithProviders(
      <WasteTypeSelector 
        wasteTypes={mockWasteTypes} 
        selectedId={null} 
        onSelect={onSelect} 
      />
    );

    fireEvent.press(getByTestId('waste-type-1'));
    expect(onSelect).toHaveBeenCalledWith('1');
  });

  it('displays RTL text when RTL is enabled', () => {
    const onSelect = jest.fn();
    const { getByText } = renderWithProviders(
      <WasteTypeSelector 
        wasteTypes={mockWasteTypes} 
        selectedId={null} 
        onSelect={onSelect} 
      />
    );

    expect(getByText('نفايات البناء')).toBeTruthy();
    expect(getByText('نفايات منزلية')).toBeTruthy();
  });
}); 