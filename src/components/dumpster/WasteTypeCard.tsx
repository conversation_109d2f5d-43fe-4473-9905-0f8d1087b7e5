import React from 'react';
import { View, Text, Image, StyleSheet, Pressable } from 'react-native';
import { WasteType } from '@/types/dumpster';
import { useTranslation } from 'react-i18next';
import { Card, Chip } from 'react-native-paper';
import { cssInterop } from 'nativewind';

const StyledView = cssInterop(View, { className: 'style' });
const StyledText = cssInterop(Text, { className: 'style' });
const StyledCard = cssInterop(Card, { className: 'style' });
const StyledChip = cssInterop(Chip, { className: 'style' });

interface WasteTypeCardProps {
  wasteType: WasteType;
  selected?: boolean;
  onPress?: () => void;
}

export const WasteTypeCard: React.FC<WasteTypeCardProps> = ({
  wasteType,
  selected = false,
  onPress,
}) => {
  const { t } = useTranslation();
  
  return (
    <StyledCard
      className={`mb-2 overflow-hidden ${selected ? 'border-2 border-primary' : ''}`}
      onPress={onPress}
    >
      <StyledView className="flex-row">
        <Image
          source={{ uri: wasteType.imageUrl }}
          style={styles.image}
          resizeMode="cover"
        />
        <StyledView className="flex-1 p-3">
          <StyledText className="text-lg font-bold mb-1">{wasteType.name}</StyledText>
          {wasteType.description && (
            <StyledText className="text-sm text-gray-600 mb-2" numberOfLines={2}>
              {wasteType.description}
            </StyledText>
          )}
          <StyledView className="flex-row flex-wrap">
            {wasteType.tags.map((tag) => (
              <StyledChip
                key={tag.id}
                className="mr-1 mb-1"
                compact
                mode="outlined"
              >
                {tag.name}
              </StyledChip>
            ))}
          </StyledView>
        </StyledView>
      </StyledView>
    </StyledCard>
  );
};

const styles = StyleSheet.create({
  image: {
    width: 100,
    height: 100,
  },
});

export default WasteTypeCard; 