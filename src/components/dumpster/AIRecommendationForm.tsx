import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Button, Card, Chip, TextInput, HelperText } from 'react-native-paper';
import { cssInterop } from "nativewind";
import { AIRecommendationRequest, WasteType } from '@/types/dumpster';
// Temporarily comment out DateTimePicker to avoid the error
// import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';

const StyledView = cssInterop(View, { className: 'style' });
const StyledText = cssInterop(Text, { className: 'style' });
const StyledScrollView = cssInterop(ScrollView, { className: 'style' });
const StyledCard = cssInterop(Card, { className: 'style' });
const StyledChip = cssInterop(Chip, { className: 'style' });
const StyledTextInput = cssInterop(TextInput, { className: 'style' });
const StyledButton = cssInterop(Button, { className: 'style' });
const StyledHelperText = cssInterop(HelperText, { className: 'style' });

interface AIRecommendationFormProps {
  wasteTypes: WasteType[];
  onSubmit: (request: AIRecommendationRequest) => void;
  isLoading: boolean;
}

export const AIRecommendationForm: React.FC<AIRecommendationFormProps> = ({
  wasteTypes,
  onSubmit,
  isLoading,
}) => {
  const { t } = useTranslation();
  const [request, setRequest] = useState<AIRecommendationRequest>({
    projectDescription: '',
    wasteTypeIds: [],
    duration: {
      startDate: format(new Date(), 'yyyy-MM-dd'),
    },
    budget: {},
  });
  
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  
  // Handle waste type selection
  const toggleWasteType = (wasteTypeId: string) => {
    const wasteTypeIds = request.wasteTypeIds || [];
    const newWasteTypeIds = wasteTypeIds.includes(wasteTypeId)
      ? wasteTypeIds.filter(id => id !== wasteTypeId)
      : [...wasteTypeIds, wasteTypeId];
    
    setRequest({
      ...request,
      wasteTypeIds: newWasteTypeIds,
    });
  };
  
  // Handle date changes
  const handleStartDateChange = () => {
    // Simplified date handling without DateTimePicker
    const today = new Date();
    setRequest({
      ...request,
      duration: {
        ...request.duration,
        startDate: format(today, 'yyyy-MM-dd'),
      },
    });
    setShowStartDatePicker(false);
  };
  
  const handleEndDateChange = () => {
    // Simplified date handling without DateTimePicker
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    
    // Ensure startDate is always defined
    const startDate = request.duration?.startDate || format(new Date(), 'yyyy-MM-dd');
    
    setRequest({
      ...request,
      duration: {
        startDate,
        endDate: format(nextWeek, 'yyyy-MM-dd'),
      },
    });
    setShowEndDatePicker(false);
  };
  
  // Handle budget changes
  const handleBudgetChange = (type: 'min' | 'max', value: string) => {
    const numValue = value ? parseFloat(value) : undefined;
    setRequest({
      ...request,
      budget: {
        ...request.budget,
        [type]: numValue,
      },
    });
  };
  
  // Validate form
  const isValid = () => {
    return (
      (request.projectDescription && request.projectDescription.length > 0) ||
      (request.wasteTypeIds && request.wasteTypeIds.length > 0)
    );
  };
  
  return (
    <StyledCard className="mb-4">
      <StyledCard.Content>
        <StyledText className="text-xl font-bold mb-4">
          {t('Get AI Recommendations')}
        </StyledText>
        
        <StyledView className="mb-4">
          <StyledText className="font-bold mb-2">
            {t('Describe Your Project')}
          </StyledText>
          <StyledTextInput
            className="border border-gray-300 rounded-md p-2"
            placeholder={t('What are you working on?')}
            multiline
            numberOfLines={3}
            value={request.projectDescription}
            onChangeText={(text: string) => setRequest({ ...request, projectDescription: text })}
          />
          <StyledHelperText>
            {t('Tell us about your project to get better recommendations')}
          </StyledHelperText>
        </StyledView>
        
        <StyledView className="mb-4">
          <StyledText className="font-bold mb-2">
            {t('Select Waste Types')}
          </StyledText>
          <StyledScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="mb-2"
          >
            {wasteTypes.map((wasteType) => (
              <StyledChip
                key={wasteType.id}
                selected={(request.wasteTypeIds || []).includes(wasteType.id)}
                onPress={() => toggleWasteType(wasteType.id)}
                className="mr-2"
                mode="outlined"
              >
                {wasteType.name}
              </StyledChip>
            ))}
          </StyledScrollView>
        </StyledView>
        
        <StyledView className="mb-4">
          <StyledText className="font-bold mb-2">
            {t('Rental Duration')}
          </StyledText>
          
          <StyledView className="flex-row mb-2">
            <StyledView className="flex-1 mr-2">
              <StyledButton
                mode="outlined"
                onPress={handleStartDateChange}
              >
                {request.duration?.startDate
                  ? format(new Date(request.duration.startDate), 'MMM dd, yyyy')
                  : t('Start Date')}
              </StyledButton>
            </StyledView>
            
            <StyledView className="flex-1 ml-2">
              <StyledButton
                mode="outlined"
                onPress={handleEndDateChange}
              >
                {request.duration?.endDate
                  ? format(new Date(request.duration.endDate), 'MMM dd, yyyy')
                  : t('End Date (Optional)')}
              </StyledButton>
            </StyledView>
          </StyledView>
          
          {/* Temporarily comment out DateTimePicker components
          {showStartDatePicker && (
            <DateTimePicker
              value={request.duration?.startDate ? new Date(request.duration.startDate) : new Date()}
              mode="date"
              display="default"
              onChange={handleStartDateChange}
              minimumDate={new Date()}
            />
          )}
          
          {showEndDatePicker && (
            <DateTimePicker
              value={request.duration?.endDate ? new Date(request.duration.endDate) : new Date()}
              mode="date"
              display="default"
              onChange={handleEndDateChange}
              minimumDate={request.duration?.startDate ? new Date(request.duration.startDate) : new Date()}
            />
          )}
          */}
        </StyledView>
        
        <StyledView className="mb-4">
          <StyledText className="font-bold mb-2">
            {t('Budget (Optional)')}
          </StyledText>
          
          <StyledView className="flex-row">
            <StyledView className="flex-1 mr-2">
              <StyledTextInput
                className="border border-gray-300 rounded-md p-2"
                placeholder={t('Min ($)')}
                keyboardType="numeric"
                value={request.budget?.min?.toString() || ''}
                onChangeText={(text: string) => handleBudgetChange('min', text)}
              />
            </StyledView>
            
            <StyledView className="flex-1 ml-2">
              <StyledTextInput
                className="border border-gray-300 rounded-md p-2"
                placeholder={t('Max ($)')}
                keyboardType="numeric"
                value={request.budget?.max?.toString() || ''}
                onChangeText={(text: string) => handleBudgetChange('max', text)}
              />
            </StyledView>
          </StyledView>
        </StyledView>
        
        <StyledButton
          mode="contained"
          onPress={() => onSubmit(request)}
          disabled={!isValid() || isLoading}
          loading={isLoading}
        >
          {isLoading ? t('Getting Recommendations...') : t('Get Recommendations')}
        </StyledButton>
      </StyledCard.Content>
    </StyledCard>
  );
};

export default AIRecommendationForm; 