import React from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import { styled } from 'nativewind';
import { useTheme } from '@/context/ThemeContext';
import { useRTLContext } from '@/components/rtl/new-index';
import { NewRTLText, NewRTLView } from '@/components/rtl/new-index';
import * as colors from '@/theme/colors';
import { MaterialIcons, Feather } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  interpolate,
  Extrapolation
} from 'react-native-reanimated';
import { Surface } from 'react-native-paper';
import { useTranslation } from 'react-i18next';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledTextInput = styled(TextInput);
const StyledAnimatedView = styled(Animated.View);
const StyledSurface = styled(Surface);

export interface FilterOption {
  id: string;
  label: string;
}

export interface StickySearchHeaderProps {
  searchValue: string;
  onSearchChange: (text: string) => void;
  onFilterPress: () => void;
  onSortPress: () => void;
  projectTypes: FilterOption[];
  selectedProjectType: string | null;
  onProjectTypeChange: (id: string) => void;
  countOptions: FilterOption[];
  selectedCount: string | null;
  onCountChange: (id: string) => void;
  scrollY: Animated.SharedValue<number>;
  stickyThreshold: number;
  testID?: string;
  isTopHeader?: boolean;
}

/**
 * StickySearchHeader - A search and filter header that can appear in two places:
 * 1. In the top navigation area (hidden until scrolled past the threshold)
 * 2. In the content area below the marketing carousel (visible initially)
 *
 * Contains a search input, filter button, and two pickers for project type and count/size
 */
export default function StickySearchHeader({
  searchValue,
  onSearchChange,
  onFilterPress,
  onSortPress,
  projectTypes,
  selectedProjectType,
  onProjectTypeChange,
  countOptions,
  selectedCount,
  onCountChange,
  scrollY,
  stickyThreshold,
  testID = 'sticky-search-header',
  isTopHeader = false,
}: StickySearchHeaderProps) {
  const { isDarkMode } = useTheme();
  const { isRTL } = useRTLContext();
  const { t } = useTranslation();

  // Animated styles for visibility and sticky behavior
  const animatedContainerStyle = useAnimatedStyle(() => {
    // For top header: show when scrolled past threshold
    if (isTopHeader) {
      const opacity = interpolate(
        scrollY.value,
        [stickyThreshold - 20, stickyThreshold],
        [0, 1],
        Extrapolation.CLAMP
      );

      return {
        opacity,
        zIndex: 1020,
        position: 'absolute',
        top: -8,
        left: 0,
        right: 0,
        backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
      };
    }
    // For content header: hide when scrolled past threshold
    else {
      const opacity = interpolate(
        scrollY.value,
        [stickyThreshold - 20, stickyThreshold],
        [1, 0],
        Extrapolation.CLAMP
      );

      return {
        opacity,
        //zIndex: 1010,
        position: 'relative',
        backgroundColor: 'transparent', // made it red to inspect the UI elements
        borderWidth: 0, // made the border visible to inspect the UI elements
        borderColor: 'black', // made the border black to inspect the UI elements
        elevation: 0,
      };
    }
  });

  return (
    <StyledAnimatedView
      style={[
        animatedContainerStyle,
        {
          //paddingHorizontal: 16,
          paddingTop: isTopHeader ? 16 : 0,
          paddingBottom: isTopHeader ? 0 : 0,
          //paddingVertical: isTopHeader ? 8 : 12,
          marginTop: isTopHeader ? 0 : 0,
          marginHorizontal: isTopHeader ? 0 : -16,
          elevation: 0,
        }
      ]}
      testID={testID}
    >
      <StyledSurface
        elevation={0}
        style={{
          backgroundColor: isDarkMode ? colors.backgroundColors.main.dark : colors.backgroundColors.main.light,
          //borderRadius: 16,
          paddingHorizontal: 16,
          paddingVertical: 12,
          //paddingBottom: 0
          elevation: 0,
        }}
        className={` overflow-hidden `}
      >
        {/* Search Input */}
        <StyledView className='flex-row items-center justify-between'>
          <NewRTLView style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            width: '90%',
            borderColor: isDarkMode ? colors.surfaceColors.outline.dark : colors.surfaceColors.outline.light,
            borderWidth: 1,
            alignItems: 'center',
            //marginBottom: 12,
            borderRadius: 32,
            paddingHorizontal: 12,
            paddingVertical: 8,

            backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
          }}>
            <Feather name="search" size={20} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
            <StyledTextInput
              className="flex-1 ml-2 text-base"
              placeholder={t('search.placeholder', 'Search size, type, project...')}
              placeholderTextColor={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light}
              value={searchValue}
              onChangeText={onSearchChange}
              style={{
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                textAlign: isRTL ? 'right' : 'left',
                marginLeft: isRTL ? 0 : 8,
                marginRight: isRTL ? 8 : 0,
              }}
              testID="search-input"
            />
            {searchValue.length > 0 && (
              <StyledTouchableOpacity onPress={() => onSearchChange('')}>
                <Feather name="x" size={18} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
              </StyledTouchableOpacity>
            )}
          </NewRTLView>
           {/* Filter Button */}
          <StyledTouchableOpacity
            className="ml-2"
            style={{alignSelf: 'center'}}
            onPress={onFilterPress}
            testID="filter-button"
          >
            <MaterialIcons name="filter-list-alt" size={28} color={colors.brandColors.primary[500]} />
          </StyledTouchableOpacity>
        </StyledView>

        {/* Pickers Row */}
        {/* <NewRTLView style={{
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>

          <StyledTouchableOpacity
            className="flex-row items-center justify-between  border border-gray-300 dark:border-gray-700 px-3 py-2"
            style={{
              width: '37%',
              borderRadius: 32,
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
            }}
            onPress={onFilterPress}
            // {() => {
            //   // Show project type picker
            //   if (projectTypes.length > 0) {
            //     const firstType = projectTypes[0].id;
            //     onProjectTypeChange(selectedProjectType === firstType ? '' : firstType);
            //   }
            // }}
            testID="project-type-picker"
          >
            <NewRTLText
              style={{
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                fontSize: 14,
              }}
            >
              {selectedProjectType
                ? projectTypes.find(t => t.id === selectedProjectType)?.label || 'Project type'
                : 'Project type'}
            </NewRTLText>
            <MaterialIcons name="arrow-drop-down" size={24} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
          </StyledTouchableOpacity>


          <StyledTouchableOpacity
            className="flex-row items-center justify-between  border border-gray-300 dark:border-gray-700 px-3 py-2"
            style={{
              width: '50%',
              borderRadius: 32,
              backgroundColor: isDarkMode ? colors.surfaceColors.container.dark : colors.surfaceColors.container.light,
            }}
            onPress={onFilterPress}

            testID="count-picker"
          >
            <NewRTLText
              style={{
                color: isDarkMode ? colors.textColors.dark : colors.textColors.light,
                fontSize: 14,
              }}
            >
              {selectedCount
                ? countOptions.find(c => c.id === selectedCount)?.label || 'Waste type'
                : 'Waste type'}
            </NewRTLText>
            <MaterialIcons name="arrow-drop-down" size={24} color={isDarkMode ? colors.textColors.secondary.dark : colors.textColors.secondary.light} />
          </StyledTouchableOpacity>



          <StyledTouchableOpacity onPress={onSortPress} testID="sort-button">
            <MaterialIcons name="sort" size={24} color={colors.brandColors.primary[500]} />
          </StyledTouchableOpacity>
        </NewRTLView> */}
      </StyledSurface>
    </StyledAnimatedView>
  );
}
