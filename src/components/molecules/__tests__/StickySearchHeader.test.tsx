import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import StickySearchHeader, { FilterOption } from '../StickySearchHeader';
import { useSharedValue } from 'react-native-reanimated';

// Mock the hooks
jest.mock('@/context/ThemeContext', () => ({
  useTheme: () => ({ isDarkMode: false }),
}));

jest.mock('@/components/rtl/new-index', () => ({
  useRTLContext: () => ({ isRTL: false }),
  NewRTLText: 'Text',
  NewRTLView: 'View',
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = jest.requireActual('react-native-reanimated/mock');

  // Add useAnimatedStyle mock
  Reanimated.useAnimatedStyle = jest.fn(() => ({}));

  // Add interpolate mock
  Reanimated.interpolate = jest.fn((value, inputRange, outputRange) => {
    // Simulate interpolation based on scroll position
    if (value < inputRange[0]) return outputRange[0];
    if (value > inputRange[1]) return outputRange[1];
    return outputRange[0];
  });

  // Add useSharedValue mock
  Reanimated.useSharedValue = jest.fn((initialValue) => ({
    value: initialValue,
  }));

  // Add Extrapolation mock
  Reanimated.Extrapolation = {
    CLAMP: 'clamp',
  };

  return Reanimated;
});

const mockProjectTypes: FilterOption[] = [
  { id: 'construction', label: 'Construction' },
  { id: 'renovation', label: 'Renovation' },
];

const mockCountOptions: FilterOption[] = [
  { id: 'small', label: 'Small' },
  { id: 'medium', label: 'Medium' },
];

describe('StickySearchHeader', () => {
  const defaultProps = {
    searchValue: '',
    onSearchChange: jest.fn(),
    onFilterPress: jest.fn(),
    onSortPress: jest.fn(),
    projectTypes: mockProjectTypes,
    selectedProjectType: null,
    onProjectTypeChange: jest.fn(),
    countOptions: mockCountOptions,
    selectedCount: null,
    onCountChange: jest.fn(),
    scrollY: { value: 0 },
    stickyThreshold: 200,
  };

  it('renders correctly', () => {
    const { getByTestId, getByPlaceholderText } = render(
      <StickySearchHeader {...defaultProps} />
    );

    expect(getByTestId('sticky-search-header')).toBeTruthy();
    expect(getByPlaceholderText('Search dumpsters...')).toBeTruthy();
  });

  it('calls onSearchChange when text input changes', () => {
    const onSearchChange = jest.fn();
    const { getByPlaceholderText } = render(
      <StickySearchHeader {...defaultProps} onSearchChange={onSearchChange} />
    );

    const searchInput = getByPlaceholderText('Search dumpsters...');
    fireEvent.changeText(searchInput, 'test search');

    expect(onSearchChange).toHaveBeenCalledWith('test search');
  });

  it('calls onFilterPress when filter button is pressed', () => {
    const onFilterPress = jest.fn();
    const { getByTestId } = render(
      <StickySearchHeader {...defaultProps} onFilterPress={onFilterPress} />
    );

    const filterButton = getByTestId('filter-button');
    fireEvent.press(filterButton);

    expect(onFilterPress).toHaveBeenCalled();
  });

  it('calls onSortPress when sort button is pressed', () => {
    const onSortPress = jest.fn();
    const { getByTestId } = render(
      <StickySearchHeader {...defaultProps} onSortPress={onSortPress} />
    );

    const sortButton = getByTestId('sort-button');
    fireEvent.press(sortButton);

    expect(onSortPress).toHaveBeenCalled();
  });

  it('displays selected project type when available', () => {
    const { getByText } = render(
      <StickySearchHeader
        {...defaultProps}
        selectedProjectType="construction"
      />
    );

    expect(getByText('Construction')).toBeTruthy();
  });

  it('displays selected count when available', () => {
    const { getByText } = render(
      <StickySearchHeader
        {...defaultProps}
        selectedCount="medium"
      />
    );

    expect(getByText('Medium')).toBeTruthy();
  });

  it('applies sticky styles when scrolling past threshold', () => {
    const mockScrollY = {
      value: 210, // Past the threshold
    };

    const { getByTestId } = render(
      <StickySearchHeader
        {...defaultProps}
        scrollY={mockScrollY}
        stickyThreshold={200}
      />
    );

    // Verify the component renders with the correct testID
    expect(getByTestId('sticky-search-header')).toBeTruthy();

    // The useAnimatedStyle mock should have been called
    expect(require('react-native-reanimated').useAnimatedStyle).toHaveBeenCalled();
  });

  it('applies non-sticky styles when before threshold', () => {
    const mockScrollY = {
      value: 190, // Before the threshold
    };

    const { getByTestId } = render(
      <StickySearchHeader
        {...defaultProps}
        scrollY={mockScrollY}
        stickyThreshold={200}
      />
    );

    // Verify the component renders with the correct testID
    expect(getByTestId('sticky-search-header')).toBeTruthy();

    // The useAnimatedStyle mock should have been called
    expect(require('react-native-reanimated').useAnimatedStyle).toHaveBeenCalled();
  });
});
