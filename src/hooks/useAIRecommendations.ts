import { useMutation } from '@tanstack/react-query';
import { getDumpsterRecommendations } from '@/services/ai/openai';
import { AIRecommendationRequest, DumpsterRecommendation, Dumpster } from '@/types/dumpster';
import { dumpsters, wasteTypes } from '@/services/dumpster/mockData';

// Helper function to calculate size score
function calculateSizeScore(dumpster: Dumpster, projectDescription?: string): { score: number; reason: string } {
  // Get the largest size
  const largestSize = dumpster.sizes.reduce((max, size) => 
    size.volumeCubicYards > max.volumeCubicYards ? size : max
  , dumpster.sizes[0]);

  // Basic size categories (in cubic yards)
  const sizeCategories = {
    small: { max: 10, terms: ['small', 'tiny', 'minor', 'basic'] },
    medium: { max: 20, terms: ['medium', 'moderate', 'average'] },
    large: { max: 30, terms: ['large', 'big', 'major'] },
    extraLarge: { max: Infinity, terms: ['huge', 'massive', 'enormous', 'extra large'] }
  };

  // Determine size category of the dumpster
  let dumpsterCategory: keyof typeof sizeCategories = 'small';
  for (const [category, { max }] of Object.entries(sizeCategories)) {
    if (largestSize.volumeCubicYards <= max) {
      dumpsterCategory = category as keyof typeof sizeCategories;
      break;
    }
  }

  // If we have a project description, try to match size terms
  if (projectDescription) {
    const descLower = projectDescription.toLowerCase();
    for (const [category, { terms }] of Object.entries(sizeCategories)) {
      if (terms.some(term => descLower.includes(term))) {
        // Perfect match
        if (category === dumpsterCategory) {
          return {
            score: 100,
            reason: `Perfect size match: ${largestSize.volumeCubicYards} cubic yards for your ${category} project`
          };
        }
        // Adjacent category (e.g., medium when looking for large)
        const categories = Object.keys(sizeCategories);
        const requestedIndex = categories.indexOf(category);
        const actualIndex = categories.indexOf(dumpsterCategory);
        if (Math.abs(requestedIndex - actualIndex) === 1) {
          return {
            score: 80,
            reason: `Good size option: ${largestSize.volumeCubicYards} cubic yards, close to your requested ${category} size`
          };
        }
      }
    }
  }

  // Default scoring based on size variety
  return {
    score: 85,
    reason: `Versatile size: ${largestSize.volumeCubicYards} cubic yards suitable for many projects`
  };
}

/**
 * Hook to get AI recommendations for dumpsters with enhanced size and waste type focus
 */
export function useAIRecommendations() {
  return useMutation<
    DumpsterRecommendation[],
    Error,
    AIRecommendationRequest
  >({
    mutationFn: async (request: AIRecommendationRequest) => {
      try {
        // In development, we'll use enhanced local recommendations
        if (process.env.NODE_ENV === 'development') {
          await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API delay
          
          const recommendations: DumpsterRecommendation[] = [];
          
          // First pass: Calculate base scores and filter by waste type
          for (const dumpster of dumpsters) {
            const reasons: string[] = [];
            let totalScore = 0;
            
            // 1. Size Compatibility (40% of total score)
            const { score: sizeScore, reason: sizeReason } = calculateSizeScore(dumpster, request.projectDescription);
            reasons.push(sizeReason);
            totalScore += sizeScore * 0.4;
            
            // 2. Waste Type Compatibility (40% of total score)
            if (request.wasteTypeIds?.length) {
              const compatibleTypes = request.wasteTypeIds.filter(wtId => 
                dumpster.compatibleWasteTypes.includes(wtId)
              );
              const wasteTypeScore = (compatibleTypes.length / request.wasteTypeIds.length) * 100;
              totalScore += wasteTypeScore * 0.4;
              
              if (compatibleTypes.length > 0) {
                const compatibilityPercent = Math.round((compatibleTypes.length / request.wasteTypeIds.length) * 100);
                reasons.push(`${compatibilityPercent}% waste type compatibility match`);
              }
            } else {
              totalScore += 85 * 0.4; // Default waste type score if no specific types requested
              reasons.push('Compatible with various waste types');
            }
            
            // 3. Rating and Reviews (20% of total score)
            if (dumpster.rating) {
              const ratingScore = (dumpster.rating / 5) * 100;
              totalScore += ratingScore * 0.2;
              
              if (dumpster.rating >= 4.5) {
                reasons.push(`Excellent rating: ${dumpster.rating}/5 from ${dumpster.reviewCount} reviews`);
              } else if (dumpster.rating >= 4.0) {
                reasons.push(`Good rating: ${dumpster.rating}/5 from ${dumpster.reviewCount} reviews`);
              }
            } else {
              totalScore += 70 * 0.2; // Default rating score for unrated dumpsters
            }
            
            // Add price information as additional context
            reasons.push(`Priced at $${dumpster.pricePerDay}/day`);
            
            // Only include recommendations with a total score above 70
            if (totalScore >= 70) {
              recommendations.push({
                dumpsterId: dumpster.id,
                score: Math.round(totalScore),
                reasons
              });
            }
          }
          
          // Sort by score (highest first) and limit to top 5
          return recommendations
            .sort((a, b) => b.score - a.score)
            .slice(0, 5);
        }
        
        // In production, use the actual API
        return getDumpsterRecommendations(request, dumpsters, wasteTypes);
      } catch (error) {
        console.error('Error getting AI recommendations:', error);
        throw error;
      }
    }
  });
} 