import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { GlobalUserIdentifier } from '@/context/AuthContext';
interface UserLocation {
  latitude: number;
  longitude: number;
  address: string;
}

export function useUserLocation() {
  return useQuery<UserLocation>({
    queryKey: ['userLocation'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) throw new Error('No user found');

      const { data, error } = await supabase
        .from('addresses')
        .select('latitude, longitude, street_address, city, state, zip_code')
        .eq('user_id', GlobalUserIdentifier.profileId)
        .eq('is_default', true)
        .single();

      if (error) throw error;

      return {
        latitude: data.latitude,
        longitude: data.longitude,
        address: `${data.street_address}, ${data.city}, ${data.state} ${data.zip_code}`
      };
    }
  });
}