import { useTheme as usePaperTheme } from 'react-native-paper';
import { useTheme as useCustomTheme } from '@/context/ThemeContext';
import { buttonVariants, backgroundColors, textColors } from '@/theme/colors';
import * as colors from '@/theme/colors';
import type { ButtonVariant } from '@/components/ui/Button';

export function useAppTheme() {
  const paperTheme = usePaperTheme();
  const { theme, isDarkMode, toggleTheme, setTheme } = useCustomTheme();

  return {
    // Paper theme values
    ...paperTheme,
    
    // Custom theme values
    theme,
    isDarkMode,
    toggleTheme,
    setTheme,
    
    // Button variants
    buttons: buttonVariants,
    
    // Helper functions
    getButtonStyle: (variant: ButtonVariant, state: 'default' | 'hover' | 'active' | 'disabled', theme: 'light' | 'dark') => {
      return buttonVariants[variant]?.[theme]?.[state];
    },
    
    getShadow: (elevation: number) => ({
      shadowColor: colors.textColors.dark,
      shadowOffset: { width: 0, height: elevation },
      shadowOpacity: 0.1,
      shadowRadius: elevation * 2,
      elevation: elevation,
    }),
  };
} 