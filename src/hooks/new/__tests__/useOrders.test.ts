/// <reference types="jest" />

import { renderHook, act } from '@testing-library/react-hooks';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useCreateOrder } from '../useOrders';
import { api } from '@/lib/api';

// Mock the API client
jest.mock('@/lib/api', () => ({
  api: {
    post: jest.fn(),
  },
}));

describe('useCreateOrder', () => {
  const queryClient = new QueryClient();

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );

  const mockOrderData = {
    dumpsterId: '1',
    wasteTypeId: '1',
    quantity: 1,
    deliveryDate: new Date('2024-03-20'),
    installationType: 'ground' as const,
    location: {
      latitude: 25.2048,
      longitude: 55.2708,
      address: 'Dubai, UAE',
    },
    notes: 'Please deliver in the morning',
  };

  const mockResponse = {
    id: '1',
    ...mockOrderData,
    status: 'pending',
    createdAt: new Date('2024-03-19'),
    updatedAt: new Date('2024-03-19'),
    totalPrice: 299,
    userId: 'user123',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
  });

  it('should create an order successfully', async () => {
    (api.post as jest.Mock).mockResolvedValueOnce({ data: mockResponse });

    const { result } = renderHook(() => useCreateOrder(), { wrapper });

    await act(async () => {
      const response = await result.current.createOrder(mockOrderData);
      expect(response).toEqual(mockResponse);
    });

    expect(api.post).toHaveBeenCalledWith('/orders', mockOrderData);
    expect(result.current.isSuccess).toBe(true);
    expect(result.current.error).toBeNull();
  });

  it('should handle errors when creating an order', async () => {
    const errorMessage = 'Failed to create order';
    (api.post as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));

    const { result } = renderHook(() => useCreateOrder(), { wrapper });

    await act(async () => {
      try {
        await result.current.createOrder(mockOrderData);
      } catch (error) {
        expect(error.message).toBe(errorMessage);
      }
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.isSuccess).toBe(false);
  });

  it('should show loading state while creating an order', async () => {
    (api.post as jest.Mock).mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 100))
    );

    const { result } = renderHook(() => useCreateOrder(), { wrapper });

    act(() => {
      result.current.createOrder(mockOrderData);
    });

    expect(result.current.isLoading).toBe(true);

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
    });

    expect(result.current.isLoading).toBe(false);
  });

  it('should invalidate orders query on successful creation', async () => {
    (api.post as jest.Mock).mockResolvedValueOnce({ data: mockResponse });

    const { result } = renderHook(() => useCreateOrder(), { wrapper });

    await act(async () => {
      await result.current.createOrder(mockOrderData);
    });

    expect(queryClient.getQueryData(['orders'])).toBeUndefined();
  });
}); 