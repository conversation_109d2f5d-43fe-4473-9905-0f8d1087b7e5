import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CreateOrderInput, OrderDetails } from '@/types/new/order';
import { api } from '@/lib/api';

export function useCreateOrder() {
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (orderData: CreateOrderInput): Promise<OrderDetails> => {
      const response = await api.post('/orders', orderData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch orders list
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const createOrder = async (orderData: CreateOrderInput) => {
    try {
      setError(null);
      const result = await mutation.mutateAsync(orderData);
      return result;
    } catch (error) {
      throw error;
    }
  };

  return {
    createOrder,
    isLoading: mutation.isPending,
    error,
    isSuccess: mutation.isSuccess,
  };
}

export function useOrder(orderId: string) {
  return useQueryClient().getQueryData<OrderDetails>(['order', orderId]);
}

export function useOrders(filters?: {
  status?: OrderDetails['status'][];
  startDate?: Date;
  endDate?: Date;
  search?: string;
}) {
  return useQueryClient().getQueryData<OrderDetails[]>(['orders', filters]);
} 