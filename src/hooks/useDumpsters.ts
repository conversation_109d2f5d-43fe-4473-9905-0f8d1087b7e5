import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/services/supabase';
import { useTranslation } from 'react-i18next';

import { Dumpster, DumpsterFilter, DumpsterSize, WasteType } from '@/types/dumpster';
import { WasteType as NewWasteType } from '@/types/new/dumpster';
import { dumpsters as mockDumpsters, wasteTypes as mockWasteTypes } from '@/services/dumpster/mockData';
import { config } from '@/config/appConfig';
import { getSizeFromName } from '@/utils/dumpsterAdapter';

// For development, we'll use mock data
// In production, we would use the actual API calls

// Feature flag to control data source (for development/testing)
// Set to true to force using mock data or false to try real data with fallback
const USE_MOCK_DATA = false;

// Helper function to determine if we should use mock data
const shouldUseMockData = (id?: string) => {
  // Always use real data unless explicitly configured otherwise
  if (!USE_MOCK_DATA) return false;
  
  // For backwards compatibility with mock data IDs (non-UUIDs)
  if (id && !id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    return true;
  }
  
  return USE_MOCK_DATA;
};

/**
 * Hook to fetch all waste types
 */
export function useWasteTypes() {
  return useQuery<WasteType[], Error>({
    queryKey: ['wasteTypes'],
    queryFn: getWasteTypes,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

/**
 * Hook to fetch all dumpsters with optional filtering and real-time updates
 */
export function useDumpsters(filters?: DumpsterFilter) {
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';
  const queryClient = useQueryClient();

  // Subscribe to real-time changes in dumpsters
  useEffect(() => {
    const subscription = supabase
      .channel('dumpsters')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'dumpsters' },
        () => {
          // Invalidate the query to refetch data when changes occur
          queryClient.invalidateQueries({ queryKey: ['dumpsters'] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [queryClient]);

  return useQuery<Dumpster[], Error>({
    queryKey: ['dumpsters', filters, currentLocale],
    queryFn: () => getDumpsters(filters, currentLocale),
    staleTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // This replaces keepPreviousData
    retry: 1, // Only retry once if there's an error
    retryDelay: 1000,
  });
}

/**
 * Hook to fetch a single dumpster by ID with real-time updates
 */
export function useDumpster(id: string, disabled: boolean = false) {
  const queryClient = useQueryClient();

  // Set up real-time subscription for single dumpster
  useEffect(() => {
    if (!id || disabled) return;

    const subscription = supabase
      .channel(`dumpster_${id}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'dumpsters',
          filter: `id=eq.${id}` 
        }, 
        () => {
          queryClient.invalidateQueries({ queryKey: ['dumpster', id] });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [id, queryClient, disabled]);

  return useQuery<Dumpster | null, Error>({
    queryKey: ['dumpster', id],
    queryFn: () => getDumpsterById(id),
    staleTime: 1000 * 60 * 5,
    enabled: !disabled && !!id,
    retry: false,
  });
}

/**
 * Get all dumpsters with optional filtering
 */
export async function getDumpsters(
  filters?: DumpsterFilter,
  locale: string = 'en'
): Promise<Dumpster[]> {
  if (USE_MOCK_DATA) {
    return getFilteredMockDumpsters(filters);
  }

  try {
    const { data, error } = await supabase
      .from('dumpsters')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url,
        price_per_load,
        rating,
        review_count,
        is_available,
        next_available_date,
        size_id,
        partner_id,
        dumpster_waste_types (
          waste_type_id
        ),
        dumpster_feature_links (
          feature_id
        )
      `);

    if (error) {
      console.error('Error fetching dumpsters:', error);
      throw error;
    }

    if (!data || !Array.isArray(data)) {
      console.error('Invalid dumpster data returned from database:', data);
      return getFilteredMockDumpsters(filters);
    }

    // First fetch all sizes for the dumpsters
    const sizeIds = data
      .map(d => d.size_id)
      .filter(Boolean);
    
    // Fetch all sizes in one query
    const { data: sizeData, error: sizeError } = await supabase
      .from('dumpster_sizes')
      .select('*')
      .in('id', sizeIds);
    
    if (sizeError) {
      console.error('Error fetching dumpster sizes:', sizeError);
      throw sizeError;
    }

    // Create a map of sizes by id for quick lookup
    const sizesMap: Record<string, DumpsterSize> = (sizeData || []).reduce((acc: Record<string, DumpsterSize>, size) => {
      acc[size.id] = {
        id: size.id,
        name: size.name,
        volumeCubicYards: size.volume_cubic_yards,
        maxWeightPounds: size.max_weight_pounds,
        dimensions: {
          length: size.length,
          width: size.width,
          height: size.height
        },
        description: size.description
      };
      return acc;
    }, {});

    // Map the database results to our Dumpster interface
    const dumpsters: Dumpster[] = data.filter(d => d && d.id).map(dumpsterData => {
      // First create a base dumpster object using our transform function
      const baseDumpster = transformDumpsterData(dumpsterData);
      
      // Now update the size with the actual size data if we have it
      if (dumpsterData.size_id && sizesMap[dumpsterData.size_id]) {
        baseDumpster.size = sizesMap[dumpsterData.size_id];
      }
      
      return baseDumpster;
    });

    // Apply filters if provided
    if (filters) {
      return applyFilters(dumpsters, filters);
    }

    return dumpsters;
  } catch (error) {
    console.error('Error in getDumpsters:', error);
    // Fall back to mock data if real data fetch fails
    return getFilteredMockDumpsters(filters);
  }
}

// Transform raw database dumpster into our Dumpster interface
function transformDumpsterData(dumpster: any, locale: string = 'en'): Dumpster {
  // Extract waste type IDs from the relationship data - filter null values
  const wasteTypeIds = dumpster.dumpster_waste_types
    ? dumpster.dumpster_waste_types
        .filter((dwt: any) => dwt && dwt.waste_type_id)
        .map((dwt: any) => dwt.waste_type_id)
        .filter(Boolean) // Remove any null/undefined IDs
    : [];
  
  // Extract feature IDs from the new dumpster_feature_links - filter null values
  const featureIds = dumpster.dumpster_feature_links
    ? dumpster.dumpster_feature_links
        .filter((link: any) => link && link.feature_id)
        .map((link: any) => link.feature_id)
        .filter(Boolean) // Remove any null/undefined IDs
    : [];
  
  // Use locale-appropriate name and description
  const name = locale === 'ar' && dumpster.name_ar 
    ? dumpster.name_ar 
    : dumpster.name_en || 'Unnamed Dumpster';
    
  const description = locale === 'ar' && dumpster.description_ar 
    ? dumpster.description_ar 
    : dumpster.description_en || 'No description available';
  
  // Use image URL directly from database with fallback
  const imageUrl = typeof dumpster.image_url === 'string' && dumpster.image_url
    ? dumpster.image_url 
    : 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';
  
  // Create default size based on dumpster name if available
  const sizeFromName = getSizeFromName(dumpster.name_en);
  
  // Default size when no size is associated
  const defaultSize: DumpsterSize = {
    id: `size-${dumpster.id}-default`,
    name: sizeFromName ? `${sizeFromName}m³` : "Standard Size",
    volumeCubicYards: sizeFromName || 10,
    maxWeightPounds: 2000,
    dimensions: { 
      length: sizeFromName ? Math.max(3.5, sizeFromName * 0.4) : 10, 
      width: sizeFromName ? Math.max(2, sizeFromName * 0.3) : 8, 
      height: sizeFromName ? Math.max(1.5, sizeFromName * 0.2) : 6 
    },
    description: sizeFromName ? `${sizeFromName} cubic meter size` : "Standard size dumpster"
  };
  
  const transformedDumpster: Dumpster = {
    id: dumpster.id,
    name: name,
    description: description,
    imageUrl: imageUrl,
    size: defaultSize, // Note: The actual size object is set separately after fetching the size data
    compatibleWasteTypes: wasteTypeIds,
    pricePerDay: dumpster.price_per_load || 75,
    availability: {
      isAvailable: dumpster.is_available !== undefined ? dumpster.is_available : true,
      nextAvailableDate: dumpster.next_available_date,
    },
    featureIds: featureIds,
    rating: dumpster.rating || 4.0,
    reviewCount: dumpster.review_count || 0,
    partnerId: dumpster.partner_id,
  };
  
  return transformedDumpster;
}

// Fallback to mock data when real data is unavailable
function fallbackToMockData(filters?: DumpsterFilter): Dumpster[] {
  console.log('Falling back to mock dumpster data');
  return getFilteredMockDumpsters(filters);
}

/**
 * Get mock dumpsters with filtering options
 * This is used for development and testing
 */
function getFilteredMockDumpsters(filters?: DumpsterFilter): Dumpster[] {
  let result = mockDumpsters;

  if (!filters) {
    return result;
  }

  // Filter by waste type IDs if specified
  if (filters.wasteTypeIds?.length) {
    result = result.filter(dumpster => 
      filters.wasteTypeIds?.some(id => 
        dumpster.compatibleWasteTypes.includes(id)
      )
    );
  }

  // Filter by price range if specified
  if (filters.priceRange) {
    if (filters.priceRange.min !== undefined) {
      result = result.filter(d => 
        d.pricePerDay >= (filters.priceRange?.min || 0)
      );
    }
    
    if (filters.priceRange.max !== undefined) {
      result = result.filter(d => 
        d.pricePerDay <= (filters.priceRange?.max || Infinity)
      );
    }
  }

  // Filter by search term if provided
  if (filters.searchTerm) {
    const searchTerm = filters.searchTerm.toLowerCase();
    result = result.filter(d => {
      const nameMatch = d.name.toLowerCase().includes(searchTerm);
      const descMatch = d.description ? 
        d.description.toLowerCase().includes(searchTerm) : false;
      return nameMatch || descMatch;
    });
  }

  // Filter by size ID if specified
  if (filters.sizeId) {
    result = result.filter(dumpster => dumpster.size.id === filters.sizeId);
  }

  return result;
}

/**
 * Get a single dumpster by ID
 */
export async function getDumpsterById(id: string): Promise<Dumpster | null> {
  if (!id) {
    console.error('No dumpster ID provided');
    return null;
  }
  
  if (shouldUseMockData(id)) {
    return mockDumpsters.find(d => d.id === id) || null;
  }

  try {
    // First fetch the dumpster basic info
    const { data, error } = await supabase
      .from('dumpsters')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url,
        price_per_load,
        rating,
        review_count,
        is_available,
        next_available_date,
        size_id,
        partner_id,
        dumpster_waste_types (
          waste_type_id
        ),
        dumpster_feature_links (
          feature_id
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      console.error('Error fetching dumpster:', error);
      throw error;
    }
    
    if (!data || !data.id) {
      console.error('Invalid dumpster data returned from database:', data);
      return null;
    }

    // Create the initial dumpster object without size
    const dumpster = transformDumpsterData(data);

    // Fetch the dumpster size if size_id exists
    if (data.size_id) {
      const { data: sizeData, error: sizeError } = await supabase
        .from('dumpster_sizes')
        .select('*')
        .eq('id', data.size_id)
        .single();
      
      if (sizeError && sizeError.code !== 'PGRST116') {
        console.error('Error fetching dumpster size:', sizeError);
        throw sizeError;
      }

      // Update the dumpster size if we found one
      if (sizeData) {
        dumpster.size = {
          id: sizeData.id,
          name: sizeData.name,
          volumeCubicYards: sizeData.volume_cubic_yards,
          maxWeightPounds: sizeData.max_weight_pounds,
          dimensions: {
            length: sizeData.length || dumpster.size.dimensions.length,
            width: sizeData.width || dumpster.size.dimensions.width,
            height: sizeData.height || dumpster.size.dimensions.height
          },
          description: sizeData.description || dumpster.size.description
        };
      } else {
        // Log that we couldn't find the size data for this dumpster
        console.warn(`Size with ID ${data.size_id} not found for dumpster ${dumpster.id}, using default size`);
      }
    } else {
      // Log that this dumpster doesn't have a size_id
      console.log(`Dumpster ${dumpster.id} doesn't have a size_id, using derived size from name`);
    }

    return dumpster;
  } catch (error) {
    console.error('Error in getDumpsterById:', error);
    return mockDumpsters.find(d => d.id === id) || null;
  }
}

/**
 * Get all waste types
 */
export async function getWasteTypes(): Promise<WasteType[]> {
  try {
    const { data, error } = await supabase
      .from('waste_types')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url
      `);

    if (error) throw error;

    // Convert the data from new format to old format for compatibility
    return data.map(item => ({
      id: item.id,
      name: item.name_en, // Use English name for compatibility
      description: item.description_en, // Use English description for compatibility
      imageUrl: item.image_url,
      tags: []
    }));
  } catch (error) {
    console.error('Error fetching waste types:', error);
    // Return mock waste types in old format
    return mockWasteTypes;
  }
}

/**
 * Apply filters to a list of dumpsters
 */
function applyFilters(dumpsters: Dumpster[], filters: DumpsterFilter): Dumpster[] {
  let result = [...dumpsters];
  
  // Filter by waste type IDs if specified
  if (filters.wasteTypeIds?.length) {
    result = result.filter(dumpster => 
      filters.wasteTypeIds?.some(id => 
        dumpster.compatibleWasteTypes.includes(id)
      )
    );
  }
  
  // Filter by price range if specified
  if (filters.priceRange) {
    if (filters.priceRange.min !== undefined) {
      result = result.filter(d => d.pricePerDay >= (filters.priceRange?.min || 0));
    }
    
    if (filters.priceRange.max !== undefined) {
      result = result.filter(d => d.pricePerDay <= (filters.priceRange?.max || Infinity));
    }
  }
  
  // Filter by search term if provided
  if (filters.searchTerm) {
    const searchTerm = filters.searchTerm.toLowerCase();
    result = result.filter(d => {
      const nameMatch = d.name.toLowerCase().includes(searchTerm);
      // Description is optional, check if it exists first
      const descriptionMatch = d.description ? 
        d.description.toLowerCase().includes(searchTerm) : 
        false;
      return nameMatch || descriptionMatch;
    });
  }

  // Filter by size ID if specified
  if (filters.sizeId) {
    result = result.filter(dumpster => dumpster.size.id === filters.sizeId);
  }
  
  return result;
}

export const getMockDumpsters = async (): Promise<Dumpster[]> => {
  return mockDumpsters;
};

// Convert API dumpster to application Dumpster model
export const convertApiDumpster = (dumpster: any): Dumpster => {
  const { id, name, description, imageUrl, wasteTypeIds = [] } = dumpster;
  
  // Create a default size if none is provided
  const defaultSize: DumpsterSize = {
    id: dumpster.size_id || 'size_medium',
    name: dumpster.size_name || 'Medium',
    volumeCubicYards: dumpster.volume_cubic_yards || 20,
    maxWeightPounds: dumpster.max_weight_pounds || 2000,
    dimensions: {
      length: dumpster.length || 10,
      width: dumpster.width || 8,
      height: dumpster.height || 6
    },
    description: dumpster.size_description || 'Standard size dumpster'
  };
  
  return {
    id: id || `temp_${Date.now()}`,
    name: name || 'Unnamed Dumpster',
    description: description || 'No description available',
    imageUrl: imageUrl || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png',
    size: defaultSize,
    compatibleWasteTypes: wasteTypeIds,
    pricePerDay: dumpster.price_per_load || 75,
    availability: {
      isAvailable: dumpster.is_available !== undefined ? dumpster.is_available : true,
      nextAvailableDate: dumpster.next_available_date,
    },
    featureIds: dumpster.feature_ids || [],
    rating: dumpster.rating || 4.0,
    reviewCount: dumpster.review_count || 0,
    partnerId: dumpster.partner_id,
  };
}; 