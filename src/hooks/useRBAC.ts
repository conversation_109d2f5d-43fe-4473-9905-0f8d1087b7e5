import { useAuth } from '@/context/AuthContext';
import { 
  UserRole, 
  getUserRole, 
  hasRole, 
  hasAnyRole, 
  isAdmin, 
  isPartner, 
  isDriver, 
  isCustomer,
  getHomeRouteForRole
} from '@/utils/rbac';

/**
 * Hook for role-based access control
 * Provides functions to check user roles and permissions
 */
export function useRBAC() {
  const { session } = useAuth();
  
  return {
    // Get the current user's role
    role: getUserRole(session),
    
    // Check if the user has a specific role
    hasRole: (role: UserRole) => hasRole(session, role),
    
    // Check if the user has any of the specified roles
    hasAnyRole: (roles: UserRole[]) => hasAnyRole(session, roles),
    
    // Convenience methods for common role checks
    isAdmin: isAdmin(session),
    isPartner: isPartner(session),
    isDriver: isDriver(session),
    isCustomer: isCustomer(session),
    
    // Get the home route for the current user's role
    homeRoute: getHomeRouteForRole(session),
    
    // All available roles
    roles: User<PERSON>ole
  };
}

export default useRBAC;
