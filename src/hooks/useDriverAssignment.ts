import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/services/supabase';
import { useEffect } from 'react';
import { useDriver } from './useDriver';

export interface DriverAssignment {
  id: string;
  driver_id: string;
  order_id: string;
  status: string;
  assigned_at: string;
  completed_at: string | null;
  vehicle_id: string | null;
}

export function useDriverAssignment(orderId: string | null) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!orderId) return;

    const subscription = supabase
      .channel(`driver_assignment_${orderId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'driver_assignments',
          filter: `order_id=eq.${orderId}`
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ['driver_assignment', orderId] });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [orderId, queryClient]);

  const assignmentQuery = useQuery<DriverAssignment | null>({
    queryKey: ['driver_assignment', orderId],
    queryFn: async () => {
      if (!orderId) return null;

      console.log(`[useDriverAssignment] Fetching assignment for order: ${orderId}`);

      // Get the driver assignment for this order
      const { data, error } = await supabase
        .from('driver_assignments')
        .select('*')
        .eq('order_id', orderId)
        .order('assigned_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('[useDriverAssignment] Error fetching driver assignment:', error);
        return null;
      }

      if (!data || data.length === 0) {
        console.log(`[useDriverAssignment] No driver assignment found for order: ${orderId}`);
        return null;
      }

      console.log(`[useDriverAssignment] Found assignment:`, data[0]);
      return data[0];
    },
    enabled: !!orderId,
    retry: 2
  });

  // Only fetch driver if we have a driver_id from the assignment
  const driverQuery = useDriver(assignmentQuery.data?.driver_id || null);

  return {
    assignment: assignmentQuery.data,
    driver: driverQuery.data,
    isLoading: assignmentQuery.isLoading || (assignmentQuery.data?.driver_id ? driverQuery.isLoading : false),
    error: assignmentQuery.error || driverQuery.error
  };
}
