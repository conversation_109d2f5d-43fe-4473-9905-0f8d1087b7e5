import { useState, useEffect } from 'react';
import { supabase } from '@/services/supabase/client';
import { UserSettings } from '@/types/settings';
import { getProfileByAuthId, GlobalUserIdentifier } from '@/context/AuthContext';

const defaultSettings: UserSettings = {
  theme: 'system',
  notifications: {
    push_enabled: true
  },
  location_sharing: false,
  privacy: {
    policy_url: 'https://dumpster.app/privacy',
    terms_url: 'https://dumpster.app/terms'
  }
};

export function useSettings() {
  const [settings, setSettings] = useState<UserSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const profile = await getProfileByAuthId(user.id);

      if (profile) {
        GlobalUserIdentifier.updateProfileId(profile.id);
      }

      const { data, error } = await supabase
        .from('user_settings')
        .select('settings')
        .eq('user_id', GlobalUserIdentifier.profileId)
        .maybeSingle();

      if (error) throw error;

      if (data) {
        setSettings(data.settings);
      } else {
        // If no settings exist, create default settings
        const { error: insertError } = await supabase
          .from('user_settings')
          .insert({
            user_id: GlobalUserIdentifier.profileId,
            settings: defaultSettings,
          });

        if (insertError) throw insertError;
        setSettings(defaultSettings);
      }
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<UserSettings>) => {
    try {
      setError(null);
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const updatedSettings = { ...settings, ...newSettings };
      
      const { error } = await supabase
        .from('user_settings')
        .update({
          settings: updatedSettings,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', GlobalUserIdentifier.profileId);

      if (error) throw error;

      setSettings(updatedSettings);
      return true;
    } catch (err) {
      console.error('Error updating settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to update settings');
      return false;
    }
  };

  return {
    settings,
    loading,
    error,
    updateSettings,
    refreshSettings: fetchSettings,
  };
} 