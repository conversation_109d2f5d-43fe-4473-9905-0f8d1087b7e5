import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Partner } from '@/types/partner';
import { PartnerOffer } from '@/components/PartnerOfferList';

/**
 * Hook to fetch partner offers for a specific dumpster
 * Since we don't have a dedicated offers table yet, we'll generate offers
 * based on available partners and add some pricing logic
 */
export function usePartnerOffers(dumpsterId: string | null, basePrice?: number) {
  return useQuery<PartnerOffer[]>({
    queryKey: ['partner-offers', dumpsterId, basePrice],
    queryFn: async () => {
      if (!dumpsterId) return [];

      // Get all active partners first, then fetch profiles separately to avoid RLS issues
      const { data: partners, error: partnersError } = await supabase
        .from('partners')
        .select('*')
        .eq('status', 'active')
        .eq('is_verified', true)
        .order('rating', { ascending: false });

      if (partnersError) {
        console.error('Partners query error:', partnersError);
        return [];
      }

      if (!partners || partners.length === 0) {
        return [];
      }

      // Fetch profiles for the partners
      const profileIds = partners.map(p => p.profile_id).filter(Boolean);

      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name, avatar_url, email, phone, user_type')
        .in('id', profileIds);

      if (profilesError) {
        console.error('Profiles query error:', profilesError);
        // Continue without profiles if there's an error
      }

      // Transform partners into offers with pricing logic
      const offers: PartnerOffer[] = partners.map((partner, index) => {
        // Find the corresponding profile
        const profile = profiles?.find(p => p.id === partner.profile_id);
        // Generate realistic pricing based on base price and partner rating
        const basePriceValue = basePrice || 250; // Default base price
        const ratingMultiplier = partner.rating ? (partner.rating / 5) : 0.8; // Higher rating = higher price
        const randomVariation = 0.8 + (Math.random() * 0.4); // ±20% variation
        const calculatedPrice = Math.round(basePriceValue * ratingMultiplier * randomVariation);

        // Generate delivery time estimates
        const deliveryTimes = ['Same day', '24 hours', '48 hours', '2-3 days'];
        const deliveryTime = deliveryTimes[index % deliveryTimes.length];

        return {
          id: `offer-${partner.id}-${dumpsterId}`,
          partnerId: partner.id,
          partner: {
            ...partner,
            service_areas: partner.service_areas as string[] || [],
            profile: profile ? {
              id: profile.id,
              full_name: profile.full_name,
              avatar_url: profile.avatar_url,
              email: profile.email,
              phone: profile.phone,
              user_type: profile.user_type
            } : undefined
          } as Partner,
          price: calculatedPrice,
          isAvailable: true,
          deliveryTime,
          imageUrl: partner.company_logo_url || profile?.avatar_url
        };
      });

      // Sort offers by price (ascending) then by rating (descending)
      return offers.sort((a, b) => {
        if (a.price !== b.price) {
          return a.price - b.price;
        }
        return (b.partner.rating || 0) - (a.partner.rating || 0);
      });
    },
    enabled: !!dumpsterId,
    retry: 1,
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter than partners since prices might change)
  });
}

/**
 * Hook to fetch a specific partner offer
 */
export function usePartnerOffer(offerId: string | null) {
  return useQuery<PartnerOffer | null>({
    queryKey: ['partner-offer', offerId],
    queryFn: async () => {
      if (!offerId) return null;

      // Parse the offer ID to extract partner ID and dumpster ID
      const [, partnerId, dumpsterId] = offerId.split('-');
      
      if (!partnerId || !dumpsterId) return null;

      // Get the specific partner
      const { data: partner, error: partnerError } = await supabase
        .from('partners')
        .select(`
          *,
          profile:profiles!inner(
            id,
            full_name,
            avatar_url,
            email,
            phone,
            user_type
          )
        `)
        .eq('id', partnerId)
        .eq('status', 'active')
        .single();

      if (partnerError || !partner) {
        console.error('Partner query error:', partnerError);
        return null;
      }

      // Generate the same pricing logic as in usePartnerOffers
      const basePriceValue = 250; // Default base price
      const ratingMultiplier = partner.rating ? (partner.rating / 5) : 0.8;
      const randomVariation = 0.8 + (Math.random() * 0.4);
      const calculatedPrice = Math.round(basePriceValue * ratingMultiplier * randomVariation);

      return {
        id: offerId,
        partnerId: partner.id,
        partner: {
          ...partner,
          service_areas: partner.service_areas as string[] || [],
          profile: partner.profile ? {
            id: partner.profile.id,
            full_name: partner.profile.full_name,
            avatar_url: partner.profile.avatar_url,
            email: partner.profile.email,
            phone: partner.profile.phone,
            user_type: partner.profile.user_type
          } : undefined
        } as Partner,
        price: calculatedPrice,
        isAvailable: true,
        deliveryTime: '24 hours',
        imageUrl: partner.company_logo_url || partner.profile?.avatar_url
      };
    },
    enabled: !!offerId,
    retry: 1,
    staleTime: 2 * 60 * 1000,
  });
}
