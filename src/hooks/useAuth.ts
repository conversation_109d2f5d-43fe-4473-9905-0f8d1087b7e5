import { useContext, useEffect } from 'react';
import { useAuth as useAuthContextHook, GlobalUserIdentifier } from '@/context/AuthContext';

export const useAuth = () => {
  const context = useAuthContextHook();
  
  // Use GlobalUserIdentifier values for easy access
  const userId = GlobalUserIdentifier.profileId || GlobalUserIdentifier.authId;
  const { authId, profileId } = GlobalUserIdentifier;
  
  // Log the current state of the GlobalUserIdentifier
  useEffect(() => {
    console.log('useAuth hook - GlobalUserIdentifier state:', { 
      authId: GlobalUserIdentifier.authId, 
      profileId: GlobalUserIdentifier.profileId,
      userId
    });
  }, [GlobalUserIdentifier.authId, GlobalUserIdentifier.profileId, userId]);

  return {
    ...context,
    userId,  // Convenience field for the most appropriate ID to use
    authId,  // Direct access to the auth ID
    profileId, // Direct access to the profile ID
  };
}; 