import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/services/supabase/client';
import { useState, useEffect } from 'react';
import { Profile } from '@/types/profile';
import { useAuth, GlobalUserIdentifier } from '@/context/AuthContext';
import { Database } from '@/types/supabase';

type ProfileResponse = Database['public']['Tables']['profiles']['Row'];
type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

// Function to get profile by auth ID or profile ID
const getProfile = async (userId: string): Promise<Profile | null> => {
  if (!userId) return null;
  
  console.log('Fetching profile for user:', userId, 'with token:', 'Present');
  
  try {
    // First try to get profile directly if userId is a profile ID
    const { data: profileById, error: profileByIdError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
      
    if (!profileByIdError && profileById) {
      console.log('Profile found by direct ID lookup');
      return profileById;
    }
    
    // If that fails, try searching for profiles with this auth ID
    // in either email_auth_id or phone_auth_id fields
    console.log('Profile not found directly, trying auth ID lookup');
    const { data: profilesByAuthId, error: authIdError } = await supabase
      .from('profiles')
      .select('*')
      .or(`email_auth_id.eq.${userId},phone_auth_id.eq.${userId}`)
      .limit(1);
      
    if (!authIdError && profilesByAuthId && profilesByAuthId.length > 0) {
      console.log('Profile found by auth ID lookup:', profilesByAuthId[0]);
      
      // Update the global user identifier with the found profile ID
      GlobalUserIdentifier.updateProfileId(profilesByAuthId[0].id);
      
      return profilesByAuthId[0];
    }
    
    // If no profile found by any method
    console.log('Error fetching profile (attempt 1):', profileByIdError || authIdError);
    console.log('Profile not found, might need to create one');
    return null;
  } catch (error) {
    console.error('Unexpected error in getProfile:', error);
    return null;
  }
};

// Function to update profile
const updateProfileById = async (userId: string, userData: Partial<Profile>): Promise<Profile> => {
  try {
    // Make sure we have the correct profile ID to update
    const profileId = GlobalUserIdentifier.profileId || userId;
    
    const { data, error } = await supabase
      .from('profiles')
      .update(userData)
      .eq('id', profileId)
      .select()
      .single();

    if (error) {
      console.error('Error updating profile:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateProfileById:', error);
    throw error;
  }
};

const checkRLSPolicies = async (userId: string) => {
  console.log('Checking RLS policies for user:', userId);
  try {
    // Try to read the profile
    const { data: readData, error: readError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    console.log('Read policy check:', { success: !!readData, error: readError });

    // Try to update a single field to test update permission
    const testUpdate = { last_checked: new Date().toISOString() };
    const { data: updateData, error: updateError } = await supabase
      .from('profiles')
      .update(testUpdate)
      .eq('id', userId)
      .select()
      .single();

    console.log('Update policy check:', { success: !!updateData, error: updateError });

    return {
      canRead: !readError,
      canUpdate: !updateError,
      readError,
      updateError
    };
  } catch (error) {
    console.error('Error checking RLS policies:', error);
    throw error;
  }
};

const createProfile = async (data: Omit<Profile, 'created_at' | 'updated_at'>): Promise<Profile> => {
  console.log('Creating profile with data:', data);
  try {
    // Check if profile already exists
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing profile:', checkError);
      throw checkError;
    }

    if (existingProfile) {
      console.log('Profile already exists:', existingProfile);
      return existingProfile as Profile;
    }

    const { data: newProfile, error } = await supabase
      .from('profiles')
      .insert([data])
      .select()
      .single();

    if (error) {
      console.error('Error creating profile:', error);
      throw error;
    }

    console.log('Created profile:', newProfile);
    return newProfile;
  } catch (error) {
    console.error('Error in createProfile:', error);
    throw error;
  }
};

export function useProfile(userId: string) {
  const queryClient = useQueryClient();
  const { session } = useAuth();
  
  // Use session user ID as fallback if no userId provided
  const effectiveUserId = userId || session?.user?.id || '';
  
  const { data: profile, isLoading, error, refetch } = useQuery({
    queryKey: ['profile', effectiveUserId],
    queryFn: () => getProfile(effectiveUserId),
    enabled: !!effectiveUserId,
  });

  const updateProfile = useMutation({
    mutationFn: (userData: Partial<Profile>) => updateProfileById(effectiveUserId, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile', effectiveUserId] });
    },
  });

  const createProfileMutation = useMutation({
    mutationFn: createProfile,
    onSuccess: (data) => {
      console.log('Profile created successfully:', data);
      queryClient.setQueryData(['profile', effectiveUserId], data);
    },
    onError: (error) => {
      console.error('Error in createProfileMutation:', error);
    },
  });

  return {
    profile,
    isLoading,
    error,
    updateProfile: updateProfile.mutate,
    isUpdating: updateProfile.isPending,
    refetch,
    createProfile: createProfileMutation.mutate,
    isCreating: createProfileMutation.isPending,
  };
} 