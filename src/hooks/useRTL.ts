import { I18nManager, TextStyle, ViewStyle } from 'react-native';

export function useRTL() {
  const isRTL = I18nManager.isRTL;

  return {
    isRTL,
    textAlign: isRTL ? 'right' as const : 'left' as const,
    flexDirection: isRTL ? 'row-reverse' as const : 'row' as const,
    marginStart: (value: number): ViewStyle => ({
      marginLeft: isRTL ? 0 : value,
      marginRight: isRTL ? value : 0,
    }),
    marginEnd: (value: number): ViewStyle => ({
      marginLeft: isRTL ? value : 0,
      marginRight: isRTL ? 0 : value,
    }),
    paddingStart: (value: number): ViewStyle => ({
      paddingLeft: isRTL ? 0 : value,
      paddingRight: isRTL ? value : 0,
    }),
    paddingEnd: (value: number): ViewStyle => ({
      paddingLeft: isRTL ? value : 0,
      paddingRight: isRTL ? 0 : value,
    }),
    // Helper for icon direction
    iconDirection: (leftIcon: string, rightIcon: string) => 
      isRTL ? rightIcon : leftIcon,
    // Helper for text alignment in TextInput
    textInputAlign: {
      textAlign: isRTL ? 'right' as const : 'left' as const,
      writingDirection: isRTL ? 'rtl' as const : 'ltr' as const,
    } as TextStyle,
  };
} 