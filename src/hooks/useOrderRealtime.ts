import { useState, useEffect, useRef } from 'react';
import { supabase } from '@/services/supabase/client';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { Order, DriverAssignment } from '@/types/supabase';
import { useAuth, GlobalUserIdentifier } from '@/context/AuthContext';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Conditionally import background fetch modules
let BackgroundFetch: any = null;
let TaskManager: any = null;

// Try to import the modules, but don't crash if they're not available
try {
  if (Platform.OS !== 'web') {
    BackgroundFetch = require('expo-background-fetch');
    TaskManager = require('expo-task-manager');
  }
} catch (err) {
  console.warn('[BACKGROUND] Background fetch modules not available:', err);
}

type SubscriptionStatus = 'DISCONNECTED' | 'CONNECTING' | 'SUBSCRIBED';

// Define a background task for checking orders
const BACKGROUND_ORDER_FETCH_TASK = 'background-order-fetch';

// Configure notifications for background reception
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Register the background task if it's not already registered and if the modules are available
if (TaskManager && BackgroundFetch && !TaskManager.isTaskDefined?.(BACKGROUND_ORDER_FETCH_TASK)) {
  try {
    TaskManager.defineTask(BACKGROUND_ORDER_FETCH_TASK, async () => {
      try {
        // Get the last checked timestamp
        const lastCheckedStr = await AsyncStorage.getItem('lastOrderCheckTimestamp');
        const lastChecked = lastCheckedStr ? parseInt(lastCheckedStr) : 0;
        const now = Date.now();
        
        // Check for new orders or status changes since last check
        const { data: recentUpdates, error } = await supabase
          .from('order_status_history')
          .select('order_id, status, created_at')
          .gt('created_at', new Date(lastChecked).toISOString())
          .order('created_at', { ascending: false });
        
        // Store the current timestamp for next check
        await AsyncStorage.setItem('lastOrderCheckTimestamp', now.toString());
        
        if (error) {
          console.error('[BACKGROUND] Error checking for order updates:', error);
          return BackgroundFetch.BackgroundFetchResult.Failed;
        }
        
        if (recentUpdates && recentUpdates.length > 0) {
          // Send a notification for the most recent update
          const mostRecent = recentUpdates[0];
          await sendRealtimeNotification(
            `Order Status Update`,
            `Your order #${mostRecent.order_id.slice(0, 4)} status has changed to ${mostRecent.status}`,
            { type: 'order_update', orderId: mostRecent.order_id, status: mostRecent.status }
          );
          
          return BackgroundFetch.BackgroundFetchResult.NewData;
        }
        
        return BackgroundFetch.BackgroundFetchResult.NoData;
      } catch (err) {
        console.error('[BACKGROUND] Background task error:', err);
        return BackgroundFetch.BackgroundFetchResult.Failed;
      }
    });
    console.log('[BACKGROUND] Task defined successfully');
  } catch (err) {
    console.warn('[BACKGROUND] Could not define background task:', err);
  }
}

// Function to register background fetch
const registerBackgroundFetch = async () => {
  if (!BackgroundFetch) {
    console.log('[BACKGROUND] Background fetch not available on this platform');
    return;
  }

  try {
    await BackgroundFetch.registerTaskAsync(BACKGROUND_ORDER_FETCH_TASK, {
      minimumInterval: 15 * 60, // 15 minutes
      stopOnTerminate: false,
      startOnBoot: true,
    });
    console.log('[BACKGROUND] Background fetch registered');
  } catch (err) {
    console.error('[BACKGROUND] Error registering background fetch:', err);
  }
};

// Function to send a notification when updates occur
const sendRealtimeNotification = async (title: string, body: string, data: any = {}) => {
  try {
    console.log('[NOTIFICATION] Attempting to send notification:', { title, body, data });
    
    // First, make sure we have permission
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    console.log('[NOTIFICATION] Current permission status:', existingStatus);
    
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      console.log('[NOTIFICATION] Permission not granted, requesting...');
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
      console.log('[NOTIFICATION] New permission status:', status);
    }

    if (finalStatus !== 'granted') {
      console.warn('[NOTIFICATION] Permission denied, cannot send notification');
      return false;
    }

    // Add sound to make notifications more noticeable
    const identifier = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: 'notification_sound.wav',
        badge: 1,
      },
      trigger: null, // Send immediately
    });

    console.log('[NOTIFICATION] Successfully scheduled notification with ID:', identifier);
    return true;
  } catch (error) {
    console.error('[NOTIFICATION] Error sending notification:', error);
    return false;
  }
};

// Set up notification listeners
export const setupNotificationListeners = () => {
  const notificationListener = Notifications.addNotificationReceivedListener(notification => {
    console.log('[NOTIFICATION] Notification received:', notification);
  });

  const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
    console.log('[NOTIFICATION] Notification response received:', response);
    
    // Handle notification tap - navigate to order screen
    const data = response.notification.request.content.data;
    if (data && data.type === 'order_update' && data.orderId) {
      console.log('[NOTIFICATION] User tapped notification for order:', data.orderId);
      
      // Store orderId for navigation on next app focus
      AsyncStorage.setItem('pendingOrderNavigation', data.orderId);
      
      // Also store timestamp to track when this happened
      AsyncStorage.setItem('pendingNavigationTimestamp', Date.now().toString());
      
      // Set a flag indicating this is a notification-triggered navigation
      AsyncStorage.setItem('notificationTriggeredNavigation', 'true');
    }
  });

  return () => {
    Notifications.removeNotificationSubscription(notificationListener);
    Notifications.removeNotificationSubscription(responseListener);
  };
};

// Helper function to verify database connection and subscription readiness
const verifyDbConnection = async (userIdentifier: any, session: any) => {
  try {
    console.log('[REALTIME] Testing database connection...');
    const { data, error } = await supabase
      .from('order_status_history')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (error) {
      console.error('[REALTIME] Database connection test failed:', error);
      return false;
    }
    
    console.log('[REALTIME] Database connection successful. Recent entries:', data);
    return true;
  } catch (err) {
    console.error('[REALTIME] Error testing database connection:', err);
    return false;
  }
};

export const useOrderRealtime = (
    onOrderUpdate: (payload: RealtimePostgresChangesPayload<Order>) => void,
    onDriverAssignmentUpdate: (payload: RealtimePostgresChangesPayload<DriverAssignment>) => void
) => {
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus>('DISCONNECTED');
  const [error, setError] = useState<string | null>(null);
  
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const channelRef = useRef<RealtimeChannel | null>(null);

  // Use the auth context to get authentication state
  const { isAuthenticated, userIdentifier, session } = useAuth();

  // Two-phase update handler for orders
  const handleOrderUpdate = async (payload: RealtimePostgresChangesPayload<Order>) => {
    try {
      console.log('[REALTIME] Processing order update:', payload);
      // Phase 1: Immediate optimistic update
      if (payload.new) {
        onOrderUpdate(payload);
        
        // Send notification about order status update
        const orderData = payload.new as Order;
        console.log('[REALTIME] Order data for notification:', orderData);
        
        // Ensure required properties exist before using them
        if (orderData.id) {
          const orderId = orderData.id;
          const orderStatus = orderData.status || 'updated';
          
          // Prepare notification data with proper order ID in payload
          const notificationData = {
            type: 'order_update',
            orderId: orderId,
            status: orderStatus,
            timestamp: Date.now()
          };
          
          console.log('[REALTIME] Sending notification with data:', notificationData);
          
          await sendRealtimeNotification(
            `Order Status Update`,
            `Your order #${orderId.slice(0, 4)} status has changed to ${orderStatus}`,
            notificationData
          );
        } else {
          console.warn('[REALTIME] Cannot send notification: Order ID missing');
        }
      }

      // Phase 2: Background fetch for complete data
      const orderId = payload.new && 'id' in payload.new ? payload.new.id : undefined;
      if (orderId) {
        const { data: fullData, error: fetchError } = await supabase
          .from('orders')
          .select('*')
          .eq('id', orderId)
          .single();

        if (!fetchError && fullData) {
          onOrderUpdate({
            ...payload,
            new: fullData
          });
        }
      }
    } catch (err) {
      console.error('[REALTIME] Error in order update handler:', err);
    }
  };

  // Two-phase update handler for driver assignments
  const handleDriverAssignmentUpdate = async (payload: RealtimePostgresChangesPayload<DriverAssignment>) => {
    try {
      // Phase 1: Immediate optimistic update
      if (payload.new) {
        onDriverAssignmentUpdate(payload);
        
        // Send notification about driver assignment
        const assignmentData = payload.new as DriverAssignment;
        const assignmentId = assignmentData.id;
        const driverId = assignmentData.driver_id;
        const orderId = assignmentData.order_id;
        
        // Prepare notification data with proper order ID in payload
        const notificationData = {
          type: 'order_update', // Using same type for consistent navigation
          orderId: orderId,
          assignmentId: assignmentId,
          driverId: driverId,
          timestamp: Date.now()
        };
        
        console.log('[REALTIME] Sending driver assignment notification with data:', notificationData);
        
        await sendRealtimeNotification(
          `Driver Assigned`,
          `A driver has been assigned to your order #${orderId.slice(0, 4)}`,
          notificationData
        );
      }

      // Phase 2: Background fetch for complete data
      const assignmentId = payload.new && 'id' in payload.new ? payload.new.id : undefined;
      if (assignmentId) {
        const { data: fullData, error: fetchError } = await supabase
          .from('driver_assignments')
          .select('*')
          .eq('id', assignmentId)
          .single();

        if (!fetchError && fullData) {
          onDriverAssignmentUpdate({
            ...payload,
            new: fullData
          });
        }
      }
    } catch (err) {
      console.error('Error in two-phase update:', err);
    }
  };

  const setupRealtimeSubscription = () => {
    // Check if we're authenticated and have a user ID
    if (!supabase) {
      setError("Supabase client not initialized.");
      return;
    }

    // Get the user ID from the auth context - PRIORITIZE PROFILE ID
    const userId = GlobalUserIdentifier.profileId || userIdentifier.profileId || userIdentifier.authId || (session?.user?.id || null);
    console.log('[REALTIME] Setting up subscription with user ID:', userId, 'Profile ID:', GlobalUserIdentifier.profileId);
    
    if (!userId) {
      setError("User not authenticated.");
      setSubscriptionStatus('DISCONNECTED');
      setIsLoading(false);
      return;
    }

    setSubscriptionStatus('CONNECTING');
    setIsLoading(true);

    // Create a new channel with profile ID
    const channelName = `public-orders-${userId}-${Date.now()}`;
    console.log(`[REALTIME] Creating new channel: ${channelName}`);
    
    // Debug - verify Supabase client has realtime enabled
    console.log('[REALTIME] Supabase client config:', {
      realtime: supabase.realtime ? 'configured' : 'not available',
      connected: supabase.realtime?.isConnected() || false
    });
    
    const channel = supabase.channel(channelName, {
      config: {
        broadcast: { self: true },
      },
    });

    channelRef.current = channel;

    // Try multiple table combinations to ensure we catch the events
    console.log('[REALTIME] Setting up table subscriptions with flexible schema');
    
    // Main order_status_history table subscription
    channel
      .on<Order>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',  
          table: 'order_status_history',
        },
        (payload) => {
          console.log('[REALTIME] Received order_status_history update:', payload);
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            handleOrderUpdate(payload);
          }
        }
      );
      
    // Add channels for order status changes through various other tables
    channel
      .on<any>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_statuses', // Alternative table name
        },
        (payload) => {
          console.log('[REALTIME] Received order_statuses update:', payload);
          // Convert to order update format
          const orderPayload = {
            ...payload,
            table: 'orders',
            schema: 'public',
          };
          handleOrderUpdate(orderPayload as any);
        }
      );
      
    // Listen for direct order table changes too
    channel
      .on<Order>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders',
        },
        (payload) => {
          console.log('[REALTIME] Received direct orders table update:', payload);
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            handleOrderUpdate(payload);
          }
        }
      );

    // Driver assignments subscription
    channel
      .on<DriverAssignment>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'driver_assignments',
        },
        (payload) => {
          console.log('[REALTIME] Received driver_assignment update:', payload);
          handleDriverAssignmentUpdate(payload);
        }
      );
      
    // Add a channel for ALL tables to debug what's actually coming through
    channel
      .on<any>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
        },
        (payload) => {
          console.log('[REALTIME:DEBUG] Received ANY table update:', payload);
        }
      );

    // Subscribe to the channel
    channel.subscribe((status, err) => {
      console.log(`[REALTIME] Subscription status: ${status}`);
      if (status === 'SUBSCRIBED') {
        console.log(`[REALTIME] Channel '${channelName}' subscribed successfully`);
        
        // Test the channel by sending a broadcast message
        try {
          channel.send({
            type: 'broadcast',
            event: 'test',
            payload: { message: 'Test broadcast from client' }
          });
          console.log('[REALTIME] Test broadcast sent');
        } catch (broadcastError) {
          console.error('[REALTIME] Error sending test broadcast:', broadcastError);
        }
        
        setSubscriptionStatus('SUBSCRIBED');
        setError(null);
        setIsLoading(false);
        reconnectAttempts.current = 0; // Reset reconnection attempts on success
      } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
        console.error(`[REALTIME] Subscription error on channel '${channelName}':`, err?.message);
        setError(`Subscription error: ${err?.message || 'Unknown error'}`);
        setIsLoading(false);
        
        // Handle reconnection with exponential backoff
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            setupRealtimeSubscription();
          }, delay);
        } else {
          setSubscriptionStatus('DISCONNECTED');
          setError('Maximum reconnection attempts reached. Please refresh the page.');
        }
      } else if (status === 'CLOSED') {
        console.log(`[REALTIME] Channel '${channelName}' closed.`);
        setSubscriptionStatus('DISCONNECTED');
      }
    });
  };

  useEffect(() => {
    // Register background fetch for periodic checking
    registerBackgroundFetch();
    
    // Set up notification listeners
    const cleanupListeners = setupNotificationListeners();
    
    // Only set up subscription if authenticated
    if (isAuthenticated && (GlobalUserIdentifier.profileId || userIdentifier.profileId || userIdentifier.authId || session?.user?.id)) {
      // First verify DB connection
      verifyDbConnection(userIdentifier, session).then(isConnected => {
        if (isConnected) {
          console.log('[REALTIME] Database connection verified, setting up subscription');
          setupRealtimeSubscription();
        } else {
          console.error('[REALTIME] Cannot set up subscription due to database connection issues');
          setError("Database connection error. Please try again later.");
          setSubscriptionStatus('DISCONNECTED');
          setIsLoading(false);
        }
      });
    } else {
      setError("User not authenticated.");
      setSubscriptionStatus('DISCONNECTED');
      setIsLoading(false);
    }

    // Cleanup function
    return () => {
      cleanupListeners();
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (channelRef.current) {
        console.log(`[REALTIME] Unsubscribing from Realtime channel`);
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [isAuthenticated, userIdentifier.profileId, GlobalUserIdentifier.profileId, session?.user?.id]); // Re-run effect when auth state changes

  return { 
    isLoading, 
    subscriptionStatus, 
    error,
    reconnectAttempts: reconnectAttempts.current,
    maxReconnectAttempts
  };
};

export { registerBackgroundFetch, sendRealtimeNotification };
