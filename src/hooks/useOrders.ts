import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase, safeReadData } from '@/services/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { getProfileId, syncProfileAuthIds } from '@/utils/user';
import { GlobalUserIdentifier } from '@/context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Dumpster } from '@/types/dumpster';

export interface Partner {
  id: string;
  company_name: string;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
}

export interface OrderDumpster {
  id: string;
  name_en: string;
  name_ar: string;
  image_url: string;
}

export interface Order {
  id: string;
  created_at: string;
  status: 'pending' | 'confirmed' | 'delivered' | 'in_use' | 'pickup_scheduled' | 'completed' | 'cancelled';
  delivery_address: string;
  delivery_date: string;
  total_amount: number;
  partner_id: string | null;
  partner?: Partner[] | null;
  customer_id?: string;
  user_id?: string;
  payment_status: 'pending' | 'partial' | 'paid' | 'refunded';
  payment_method: 'credit_card' | 'cash' | 'bank_transfer' | null;
  base_price: number;
  delivery_instructions?: string;
  dumpster_id: string | null;
  dumpster?: OrderDumpster[] | null;
  driver_id: string | null;
}

export const fetchOrderById = async (orderId: string) => {
  try {
    console.log('🔍 FETCHING ORDER BY ID - START:', orderId);

    // Revert back to using safeReadData
    const { data, error } = await safeReadData('orders', { 'id': orderId }); 
    
    if (error) {
      // Type guard for error message (as it was before)
      const errorMessage = (error instanceof Error && error.message) 
        ? error.message 
        : (typeof error === 'object' && error !== null && 'message' in error) 
          ? String(error.message) 
          : 'Unknown error fetching order';
      console.log(`❌ Error fetching order ${orderId} with safeReadData:`, errorMessage, error);
      throw new Error(errorMessage || 'Order not found');
    }

    // safeReadData returns an array
    if (data && data.length > 0) {
      console.log(`✅ Successfully fetched order ${orderId}`);
      console.log('🔍 FETCHING ORDER BY ID - END');
      return data[0] as Order; // Return first element
    } else {
      console.log(`🤷 Order ${orderId} not found with safeReadData.`);
      console.log('🔍 FETCHING ORDER BY ID - END');
      throw new Error('Order not found');
    }
  } catch (error) {
    console.error(`❌ Error in fetchOrderById for order ${orderId}:`, error);
    console.log('🔍 FETCHING ORDER BY ID - END');
    throw error; 
  }
};

export const useOrders = () => {
  const { t } = useTranslation();
  const profileId = GlobalUserIdentifier.profileId || '';
  const queryKey = ['orders', profileId];

  return useQuery<Order[], Error>({ 
    queryKey,
    queryFn: async () => {
      console.log('🔍 useOrders: Fetching orders for profileId:', profileId);
      if (!profileId) {
        console.warn('⚠️ useOrders: No profileId available, returning empty array.');
        return [];
      }
      try {
        // Revert back to using safeReadData
        const { data, error } = await safeReadData('orders', { 'customer_id': profileId }); 

        if (error) {
          console.error('❌ useOrders: Error fetching orders with safeReadData:', error);
          throw error; // Let useQuery handle the error state
        }
        
        console.log(`✅ useOrders: Successfully fetched ${data?.length ?? 0} orders via safeReadData.`);
        return (data as Order[]) || []; // Return data or empty array if null
      } catch (error) {
        console.error('❌ useOrders: Error in queryFn:', error);
        // Let useQuery handle the error state by re-throwing or returning based on needs
        // Returning empty array to prevent UI crash
        return []; 
      }
    },
    retry: 1,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true
  });
};

export const useOrder = (orderId: string | null) => {
  const { profileId, authId } = GlobalUserIdentifier;
  
  // Use more specific query key
  const queryKey = profileId 
    ? ['order', orderId, profileId]
    : ['order', orderId, authId];
    
  return useQuery({
    queryKey,
    queryFn: async () => {
      if (!orderId) return null;
      try {
        return await fetchOrderById(orderId);
      } catch (error) {
        console.error('Error fetching order by ID:', error);
        return null;
      }
    },
    enabled: !!orderId,
  });
}; 
