import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/services/supabase';
import { Driver } from '@/types/partner';
import { useEffect } from 'react';

export function useDriver(driverId: string | null) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!driverId) return;

    const subscription = supabase
      .channel(`driver_${driverId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'drivers',
          filter: `id=eq.${driverId}`
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [driverId, queryClient]);

  return useQuery<Driver | null>({
    queryKey: ['driver', driverId],
    queryFn: async () => {
      if (!driverId) {
        console.log('[useDriver] No driver ID provided');
        return null;
      }

      console.log(`[useDriver] Fetching driver with ID: ${driverId}`);

      try {
        // First, get the driver data
        const { data: driver, error: driverError } = await supabase
          .from('drivers')
          .select('*')
          .eq('id', driverId)
          .single();

        if (driverError) {
          console.error('[useDriver] Error fetching driver:', driverError);
          return null;
        }

        console.log('[useDriver] Found driver:', driver);

        // Now get the profile data
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id, full_name, phone, avatar_url')
          .eq('id', driver.profile_id)
          .single();

        if (profileError) {
          console.error('[useDriver] Error fetching profile:', profileError);
          return null;
        }

        console.log('[useDriver] Found profile:', profile);

        // Combine the data
        const result = {
          ...driver,
          profile
        };

        console.log('[useDriver] Combined result:', result);
        return result;
      } catch (e) {
        console.error('[useDriver] Unexpected error:', e);
        return null;
      }
    },
    enabled: !!driverId,
    retry: 2
  });
}