import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/services/supabase';

export function useOrderDriver(orderId: string | null) {
  const queryClient = useQueryClient();

  // Set up real-time subscription for driver assignments
  useEffect(() => {
    if (!orderId) return;

    const subscription = supabase
      .channel(`driver_assignment_${orderId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'driver_assignments',
          filter: `order_id=eq.${orderId}`
        },
        () => {
          console.log('[useOrderDriver] Driver assignment changed, invalidating query');
          queryClient.invalidateQueries({ queryKey: ['order-driver', orderId] });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [orderId, queryClient]);

  return useQuery({
    queryKey: ['order-driver', orderId],
    queryFn: async () => {
      if (!orderId) return null;

      console.log(`[useOrderDriver] Fetching driver for order: ${orderId}`);

      try {
        // Use the RPC function to get driver details
        const { data, error } = await supabase.rpc('get_driver_details_for_order', {
          order_id_param: orderId
        });

        if (error) {
          console.error('[useOrderDriver] Error calling RPC function:', error);
          return null;
        }

        console.log('[useOrderDriver] RPC result:', data);
        return data;
      } catch (e) {
        console.error('[useOrderDriver] Unexpected error:', e);
        return null;
      }
    },
    enabled: !!orderId,
    retry: 1
  });
}
