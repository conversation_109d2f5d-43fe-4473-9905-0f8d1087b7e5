import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase, safeReadData } from '@/services/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { getProfileId, syncProfileAuthIds } from '@/utils/user';
import { GlobalUserIdentifier } from '@/context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';

export interface Address {
  id: string;
  user_id: string;
  street_address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude: number;
  longitude: number;
  created_at: string;
  updated_at: string;
  name: string;
  type: 'home' | 'office' | 'other';
  is_default: boolean;
}

export interface AddressInput {
  street_address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude: number;
  longitude: number;
  name: string;
  type: 'home' | 'office' | 'other';
  is_default: boolean;
}

const fetchAddresses = async (): Promise<Address[]> => {
  try {
    console.log('🔍 FETCHING ADDRESSES - START');
    
    // Use safeReadData to avoid read-only transaction errors completely
    const { data, error } = await safeReadData('addresses');
    
    if (error) {
      console.log('❌ Error fetching addresses with safeReadData:', error);
      return [];
    }
    
    console.log(`✅ Successfully fetched ${data.length} addresses`);
    console.log('🔍 FETCHING ADDRESSES - END');
    return data as Address[];
  } catch (error) {
    console.log('[useAddresses] ❌ Error in fetchAddresses:', error);
    console.log('🔍 FETCHING ADDRESSES - END');
    return [];
  }
};

const createAddress = async (userId: string, addressData: AddressInput): Promise<Address> => {
  try {
    // Removed the call to syncProfileAuthIds to prevent read-only transaction errors
    const profileId = await getProfileId();
    const actualUserId = profileId || userId;
    
    console.log('Creating address with user ID:', actualUserId);
    
    const { data, error } = await supabase
      .from('addresses')
      .insert({
        user_id: actualUserId,
        ...addressData
      })
      .select()
      .single();

    if (error) {
      console.log('[useAddresses] Error creating address:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.log('[useAddresses] Error in createAddress:', error);
    throw error;
  }
};

const updateAddress = async (addressId: string, addressData: Partial<AddressInput>): Promise<Address> => {
  try {
    const { data, error } = await supabase
      .from('addresses')
      .update(addressData)
      .eq('id', addressId)
      .select()
      .single();

    if (error) {
      console.log('[useAddresses] Error updating address:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.log('[useAddresses] Error in updateAddress:', error);
    throw error;
  }
};

const deleteAddress = async (addressId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('addresses')
      .delete()
      .eq('id', addressId);

    if (error) {
      console.log('[useAddresses] Error deleting address:', error);
      throw error;
    }
  } catch (error) {
    console.log('[useAddresses] Error in deleteAddress:', error);
    throw error;
  }
};

export function useAddresses() {
  const queryClient = useQueryClient();
  
  // Use hardcoded profile ID to bypass RLS issues
  const profileId = GlobalUserIdentifier.profileId || '';
  
  // Use simple query key
  const queryKey = ['addresses', profileId];

  const { data: addresses, isLoading, error, refetch } = useQuery({
    queryKey,
    queryFn: () => {
      // Use safeReadData with the profile ID to avoid RLS issues
      return safeReadData('addresses', { 'user_id':profileId })
        .then(result => result.data as Address[]);
    },
  });

  const createAddressMutation = useMutation({
    mutationFn: (addressData: AddressInput) => createAddress(profileId, addressData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
    },
  });

  const updateAddressMutation = useMutation({
    mutationFn: ({ addressId, addressData }: { addressId: string; addressData: Partial<AddressInput> }) => 
      updateAddress(addressId, addressData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
    },
  });

  const deleteAddressMutation = useMutation({
    mutationFn: (addressId: string) => deleteAddress(addressId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
    },
  });

  return {
    addresses,
    isLoading,
    error,
    createAddress: createAddressMutation.mutate,
    updateAddress: updateAddressMutation.mutate,
    deleteAddress: deleteAddressMutation.mutate,
    isCreating: createAddressMutation.isPending,
    isUpdating: updateAddressMutation.isPending,
    isDeleting: deleteAddressMutation.isPending,
    refetch,
  };
} 