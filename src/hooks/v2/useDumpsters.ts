import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/services/supabase';
import { useTranslation } from 'react-i18next';

import {
  Dumpster,
  DumpsterSize,
  DumpsterFeature,
  WasteType,
  DumpsterFilters,
  DumpsterWithRecommendation,
  RecommendationRequest
} from '@/types/v2/dumpster';

/**
 * QUERY KEYS
 * Centralized query key definitions for React Query
 */
export const DumpsterKeys = {
  all: ['dumpsters-v2'] as const,
  lists: () => [...DumpsterKeys.all, 'list'] as const,
  list: (filters: DumpsterFilters | undefined) => [...DumpsterKeys.lists(), filters] as const,
  details: () => [...DumpsterKeys.all, 'detail'] as const,
  detail: (id: string) => [...DumpsterKeys.details(), id] as const,
  wasteTypes: ['waste-types-v2'] as const,
  sizes: ['dumpster-sizes-v2'] as const,
  features: ['dumpster-features-v2'] as const,
  recommendations: ['dumpster-recommendations-v2'] as const
};

/**
 * Hook to fetch all dumpsters with optional filtering
 */
export function useDumpsters(filters?: DumpsterFilters) {
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';
  const queryClient = useQueryClient();

  // Subscribe to real-time changes
  useEffect(() => {
    const subscription = supabase
      .channel('dumpsters-v2')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'dumpsters' },
        () => {
          // Invalidate the query to refetch data when changes occur
          queryClient.invalidateQueries({ queryKey: DumpsterKeys.lists() });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [queryClient]);

  return useQuery<Dumpster[], Error>({
    queryKey: [...DumpsterKeys.list(filters), currentLocale],
    queryFn: () => getDumpsters(filters, currentLocale),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch a single dumpster by ID with real-time updates
 */
export function useDumpster(id: string, options = { disabled: false }) {
  const queryClient = useQueryClient();
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';

  // Set up real-time subscription for single dumpster
  useEffect(() => {
    if (!id || options.disabled) return;

    const subscription = supabase
      .channel(`dumpster-${id}-v2`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'dumpsters',
          filter: `id=eq.${id}` 
        }, 
        () => {
          queryClient.invalidateQueries({ queryKey: DumpsterKeys.detail(id) });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [id, queryClient, options.disabled]);

  return useQuery<Dumpster | null, Error>({
    queryKey: [...DumpsterKeys.detail(id), currentLocale],
    queryFn: () => getDumpsterById(id, currentLocale),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !options.disabled && !!id,
  });
}

/**
 * Hook to fetch all waste types
 */
export function useWasteTypes() {
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';
  
  return useQuery<WasteType[], Error>({
    queryKey: [...DumpsterKeys.wasteTypes, currentLocale],
    queryFn: () => getWasteTypes(currentLocale),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook to fetch all dumpster sizes
 */
export function useDumpsterSizes() {
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';
  
  return useQuery<DumpsterSize[], Error>({
    queryKey: [...DumpsterKeys.sizes, currentLocale],
    queryFn: () => getDumpsterSizes(currentLocale),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook to fetch all dumpster features
 */
export function useDumpsterFeatures() {
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';
  
  return useQuery<DumpsterFeature[], Error>({
    queryKey: [...DumpsterKeys.features, currentLocale],
    queryFn: () => getDumpsterFeatures(currentLocale),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook to get dumpster recommendations based on user inputs
 */
export function useDumpsterRecommendations(request: RecommendationRequest) {
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';
  
  return useQuery<DumpsterWithRecommendation[], Error>({
    queryKey: [...DumpsterKeys.recommendations, request, currentLocale],
    queryFn: () => getDumpsterRecommendations(request, currentLocale),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!request && (!!request.projectDescription || !!request.wasteTypeIds?.length),
  });
}

/**
 * Get all dumpsters with optional filtering
 */
export async function getDumpsters(
  filters?: DumpsterFilters,
  locale: string = 'en'
): Promise<Dumpster[]> {
  try {
    // Build the query
    let query = supabase
      .from('dumpsters')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url,
        price_per_load,
        rating,
        review_count,
        is_available,
        next_available_date,
        size_id,
        partner_id,
        dumpster_waste_types (
          waste_type_id
        ),
        dumpster_feature_links (
          feature_id
        ),
        dumpster_images (
          image_url,
          sort_order
        )
      `);
    
    // Apply filters if provided
    if (filters?.availability) {
      query = query.eq('is_available', true);
    }
    
    if (filters?.sizeIds && filters.sizeIds.length > 0) {
      query = query.in('size_id', filters.sizeIds);
    }
    
    if (filters?.rating && filters.rating > 0) {
      query = query.gte('rating', filters.rating);
    }
    
    if (filters?.priceRange?.min !== undefined) {
      query = query.gte('price_per_load', filters.priceRange.min);
    }
    
    if (filters?.priceRange?.max !== undefined) {
      query = query.lte('price_per_load', filters.priceRange.max);
    }
    
    if (filters?.searchTerm) {
      const term = `%${filters.searchTerm}%`;
      query = query.or(`name_en.ilike.${term},name_ar.ilike.${term},description_en.ilike.${term},description_ar.ilike.${term}`);
    }
    
    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching dumpsters:', error);
      throw error;
    }

    if (!data || !Array.isArray(data)) {
      console.error('Invalid dumpster data returned from database:', data);
      return [];
    }

    // Get all size IDs for a batch query
    const sizeIds = data
      .map(d => d.size_id)
      .filter(Boolean);
    
    // Fetch all sizes in one query
    const { data: sizeData, error: sizeError } = await supabase
      .from('dumpster_sizes')
      .select('*')
      .in('id', sizeIds);
    
    if (sizeError) {
      console.error('Error fetching dumpster sizes:', sizeError);
      throw sizeError;
    }

    // Create a map of sizes by id for quick lookup
    const sizesMap = (sizeData || []).reduce((acc, size) => {
      acc[size.id] = {
        id: size.id,
        nameEn: size.name_en,
        nameAr: size.name_ar,
        length: size.length,
        width: size.width,
        height: size.height,
        capacity: size.volume_cubic_yards,
        maxWeight: size.max_weight_pounds,
        description: {
          en: size.description_en,
          ar: size.description_ar
        }
      };
      return acc;
    }, {});

    // Transform data to our interface
    let dumpsters = data.map(d => transformDumpsterData(d, sizesMap, locale));
    
    // Post-processing for waste type filtering (if needed)
    if (filters?.wasteTypeIds && filters.wasteTypeIds.length > 0) {
      dumpsters = dumpsters.filter(dumpster => {
        // If dumpster has no waste types, skip it
        if (!dumpster.compatibleWasteTypes || dumpster.compatibleWasteTypes.length === 0) {
          return false;
        }
        
        // Check if any of the filtered waste types match
        return filters.wasteTypeIds.some(wasteTypeId => 
          dumpster.compatibleWasteTypes?.includes(wasteTypeId)
        );
      });
    }
    
    return dumpsters;
  } catch (error) {
    console.error('Error in getDumpsters:', error);
    return [];
  }
}

/**
 * Get a single dumpster by ID
 */
export async function getDumpsterById(
  id: string,
  locale: string = 'en'
): Promise<Dumpster | null> {
  if (!id) {
    console.error('No dumpster ID provided');
    return null;
  }

  try {
    // Fetch the dumpster with its relationships
    const { data, error } = await supabase
      .from('dumpsters')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url,
        price_per_load,
        rating,
        review_count,
        is_available,
        next_available_date,
        size_id,
        partner_id,
        dumpster_waste_types (
          waste_type_id
        ),
        dumpster_feature_links (
          feature_id
        ),
        dumpster_images (
          image_url,
          sort_order
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      console.error('Error fetching dumpster:', error);
      throw error;
    }
    
    if (!data || !data.id) {
      console.error('Invalid dumpster data returned from database:', data);
      return null;
    }

    // Fetch the size data
    if (data.size_id) {
      const { data: sizeData, error: sizeError } = await supabase
        .from('dumpster_sizes')
        .select('*')
        .eq('id', data.size_id)
        .single();
      
      if (sizeError && sizeError.code !== 'PGRST116') {
        console.error('Error fetching dumpster size:', sizeError);
        throw sizeError;
      }

      // Create a size map with just this one size
      const sizesMap = {};
      if (sizeData) {
        sizesMap[sizeData.id] = {
          id: sizeData.id,
          nameEn: sizeData.name_en,
          nameAr: sizeData.name_ar,
          length: sizeData.length,
          width: sizeData.width,
          height: sizeData.height,
          capacity: sizeData.volume_cubic_yards,
          maxWeight: sizeData.max_weight_pounds,
          description: {
            en: sizeData.description_en,
            ar: sizeData.description_ar
          }
        };
      }

      // Also fetch feature details
      const featureIds = data.dumpster_feature_links
        ?.map(link => link.feature_id)
        .filter(Boolean) || [];
        
      let features = [];
      if (featureIds.length > 0) {
        const { data: featureData, error: featureError } = await supabase
          .from('features')
          .select('*')
          .in('id', featureIds);
          
        if (!featureError && featureData) {
          features = featureData.map(f => ({
            id: f.id,
            nameEn: f.name_en,
            nameAr: f.name_ar,
            iconName: f.icon_name
          }));
        }
      }
      
      // Also fetch waste type details
      const wasteTypeIds = data.dumpster_waste_types
        ?.map(link => link.waste_type_id)
        .filter(Boolean) || [];
        
      let wasteTypes = [];
      if (wasteTypeIds.length > 0) {
        const { data: wasteTypeData, error: wasteTypeError } = await supabase
          .from('waste_types')
          .select('*')
          .in('id', wasteTypeIds);
          
        if (!wasteTypeError && wasteTypeData) {
          wasteTypes = wasteTypeData.map(wt => ({
            id: wt.id,
            nameEn: wt.name_en,
            nameAr: wt.name_ar,
            descriptionEn: wt.description_en,
            descriptionAr: wt.description_ar,
            imageUrl: wt.image_url
          }));
        }
      }
      
      // Transform the base dumpster data
      const dumpster = transformDumpsterData(data, sizesMap, locale);
      
      // Add the detailed relationships
      dumpster.features = features;
      dumpster.wasteTypes = wasteTypes;
      
      return dumpster;
    }
    
    // If no size_id, just transform the base data
    return transformDumpsterData(data, {}, locale);
  } catch (error) {
    console.error('Error in getDumpsterById:', error);
    return null;
  }
}

/**
 * Get all waste types
 */
export async function getWasteTypes(locale: string = 'en'): Promise<WasteType[]> {
  try {
    const { data, error } = await supabase
      .from('waste_types')
      .select(`
        id,
        name_en,
        name_ar,
        description_en,
        description_ar,
        image_url
      `);

    if (error) throw error;

    return data.map(item => ({
      id: item.id,
      nameEn: item.name_en,
      nameAr: item.name_ar,
      descriptionEn: item.description_en,
      descriptionAr: item.description_ar,
      imageUrl: item.image_url
    }));
  } catch (error) {
    console.error('Error fetching waste types:', error);
    return [];
  }
}

/**
 * Get all dumpster sizes
 */
export async function getDumpsterSizes(locale: string = 'en'): Promise<DumpsterSize[]> {
  try {
    const { data, error } = await supabase
      .from('dumpster_sizes')
      .select('*');

    if (error) throw error;

    return data.map(size => ({
      id: size.id,
      nameEn: size.name_en,
      nameAr: size.name_ar,
      length: size.length,
      width: size.width,
      height: size.height,
      capacity: size.volume_cubic_yards,
      maxWeight: size.max_weight_pounds,
      description: {
        en: size.description_en,
        ar: size.description_ar
      }
    }));
  } catch (error) {
    console.error('Error fetching dumpster sizes:', error);
    return [];
  }
}

/**
 * Get all dumpster features
 */
export async function getDumpsterFeatures(locale: string = 'en'): Promise<DumpsterFeature[]> {
  try {
    const { data, error } = await supabase
      .from('features')
      .select('*');

    if (error) throw error;

    return data.map(feature => ({
      id: feature.id,
      nameEn: feature.name_en,
      nameAr: feature.name_ar,
      iconName: feature.icon_name
    }));
  } catch (error) {
    console.error('Error fetching dumpster features:', error);
    return [];
  }
}

/**
 * Get dumpster recommendations based on user inputs
 */
export async function getDumpsterRecommendations(
  request: RecommendationRequest,
  locale: string = 'en'
): Promise<DumpsterWithRecommendation[]> {
  try {
    // First, get all dumpsters
    const dumpsters = await getDumpsters(undefined, locale);
    
    // If no dumpsters or no request criteria, return empty array
    if (!dumpsters.length || (!request.projectDescription && !request.wasteTypeIds?.length)) {
      return [];
    }
    
    // For this implementation, we'll do a simple matching algorithm
    // In a real implementation, you might call an AI service or more complex matching logic
    
    const recommendedDumpsters: DumpsterWithRecommendation[] = dumpsters.map(dumpster => {
      let score = 0;
      const matchReasons: string[] = [];
      
      // Match waste types if provided
      if (request.wasteTypeIds && request.wasteTypeIds.length && dumpster.compatibleWasteTypes) {
        const matchingWasteTypes = request.wasteTypeIds.filter(id => 
          dumpster.compatibleWasteTypes?.includes(id)
        );
        
        if (matchingWasteTypes.length) {
          const wasteTypeScore = (matchingWasteTypes.length / request.wasteTypeIds.length) * 50;
          score += wasteTypeScore;
          matchReasons.push(`Compatible with ${matchingWasteTypes.length} requested waste types`);
        }
      }
      
      // Match size preference if provided
      if (request.sizePreference && request.sizePreference.length && dumpster.sizeId) {
        if (request.sizePreference.includes(dumpster.sizeId)) {
          score += 25;
          matchReasons.push('Matches size preference');
        }
      }
      
      // Match budget if provided
      if (request.budget) {
        if (
          (!request.budget.min || dumpster.pricePerLoad >= request.budget.min) &&
          (!request.budget.max || dumpster.pricePerLoad <= request.budget.max)
        ) {
          score += 15;
          matchReasons.push('Within budget range');
        }
      }
      
      // Add availability score
      if (dumpster.isAvailable) {
        score += 10;
        matchReasons.push('Currently available');
      }
      
      return {
        ...dumpster,
        recommendationScore: score,
        matchReasons
      };
    });
    
    // Sort by score (descending) and filter out low scores
    return recommendedDumpsters
      .filter(d => d.recommendationScore > 10) // Only include meaningful matches
      .sort((a, b) => b.recommendationScore - a.recommendationScore);
  } catch (error) {
    console.error('Error in getDumpsterRecommendations:', error);
    return [];
  }
}

/**
 * Helper function to transform raw dumpster data into our interface
 */
function transformDumpsterData(
  dumpster: any, 
  sizesMap: Record<string, DumpsterSize>,
  locale: string = 'en'
): Dumpster {
  // Extract waste type IDs
  const wasteTypeIds = dumpster.dumpster_waste_types
    ? dumpster.dumpster_waste_types
        .filter(dwt => dwt && dwt.waste_type_id)
        .map(dwt => dwt.waste_type_id)
    : [];
  
  // Extract feature IDs
  const featureIds = dumpster.dumpster_feature_links
    ? dumpster.dumpster_feature_links
        .filter(link => link && link.feature_id)
        .map(link => link.feature_id)
    : [];
  
  // Get additional images and sort them
  const additionalImages = dumpster.dumpster_images
    ? dumpster.dumpster_images
        .filter(img => img && img.image_url)
        .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
        .map(img => img.image_url)
    : [];
  
  // Get the size object if available
  const size = dumpster.size_id && sizesMap[dumpster.size_id]
    ? sizesMap[dumpster.size_id]
    : null;
  
  // Calculate dimensions or use defaults
  const length = size ? size.length : 10;
  const width = size ? size.width : 8;
  const height = size ? size.height : 6;
  const capacity = size ? size.capacity : 10;
  const maxWeight = size ? size.maxWeight : 2000;
  const standingArea = length * width;
  
  // Get a safe image URL
  const imageUrl = typeof dumpster.image_url === 'string' && dumpster.image_url
    ? dumpster.image_url 
    : 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';
  
  return {
    id: dumpster.id,
    nameEn: dumpster.name_en || 'Unnamed Dumpster',
    nameAr: dumpster.name_ar || 'حاوية بدون اسم',
    description: {
      en: dumpster.description_en || '',
      ar: dumpster.description_ar || ''
    },
    imageUrl,
    additionalImages,
    
    // Dimensions
    length,
    width,
    height,
    capacity,
    maxWeight,
    standingArea,
    
    // Business details
    pricePerLoad: dumpster.price_per_load || 75,
    rating: dumpster.rating || 4.0,
    reviewCount: dumpster.review_count || 0,
    isAvailable: dumpster.is_available !== undefined ? dumpster.is_available : true,
    nextAvailableDate: dumpster.next_available_date,
    partnerId: dumpster.partner_id,
    
    // Relationships
    sizeId: dumpster.size_id,
    size,
    compatibleWasteTypes: wasteTypeIds,
  };
} 