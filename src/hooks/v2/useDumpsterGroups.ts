import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useTranslation } from 'react-i18next';

export interface DumpsterGroup {
  sizeId: string;
  sizeName: string;
  sizeNameAr: string;
  capacity: number; // cubic yards
  dumpsterCount: number;
  minPrice: number;
  maxPrice: number;
  averagePrice: number;
  imageUrl: string;
  bestFor: string[];
  dumpsters: {
    id: string;
    nameEn: string;
    nameAr: string;
    pricePerLoad: number;
    imageUrl: string;
    isAvailable: boolean;
  }[];
}

export interface DumpsterGroupFilters {
  availability?: boolean;
  cityId?: string;
  searchTerm?: string;
  wasteTypeIds?: string[];
  priceRange?: {
    min?: number;
    max?: number;
  };
}

/**
 * Hook to fetch dumpsters grouped by size
 */
export function useDumpsterGroups(filters?: DumpsterGroupFilters) {
  const { i18n } = useTranslation();
  const currentLocale = i18n.language || 'en';

  return useQuery<DumpsterGroup[], Error>({
    queryKey: ['dumpster-groups', filters, currentLocale],
    queryFn: () => getDumpsterGroups(filters, currentLocale),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get dumpsters grouped by size
 */
export async function getDumpsterGroups(
  filters?: DumpsterGroupFilters,
  locale: string = 'en'
): Promise<DumpsterGroup[]> {
  try {
    console.log('Fetching dumpster groups with filters:', filters);

    // Build the query for dumpsters with their size information
    let query = supabase
      .from('dumpsters')
      .select(`
        id,
        name_en,
        name_ar,
        image_url,
        price_per_load,
        is_available,
        size_id,
        dumpster_sizes (
          id,
          name,
          volume_cubic_yards
        )
      `);

    // Apply filters
    if (filters?.availability) {
      query = query.eq('is_available', true);
    }

    if (filters?.searchTerm) {
      const term = `%${filters.searchTerm}%`;
      query = query.or(`name_en.ilike.${term},name_ar.ilike.${term}`);
    }

    if (filters?.priceRange?.min !== undefined) {
      query = query.gte('price_per_load', filters.priceRange.min);
    }

    if (filters?.priceRange?.max !== undefined) {
      query = query.lte('price_per_load', filters.priceRange.max);
    }

    const { data: dumpsters, error } = await query;

    if (error) {
      console.error('Error fetching dumpsters for grouping:', error);
      throw error;
    }

    if (!dumpsters || !Array.isArray(dumpsters)) {
      console.error('Invalid dumpster data returned:', dumpsters);
      return [];
    }

    console.log(`Found ${dumpsters.length} dumpsters to group`);

    // Group dumpsters by size_id
    const groupedBySizeId: Record<string, any[]> = {};
    
    dumpsters.forEach(dumpster => {
      const sizeId = dumpster.size_id;
      if (!sizeId) return; // Skip dumpsters without size

      if (!groupedBySizeId[sizeId]) {
        groupedBySizeId[sizeId] = [];
      }
      groupedBySizeId[sizeId].push(dumpster);
    });

    console.log(`Grouped into ${Object.keys(groupedBySizeId).length} size categories`);

    // Transform groups into DumpsterGroup objects
    const dumpsterGroups: DumpsterGroup[] = Object.entries(groupedBySizeId).map(([sizeId, sizeDumpsters]) => {
      // Get size info from the first dumpster (they should all have the same size info)
      const sizeInfo = sizeDumpsters[0]?.dumpster_sizes;
      
      if (!sizeInfo) {
        console.warn(`No size info found for size ID: ${sizeId}`);
        return null;
      }

      // Calculate price statistics
      const prices = sizeDumpsters.map(d => d.price_per_load || 0);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const averagePrice = Math.round(prices.reduce((sum, price) => sum + price, 0) / prices.length);

      // Get the best image from available dumpsters
      const imageUrl = sizeDumpsters.find(d => d.image_url)?.image_url || 
        'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';

      // Generate best-for tags based on size
      const capacity = sizeInfo.volume_cubic_yards || 0;
      const bestFor = generateBestForTags(capacity);

      // Transform dumpster data
      const dumpsters = sizeDumpsters.map(d => ({
        id: d.id,
        nameEn: d.name_en || 'Unnamed Dumpster',
        nameAr: d.name_ar || 'حاوية بدون اسم',
        pricePerLoad: d.price_per_load || 0,
        imageUrl: d.image_url || imageUrl,
        isAvailable: d.is_available !== undefined ? d.is_available : true,
      }));

      return {
        sizeId,
        sizeName: sizeInfo.name || `${capacity} Yard`,
        sizeNameAr: sizeInfo.name_ar || `${capacity} ياردة`,
        capacity,
        dumpsterCount: sizeDumpsters.length,
        minPrice,
        maxPrice,
        averagePrice,
        imageUrl,
        bestFor,
        dumpsters,
      };
    }).filter(Boolean) as DumpsterGroup[];

    // Sort by capacity (ascending)
    dumpsterGroups.sort((a, b) => a.capacity - b.capacity);

    console.log(`Returning ${dumpsterGroups.length} dumpster groups`);
    return dumpsterGroups;

  } catch (error) {
    console.error('Error in getDumpsterGroups:', error);
    return [];
  }
}

/**
 * Generate best-for tags based on dumpster capacity
 */
function generateBestForTags(capacity: number): string[] {
  if (capacity <= 5) {
    return ['Small Projects', 'Bathroom Renovation', 'Small Repairs'];
  } else if (capacity <= 10) {
    return ['Medium Projects', 'Room Renovation', 'Small Landscaping'];
  } else if (capacity <= 20) {
    return ['Large Projects', 'House Renovation', 'Construction'];
  } else if (capacity <= 30) {
    return ['Commercial Projects', 'Large Construction', 'Demolition'];
  } else {
    return ['Industrial Projects', 'Large Demolition', 'Commercial Buildings'];
  }
}
