import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Partner } from '@/types/partner';
import { Profile } from '@/types/profile';

export function usePartner(partnerId: string | null) {
  return useQuery<Partner | null>({
    queryKey: ['partner', partnerId],
    queryFn: async () => {
      if (!partnerId) return null;

      // Get partner with profile in a single query using join
      const { data: partner, error: partnerError } = await supabase
        .from('partners')
        .select(`
          *,
          profile:profiles!inner(
            id,
            full_name,
            avatar_url,
            email,
            phone,
            user_type
          )
        `)
        .eq('id', partnerId)
        .single();

      if (partnerError) {
        console.error('Partner query error:', partnerError);
        return null;
      }

      console.log('Partner with profile data:', partner);

      return {
        ...partner,
        service_areas: partner.service_areas as string[] || [],
        profile: partner.profile ? {
          id: partner.profile.id,
          full_name: partner.profile.full_name,
          avatar_url: partner.profile.avatar_url,
          email: partner.profile.email,
          phone: partner.profile.phone,
          user_type: partner.profile.user_type
        } : undefined
      } as Partner;
    },
    enabled: !!partnerId,
    retry: 1
  });
}

/**
 * Hook to fetch all active partners
 */
export function usePartners() {
  return useQuery<Partner[]>({
    queryKey: ['partners'],
    queryFn: async () => {
      // Get all active partners with their profiles
      const { data: partners, error: partnersError } = await supabase
        .from('partners')
        .select(`
          *,
          profile:profiles!inner(
            id,
            full_name,
            avatar_url,
            email,
            phone,
            user_type
          )
        `)
        .eq('status', 'active')
        .order('company_name');

      if (partnersError) {
        console.error('Partners query error:', partnersError);
        return [];
      }

      console.log('Partners with profile data:', partners);

      return partners.map(partner => ({
        ...partner,
        service_areas: partner.service_areas as string[] || [],
        profile: partner.profile ? {
          id: partner.profile.id,
          full_name: partner.profile.full_name,
          avatar_url: partner.profile.avatar_url,
          email: partner.profile.email,
          phone: partner.profile.phone,
          user_type: partner.profile.user_type
        } : undefined
      })) as Partner[];
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
