/**
 * Utility for converting between legacy and v2 dumpster data models
 */

import { Dumpster as LegacyDumpster } from '@/types/dumpster';
import { Dumpster as NewDumpster } from '@/types/new/dumpster';
import { Dumpster as V2Dumpster } from '@/types/v2';

/**
 * Convert a legacy dumpster to a v2 dumpster
 */
export function convertToV2Dumpster(legacy: LegacyDumpster | NewDumpster | null): V2Dumpster | null {
  if (!legacy || !legacy.id) {
    return null;
  }

  // Handle NewDumpster (current transition model)
  if ('nameEn' in legacy) {
    return {
      id: legacy.id,
      nameEn: legacy.nameEn,
      nameAr: legacy.nameAr,
      description: {
        en: legacy.description || '',
        ar: legacy.description || ''
      },
      imageUrl: legacy.imageUrl,
      additionalImages: [],
      
      // Dimensions
      length: legacy.length || 10,
      width: legacy.width || 8,
      height: legacy.height || 6,
      capacity: 10, // Default
      maxWeight: 2000, // Default
      standingArea: legacy.standingArea || (legacy.length || 10) * (legacy.width || 8),
      
      // Business details
      pricePerLoad: legacy.pricePerLoad || 75,
      rating: legacy.rating || 4.0,
      reviewCount: legacy.reviewCount || 0,
      isAvailable: legacy.isAvailable !== undefined ? legacy.isAvailable : true,
      
      // Relationships
      sizeId: legacy.size?.id || '',
      compatibleWasteTypes: legacy.compatibleWasteTypes || [],
    };
  }

  // Handle LegacyDumpster
  return {
    id: legacy.id,
    nameEn: legacy.name,
    nameAr: legacy.name,
    description: {
      en: legacy.description || '',
      ar: legacy.description || ''
    },
    imageUrl: legacy.imageUrl,
    additionalImages: legacy.additionalImages || [],
    
    // Use first size or defaults
    length: legacy.size?.dimensions?.length || 10,
    width: legacy.size?.dimensions?.width || 8,
    height: legacy.size?.dimensions?.height || 6,
    capacity: legacy.size?.volumeCubicYards || 10,
    maxWeight: legacy.size?.maxWeightPounds || 2000,
    standingArea: (legacy.size?.dimensions?.length || 10) * (legacy.size?.dimensions?.width || 8),
    
    // Business details
    pricePerLoad: legacy.pricePerDay || 75,
    rating: legacy.rating || 4.0,
    reviewCount: legacy.reviewCount || 0,
    isAvailable: legacy.availability?.isAvailable !== undefined ? legacy.availability.isAvailable : true,
    nextAvailableDate: legacy.availability?.nextAvailableDate,
    partnerId: legacy.partnerId,
    
    // Relationships
    sizeId: legacy.size?.id || '',
    compatibleWasteTypes: legacy.compatibleWasteTypes || [],
  };
}

/**
 * Convert an array of legacy dumpsters to v2 dumpsters
 */
export function convertToV2Dumpsters(dumpsters: (LegacyDumpster | NewDumpster)[]): V2Dumpster[] {
  if (!dumpsters || !Array.isArray(dumpsters)) {
    console.error('Invalid dumpsters array passed to convertToV2Dumpsters:', dumpsters);
    return [];
  }
  
  return dumpsters
    .filter(dumpster => dumpster && dumpster.id)
    .map(convertToV2Dumpster)
    .filter((dumpster): dumpster is V2Dumpster => dumpster !== null);
}

/**
 * Convert a v2 dumpster back to a legacy dumpster
 * Useful for compatibility with existing components
 */
export function convertFromV2ToLegacy(v2Dumpster: V2Dumpster): LegacyDumpster {
  // Create a size object from the dimensions
  const size = {
    id: v2Dumpster.sizeId || `size-${v2Dumpster.id}`,
    name: v2Dumpster.size?.nameEn || 'Standard Size',
    volumeCubicYards: v2Dumpster.capacity,
    maxWeightPounds: v2Dumpster.maxWeight,
    dimensions: {
      length: v2Dumpster.length,
      width: v2Dumpster.width,
      height: v2Dumpster.height
    }
  };
  
  return {
    id: v2Dumpster.id,
    name: v2Dumpster.nameEn,
    description: v2Dumpster.description?.en,
    imageUrl: v2Dumpster.imageUrl,
    additionalImages: v2Dumpster.additionalImages,
    size: size,
    compatibleWasteTypes: v2Dumpster.compatibleWasteTypes || [],
    pricePerDay: v2Dumpster.pricePerLoad,
    availability: {
      isAvailable: v2Dumpster.isAvailable,
      nextAvailableDate: v2Dumpster.nextAvailableDate
    },
    rating: v2Dumpster.rating,
    reviewCount: v2Dumpster.reviewCount,
    partnerId: v2Dumpster.partnerId
  };
}

/**
 * Convert a v2 dumpster to the new transition model
 */
export function convertFromV2ToNew(v2Dumpster: V2Dumpster): NewDumpster {
  return {
    id: v2Dumpster.id,
    nameEn: v2Dumpster.nameEn,
    nameAr: v2Dumpster.nameAr,
    description: v2Dumpster.description?.en,
    imageUrl: v2Dumpster.imageUrl,
    partnerLogo: v2Dumpster.partnerLogo,
    length: v2Dumpster.length,
    width: v2Dumpster.width,
    height: v2Dumpster.height,
    pricePerLoad: v2Dumpster.pricePerLoad,
    rating: v2Dumpster.rating,
    reviewCount: v2Dumpster.reviewCount,
    isAvailable: v2Dumpster.isAvailable,
    standingArea: v2Dumpster.standingArea,
    compatibleWasteTypes: v2Dumpster.compatibleWasteTypes
  };
}