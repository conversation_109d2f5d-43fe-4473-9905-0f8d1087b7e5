/**
 * Utility functions for formatting data
 */

import { useTranslation } from 'react-i18next';

/**
 * Format a number as currency based on the current locale
 * 
 * @param amount - The amount to format
 * @param currencyCode - The currency code (default: SAR for Saudi Riyal)
 * @param locale - The locale to use for formatting (default: current locale or fallback to en)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number, 
  currencyCode: string = 'SAR',
  locale?: string
): string {
  try {
    // Get current locale if not provided
    if (!locale) {
      // Try to get it from i18n or fallback to 'en-US'
      try {
        const { i18n } = useTranslation();
        locale = i18n.language || 'en-US';
      } catch (error) {
        locale = 'en-US';
      }
    }
    
    // Map locale codes to appropriate format
    const localeMap: Record<string, string> = {
      'ar': 'ar-SA',
      'en': 'en-US',
    };
    
    const formattingLocale = localeMap[locale] || locale;
    
    return new Intl.NumberFormat(formattingLocale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (error) {
    // Fallback to basic formatting if Intl is not supported
    return `${currencyCode} ${amount.toFixed(0)}`;
  }
}

/**
 * Format a date object to a localized string
 * 
 * @param date - The date to format
 * @param locale - The locale to use for formatting (default: current locale or fallback to en)
 * @param options - Additional formatting options
 * @returns Formatted date string
 */
export function formatDate(
  date: Date | string,
  locale?: string,
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  }
): string {
  try {
    // Convert string to Date if needed
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Get current locale if not provided
    if (!locale) {
      try {
        const { i18n } = useTranslation();
        locale = i18n.language || 'en-US';
      } catch (error) {
        locale = 'en-US';
      }
    }
    
    // Map locale codes
    const localeMap: Record<string, string> = {
      'ar': 'ar-SA',
      'en': 'en-US',
    };
    
    const formattingLocale = localeMap[locale] || locale;
    
    return new Intl.DateTimeFormat(formattingLocale, options).format(dateObj);
  } catch (error) {
    // Fallback to ISO string if formatting fails
    return typeof date === 'string' ? date : date.toISOString().split('T')[0];
  }
}

/**
 * Format a number with thousands separators based on locale
 * 
 * @param num - The number to format
 * @param locale - The locale to use for formatting (default: current locale or fallback to en)
 * @returns Formatted number string with proper separators
 */
export function formatNumber(num: number, locale?: string): string {
  try {
    if (!locale) {
      try {
        const { i18n } = useTranslation();
        locale = i18n.language || 'en-US';
      } catch (error) {
        locale = 'en-US';
      }
    }
    
    const localeMap: Record<string, string> = {
      'ar': 'ar-SA',
      'en': 'en-US',
    };
    
    const formattingLocale = localeMap[locale] || locale;
    
    return new Intl.NumberFormat(formattingLocale).format(num);
  } catch (error) {
    // Basic fallback
    return num.toString();
  }
} 