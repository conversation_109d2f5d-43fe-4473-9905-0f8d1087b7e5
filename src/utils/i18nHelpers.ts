import i18next from 'i18next';
import { I18nManager } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Ensures that all translations are properly loaded and applied.
 * This is useful when switching between languages or when the app starts.
 */
export const ensureTranslationsLoaded = async () => {
  try {
    // Get the current language
    const language = await AsyncStorage.getItem('@app:language') || 'en';
    
    // Check if the current language matches i18next's language
    if (i18next.language !== language) {
      console.log(`Ensuring translations are loaded for ${language}`);
      
      // Force reload resources for the current language
      await i18next.reloadResources([language]);
      
      // Change language if needed
      if (i18next.language !== language) {
        await i18next.changeLanguage(language);
      }
      
      // Ensure RTL settings match the language
      const shouldBeRTL = language === 'ar';
      if (I18nManager.isRTL !== shouldBeRTL) {
        console.log(`RTL mismatch detected. Current: ${I18nManager.isRTL}, Should be: ${shouldBeRTL}`);
        
        // Mark that RTL needs to be applied on next reload
        await AsyncStorage.setItem('@app:rtl_applied', 'false');
        await AsyncStorage.setItem('@app:force_restart', 'true');
      }
    }
    
    return true;
  } catch (error) {
    console.error('Failed to ensure translations are loaded:', error);
    return false;
  }
};

/**
 * Checks if a translation key exists and returns a fallback if it doesn't.
 * This is useful for debugging missing translations.
 */
export const safeTranslate = (t: Function, key: string, fallback: string = '') => {
  const translation = t(key);
  
  // If the translation is the same as the key, it's likely missing
  if (translation === key) {
    console.warn(`Missing translation for key: ${key}`);
    return fallback || key;
  }
  
  return translation;
}; 