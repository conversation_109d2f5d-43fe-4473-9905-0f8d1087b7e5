interface LocationPickerFormat {
  lat: number;
  lng: number;
  address: string;
}

interface OrderLocationFormat {
  latitude: number;
  longitude: number;
  address: string;
}

export const toLocationPickerFormat = (location: OrderLocationFormat): LocationPickerFormat => ({
  lat: location.latitude,
  lng: location.longitude,
  address: location.address,
});

export const toOrderLocationFormat = (location: LocationPickerFormat): OrderLocationFormat => ({
  latitude: location.lat,
  longitude: location.lng,
  address: location.address,
}); 