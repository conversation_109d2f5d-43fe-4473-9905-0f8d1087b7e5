import { Session } from '@supabase/supabase-js';

/**
 * User roles in the application
 */
export enum UserRole {
  CUSTOMER = 'customer',
  PARTNER = 'partner',
  ADMIN = 'admin',
  DRIVER = 'driver'
}

/**
 * Get the user's role from the session
 * @param session The user's session
 * @returns The user's role or null if not authenticated
 */
export function getUserRole(session: Session | null): UserRole | null {
  if (!session) return null;
  
  // Try to get the role from the JWT claims
  const role = session.user?.app_metadata?.user_role || 
               session.user?.role || 
               session.user?.app_metadata?.role;
  
  if (role && Object.values(UserRole).includes(role as UserRole)) {
    return role as UserRole;
  }
  
  // Default to customer if no valid role is found
  return UserRole.CUSTOMER;
}

/**
 * Check if the user has a specific role
 * @param session The user's session
 * @param role The role to check
 * @returns True if the user has the specified role
 */
export function hasRole(session: Session | null, role: UserRole): boolean {
  const userRole = getUserRole(session);
  return userRole === role;
}

/**
 * Check if the user has any of the specified roles
 * @param session The user's session
 * @param roles The roles to check
 * @returns True if the user has any of the specified roles
 */
export function hasAnyRole(session: Session | null, roles: UserRole[]): boolean {
  const userRole = getUserRole(session);
  return userRole !== null && roles.includes(userRole);
}

/**
 * Check if the user is an admin
 * @param session The user's session
 * @returns True if the user is an admin
 */
export function isAdmin(session: Session | null): boolean {
  return hasRole(session, UserRole.ADMIN);
}

/**
 * Check if the user is a partner
 * @param session The user's session
 * @returns True if the user is a partner
 */
export function isPartner(session: Session | null): boolean {
  return hasRole(session, UserRole.PARTNER);
}

/**
 * Check if the user is a driver
 * @param session The user's session
 * @returns True if the user is a driver
 */
export function isDriver(session: Session | null): boolean {
  return hasRole(session, UserRole.DRIVER);
}

/**
 * Check if the user is a customer
 * @param session The user's session
 * @returns True if the user is a customer
 */
export function isCustomer(session: Session | null): boolean {
  return hasRole(session, UserRole.CUSTOMER);
}

/**
 * Get the appropriate app route based on user role
 * @param session The user's session
 * @returns The route path for the user's role
 */
export function getHomeRouteForRole(session: Session | null): string {
  const role = getUserRole(session);
  
  switch (role) {
    case UserRole.ADMIN:
      return '/admin';
    case UserRole.PARTNER:
      return '/partner';
    case UserRole.DRIVER:
      return '/driver';
    case UserRole.CUSTOMER:
    default:
      return '/';
  }
}

/**
 * A React component that renders children only if the user has the required role
 */
export function RoleGuard({ 
  session, 
  allowedRoles, 
  children, 
  fallback = null 
}: { 
  session: Session | null; 
  allowedRoles: UserRole[]; 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
}): React.ReactNode {
  if (hasAnyRole(session, allowedRoles)) {
    return children;
  }
  return fallback;
}
