import { Dumpster as OldDumpster, DumpsterSize as OldDumpsterSize } from '@/types/dumpster';
import { Dumpster as NewDumpster } from '@/types/new/dumpster';

/**
 * Safe image URL helper - ensures we have a valid image URL
 */
export function getSafeImageUrl(url?: string): string {
  // If URL is undefined, empty or not a string, use default
  if (!url || typeof url !== 'string') {
    return 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/settledcontainer_10.png';
  }
  
  // If URL doesn't start with http, assume it's a Supabase storage URL
  if (!url.startsWith('http')) {
    return `https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/${url}`;
  }
  
  return url;
}

/**
 * Extract numeric size from dumpster name
 */
export function getSizeFromName(name: string | undefined | null): number | null {
  if (!name) return null;
  
  // Match patterns like "10 m³", "10m³", "10 cubic meter", "10yd", "10 yd", etc.
  const sizeMatch = name.match(/(\d+(\.\d+)?)\s*(m³|cubic\s*meter|yd|yard)/i);
  if (sizeMatch) {
    return parseFloat(sizeMatch[1]);
  }
  
  // Try a more general approach - just find the first number
  const generalMatch = name.match(/\d+(\.\d+)?/);
  if (generalMatch) {
    return parseFloat(generalMatch[0]);
  }
  
  return null;
}

/**
 * Helper to get safe dimensions from a dumpster size
 */
export function getSafeDimensions(size: OldDumpsterSize | undefined | null) {
  const defaultDimensions = { length: 10, width: 8, height: 6 };
  
  if (!size || !size.dimensions) return defaultDimensions;
  
  return {
    length: size.dimensions.length || defaultDimensions.length,
    width: size.dimensions.width || defaultDimensions.width,
    height: size.dimensions.height || defaultDimensions.height
  };
}

/**
 * Converts from the old dumpster format to the new one
 */
export function convertToNewDumpster(oldDumpster: OldDumpster | null): NewDumpster {
  // Return default dumpster if null
  if (!oldDumpster || !oldDumpster.id) {
    return {
      id: 'default-id',
      nameEn: 'Unnamed Dumpster',
      nameAr: 'حاوية بدون اسم',
      imageUrl: getSafeImageUrl(),
      partnerLogo: '/assets/images/partners/default-logo.png',
      length: 10,
      width: 8,
      height: 6,
      pricePerLoad: 75,
      rating: 4.0,
      reviewCount: 0,
      isAvailable: true,
      standingArea: 80,
      description: 'No description available',
    };
  }

  // Get dimensions from the single size object
  const dimensions = getSafeDimensions(oldDumpster.size);
  
  // Get availability with fallbacks
  const isAvailable = oldDumpster.availability?.isAvailable !== undefined 
    ? oldDumpster.availability.isAvailable 
    : true;

  // Calculate standing area
  const standingArea = dimensions.length * dimensions.width;

  // Get a safe image URL
  const imageUrl = getSafeImageUrl(oldDumpster.imageUrl);

  return {
    id: oldDumpster.id,
    nameEn: oldDumpster.name || 'Unnamed Dumpster',
    nameAr: oldDumpster.name || 'حاوية بدون اسم', // Default to English if Arabic not available
    imageUrl: imageUrl,
    partnerLogo: '/assets/images/partners/default-logo.png', // Default logo
    length: dimensions.length,
    width: dimensions.width,
    height: dimensions.height,
    pricePerLoad: oldDumpster.pricePerDay || 75,
    rating: oldDumpster.rating || 4.0,
    reviewCount: oldDumpster.reviewCount || 0,
    isAvailable,
    standingArea,
    description: oldDumpster.description || '',
  };
}

/**
 * Converts all dumpsters from old format to new format
 */
export function convertAllDumpsters(oldDumpsters: OldDumpster[]): NewDumpster[] {
  if (!oldDumpsters || !Array.isArray(oldDumpsters)) {
    console.error('Invalid dumpsters array passed to convertAllDumpsters:', oldDumpsters);
    return [];
  }
  
  // Filter out any null or undefined dumpsters first
  const validDumpsters = oldDumpsters.filter(dumpster => dumpster && dumpster.id);
  
  // Convert each dumpster with error handling
  return validDumpsters.map(dumpster => {
    try {
      return convertToNewDumpster(dumpster);
    } catch (error) {
      console.error(`Error converting dumpster ${dumpster.id}:`, error);
      // Return a default dumpster with the original ID if possible
      return {
        id: dumpster.id || 'error-id',
        nameEn: 'Error Loading Dumpster',
        nameAr: 'خطأ في تحميل الحاوية',
        imageUrl: getSafeImageUrl(),
        partnerLogo: '/assets/images/partners/default-logo.png',
        length: 10,
        width: 8,
        height: 6,
        pricePerLoad: 75,
        rating: 4.0,
        reviewCount: 0,
        isAvailable: false,
        standingArea: 80,
        description: 'There was an error loading this dumpster data.',
      };
    }
  });
} 