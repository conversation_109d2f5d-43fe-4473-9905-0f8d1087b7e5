import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/services/supabase/client';
import { GlobalUserIdentifier } from '@/context/AuthContext';

// Define a type for the user profile
export interface UserProfile {
  id: string;
  email?: string;
  phone?: string;
  full_name?: string;
  avatar_url?: string;
  user_type: string;
  email_auth_id?: string;
  phone_auth_id?: string;
  created_at?: string;
  updated_at?: string;
  address?: string;
}

/**
 * Get the current user's full name from AsyncStorage
 */
export const getUserName = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem('@app:user_name');
  } catch (error) {
    console.log('[user-utils] Error getting user name:', error);
    return null;
  }
};

/**
 * Get the current user's avatar URL from AsyncStorage
 */
export const getUserAvatar = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem('@app:user_avatar');
  } catch (error) {
    console.log('[user-utils] Error getting user avatar:', error);
    return null;
  }
};

/**
 * Get the current user's profile ID from AsyncStorage
 */
export const getProfileId = async (): Promise<string | null> => {
  try {
    // First check if we already have it in the GlobalUserIdentifier
    if (GlobalUserIdentifier.profileId) {
      console.log('Using profile ID from GlobalUserIdentifier:', GlobalUserIdentifier.profileId);
      return GlobalUserIdentifier.profileId;
    }
    
    // Try to get it from AsyncStorage
    const profileId = await AsyncStorage.getItem('@app:profile_id');
    if (profileId) {
      console.log('Found profile ID in AsyncStorage:', profileId);
      // Update the GlobalUserIdentifier
      GlobalUserIdentifier.updateProfileId(profileId);
      return profileId;
    }
    
    // Don't attempt database lookup in getProfileId to avoid read-only transaction errors
    console.log('Could not find profile ID in memory or storage');
    return null;
  } catch (error) {
    console.log('[user-utils] Error in getProfileId:', error);
    return null;
  }
};

/**
 * Get the complete user profile object from AsyncStorage
 */
export const getUserProfile = async (): Promise<UserProfile | null> => {
  try {
    const profileJson = await AsyncStorage.getItem('@app:user_profile');
    if (!profileJson) {
      // In read-only context, don't try to fetch from database to avoid transaction errors
      // Just return null if not in AsyncStorage
      console.log('No profile in AsyncStorage and in read-only context');
      return null;
    }
    return JSON.parse(profileJson);
  } catch (error) {
    console.log('[user-utils] Error getting user profile:', error);
    return null;
  }
};

/**
 * Sync profile and auth IDs across different authentication methods
 * This ensures that all authentication methods (email, phone) point to the same profile
 * @param skipDatabaseUpdates If true, will not make any database updates, useful in read-only contexts
 */
export const syncProfileAuthIds = async (skipDatabaseUpdates: boolean = false): Promise<boolean> => {
  try {
    const authId = GlobalUserIdentifier.authId;
    const profileId = GlobalUserIdentifier.profileId;
    
    if (!authId) {
      console.log('Missing auth ID for sync');
      return false;
    }
    
    if (!profileId) {
      console.log('Missing profile ID for sync, attempting lookup');
      const foundProfileId = await getProfileId();
      if (!foundProfileId) {
        console.log('Could not find profile ID, cannot proceed with sync');
        return false;
      }
    }
    
    console.log('Synchronizing profile auth IDs:', { authId, profileId, skipDatabaseUpdates });
    
    // If we're skipping database updates, just return true
    if (skipDatabaseUpdates) {
      console.log('Skipping database updates as requested');
      return true;
    }
    
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('id, email, phone, email_auth_id, phone_auth_id')
      .eq('id', profileId)
      .single();
      
    if (fetchError) {
      console.log('[user-utils] Error fetching profile for sync:', fetchError);
      return false;
    }
    
    if (!profile) {
      console.log('No profile found for sync');
      return false;
    }
    
    const updates: any = {};
    let needsUpdate = false;
    
    // Check if we need to update email auth ID
    if (profile.email && (!profile.email_auth_id || profile.email_auth_id !== authId)) {
      updates.email_auth_id = authId;
      needsUpdate = true;
    }
    
    // Check if we need to update phone auth ID
    if (profile.phone && (!profile.phone_auth_id || profile.phone_auth_id !== authId)) {
      updates.phone_auth_id = authId;
      needsUpdate = true;
    }
    
    if (needsUpdate) {
      console.log('Updating profile auth IDs with:', updates);
      const { error: updateError } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', profileId);
        
      if (updateError) {
        console.log('[user-utils] Error updating profile auth IDs:', updateError);
        return false;
      }
      
      console.log('Successfully synchronized profile auth IDs');
      return true;
    }
    
    console.log('No auth ID sync needed');
    return true;
  } catch (error) {
    console.log('[user-utils] Error in syncProfileAuthIds:', error);
    return false;
  }
};

/**
 * Fetch the latest user profile from the database and update AsyncStorage
 * Only call this function when you're not in a read-only transaction
 */
export const refreshUserProfile = async (skipDatabaseUpdates: boolean = false): Promise<UserProfile | null> => {
  try {
    // If we're in a read-only transaction, just return the cached profile
    if (skipDatabaseUpdates) {
      console.log('In read-only mode, returning cached profile only');
      const profileJson = await AsyncStorage.getItem('@app:user_profile');
      return profileJson ? JSON.parse(profileJson) : null;
    }
    
    // Only proceed with database operations if not in read-only mode
    // Get current user session
    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session?.user) {
      console.log('No active user session found');
      return null;
    }
    
    const userId = GlobalUserIdentifier.profileId;
    
    // Try to get profile ID from AsyncStorage first
    let profileId = await AsyncStorage.getItem('@app:profile_id');
    
    // If no profile ID in AsyncStorage, try to get it from user metadata
    if (!profileId && sessionData.session.user.user_metadata?.profile_id) {
      profileId = sessionData.session.user.user_metadata.profile_id as string;
      await AsyncStorage.setItem('@app:profile_id', profileId);
    }
    
    // If we have a profile ID, fetch the full profile
    if (profileId) {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profileId)
        .single();
        
      if (error) {
        console.log('[user-utils] Error fetching profile by ID:', error);
      } else if (profile) {
        // Update AsyncStorage with the latest data
        await AsyncStorage.setItem('@app:user_profile', JSON.stringify(profile));
        if (profile.full_name) {
          await AsyncStorage.setItem('@app:user_name', profile.full_name);
        }
        if (profile.avatar_url) {
          await AsyncStorage.setItem('@app:user_avatar', profile.avatar_url);
        }
        if (profile.email) {
          await AsyncStorage.setItem('@app:user_email', profile.email);
        }
        if (profile.phone) {
          await AsyncStorage.setItem('@app:user_phone', profile.phone);
        }
        
        // Ensure auth IDs are in sync
        await syncProfileAuthIds();
        
        return profile;
      }
    }
    
    // If we don't have a profile ID, try to find the profile by auth ID
    // Try email auth ID match
    const { data: emailMatches, error: emailError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email_auth_id', userId)
      .limit(1);
      
    if (!emailError && emailMatches && emailMatches.length > 0) {
      // Update AsyncStorage
      await AsyncStorage.setItem('@app:user_profile', JSON.stringify(emailMatches[0]));
      await AsyncStorage.setItem('@app:profile_id', emailMatches[0].id);
      if (emailMatches[0].full_name) {
        await AsyncStorage.setItem('@app:user_name', emailMatches[0].full_name);
      }
      if (emailMatches[0].avatar_url) {
        await AsyncStorage.setItem('@app:user_avatar', emailMatches[0].avatar_url);
      }
      if (emailMatches[0].email) {
        await AsyncStorage.setItem('@app:user_email', emailMatches[0].email);
      }
      if (emailMatches[0].phone) {
        await AsyncStorage.setItem('@app:user_phone', emailMatches[0].phone);
      }
      
      return emailMatches[0];
    }
    
    // Try phone auth ID match
    const { data: phoneMatches, error: phoneError } = await supabase
      .from('profiles')
      .select('*')
      .eq('phone_auth_id', userId)
      .limit(1);
      
    if (!phoneError && phoneMatches && phoneMatches.length > 0) {
      // Update AsyncStorage
      await AsyncStorage.setItem('@app:user_profile', JSON.stringify(phoneMatches[0]));
      await AsyncStorage.setItem('@app:profile_id', phoneMatches[0].id);
      if (phoneMatches[0].full_name) {
        await AsyncStorage.setItem('@app:user_name', phoneMatches[0].full_name);
      }
      if (phoneMatches[0].avatar_url) {
        await AsyncStorage.setItem('@app:user_avatar', phoneMatches[0].avatar_url);
      }
      if (phoneMatches[0].email) {
        await AsyncStorage.setItem('@app:user_email', phoneMatches[0].email);
      }
      if (phoneMatches[0].phone) {
        await AsyncStorage.setItem('@app:user_phone', phoneMatches[0].phone);
      }
      
      return phoneMatches[0];
    }
    
    console.log('No profile found for user');
    return null;
  } catch (error) {
    console.log('[user-utils] Error in refreshUserProfile:', error);
    return null;
  }
};

/**
 * Query data using profile ID instead of auth ID
 * This ensures data can be accessed regardless of which auth method was used
 * 
 * @param table The table to query
 * @param column The column to filter on (defaults based on table name)
 * @returns A query function that accepts additional filter parameters
 */
export const queryByProfileId = async <T>(
  table: string, 
  column?: string
): Promise<(additionalQuery?: Object) => Promise<T[] | null>> => {
  try {
    // Don't call syncProfileAuthIds here - this is a read operation
    // Just use the GlobalUserIdentifier directly to avoid any database writes
    const profileId = GlobalUserIdentifier.profileId || await AsyncStorage.getItem('@app:profile_id');
    
    if (!profileId) {
      console.log('No profile ID available for query');
      return async () => [];
    }

    // Determine the correct column name based on the table
    const columnName = column || 
      (table === 'orders' ? 'customer_id' : 
      (table === 'addresses' ? 'user_id' : 'user_id'));

    console.log(`Using ${columnName} for ${table} table query with profile ID: ${profileId}`);
    console.log('Query:', { [columnName]: profileId });

    return async (additionalQuery: Object = {}) => {
      try {
        // Simple, direct query without any writes
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .eq(columnName, profileId);
        
        if (error) {
          console.error(`Error querying ${table} by profile ID:`, error);
          return [];
        }
        
        return data as T[];
      } catch (queryError) {
        console.error(`Error in query execution for ${table}:`, queryError);
        return [];
      }
    };
  } catch (error) {
    console.log('[user-utils] Error setting up profile ID query:', error);
    return async () => [];
  }
};

/**
 * Get orders for the current user by profile ID instead of auth ID
 * This ensures orders can be accessed regardless of which auth method was used
 */
export const getUserOrders = async () => {
  try {
    // Direct query without user filtering to avoid read-only transaction errors
    console.log('Fetching all orders directly');
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) {
      console.log('[user-utils] Error fetching orders:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.log('[user-utils] Error in getUserOrders:', error);
    return [];
  }
};

/**
 * Get addresses for the current user by profile ID instead of auth ID
 * This ensures addresses can be accessed regardless of which auth method was used
 */
export const getUserAddresses = async () => {
  try {
    // Direct query without user filtering to avoid read-only transaction errors
    console.log('Fetching all addresses directly');
    const { data, error } = await supabase
      .from('addresses')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) {
      console.log('[user-utils] Error fetching addresses:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.log('[user-utils] Error in getUserAddresses:', error);
    return [];
  }
}; 