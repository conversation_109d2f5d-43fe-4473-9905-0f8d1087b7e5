import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';

export async function resetAppState() {
  try {
    // Sign out from Supabase
    await supabase.auth.signOut();
    
    // Clear language settings
    await AsyncStorage.removeItem('@app:language');
    
    // Clear any OTP verification in progress
    await AsyncStorage.removeItem('@app:otp_verification_in_progress');
    
    // Clear any other app state
    await AsyncStorage.multiRemove([
      // Add other keys as needed
    ]);
    
    console.log('App state reset successfully');
    return true;
  } catch (error) {
    console.error('Error resetting app state:', error);
    return false;
  }
} 