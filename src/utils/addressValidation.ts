import axios from 'axios';

const GOOGLE_MAPS_API_KEY = 'YOUR_GOOGLE_MAPS_API_KEY';

export async function validateAddress(address: string): Promise<boolean> {
  try {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/place/findplacefromtext/json`,
      {
        params: {
          input: address,
          inputtype: 'textquery',
          fields: 'formatted_address',
          key: GOOGLE_MAPS_API_KEY,
        },
      }
    );

    const candidates = response.data.candidates;
    return candidates && candidates.length > 0;
  } catch (error) {
    console.error('Error validating address:', error);
    return false;
  }
} 