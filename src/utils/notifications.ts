import { Platform, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import { supabase } from '@/lib/supabase';

/**
 * Registers the device for push notifications and returns the token
 * @returns The Expo push token or undefined if registration failed
 */
export async function registerForPushNotificationsAsync(): Promise<string | undefined> {
  let token;
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  if (existingStatus !== 'granted') {
    console.log('Requesting push notification permissions...');
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }

  if (finalStatus !== 'granted') {
    // User denied permission or it failed
    console.warn('Push notification permission not granted.');
    return;
  }

  try {
    // EAS Project ID from app config
    const projectId = '5397a280-f94c-492d-84e9-3b24c69f8c98';
    if (!projectId) {
      console.error('EAS Project ID not found in app config. Cannot get push token.');
      Alert.alert('Configuration Error', 'Cannot register for push notifications. Project ID missing.');
      return;
    }
    token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
    console.log("Expo Push Token fetched:", token);
  } catch (error) {
    console.error('Error getting Expo push token:', error);
    Alert.alert('Error', 'Could not get push token for notifications.');
  }

  return token;
}

/**
 * Saves the push token to the user's profile in Supabase
 * @param userId The user's ID
 * @param token The push token to save
 */
export async function savePushToken(userId: string, token: string | null) {
  if (!userId || !token) {
    console.log('User ID or push token missing, cannot save.');
    return;
  }
  console.log(`Attempting to save push token for user ${userId}...`);
  const { error } = await supabase
    .from('profiles')
    .update({ push_token: token })
    .eq('id', userId);

  if (error) {
    console.error('Error saving push token to Supabase:', error);
  } else {
    console.log('Push token saved successfully to Supabase for user:', userId);
  }
}

/**
 * Configures the notification handler for the app
 */
export function setupNotifications() {
  // Set notification handler
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });
}

// Jest test stub
/*
import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { supabase } from '@/lib/supabase';
import { registerForPushNotificationsAsync, savePushToken } from '../notifications';

// Mock dependencies
jest.mock('expo-notifications');
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnValue({ error: null }),
  },
}));

describe('Notification utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('registerForPushNotificationsAsync', () => {
    it('should request permissions if not granted', async () => {
      (Notifications.getPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'undetermined' });
      (Notifications.requestPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
      (Notifications.getExpoPushTokenAsync as jest.Mock).mockResolvedValue({ data: 'test-token' });

      const token = await registerForPushNotificationsAsync();
      
      expect(Notifications.requestPermissionsAsync).toHaveBeenCalled();
      expect(token).toBe('test-token');
    });

    it('should not request permissions if already granted', async () => {
      (Notifications.getPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
      (Notifications.getExpoPushTokenAsync as jest.Mock).mockResolvedValue({ data: 'test-token' });

      const token = await registerForPushNotificationsAsync();
      
      expect(Notifications.requestPermissionsAsync).not.toHaveBeenCalled();
      expect(token).toBe('test-token');
    });
  });

  describe('savePushToken', () => {
    it('should save token to Supabase', async () => {
      await savePushToken('user-123', 'push-token-123');
      
      expect(supabase.from).toHaveBeenCalledWith('profiles');
      expect(supabase.update).toHaveBeenCalledWith({ push_token: 'push-token-123' });
      expect(supabase.eq).toHaveBeenCalledWith('id', 'user-123');
    });

    it('should not call Supabase if userId or token is missing', async () => {
      await savePushToken('', 'push-token-123');
      expect(supabase.from).not.toHaveBeenCalled();
      
      await savePushToken('user-123', null);
      expect(supabase.from).not.toHaveBeenCalled();
    });
  });
});
*/
