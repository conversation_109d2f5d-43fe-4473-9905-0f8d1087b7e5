#!/bin/bash

echo "Performing complete cleanup and restart..."

# Clear watchman watches
echo "Clearing watchman watches..."
watchman watch-del '/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app' 2>/dev/null
watchman watch-project '/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app' 2>/dev/null

# Kill any running processes
echo "Killing any running processes..."
pkill -f "expo" 2>/dev/null
pkill -f "metro" 2>/dev/null
pkill -f "node" 2>/dev/null

# Clear caches
echo "Clearing caches..."
rm -rf node_modules/.cache
rm -rf $TMPDIR/metro-*
rm -rf $HOME/.expo/cache

# Clear the .expo folder
echo "Clearing .expo folder..."
rm -rf .expo

# Start Expo with a completely clean slate
echo "Starting Expo with a clean slate..."
npx expo start --clear 