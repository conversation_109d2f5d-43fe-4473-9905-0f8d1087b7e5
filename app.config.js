module.exports = {
  expo: {
    name: "dumpster-user-app",
    slug: "dumpster-user-app",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#00BFA5"
    },
    assetBundlePatterns: ["**/*"],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.dumpstersondemand.userapp",
      buildNumber: "1",
      config: {
        usesNonExemptEncryption: false
      },
      infoPlist: {
        NSLocationWhenInUseUsageDescription: "We need your location to show nearby dumpster locations and save your addresses for delivery.",
        NSLocationAlwaysAndWhenInUseUsageDescription: "We need your location to show nearby dumpster locations and save your addresses for delivery.",
        NSLocationAlwaysUsageDescription: "We need your location to show nearby dumpster locations and save your addresses for delivery.",
        UIBackgroundModes: ["location", "fetch"],
        NSUserNotificationUsageDescription: "We need to send you notifications about your dumpster orders and delivery updates."
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#226d7a"
      },
      package: "com.dumpster.userapp",
      versionCode: 1,
      compileSdkVersion: 35,
      targetSdkVersion: 34,
      minSdkVersion: 24,
      buildToolsVersion: "35.0.0",
      permissions: [
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.POST_NOTIFICATIONS",
        "android.permission.INTERNET",
        "android.permission.ACCESS_NETWORK_STATE",
        "android.permission.WAKE_LOCK",
        "android.permission.VIBRATE"
      ],
      config: {
        googleMaps: {
          apiKey: process.env.GOOGLE_MAPS_API_KEY
        }
      },
      allowBackup: false,
      icon: "./assets/icon.png"
    },
    plugins: [
      "expo-router",
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission: "We need your location to show nearby dumpster locations and save your addresses for delivery.",
          locationAlwaysPermission: "We need your location to show nearby dumpster locations and save your addresses for delivery.",
          locationWhenInUsePermission: "We need your location to show nearby dumpster locations and save your addresses for delivery."
        }
      ],
      [
        "expo-notifications",
        {
          icon: "./assets/notification-icon.png",
          color: "#226d7a",
          sounds: ["./assets/notification_sound.wav"]
        }
      ],
      [
        "expo-build-properties",
        {
          android: {
            usesCleartextTraffic: true,
            compileSdkVersion: 35,
            targetSdkVersion: 34,
            minSdkVersion: 24,
            buildToolsVersion: "35.0.0",
            enableProguardInReleaseBuilds: true,
            enableSeparateBuildPerCPUArchitecture: false,
            universalApk: false,
            newArchEnabled: false
          },
          ios: {}
        }
      ]
    ],
    scheme: "dumpster-user-app",
    web: {
      favicon: "./assets/favicon.png"
    },
    extra: {
      router: {
        origin: false,
        root: "app"
      },
      eas: {
        projectId: "5397a280-f94c-492d-84e9-3b24c69f8c98"
      },
      OPENAI_API_KEY: process.env.OPENAI_API_KEY,
      OPENAI_API_URL: process.env.OPENAI_API_URL,
    }
  }
};
