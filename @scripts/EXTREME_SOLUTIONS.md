# Extreme Solutions for JWT Hook Issues

If you've tried the standard solutions and are still facing the error about null claims, here are some extreme approaches to try:

## 1. Ultra Simple Hooks

We've created the simplest possible hooks in both schemas with no dependencies or error handling:

```sql
-- Public schema version
CREATE OR REPLACE FUNCTION public.ultra_simple_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT '{"role": "authenticated"}'::jsonb;
$$;

-- Auth schema version
CREATE OR REPLACE FUNCTION auth.ultra_simple_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT '{"role": "authenticated"}'::jsonb;
$$;
```

Try both of these in the dashboard:
- First with schema: `public`, function: `ultra_simple_hook`
- Then with schema: `auth`, function: `ultra_simple_hook`

## 2. Auth Schema Hook

We've created a JWT hook in the auth schema to match your working SMS hook:

```sql
CREATE OR REPLACE FUNCTION auth.jwt_claims_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
...
```

Update the dashboard to use:
- Schema: `auth`
- Function: `jwt_claims_hook`

## 3. HTTP Hook Alternative

If all PostgreSQL function hooks fail, try using an HTTP hook:

1. Run the included Express server:
   ```
   npm install express
   node @scripts/http_hook_server.js
   ```

2. Use ngrok to create a public URL:
   ```
   ngrok http 3333
   ```

3. Configure the JWT hook in the dashboard:
   - Hook type: HTTPS
   - URL: your-ngrok-url/jwt-hook

## 4. Specific Troubleshooting Tests

If you're still facing issues, these are very specific tests to try:

1. **Test calling the hook function directly with the supabase_auth_admin role:**
   ```sql
   SET ROLE supabase_auth_admin;
   SELECT public.ultra_simple_hook('{}'::jsonb);
   RESET ROLE;
   ```

2. **Check if there are errors when accessing the schema:**
   ```sql
   SET ROLE supabase_auth_admin;
   SHOW search_path;
   RESET ROLE;
   ```

3. **Create a webhook to capture the actual payload sent by Supabase Auth:**
   - Use https://webhook.site to create a temporary endpoint
   - Configure the JWT hook to use this endpoint
   - Check what payload Supabase actually sends

## 5. Contact Supabase Support

If none of the solutions work, this might be a specific issue with your Supabase project. Consider:

1. Creating a support ticket with Supabase
2. Providing all the error logs and details of what you've tried
3. Ask if there are known issues with JWT claim hooks in your project's region or configuration

## Final Note

Remember to check the logs after each attempt by running:

```sql
SELECT * FROM public.logs 
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC LIMIT 10;
``` 