-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS auth.jwt_hook_final();

-- Create the function
CREATE OR REPLACE FUNCTION auth.jwt_hook_final(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_id text;
    result jsonb;
BEGIN
    -- Insert a log entry for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_called', jsonb_build_object('event', event));
    
    -- Get the user_id from the event
    user_id := event->>'user_id';
    
    -- Log the user_id for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_user_id', jsonb_build_object('user_id', user_id));
    
    -- Create a simple response with role and app_metadata
    result := jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object('user_role', 'customer')
    );
    
    -- Log the result for debugging
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_result', result);
    
    RETURN result;
EXCEPTION WHEN OTHERS THEN
    -- Log any errors
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_error', jsonb_build_object('error', SQLERRM, 'event', event));
    
    -- Return a default result even if there's an error
    RETURN jsonb_build_object('role', 'authenticated');
END;
$$;

-- Grant execution permission to the supabase_auth_admin role
GRANT EXECUTE ON FUNCTION auth.jwt_hook_final(jsonb) TO supabase_auth_admin;

-- Test the function with an empty object
DO $$
BEGIN
    PERFORM auth.jwt_hook_final('{"user_id": "test-user-id"}'::jsonb);
END;
$$; 