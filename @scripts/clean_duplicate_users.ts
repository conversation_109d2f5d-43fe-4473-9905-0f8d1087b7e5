/**
 * Utility script to clean up duplicate user accounts in Supabase
 * 
 * This script helps resolve issues where the same phone number is used by multiple profiles
 * with different roles (e.g., both customer and partner).
 * 
 * Usage:
 * 1. Make sure you have the Supabase CLI installed and authenticated
 * 2. Run: npx ts-node @scripts/clean_duplicate_users.ts --phone=************
 * 
 * CAUTION: This script performs destructive operations. Always back up your database first.
 */

import { createClient } from '@supabase/supabase-js';
import { program } from 'commander';

// Parse command-line arguments
program
  .option('--phone <phone>', 'Phone number to check for duplicates')
  .option('--keep-role <role>', 'Which role type to keep (customer, partner, driver, admin)', 'partner')
  .option('--dry-run', 'Show what would be deleted without actually deleting', false)
  .parse(process.argv);

const options = program.opts();

// Connection details - replace with your own or use env vars
const supabaseUrl = process.env.SUPABASE_URL || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY; // Use service key for admin operations

if (!supabaseKey) {
  console.error('ERROR: Missing SUPABASE_KEY environment variable');
  console.error('You must set a service role key to use this script');
  console.error('Example: SUPABASE_KEY=your_key npx ts-node @scripts/clean_duplicate_users.ts --phone=************');
  process.exit(1);
}

if (!options.phone) {
  console.error('ERROR: Phone number is required');
  console.error('Example: npx ts-node @scripts/clean_duplicate_users.ts --phone=************');
  process.exit(1);
}

// Create Supabase client with admin privileges
const supabase = createClient(supabaseUrl, supabaseKey);

async function findDuplicateProfiles(phoneNumber: string) {
  const { data, error } = await supabase
    .from('profiles')
    .select('id, user_type, email, phone, full_name, created_at')
    .eq('phone', phoneNumber);
  
  if (error) {
    console.error('Error fetching profiles:', error);
    return null;
  }
  
  return data;
}

async function cleanupUser(userId: string, dryRun: boolean) {
  if (dryRun) {
    console.log(`[DRY RUN] Would delete user ${userId} and associated data`);
    return true;
  }
  
  console.log(`Cleaning up user ${userId}...`);
  
  try {
    // First, try to delete from user_roles (auth schema)
    try {
      const { error: authRoleError } = await supabase.rpc('admin_delete_auth_user_role', { 
        user_id: userId 
      });
      if (authRoleError) {
        console.warn(`Warning: Couldn't delete from auth.user_roles:`, authRoleError);
      } else {
        console.log(`- Deleted from auth.user_roles`);
      }
    } catch (e) {
      console.warn(`Warning: Error calling admin_delete_auth_user_role:`, e);
    }
    
    // Try to delete from public.user_roles
    try {
      const { error: publicRoleError } = await supabase
        .from('user_roles')
        .delete()
        .eq('id', userId);
      
      if (publicRoleError) {
        console.warn(`Warning: Couldn't delete from public.user_roles:`, publicRoleError);
      } else {
        console.log(`- Deleted from public.user_roles`);
      }
    } catch (e) {
      console.warn(`Warning: Error deleting from public.user_roles:`, e);
    }
    
    // Clean up profile
    const { error: profileError } = await supabase
      .from('profiles')
      .delete()
      .eq('id', userId);
    
    if (profileError) {
      console.error(`Error deleting profile:`, profileError);
      return false;
    }
    console.log(`- Deleted from public.profiles`);
    
    // Delete the auth user
    try {
      const { error: authUserError } = await supabase.rpc('admin_delete_user', { 
        user_id: userId 
      });
      
      if (authUserError) {
        console.error(`Error deleting auth user:`, authUserError);
        return false;
      }
      console.log(`- Deleted from auth.users`);
    } catch (e) {
      console.error(`Error calling admin_delete_user:`, e);
      return false;
    }
    
    return true;
  } catch (e) {
    console.error(`Unexpected error during cleanup:`, e);
    return false;
  }
}

async function main() {
  console.log(`Checking for duplicate profiles with phone: ${options.phone}`);
  
  const profiles = await findDuplicateProfiles(options.phone);
  
  if (!profiles || profiles.length === 0) {
    console.log('No profiles found with this phone number');
    return;
  }
  
  if (profiles.length === 1) {
    console.log('Only one profile found with this phone number:');
    console.log(profiles[0]);
    return;
  }
  
  console.log(`Found ${profiles.length} profiles with this phone number:`);
  profiles.forEach((profile, index) => {
    console.log(`\n[${index + 1}] ID: ${profile.id}`);
    console.log(`    User Type: ${profile.user_type}`);
    console.log(`    Name: ${profile.full_name || 'Not set'}`);
    console.log(`    Email: ${profile.email || 'Not set'}`);
    console.log(`    Created: ${profile.created_at}`);
  });
  
  // Determine which profiles to keep and which to delete
  const keepRole = options.keepRole;
  const toKeep = profiles.find(p => p.user_type === keepRole);
  
  if (!toKeep) {
    console.error(`Error: No profile found with role '${keepRole}'`);
    console.log('Available roles:', profiles.map(p => p.user_type).join(', '));
    return;
  }
  
  const toDelete = profiles.filter(p => p.id !== toKeep.id);
  
  console.log(`\nWill keep profile with role '${keepRole}':`);
  console.log(`ID: ${toKeep.id}`);
  console.log(`Name: ${toKeep.full_name || 'Not set'}`);
  
  console.log(`\nProfiles to be deleted (${toDelete.length}):`);
  toDelete.forEach((profile, index) => {
    console.log(`[${index + 1}] ID: ${profile.id} (${profile.user_type})`);
  });
  
  if (options.dryRun) {
    console.log('\nDRY RUN: No changes will be made');
  } else {
    console.log('\nWARNING: This will permanently delete user data');
    console.log('Press Ctrl+C to cancel (waiting 5 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  // Perform the cleanup
  let success = true;
  for (const profile of toDelete) {
    const result = await cleanupUser(profile.id, options.dryRun);
    if (!result) {
      success = false;
    }
  }
  
  if (success) {
    console.log('\nCleanup completed successfully!');
  } else {
    console.log('\nCleanup completed with some errors. Check the logs above.');
  }
}

// Execute the main function
main().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
}); 