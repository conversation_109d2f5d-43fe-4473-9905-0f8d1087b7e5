-- FINAL ATTEMPT: Absolute minimum function with extensive debugging

-- First make sure we have the logs table
CREATE TABLE IF NOT EXISTS public.debug_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ DEFAULT now(),
    source TEXT,
    message TEXT,
    data JSONB
);

-- Create a logging function
CREATE OR REPLACE FUNCTION public.log_debug(source TEXT, message TEXT, data JSONB DEFAULT NULL)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.debug_logs (source, message, data)
    VALUES (source, message, data);
EXCEPTION WHEN OTHERS THEN
    -- Do nothing if logging fails
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant access to the logging function
GRANT EXECUTE ON FUNCTION public.log_debug TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.log_debug TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_debug TO anon;
GRANT EXECUTE ON FUNCTION public.log_debug TO service_role;

-- Drop all previous functions to avoid confusion
DROP FUNCTION IF EXISTS public.custom_access_token_hook;
DROP FUNCTION IF EXISTS public.jwt_claims_hook;
DROP FUNCTION IF EXISTS public.jwt_claims_hook_v2;
DROP FUNCTION IF EXISTS public.ultra_simple_hook;
DROP FUNCTION IF EXISTS auth.custom_access_token_hook;
DROP FUNCTION IF EXISTS auth.jwt_claims_hook;
DROP FUNCTION IF EXISTS auth.ultra_simple_hook;

-- Create the absolute simplest function in auth schema
CREATE OR REPLACE FUNCTION auth.minimal_jwt_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Log that we got called
    PERFORM public.log_debug('jwt_hook', 'Hook called', event);
    
    -- Return the simplest possible valid object
    RETURN '{"role": "authenticated"}'::jsonb;
EXCEPTION WHEN OTHERS THEN
    -- Log any errors
    PERFORM public.log_debug('jwt_hook_error', SQLERRM, event);
    
    -- Still try to return a valid object
    RETURN '{"role": "authenticated"}'::jsonb;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION auth.minimal_jwt_hook TO supabase_auth_admin;

-- Test the function
DO $$
BEGIN
    PERFORM public.log_debug('test', 'Testing hook function');
    
    -- Test with explicit call
    DECLARE
        result jsonb;
    BEGIN
        SELECT auth.minimal_jwt_hook('{"test":true}'::jsonb) INTO result;
        PERFORM public.log_debug('test_result', 'Hook returned', result);
    EXCEPTION WHEN OTHERS THEN
        PERFORM public.log_debug('test_error', 'Hook call failed: ' || SQLERRM);
    END;
END;
$$;

-- Try to directly execute as supabase_auth_admin
DO $$
BEGIN
    BEGIN
        EXECUTE 'SET ROLE supabase_auth_admin';
        PERFORM public.log_debug('auth_test', 'Set role to supabase_auth_admin');
        
        DECLARE
            result jsonb;
        BEGIN
            SELECT auth.minimal_jwt_hook('{"test":true}'::jsonb) INTO result;
            PERFORM public.log_debug('auth_test_result', 'Hook returned as supabase_auth_admin', result);
        EXCEPTION WHEN OTHERS THEN
            PERFORM public.log_debug('auth_test_error', 'Hook call failed as supabase_auth_admin: ' || SQLERRM);
        END;
    EXCEPTION WHEN OTHERS THEN
        PERFORM public.log_debug('auth_test_error', 'Could not set role: ' || SQLERRM);
    END;
    
    -- Reset role
    EXECUTE 'RESET ROLE';
END;
$$;

-- Show test results
SELECT * FROM public.debug_logs ORDER BY timestamp DESC LIMIT 10;

-- Instructions:
-- 1. Run this script
-- 2. In Supabase Dashboard → Auth → Hooks, configure JWT Claims hook:
--    - Hook type: Postgres
--    - Schema: auth
--    - Function: minimal_jwt_hook
-- 3. Test authentication
-- 4. Then check debug logs with:
--    SELECT * FROM public.debug_logs ORDER BY timestamp DESC LIMIT 20; 