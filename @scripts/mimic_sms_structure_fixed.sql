-- Create a JWT hook that mimics the exact structure of the working SMS hook

-- First, drop any existing function
DROP FUNCTION IF EXISTS auth.mimic_sms_jwt_hook;

-- Create new function with identical structure to the SMS hook
CREATE OR REPLACE FUNCTION auth.mimic_sms_jwt_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id UUID;
  result jsonb;
BEGIN
  -- Extract user ID with null checks, just like SMS hook extracts phone
  user_id := (event->> 'sub')::uuid;
  
  IF user_id IS NULL THEN
    -- Log error and return a minimal object, similar to SMS hook
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_error', jsonb_build_object('error', 'User ID is null', 'event', event));
    
    -- Return an object in the same pattern as SMS hook
    RETURN jsonb_build_object(
      'role', 'authenticated'
    );
  END IF;
  
  -- Log the event just like SMS hook does
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_hook_called', jsonb_build_object('user_id', user_id));
  
  -- This section replaces the HTTP call in the SMS hook
  -- Just create a simple result
  result := jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
  
  -- Log the result like SMS hook logs the response
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_hook_result', result);
  
  -- Return success just like SMS hook
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error exactly like SMS hook
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_exception', jsonb_build_object('error', SQLERRM));
    
    -- Return a minimal object in case of error
    RETURN jsonb_build_object(
      'role', 'authenticated'
    );
END;
$$;

-- Grant execution permission
GRANT EXECUTE ON FUNCTION auth.mimic_sms_jwt_hook TO supabase_auth_admin;

-- Test the function
DO $$
BEGIN
  -- Use jsonb_build_object for the test message
  INSERT INTO public.logs (event_type, details)
  VALUES ('test', jsonb_build_object('message', 'Testing mimic_sms_jwt_hook'));
  
  DECLARE
    test_result jsonb;
  BEGIN
    SELECT auth.mimic_sms_jwt_hook('{"sub":"00000000-0000-0000-0000-000000000000"}'::jsonb) INTO test_result;
    INSERT INTO public.logs (event_type, details)
    VALUES ('test_result', test_result);
  EXCEPTION
    WHEN OTHERS THEN
      INSERT INTO public.logs (event_type, details)
      VALUES ('test_error', jsonb_build_object('error', SQLERRM));
  END;
END;
$$;

-- Instructions:
-- 1. Run this script
-- 2. In Supabase Dashboard, update JWT hook:
--    - Schema: auth
--    - Function: mimic_sms_jwt_hook
-- 3. Test authentication
-- 4. Check logs:
--    SELECT * FROM public.logs ORDER BY created_at DESC LIMIT 10; 