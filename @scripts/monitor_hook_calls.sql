-- Create a monitoring system for hook calls to trace authentication process

-- First create a dedicated table to monitor hook calls
CREATE TABLE IF NOT EXISTS public.hook_monitoring (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ DEFAULT now(),
    hook_name TEXT,
    input_data JSONB,
    output_data JSONB,
    execution_time_ms DOUBLE PRECISION,
    caller_info TEXT,
    success BOOLEAN
);

-- Now modify our hook to include monitoring
DROP FUNCTION IF EXISTS public.custom_access_token_hook;

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    start_time TIMESTAMPTZ;
    end_time TIMESTAMPTZ;
    execution_time DOUBLE PRECISION;
    result jsonb;
    success BOOLEAN := TRUE;
    caller TEXT;
BEGIN
    -- Record start time
    start_time := clock_timestamp();
    
    -- Get information about caller
    caller := current_user || ' via ' || session_user;
    
    -- Create the claims object (our core functionality)
    result := jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
    
    -- Record end time
    end_time := clock_timestamp();
    execution_time := extract(epoch FROM (end_time - start_time)) * 1000;
    
    -- Log this call to our monitoring table
    INSERT INTO public.hook_monitoring (
        hook_name, 
        input_data, 
        output_data, 
        execution_time_ms,
        caller_info,
        success
    ) VALUES (
        'custom_access_token_hook',
        event,
        result,
        execution_time,
        caller,
        success
    );
    
    -- Return the claims
    RETURN result;
EXCEPTION WHEN OTHERS THEN
    -- Log error information
    INSERT INTO public.hook_monitoring (
        hook_name, 
        input_data, 
        output_data, 
        execution_time_ms,
        caller_info,
        success
    ) VALUES (
        'custom_access_token_hook',
        event,
        jsonb_build_object('error', SQLERRM),
        NULL,
        caller,
        FALSE
    );
    
    -- Always return a valid claims object even on error
    RETURN jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
END;
$$;

-- Set all required permissions
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;

-- Ensure logging table permissions
GRANT USAGE ON SEQUENCE public.hook_monitoring_id_seq TO supabase_auth_admin;
GRANT INSERT ON TABLE public.hook_monitoring TO supabase_auth_admin;

-- Test the function directly
SELECT public.custom_access_token_hook('{"test": true}'::jsonb);

-- Check the monitoring table
SELECT * FROM public.hook_monitoring ORDER BY timestamp DESC LIMIT 5; 