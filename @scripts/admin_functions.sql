-- Admin functions for user management
-- These functions must be run as a superuser or service role

-- Function to delete a user from auth.user_roles
CREATE OR REPLACE FUNCTION admin_delete_auth_user_role(user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM auth.user_roles WHERE id = user_id;
END;
$$;

-- Function to safely delete a user and all related data
CREATE OR REPLACE FUNCTION admin_delete_user(user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Delete from related auth tables first to handle foreign key constraints
  -- Sessions
  DELETE FROM auth.sessions WHERE user_id = user_id;
  
  -- Identities
  DELETE FROM auth.identities WHERE user_id = user_id;
  
  -- MFA factors and claims
  DELETE FROM auth.mfa_factors WHERE user_id = user_id;
  
  -- Refresh tokens
  DELETE FROM auth.refresh_tokens WHERE user_id = user_id::text;
  
  -- One time tokens
  DELETE FROM auth.one_time_tokens WHERE user_id = user_id;
  
  -- Finally delete the user
  DELETE FROM auth.users WHERE id = user_id;
END;
$$;

-- Function to check for duplicate phone numbers in profiles
CREATE OR REPLACE FUNCTION admin_check_duplicate_phones(min_profiles INT DEFAULT 2)
RETURNS TABLE (
  phone TEXT,
  count BIGINT,
  roles TEXT[]
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    phone,
    COUNT(*) as count,
    ARRAY_AGG(DISTINCT user_type) as roles
  FROM 
    public.profiles
  WHERE 
    phone IS NOT NULL
  GROUP BY 
    phone
  HAVING 
    COUNT(*) >= min_profiles
  ORDER BY 
    COUNT(*) DESC;
$$;

-- Function to find profiles by phone number
CREATE OR REPLACE FUNCTION admin_find_profiles_by_phone(phone_number TEXT)
RETURNS TABLE (
  id UUID,
  user_type TEXT,
  email TEXT,
  full_name TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    id,
    user_type,
    email,
    full_name,
    created_at
  FROM 
    public.profiles
  WHERE 
    phone = phone_number
  ORDER BY 
    created_at DESC;
$$;

-- Add additional admin functions for user account merging

-- Function to get user roles for multiple users
CREATE OR REPLACE FUNCTION admin_get_user_roles(user_ids UUID[])
RETURNS TABLE (
  id UUID,
  role TEXT
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    id,
    role
  FROM 
    auth.user_roles
  WHERE 
    id = ANY(user_ids);
$$;

-- Function to copy a user role from one user to another
CREATE OR REPLACE FUNCTION admin_copy_user_role(from_user_id UUID, to_user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  role_value TEXT;
BEGIN
  -- Get the role from the source user
  SELECT role INTO role_value FROM auth.user_roles WHERE id = from_user_id;
  
  IF role_value IS NULL THEN
    RETURN;
  END IF;
  
  -- Check if destination user already has a role
  IF EXISTS (SELECT 1 FROM auth.user_roles WHERE id = to_user_id) THEN
    -- Update existing role
    UPDATE auth.user_roles SET role = role_value WHERE id = to_user_id;
  ELSE
    -- Insert new role
    INSERT INTO auth.user_roles (id, role) VALUES (to_user_id, role_value);
  END IF;
END;
$$;

-- Function to find all users with multiple auth methods (email, phone, oauth)
CREATE OR REPLACE FUNCTION admin_find_users_with_multiple_auth_methods()
RETURNS TABLE (
  email TEXT,
  user_ids UUID[],
  auth_methods TEXT[],
  provider_count INT
)
LANGUAGE sql
SECURITY DEFINER
AS $$
WITH user_providers AS (
  -- Direct email/phone from auth.users
  SELECT 
    u.id as user_id, 
    u.email,
    CASE WHEN u.email IS NOT NULL THEN 'email' ELSE NULL END as provider
  FROM 
    auth.users u
  WHERE 
    u.email IS NOT NULL
  
  UNION ALL
  
  SELECT 
    u.id as user_id, 
    u.email,
    CASE WHEN u.phone IS NOT NULL THEN 'phone' ELSE NULL END as provider
  FROM 
    auth.users u
  WHERE 
    u.phone IS NOT NULL
  
  UNION ALL
  
  -- OAuth providers from identities
  SELECT 
    i.user_id,
    i.email,
    i.provider
  FROM 
    auth.identities i
  WHERE 
    i.email IS NOT NULL
)
SELECT 
  up.email,
  array_agg(DISTINCT up.user_id) as user_ids,
  array_agg(DISTINCT up.provider) as auth_methods,
  COUNT(DISTINCT up.user_id) as provider_count
FROM 
  user_providers up
WHERE 
  up.email IS NOT NULL
GROUP BY 
  up.email
HAVING 
  COUNT(DISTINCT up.user_id) > 1
ORDER BY 
  COUNT(DISTINCT up.user_id) DESC,
  up.email;
$$;

-- Function to find profiles that have duplicate phone numbers
CREATE OR REPLACE FUNCTION admin_find_profiles_with_duplicate_phones()
RETURNS TABLE (
  phone TEXT,
  profile_ids UUID[],
  user_types TEXT[],
  profile_count INT
)
LANGUAGE sql
SECURITY DEFINER
AS $$
SELECT 
  p.phone,
  array_agg(p.id) as profile_ids,
  array_agg(p.user_type) as user_types,
  COUNT(*) as profile_count
FROM 
  public.profiles p
WHERE 
  p.phone IS NOT NULL
GROUP BY 
  p.phone
HAVING 
  COUNT(*) > 1
ORDER BY 
  COUNT(*) DESC;
$$; 