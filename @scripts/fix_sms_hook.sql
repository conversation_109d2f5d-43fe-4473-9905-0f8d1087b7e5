-- Enable the HTTP extension if not already enabled
CREATE EXTENSION IF NOT EXISTS http WITH SCHEMA extensions;
GRANT USAGE ON SCHEMA extensions TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA extensions TO postgres, anon, authenticated, service_role;

-- Create a schema for auth hooks if it doesn't exist
CREATE SCHEMA IF NOT EXISTS auth_hooks;

-- Create a logs table for debugging if it doesn't exist
CREATE TABLE IF NOT EXISTS public.logs (
  id SERIAL PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  event_type TEXT NOT NULL,
  details JSONB DEFAULT '{}'::jsonb
);

-- Add RLS to logs table
ALTER TABLE public.logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view logs
DROP POLICY IF EXISTS "admins_can_view_logs" ON public.logs;
CREATE POLICY "admins_can_view_logs"
ON public.logs FOR SELECT
USING (auth.uid() IN (SELECT id FROM public.profiles WHERE user_type = 'admin'));

-- Drop existing functions to avoid conflicts
DROP FUNCTION IF EXISTS auth_hooks.send_sms;
DROP FUNCTION IF EXISTS auth_hooks.send_sms_hook;

-- Create a function to send SMS via Authentica
CREATE OR REPLACE FUNCTION auth_hooks.send_sms(
  phone TEXT,
  message TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  response JSONB;
  api_key TEXT := '$2y$10$TK6W./k9tr/UFRQ0ue3igeYyd9UCeLXo0wukQBB2ojOIQ05atC.0i'; -- Replace with your actual API key
 
BEGIN
  -- Log the SMS request for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('sms_request', jsonb_build_object('phone', phone, 'message_length', length(message)));
  
  -- Make HTTP request to Authentica API
  SELECT
    content::jsonb INTO response
  FROM
    extensions.http((
      'POST',
      'https://api.authentica.sa/api/v1/send-sms',
      ARRAY[
        extensions.http_header('X-Authorization', api_key),
        extensions.http_header('Accept', 'application/json'),
        extensions.http_header('Content-Type', 'application/json')
      ],
      'application/json',
      jsonb_build_object(
        'phone', phone,
        'message', message
      )::text
    ));
  
  -- Log the response for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('sms_response', response);
  
  -- Check if the request was successful
  IF response->>'success' = 'true' THEN
    RETURN TRUE;
  ELSE
    RAISE WARNING 'Failed to send SMS: %', response;
    RETURN FALSE;
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error
    INSERT INTO public.logs (event_type, details)
    VALUES ('sms_error', jsonb_build_object('error', SQLERRM));
    
    RAISE WARNING 'Error sending SMS: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the SMS auth hook function
CREATE OR REPLACE FUNCTION auth.send_sms_hook(event JSONB)
RETURNS JSONB AS $$
DECLARE
  phone TEXT := event->'user'->>'phone';
  otp TEXT := event->'sms'->>'otp';
  message TEXT;
BEGIN
  -- Log the hook event for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('sms_hook_called', jsonb_build_object('phone', phone, 'event', event));
  
  -- Create the message with the OTP
  message := 'Your verification code for Dumpster On Demand is: ' || otp;
  
  -- Send the SMS using the Authentica API
  IF auth_hooks.send_sms(phone, message) THEN
    -- Return success response
    RETURN jsonb_build_object(
      'success', true
    );
  ELSE
    -- Return error response
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Failed to send SMS'
    );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth_hooks.send_sms TO service_role;
GRANT EXECUTE ON FUNCTION auth.send_sms_hook TO service_role;
GRANT EXECUTE ON FUNCTION auth_hooks.send_sms TO postgres;
GRANT EXECUTE ON FUNCTION auth.send_sms_hook TO postgres;
GRANT EXECUTE ON FUNCTION auth_hooks.send_sms TO anon;
GRANT EXECUTE ON FUNCTION auth.send_sms_hook TO anon;
GRANT EXECUTE ON FUNCTION auth_hooks.send_sms TO authenticated;
GRANT EXECUTE ON FUNCTION auth.send_sms_hook TO authenticated;

-- Make sure the auth_hooks schema is accessible
GRANT USAGE ON SCHEMA auth_hooks TO service_role, postgres, anon, authenticated;

-- Comment explaining how to configure the hook in Supabase
COMMENT ON FUNCTION auth.send_sms_hook IS 
'SMS authentication hook for Supabase Auth. 
Configure this in the Supabase Dashboard:
1. Go to Authentication > Hooks
2. For the "Send SMS" hook, enter: auth.send_sms_hook
3. Save the configuration';

-- Insert a test log entry to verify the logs table is working
INSERT INTO public.logs (event_type, details)
VALUES ('sms_hook_setup', jsonb_build_object('status', 'completed', 'timestamp', now()));
