-- Complete fix for JWT claims functions
-- This script fixes all related functions to ensure proper JW<PERSON> claims

-- 1. First, the base jwt_claim function
CREATE OR REPLACE FUNCTION auth.jwt_claim(request jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id UUID;
  user_role TEXT;
  claims_payload JSONB;
BEGIN
  -- Extract user_id from request safely
  BEGIN
    user_id := (request->>'sub')::UUID;
  EXCEPTION WHEN OTHERS THEN
    -- Return default claims for non-user requests
    RETURN jsonb_build_object(
      'role', 'anon',
      'app_metadata', jsonb_build_object(
        'user_role', 'anon'
      )
    );
  END;

  -- Get the user's role from the profiles table if it exists
  BEGIN
    SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  EXCEPTION WHEN OTHERS THEN
    -- Ignore error and continue with default
    NULL;
  END;

  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;

  -- Build the claims payload
  claims_payload := jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );

  -- Return the claims
  RETURN claims_payload;
EXCEPTION WHEN OTHERS THEN
  -- Final fallback for any uncaught errors
  RETURN jsonb_build_object(
    'role', 'customer',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
END;
$$;

-- 2. The claim_safe wrapper function
CREATE OR REPLACE FUNCTION auth.claim_safe(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  claims JSONB;
BEGIN
  -- Ensure event is never null
  IF event IS NULL THEN
    event := '{}'::JSONB;
  END IF;

  -- Call the auth.jwt_claim function with proper error handling
  BEGIN
    claims := auth.jwt_claim(event);
    IF claims IS NULL THEN
      -- Fallback to default claims if still null
      claims := jsonb_build_object(
        'role', 'customer',
        'app_metadata', jsonb_build_object(
          'user_role', 'customer'
        )
      );
    END IF;
  EXCEPTION WHEN OTHERS THEN
    -- Handle any unexpected error with safe defaults
    claims := jsonb_build_object(
      'role', 'customer',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer'
      )
    );
  END;

  RETURN claims;
END;
$$;

-- 3. The main JWT function that's actually called
CREATE OR REPLACE FUNCTION auth.jwt()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  custom_claims JSONB;
BEGIN
  -- Get the base JWT claim safely
  BEGIN
    result := COALESCE(current_setting('request.jwt.claim', true)::JSONB, '{}'::JSONB);
  EXCEPTION WHEN OTHERS THEN
    result := '{}'::JSONB;
  END;

  -- Get custom claims using our safe function
  custom_claims := auth.claim_safe(result);

  -- Return merged result
  RETURN result || custom_claims;
EXCEPTION
  WHEN OTHERS THEN
    -- Always return a minimal valid JWT with default claims
    RETURN jsonb_build_object(
      'role', 'customer',
      'app_metadata', jsonb_build_object(
        'user_role', 'customer'
      )
    );
END;
$$;

-- Grant necessary permissions to all functions
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO service_role;

GRANT EXECUTE ON FUNCTION auth.claim_safe TO authenticated;
GRANT EXECUTE ON FUNCTION auth.claim_safe TO anon;
GRANT EXECUTE ON FUNCTION auth.claim_safe TO service_role;

GRANT EXECUTE ON FUNCTION auth.jwt TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt TO service_role;

-- Insert a log entry to track this fix
INSERT INTO public.logs (event_type, details)
VALUES ('jwt_claims_complete_fix', jsonb_build_object('status', 'applied', 'timestamp', now())); 