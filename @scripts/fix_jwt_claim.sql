-- Creating SQL file for JWT claim fix

-- Updated jwt_claim function with robust error handling
CREATE OR REPLACE FUNCTION auth.jwt_claim(request jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id UUID;
  user_role TEXT;
  claims_payload JSONB;
BEGIN
  -- Extract user_id from request safely
  BEGIN
    user_id := (request->>'sub')::UUID;
  EXCEPTION WHEN OTHERS THEN
    -- Return default claims for non-user requests
    RETURN jsonb_build_object(
      'role', 'anon',
      'app_metadata', jsonb_build_object(
        'user_role', 'anon'
      )
    );
  END;

  -- Get the user's role from the profiles table if it exists
  BEGIN
    SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  EXCEPTION WHEN OTHERS THEN
    -- Ignore error and continue with default
    NULL;
  END;

  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;

  -- Build the claims payload
  claims_payload := jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );

  -- Return the claims
  RETURN claims_payload;
EXCEPTION WHEN OTHERS THEN
  -- Final fallback for any uncaught errors
  -- Always return a valid object even on error
  RETURN jsonb_build_object(
    'role', 'customer',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO service_role;

-- Insert a log entry to track this fix
INSERT INTO public.logs (event_type, details)
VALUES ('jwt_claim_fix', jsonb_build_object('status', 'applied', 'timestamp', now()));
