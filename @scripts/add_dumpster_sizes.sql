-- First, let's insert new dumpster types if they don't exist
INSERT INTO public.dumpster_types (
    id, name_en, description_en, capacity, max_weight, 
    suitable_waste_types, image_url
)
VALUES 
    (
        'dt-10yd-001', '10 Yard Container', 'Perfect for small projects and residential cleanup',
        10, 2000, ARRAY['construction', 'residential', 'yard_waste'],
        'assets/images/dumpsters/dumpters_10YD.svg'
    ),
    (
        'dt-15yd-001', '15 Yard Container', 'Ideal for medium-sized renovation projects',
        15, 3000, ARRAY['construction', 'residential', 'renovation'],
        'assets/images/dumpsters/dumpters_15YD.svg'
    ),
    (
        'dt-20yd-001', '20 Yard Container', 'Great for large home renovations and construction',
        20, 4000, ARRAY['construction', 'commercial', 'heavy_debris'],
        'assets/images/dumpsters/dumpters_20YD.svg'
    ),
    (
        'dt-30yd-001', '30 Yard Container', 'Suitable for extra large construction projects',
        30, 6000, ARRAY['construction', 'commercial', 'industrial'],
        'assets/images/dumpsters/dumpters_30YD.svg'
    ),
    (
        'dt-40yd-001', '40 Yard Container', 'Ideal for very large commercial projects',
        40, 8000, ARRAY['construction', 'commercial', 'industrial'],
        'assets/images/dumpsters/dumpters_40YD.svg'
    )
ON CONFLICT DO NOTHING;

-- Insert corresponding dumpster sizes
INSERT INTO public.dumpster_sizes (
    id, dumpster_type_id, name, volume_cubic_yards,
    max_weight_pounds, length, width, height, description
)
VALUES 
    (
        'ds-10yd-001', 'dt-10yd-001', '10 Yard', 10,
        2000, 12, 8, 4, 'Standard 10 cubic yard container'
    ),
    (
        'ds-15yd-001', 'dt-15yd-001', '15 Yard', 15,
        3000, 15, 8, 4.5, 'Standard 15 cubic yard container'
    ),
    (
        'ds-20yd-001', 'dt-20yd-001', '20 Yard', 20,
        4000, 16, 8, 5, 'Standard 20 cubic yard container'
    ),
    (
        'ds-30yd-001', 'dt-30yd-001', '30 Yard', 30,
        6000, 20, 8, 6, 'Standard 30 cubic yard container'
    ),
    (
        'ds-40yd-001', 'dt-40yd-001', '40 Yard', 40,
        8000, 22, 8, 8, 'Standard 40 cubic yard container'
    )
ON CONFLICT DO NOTHING;

-- Get the first partner's ID (assuming it exists)
WITH first_partner AS (
    SELECT id FROM public.partners WHERE status = 'active' LIMIT 1
)
-- Insert dumpsters for each size
INSERT INTO public.dumpsters (
    id, partner_id, dumpster_type_id, image_url,
    serial_number, status, price_per_load,
    name_en, description_en, rating, review_count
)
SELECT 
    gen_random_uuid(),
    (SELECT id FROM first_partner),
    dt.id,
    CASE 
        WHEN dt.capacity = 10 THEN 'assets/images/dumpsters/dumpters_10YD.svg'
        WHEN dt.capacity = 15 THEN 'assets/images/dumpsters/dumpters_15YD.svg'
        WHEN dt.capacity = 20 THEN 'assets/images/dumpsters/dumpters_20YD.svg'
        WHEN dt.capacity = 30 THEN 'assets/images/dumpsters/dumpters_30YD.svg'
        ELSE 'assets/images/dumpsters/dumpters_40YD.svg'
    END,
    'SN-' || SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8),
    'available',
    CASE 
        WHEN dt.capacity = 10 THEN 150.00
        WHEN dt.capacity = 15 THEN 200.00
        WHEN dt.capacity = 20 THEN 250.00
        WHEN dt.capacity = 30 THEN 350.00
        ELSE 450.00
    END,
    dt.name_en,
    dt.description_en,
    4.5,
    FLOOR(RANDOM() * (100-20+1) + 20)::integer
FROM 
    public.dumpster_types dt
WHERE 
    dt.id IN ('dt-10yd-001', 'dt-15yd-001', 'dt-20yd-001', 'dt-30yd-001', 'dt-40yd-001');

-- Create dumpster size options for each dumpster
INSERT INTO public.dumpster_size_options (
    dumpster_id,
    dumpster_size_id
)
SELECT 
    d.id,
    ds.id
FROM 
    public.dumpsters d
    JOIN public.dumpster_types dt ON d.dumpster_type_id = dt.id
    JOIN public.dumpster_sizes ds ON dt.id = ds.dumpster_type_id
WHERE 
    dt.id IN ('dt-10yd-001', 'dt-15yd-001', 'dt-20yd-001', 'dt-30yd-001', 'dt-40yd-001');

-- Add some pricing plans for each dumpster type
INSERT INTO public.pricing_plans (
    partner_id,
    dumpster_type_id,
    name,
    base_price,
    daily_rate,
    minimum_days,
    maximum_days,
    is_active
)
SELECT 
    (SELECT id FROM first_partner),
    dt.id,
    'Standard ' || dt.capacity || ' Yard Plan',
    CASE 
        WHEN dt.capacity = 10 THEN 150.00
        WHEN dt.capacity = 15 THEN 200.00
        WHEN dt.capacity = 20 THEN 250.00
        WHEN dt.capacity = 30 THEN 350.00
        ELSE 450.00
    END,
    CASE 
        WHEN dt.capacity = 10 THEN 50.00
        WHEN dt.capacity = 15 THEN 75.00
        WHEN dt.capacity = 20 THEN 100.00
        WHEN dt.capacity = 30 THEN 150.00
        ELSE 200.00
    END,
    3,
    30,
    true
FROM 
    public.dumpster_types dt
WHERE 
    dt.id IN ('dt-10yd-001', 'dt-15yd-001', 'dt-20yd-001', 'dt-30yd-001', 'dt-40yd-001');