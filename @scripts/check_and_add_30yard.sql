-- Create temporary table for first active partner
CREATE TEMP TABLE temp_first_partner AS
SELECT id FROM public.partners WHERE status = 'active' LIMIT 1;

-- Add the dumpster for the existing 30 yard type
INSERT INTO public.dumpsters (
    id, partner_id, dumpster_type_id, image_url,
    serial_number, status, price_per_load,
    name_en, description_en, rating, review_count
)
SELECT 
    gen_random_uuid(),
    (SELECT id FROM temp_first_partner),
    'a2b89e34-7ccc-4e84-c9d9-912c4f850354',  -- existing 30 yard type ID
    'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_30YD.png',
    'SN-' || SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8),
    'available',
    350.00,
    '30 Yard Container',
    'Standard 30 cubic yard container',
    4.5,
    FLOOR(RANDOM() * (100-20+1) + 20)::integer
WHERE NOT EXISTS (
    SELECT 1 FROM public.dumpsters d
    WHERE d.dumpster_type_id = 'a2b89e34-7ccc-4e84-c9d9-912c4f850354'
);

DROP TABLE temp_first_partner;

-- Verify the 30 yard dumpster was added
SELECT 
    d.id,
    d.name_en,
    dt.capacity,
    d.price_per_load,
    d.image_url
FROM public.dumpsters d
JOIN public.dumpster_types dt ON d.dumpster_type_id = dt.id
WHERE dt.capacity = 30
ORDER BY d.created_at DESC
LIMIT 1;
