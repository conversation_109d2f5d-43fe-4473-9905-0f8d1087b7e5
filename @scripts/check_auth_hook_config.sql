-- Check for Supabase auth hook configuration tables
-- This might help us find where hook configuration is stored

-- Check for tables that might contain hook configuration
SELECT 
    table_schema, 
    table_name
FROM 
    information_schema.tables
WHERE 
    table_name LIKE '%hook%' OR 
    table_name LIKE '%auth%config%' OR 
    table_name LIKE '%jwt%';

-- Check for functions that might be related to hook configuration
SELECT 
    proname, 
    pronamespace::regnamespace as schema
FROM 
    pg_proc
WHERE 
    proname LIKE '%hook%config%' OR 
    proname LIKE '%auth%config%';

-- Look for any settings in Supabase settings table if it exists
SELECT * FROM pg_tables WHERE tablename = 'supabase_settings';
SELECT * FROM pg_tables WHERE tablename LIKE '%setting%';

-- If the settings table exists, try to query it
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE tablename = 'settings') THEN
        EXECUTE 'SELECT * FROM settings WHERE key LIKE ''%hook%'' OR key LIKE ''%jwt%''';
    END IF;
END $$; 