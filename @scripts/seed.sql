-- Delete existing data to avoid conflicts
DELETE FROM dumpster_waste_types;
DELETE FROM dumpster_size_options;
DELETE FROM dumpster_features;
DELETE FROM waste_type_tags;
DELETE FROM dumpsters;
DELETE FROM dumpster_sizes;
DELETE FROM pricing_plans;
DELETE FROM dumpster_types;
DELETE FROM waste_types;
DELETE FROM waste_tags;
DELETE FROM partners;
DELETE FROM profiles WHERE id NOT IN (SELECT id FROM auth.users);

-- Insert a test admin profile if it doesn't exist
INSERT INTO profiles (id, email, full_name, user_type)
SELECT 
  id, 
  email, 
  COALESCE(raw_user_meta_data->>'full_name', email) as full_name,
  'admin' as user_type
FROM auth.users
WHERE id NOT IN (SELECT id FROM profiles)
ON CONFLICT (id) DO NOTHING;

-- Insert waste tags
INSERT INTO waste_tags (name, description) VALUES
  ('Recyclable', 'Materials that can be recycled'),
  ('Hazardous', 'Materials that require special handling'),
  ('Organic', 'Natural biodegradable materials'),
  ('Non-recyclable', 'Materials that cannot be recycled'),
  ('Heavy', 'Materials with significant weight'),
  ('Bulky', 'Large or oversized materials'),
  ('Liquid', 'Non-solid materials'),
  ('Solid', 'Solid materials');

-- Insert waste types
INSERT INTO waste_types (name, description, image_url) VALUES
  ('Mixed construction waste', 'General construction debris including wood, drywall, and other building materials', '/assets/images/waste-types/teaser-rubble.jpg'),
  ('Reclaimed wood', 'Used lumber, wooden pallets, and other wood materials that can be recycled', '/assets/images/waste-types/teaser-oldwood.jpg'),
  ('Mixed waste', 'General household and commercial waste of various types', '/assets/images/waste-types/teaser-files.jpg'),
  ('Construction waste', 'Specific construction materials including concrete, bricks, and tiles', '/assets/images/waste-types/teaser-rubble.jpg'),
  ('Soil excavation', 'Dirt, soil, and other excavated materials from construction sites', '/assets/images/waste-types/teaser-ground.jpg'),
  ('Green cuttings', 'Yard waste including grass clippings, branches, and other plant materials', '/assets/images/waste-types/teaser-greencut.jpg');

-- Insert waste type tags
WITH 
  waste_types_cte AS (SELECT id, name FROM waste_types),
  waste_tags_cte AS (SELECT id, name FROM waste_tags)
INSERT INTO waste_type_tags (waste_type_id, waste_tag_id)
SELECT wt.id, tg.id FROM waste_types_cte wt, waste_tags_cte tg
WHERE 
  (wt.name = 'Mixed construction waste' AND tg.name = 'Bulky') OR
  (wt.name = 'Mixed construction waste' AND tg.name = 'Solid') OR
  (wt.name = 'Reclaimed wood' AND tg.name = 'Recyclable') OR
  (wt.name = 'Reclaimed wood' AND tg.name = 'Bulky') OR
  (wt.name = 'Mixed waste' AND tg.name = 'Non-recyclable') OR
  (wt.name = 'Mixed waste' AND tg.name = 'Solid') OR
  (wt.name = 'Construction waste' AND tg.name = 'Heavy') OR
  (wt.name = 'Construction waste' AND tg.name = 'Bulky') OR
  (wt.name = 'Construction waste' AND tg.name = 'Solid') OR
  (wt.name = 'Soil excavation' AND tg.name = 'Heavy') OR
  (wt.name = 'Soil excavation' AND tg.name = 'Solid') OR
  (wt.name = 'Green cuttings' AND tg.name = 'Recyclable') OR
  (wt.name = 'Green cuttings' AND tg.name = 'Organic');

-- Insert dumpster types
INSERT INTO dumpster_types (name, description, capacity, max_weight, image_url, suitable_waste_types) VALUES
  ('Standard Roll-Off', 'Standard roll-off dumpster for general waste', 20, 4, '/assets/images/dumpsters/dumpters_20YD.svg', ARRAY['Mixed construction waste', 'Construction waste']),
  ('Heavy Duty', 'Reinforced dumpster for heavy materials', 30, 6, '/assets/images/dumpsters/dumpters_30YD.svg', ARRAY['Construction waste', 'Soil excavation']),
  ('Residential', 'Compact dumpster for residential use', 10, 2, '/assets/images/dumpsters/dumpters_10YD.svg', ARRAY['Mixed waste', 'Green cuttings']),
  ('Recycling', 'Specialized dumpster for recyclable materials', 20, 3, '/assets/images/dumpsters/dumpters_20YD.svg', ARRAY['Reclaimed wood']),
  ('Commercial', 'Large capacity dumpster for commercial projects', 40, 8, '/assets/images/dumpsters/dumpters_40YD.svg', ARRAY['Mixed construction waste', 'Construction waste', 'Soil excavation']),
  ('Multi-Purpose', 'Versatile dumpster for various waste types', 20, 4, '/assets/images/dumpsters/dumpters_20YD.svg', ARRAY['Mixed construction waste', 'Reclaimed wood', 'Mixed waste', 'Green cuttings']);

-- Insert dumpster sizes
INSERT INTO dumpster_sizes (name, volume_cubic_yards, max_weight_pounds, length, width, height, description, dumpster_type_id) 
SELECT 
  s.name, 
  s.volume, 
  s.weight, 
  s.length, 
  s.width, 
  s.height, 
  s.description,
  dt.id
FROM 
  (VALUES 
    ('10 Yard', 10, 2000, 12, 8, 3.5, 'Good for small renovation projects and yard cleanups'),
    ('20 Yard', 20, 4000, 16, 8, 4, 'Ideal for medium-sized projects and home renovations'),
    ('30 Yard', 30, 6000, 20, 8, 5, 'Perfect for large construction projects and major cleanouts'),
    ('40 Yard', 40, 8000, 22, 8, 7, 'Best for commercial projects and large-scale construction')
  ) AS s(name, volume, weight, length, width, height, description),
  dumpster_types dt
WHERE 
  (s.name = '10 Yard' AND dt.name IN ('Residential', 'Standard Roll-Off', 'Recycling', 'Multi-Purpose')) OR
  (s.name = '20 Yard' AND dt.name IN ('Standard Roll-Off', 'Heavy Duty', 'Residential', 'Recycling', 'Multi-Purpose')) OR
  (s.name = '30 Yard' AND dt.name IN ('Standard Roll-Off', 'Heavy Duty', 'Recycling', 'Commercial', 'Multi-Purpose')) OR
  (s.name = '40 Yard' AND dt.name IN ('Heavy Duty', 'Commercial', 'Multi-Purpose'));

-- Insert a test partner
INSERT INTO partners (profile_id, company_name, status, is_verified)
SELECT 
  p.id, 
  'Demo Waste Management', 
  'active', 
  true
FROM profiles p
WHERE p.user_type = 'admin'
LIMIT 1;

-- Insert dumpsters
INSERT INTO dumpsters (
  name, 
  description, 
  image_url, 
  price_per_day, 
  is_available, 
  rating, 
  review_count,
  partner_id,
  dumpster_type_id,
  serial_number,
  status,
  current_location
)
SELECT 
  d.name,
  d.description,
  d.image_url,
  d.price,
  d.available,
  d.rating,
  d.reviews,
  (SELECT id FROM partners LIMIT 1),
  dt.id,
  'SN-' || SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8),
  CASE WHEN d.available THEN 'available' ELSE 'maintenance' END,
  jsonb_build_object(
    'latitude', 25.761681 + (random() * 0.1), 
    'longitude', -80.191788 + (random() * 0.1)
  )
FROM 
  (VALUES 
    ('Standard Construction Dumpster', 'All-purpose dumpster for construction and renovation projects', '/assets/images/dumpsters/dumpters_20YD.svg', 75, true, 4.5, 120, 'Standard Roll-Off'),
    ('Heavy Duty Dumpster', 'Reinforced dumpster for heavy materials like concrete and soil', '/assets/images/dumpsters/dumpters_30YD.svg', 95, true, 4.7, 85, 'Heavy Duty'),
    ('Residential Cleanup Dumpster', 'Perfect for home cleanouts, renovations, and yard work', '/assets/images/dumpsters/dumpters_10YD.svg', 65, true, 4.3, 210, 'Residential'),
    ('Wood Recycling Dumpster', 'Specialized dumpster for wood waste and reclaimed materials', '/assets/images/dumpsters/dumpters_20YD.svg', 70, true, 4.6, 65, 'Recycling'),
    ('Commercial Construction Dumpster', 'Large capacity dumpster for major construction projects', '/assets/images/dumpsters/dumpters_40YD.svg', 110, false, 4.8, 42, 'Commercial'),
    ('Mixed Waste Dumpster', 'General purpose dumpster for various waste types', '/assets/images/dumpsters/dumpters_20YD.svg', 85, true, 4.4, 150, 'Multi-Purpose')
  ) AS d(name, description, image_url, price, available, rating, reviews, type_name),
  dumpster_types dt
WHERE dt.name = d.type_name;

-- Update next available date for unavailable dumpsters
UPDATE dumpsters SET next_available_date = '2023-06-15' WHERE name = 'Commercial Construction Dumpster';

-- Insert pricing plans
INSERT INTO pricing_plans (
  partner_id,
  dumpster_type_id,
  name,
  base_price,
  daily_rate,
  minimum_days,
  maximum_days,
  is_active
)
SELECT
  (SELECT id FROM partners LIMIT 1),
  dt.id,
  dt.name || ' Standard Plan',
  CASE 
    WHEN dt.name = 'Residential' THEN 100
    WHEN dt.name = 'Standard Roll-Off' THEN 150
    WHEN dt.name = 'Recycling' THEN 120
    WHEN dt.name = 'Heavy Duty' THEN 200
    WHEN dt.name = 'Commercial' THEN 250
    WHEN dt.name = 'Multi-Purpose' THEN 180
    ELSE 150
  END,
  CASE 
    WHEN dt.name = 'Residential' THEN 50
    WHEN dt.name = 'Standard Roll-Off' THEN 75
    WHEN dt.name = 'Recycling' THEN 60
    WHEN dt.name = 'Heavy Duty' THEN 95
    WHEN dt.name = 'Commercial' THEN 110
    WHEN dt.name = 'Multi-Purpose' THEN 85
    ELSE 75
  END,
  3,
  30,
  true
FROM dumpster_types dt;

-- Insert dumpster features
WITH dumpsters_cte AS (SELECT id, name FROM dumpsters)
INSERT INTO dumpster_features (dumpster_id, feature)
SELECT d.id, f.feature
FROM dumpsters_cte d,
(VALUES 
  ('Standard Construction Dumpster', 'Roll-off design'),
  ('Standard Construction Dumpster', 'Easy loading'),
  ('Standard Construction Dumpster', 'Durable construction'),
  ('Heavy Duty Dumpster', 'Reinforced base'),
  ('Heavy Duty Dumpster', 'High weight capacity'),
  ('Heavy Duty Dumpster', 'Industrial-grade steel'),
  ('Residential Cleanup Dumpster', 'Compact design'),
  ('Residential Cleanup Dumpster', 'Residential-friendly'),
  ('Residential Cleanup Dumpster', 'Low entry point'),
  ('Wood Recycling Dumpster', 'Eco-friendly disposal'),
  ('Wood Recycling Dumpster', 'Recycling focused'),
  ('Wood Recycling Dumpster', 'Sustainable option'),
  ('Commercial Construction Dumpster', 'Extra-large capacity'),
  ('Commercial Construction Dumpster', 'Heavy-duty construction'),
  ('Commercial Construction Dumpster', 'Commercial grade'),
  ('Mixed Waste Dumpster', 'Versatile use'),
  ('Mixed Waste Dumpster', 'Multiple size options'),
  ('Mixed Waste Dumpster', 'General purpose')
) AS f(dumpster_name, feature)
WHERE d.name = f.dumpster_name;

-- Insert dumpster size options
WITH 
  dumpsters_cte AS (SELECT id, name FROM dumpsters),
  sizes_cte AS (SELECT id, name FROM dumpster_sizes)
INSERT INTO dumpster_size_options (dumpster_id, dumpster_size_id)
SELECT d.id, s.id
FROM dumpsters_cte d, sizes_cte s
WHERE 
  (d.name = 'Standard Construction Dumpster' AND s.name IN ('10 Yard', '20 Yard', '30 Yard')) OR
  (d.name = 'Heavy Duty Dumpster' AND s.name IN ('20 Yard', '30 Yard', '40 Yard')) OR
  (d.name = 'Residential Cleanup Dumpster' AND s.name IN ('10 Yard', '20 Yard')) OR
  (d.name = 'Wood Recycling Dumpster' AND s.name IN ('10 Yard', '20 Yard', '30 Yard')) OR
  (d.name = 'Commercial Construction Dumpster' AND s.name IN ('30 Yard', '40 Yard')) OR
  (d.name = 'Mixed Waste Dumpster' AND s.name IN ('10 Yard', '20 Yard', '30 Yard', '40 Yard'));

-- Insert dumpster waste types
WITH 
  dumpsters_cte AS (SELECT id, name FROM dumpsters),
  waste_types_cte AS (SELECT id, name FROM waste_types)
INSERT INTO dumpster_waste_types (dumpster_id, waste_type_id)
SELECT d.id, wt.id
FROM dumpsters_cte d, waste_types_cte wt
WHERE 
  (d.name = 'Standard Construction Dumpster' AND wt.name IN ('Mixed construction waste', 'Construction waste')) OR
  (d.name = 'Heavy Duty Dumpster' AND wt.name IN ('Construction waste', 'Soil excavation')) OR
  (d.name = 'Residential Cleanup Dumpster' AND wt.name IN ('Mixed waste', 'Green cuttings')) OR
  (d.name = 'Wood Recycling Dumpster' AND wt.name = 'Reclaimed wood') OR
  (d.name = 'Commercial Construction Dumpster' AND wt.name IN ('Mixed construction waste', 'Construction waste', 'Soil excavation')) OR
  (d.name = 'Mixed Waste Dumpster' AND wt.name IN ('Mixed construction waste', 'Reclaimed wood', 'Mixed waste', 'Green cuttings')); 