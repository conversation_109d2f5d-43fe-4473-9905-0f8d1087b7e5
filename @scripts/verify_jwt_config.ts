// JWT Configuration Verification Script
// This script helps diagnose issues with Supabase JWT hooks

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import axios from 'axios';

// Load environment variables
dotenv.config();

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqam5sbndpbnJtbnd5dndsaGoiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTcxMjIxNzQ0MCwiZXhwIjoyMDI3NzkzNDQwfQ.OQxeSP7v7sHVpxnrG_BLK-jEVlRkm86OOqdSHLWRVDI';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'password123';
const TEST_PHONE = process.env.TEST_PHONE || '+966582083888';

// Function to sleep
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// JWT Decoder
function decodeJWT(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(Buffer.from(base64, 'base64').toString()
      .split('')
      .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
      .join(''));
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: true,
    detectSessionInUrl: false,
  }
});

// Function to check JWT settings via Supabase API
async function checkJwtSettings() {
  console.log('\n--- Checking JWT Settings ---');
  
  try {
    // Try to get public settings
    const { data, error } = await supabase.from('logs').select('*').limit(1);
    
    // Log general connectivity
    console.log(`Database connectivity: ${error ? 'Failed' : 'Success'}`);
    if (error) {
      console.error('Error details:', error.message);
    }
    
    // Check direct auth endpoint
    try {
      console.log('\nTesting auth endpoint directly...');
      const response = await axios.get(`${SUPABASE_URL}/auth/v1/settings`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Auth API status:', response.status);
      
      // Extract JWT related settings
      const authSettings = response.data;
      
      if (authSettings?.jwt_exp) {
        console.log('JWT expiration:', authSettings.jwt_exp);
      }
      
      // Check if custom hook is configured
      if (authSettings?.hooks) {
        console.log('\nHooks configuration:');
        console.log(JSON.stringify(authSettings.hooks, null, 2));
      } else {
        console.log('\nNo hooks configured in settings');
      }
      
    } catch (error: any) {
      console.error('Error accessing auth settings:', error.message);
    }
    
  } catch (error: any) {
    console.error('Error in verification:', error.message);
  }
}

// Function to attempt sign in
async function testSignIn() {
  try {
    console.log('\n--- Testing Sign In ---');
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });
    
    if (error) {
      console.error('Sign in error:', error.message);
      return false;
    }
    
    if (!data.session) {
      console.log('No session returned');
      return false;
    }
    
    console.log('Sign in successful');
    console.log('\nJWT token details:');
    const decodedToken = decodeJWT(data.session.access_token);
    console.log('- User ID:', decodedToken?.sub);
    console.log('- Role:', decodedToken?.role);
    console.log('- App metadata:', JSON.stringify(decodedToken?.app_metadata || {}, null, 2));
    
    return true;
  } catch (error) {
    console.error('Unexpected error during sign in:', error);
    return false;
  }
}

// Main function
async function main() {
  console.log('=== JWT CONFIGURATION VERIFICATION ===');
  console.log('Supabase URL:', SUPABASE_URL);
  
  // Check JWT settings first
  await checkJwtSettings();
  
  // Test sign in
  await testSignIn();
  
  // Always sign out at the end
  try {
    await supabase.auth.signOut();
    console.log('\nSigned out successfully');
  } catch (error) {
    console.error('Error during sign out:', error);
  }
}

// Run the script
main()
  .then(() => console.log('\nVerification completed'))
  .catch(err => console.error('Fatal error:', err)); 