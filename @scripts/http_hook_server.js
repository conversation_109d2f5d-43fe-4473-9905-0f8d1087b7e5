// Simple Express server for JWT HTTP hook
// Run with: node http_hook_server.js

const express = require('express');
const app = express();
const port = process.env.PORT || 3333;

// Parse JSON request bodies
app.use(express.json());

// Middleware for logging requests
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  if (Object.keys(req.body).length > 0) {
    console.log('Request body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// JWT claims hook endpoint
app.post('/jwt-hook', (req, res) => {
  try {
    console.log('JWT hook called with data:', JSON.stringify(req.body, null, 2));
    
    // Always return a valid claims object
    const claims = {
      role: 'authenticated',
      app_metadata: {
        user_role: 'customer'
      }
    };
    
    console.log('Returning claims:', JSON.stringify(claims, null, 2));
    res.json(claims);
  } catch (error) {
    console.error('Error in JWT hook:', error);
    // Even on error, return a valid claims object
    res.json({ role: 'authenticated' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start the server
app.listen(port, () => {
  console.log(`JWT HTTP hook server running at http://localhost:${port}`);
  console.log(`JWT hook endpoint: http://localhost:${port}/jwt-hook`);
  console.log('Health check: http://localhost:${port}/health');
  console.log('Press Ctrl+C to stop');
});

/*
Setup instructions:

1. Install dependencies:
   npm install express

2. Run the server:
   node http_hook_server.js

3. Configure a public URL using ngrok:
   ngrok http 3333

4. In Supabase Dashboard:
   - Go to Authentication → Hooks
   - Click "Configure hook" for JWT Claims hook
   - Choose "HTTPS" as Hook type
   - Enter your ngrok URL + /jwt-hook path
   - Save changes

5. Test authentication again
*/ 