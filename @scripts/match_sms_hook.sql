-- Create a JWT hook that exactly matches the signature of the working SMS hook

-- Insert a log for this attempt
INSERT INTO public.debug_logs (source, message) 
VALUES ('match_sms_attempt', 'Attempting to create JWT hook matching SMS hook signature');

-- Drop existing JWT function if it exists
DROP FUNCTION IF EXISTS auth.jwt_claims;

-- Create the JWT hook with the EXACT signature as the SMS hook
CREATE OR REPLACE FUNCTION auth.jwt_claims(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
BEGIN
  -- Log the call
  INSERT INTO public.debug_logs (source, message, data) 
  VALUES ('jwt_claims', 'Hook called', event);
  
  -- Return a simple claims object
  result := jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
  
  -- Log the result
  INSERT INTO public.debug_logs (source, message, data) 
  VALUES ('jwt_claims', 'Returning result', result);
  
  RETURN result;
EXCEPTION 
  WHEN OTHERS THEN
    -- Log error
    INSERT INTO public.debug_logs (source, message, data) 
    VALUES ('jwt_claims_error', SQLERRM, event);
    
    -- Always return a valid object
    RETURN jsonb_build_object('role', 'authenticated');
END;
$$;

-- Grant execution permission
GRANT EXECUTE ON FUNCTION auth.jwt_claims TO supabase_auth_admin;

-- Test the function
SELECT auth.jwt_claims('{"test":true}'::jsonb);

-- Instructions:
-- 1. Run this script
-- 2. In Supabase Dashboard, update JWT hook:
--    - Schema: auth
--    - Function: jwt_claims
-- 3. Test authentication
-- 4. Check logs:
--    SELECT * FROM public.debug_logs ORDER BY timestamp DESC LIMIT 10; 