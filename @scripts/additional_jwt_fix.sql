-- Additional JWT functions that might be used by Supabase

-- Create test_jwt_hook_formats function if it doesn't exist
CREATE OR REPLACE FUNCTION auth.test_jwt_hook_formats(
    user_id_text text,
    format text,
    result_type text,
    result_value text
)
RETURNS TABLE(format text, result_type text, result_value jsonb)
LANGUAGE sql
SECURITY INVOKER
AS $$
    -- This is just a stub
    SELECT 
        format::text, 
        result_type::text, 
        '{}'::jsonb AS result_value
    LIMIT 0;
$$;

-- Ensture claim_safe exists which was referenced in previous code
CREATE OR REPLACE FUNCTION auth.claim_safe(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
    SELECT auth.jwt_claim(event);
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION auth.test_jwt_hook_formats TO authenticated;
GRANT EXECUTE ON FUNCTION auth.test_jwt_hook_formats TO anon;
GRANT EXECUTE ON FUNCTION auth.test_jwt_hook_formats TO service_role;

GRANT EXECUTE ON FUNCTION auth.claim_safe TO authenticated;
GRANT EXECUTE ON FUNCTION auth.claim_safe TO anon;
GRANT EXECUTE ON FUNCTION auth.claim_safe TO service_role;

-- Log the changes
INSERT INTO public.logs (event_type, details)
VALUES ('additional_jwt_fix', jsonb_build_object(
    'timestamp', now(),
    'functions_fixed', jsonb_build_array(
        'test_jwt_hook_formats',
        'claim_safe'
    )
)); 