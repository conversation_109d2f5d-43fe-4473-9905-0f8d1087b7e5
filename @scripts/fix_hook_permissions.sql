-- Fix permissions for the custom hook function

-- Step 1: Drop existing function
DROP FUNCTION IF EXISTS public.custom_access_token_hook;

-- Step 2: Create a more robust function with proper permissions
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
BEGIN
  -- Log the function call
  INSERT INTO public.logs (event_type, details) 
  VALUES ('custom_hook_called', event);
  
  -- Return a fixed claims structure for testing
  result := jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
  
  -- Log the result
  INSERT INTO public.logs (event_type, details)
  VALUES ('custom_hook_result', result);
  
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  -- Log any errors
  INSERT INTO public.logs (event_type, details)
  VALUES ('custom_hook_error', jsonb_build_object('error', SQLERRM));
  
  -- Return a default claims structure
  RETURN jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
END;
$$;

-- Step 3: Set proper permissions
-- Grant execute to supabase_auth_admin role
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO authenticated;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO anon;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO service_role;

-- Grant usage on schema public to supabase_auth_admin
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;

-- Grant usage on sequence logs_id_seq to supabase_auth_admin 
GRANT USAGE ON SEQUENCE public.logs_id_seq TO supabase_auth_admin;

-- Grant INSERT on logs table to supabase_auth_admin
GRANT INSERT ON TABLE public.logs TO supabase_auth_admin;

-- Step 4: Test the function
SELECT public.custom_access_token_hook('{"test": true}'::jsonb);

-- Step 5: Verify logs were written
SELECT * FROM public.logs WHERE event_type LIKE 'custom_hook%' ORDER BY created_at DESC LIMIT 5; 