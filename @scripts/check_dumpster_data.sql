-- Check which capacities are present
SELECT capacity, count(*) 
FROM public.dumpster_types 
GROUP BY capacity 
ORDER BY capacity;

-- Check the newly created dumpsters with their types
SELECT 
    d.id,
    d.name_en,
    dt.capacity,
    d.price_per_load,
    d.image_url
FROM public.dumpsters d
JOIN public.dumpster_types dt ON d.dumpster_type_id = dt.id
WHERE d.id IN (
    '93113ed8-31a9-404e-b42e-b398f3f5848d',
    'aab87661-9303-4841-957a-38306347b63b',
    'fa258990-3277-4ef6-accf-812ace41cccc'
)
ORDER BY dt.capacity;
