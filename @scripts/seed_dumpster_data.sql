-- Populate Dumpster Sizes
INSERT INTO dumpster_sizes (id, dumpster_type_id, name, volume_cubic_yards, max_weight_pounds, length, width, height, description)
VALUES 
('83fc7e0d-143f-4d6d-b134-a5eda406038b', '83fc7e0d-143f-4d6d-b134-a5eda406038a', '5m³', 5, 2000, 3.5, 1.8, 1.25, 'Standard 5 cubic meter size'),
('b13719d7-8b16-45f0-907f-cea22fd6e59b', 'b13719d7-8b16-45f0-907f-cea22fd6e59c', '6.5 yd', 6.5, 2500, 4.2, 2.0, 1.5, 'Standard 6.5 yard size'),
('83fc7e0d-143f-4d6d-b134-a5eda406038c', '83fc7e0d-143f-4d6d-b134-a5eda406038a', '8m³', 8, 3000, 4.5, 2.2, 1.6, 'Large 8 cubic meter size'),
('b13719d7-8b16-45f0-907f-cea22fd6e59d', 'b13719d7-8b16-45f0-907f-cea22fd6e59c', '10 yd', 10, 3500, 5.0, 2.4, 1.8, 'Large 10 yard size');

-- Populate Dumpster Images
INSERT INTO dumpster_images (id, dumpster_id, image_url, sort_order)
VALUES 
('e3fff426-3a25-4903-bd4f-557b1143e899', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'assets/images/dumpsters/settledcontainer_5.svg', 1),
('e3fff426-3a25-4903-bd4f-557b1143e89a', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'assets/images/dumpsters/settledcontainer_5_2.svg', 2),
('e3fff426-3a25-4903-bd4f-557b1143e89b', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'assets/images/dumpsters/settledcontainer_5_3.svg', 3);

-- Populate Dumpster Features
INSERT INTO dumpster_features (id, dumpster_id, feature)
VALUES 
('e3fff426-3a25-4903-bd4f-557b1143e89c', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'Easy Loading'),
('e3fff426-3a25-4903-bd4f-557b1143e89d', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'Weather Resistant'),
('e3fff426-3a25-4903-bd4f-557b1143e89e', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'Secure Locking'),
('e3fff426-3a25-4903-bd4f-557b1143e89f', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'Wide Opening'),
('e3fff426-3a25-4903-bd4f-557b1143e8a0', 'e3fff426-3a25-4903-bd4f-557b1143e898', 'Heavy Duty Construction');

-- Update dumpster ratings and review counts
UPDATE dumpsters 
SET rating = 4.5, review_count = 12 
WHERE id = 'e3fff426-3a25-4903-bd4f-557b1143e898'; 