-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:8889
-- Generation Time: Mar 20, 2025 at 03:34 PM
-- Server version: 5.7.39
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `myappdb`
--

-- --------------------------------------------------------

--
-- Table structure for table `dumpsters`
--

CREATE TABLE `dumpsters` (
  `id` int(11) NOT NULL,
  `size` varchar(10) DEFAULT NULL,
  `location` varchar(50) DEFAULT NULL,
  `code` varchar(11) DEFAULT NULL,
  `unitPrice` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `description_en` text,
  `description_ar` text,
  `name_en` varchar(255) DEFAULT NULL,
  `name_ar` varchar(255) DEFAULT NULL,
  `coverType` varchar(50) DEFAULT NULL,
  `dimensions` varchar(50) DEFAULT NULL,
  `weightLimit` varchar(50) DEFAULT NULL,
  `standArea` varchar(50) DEFAULT NULL,
  `shuntingArea` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `dumpsters`
--

INSERT INTO `dumpsters` (`id`, `size`, `location`, `code`, `unitPrice`, `image`, `description_en`, `description_ar`, `name_en`, `name_ar`, `coverType`, `dimensions`, `weightLimit`, `standArea`, `shuntingArea`) VALUES
(1, '6.5yd', '24.816612, 46.663539', '67', '100.00', 'assets/images/dumpsters/settledcontainer_5.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 6.5 yd', 'حاوية 6.5 يردة', 'open', '3.5 x 1.8 x 1.25 m', '00', '10m²', '15 x 4m'),
(2, '5m³', '24.816612, 46.663539', '150', '100.00', 'assets/images/dumpsters/settledcontainerlid_5.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 5 m³ with lid', 'حاوية 5 م³ بغطاء', 'lid', '3.5 x 1.8 x 1.25 m', '00', '10m²', '15 x 4m'),
(3, '5m³', '24.816612, 46.663539', '11', '100.00', 'assets/images/dumpsters/settledcontainerflap_5.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 5 m³ with flap', 'حاوية 5 م³ بغطاء', 'flap', '3.5 x 1.8 x 1.25 m', '00', '10m²', '15 x 4m'),
(4, '7m³', '24.816612, 46.663539', '55', '150.00', 'assets/images/dumpsters/settledcontainer_7.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 7 m³', 'حاوية 7 م³', 'open', '4.0 x 2.0 x 1.5 m', '00', '12m²', '20 x 5m'),
(5, '7m³', '24.816612, 46.663539', '88', '150.00', 'assets/images/dumpsters/settledcontainerlid_7.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 7 m³ with lid', 'حاوية 7 م³ بغطاء', 'lid', '4.0 x 2.0 x 1.5 m', '00', '12m²', '20 x 5m'),
(6, '7m³', '24.816612, 46.663539', '190', '150.00', 'assets/images/dumpsters/settledcontainerflap_7.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 7 m³ with flap', 'حاوية 7 م³ بغطاء', 'flap', '4.0 x 2.0 x 1.5 m', '00', '12m²', '20 x 5m'),
(7, '10m³', '24.816612, 46.663539', '300', '200.00', 'assets/images/dumpsters/settledcontainer_10.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 10 m³', 'حاوية 10 م³', 'open', '4.5 x 2.5 x 1.8 m', '00', '15m²', '25 x 6m'),
(8, '10m³', '24.816612, 46.663539', '290', '200.00', 'assets/images/dumpsters/settledcontainerlid_10.svg', 'Container transport including placement and disposal', 'نقل الحاويات بما في ذلك التثبيت والتخلص', 'Skip 10 m³ with lid', 'حاوية 10 م³ بغطاء', 'lid', '4.5 x 2.5 x 1.8 m', '00', '15m²', '25 x 6m');

-- --------------------------------------------------------

--
-- Table structure for table `genders`
--

CREATE TABLE `genders` (
  `id` int(11) NOT NULL,
  `name` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `genders`
--

INSERT INTO `genders` (`id`, `name`) VALUES
(1, 'Male'),
(2, 'Female');

-- --------------------------------------------------------

--
-- Table structure for table `installation_locations`
--

CREATE TABLE `installation_locations` (
  `id` int(11) NOT NULL,
  `name_en` varchar(255) DEFAULT NULL,
  `name_ar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `installation_locations`
--

INSERT INTO `installation_locations` (`id`, `name_en`, `name_ar`) VALUES
(1, 'Private property', 'ملكية خاصة'),
(2, 'Public property', 'ملكية عامة'),
(3, 'Food plaza', 'ساحة الطعام');

-- --------------------------------------------------------

--
-- Table structure for table `languages`
--

CREATE TABLE `languages` (
  `id` int(11) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `code` varchar(10) DEFAULT NULL,
  `UI` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `languages`
--

INSERT INTO `languages` (`id`, `name`, `code`, `UI`) VALUES
(1, 'English', 'en', 'ltr'),
(2, 'Arabic', 'ar', 'rtl');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `userId` int(11) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `wasteTypeId` int(11) DEFAULT NULL,
  `dumpsterId` int(11) DEFAULT NULL,
  `deliveryDate` datetime DEFAULT NULL,
  `pickupDate` datetime DEFAULT NULL,
  `additionalInfo` text,
  `status` varchar(50) DEFAULT NULL,
  `locationTypeId` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `userId`, `location`, `address`, `wasteTypeId`, `dumpsterId`, `deliveryDate`, `pickupDate`, `additionalInfo`, `status`, `locationTypeId`) VALUES
(1, 2, '24.816612, 46.663539', '123 Main St, Springfield, IL 62701', 2, 2, '2023-10-01 08:00:00', '2023-10-07 08:00:00', 'Please place the dumpster in the driveway.', 'in-progress', 1),
(2, 2, '24.816612, 46.663539', '456 Elm St, Shelbyville, IL 62565', 2, 2, '2023-10-02 09:00:00', '2023-10-08 09:00:00', 'Leave the dumpster on the street.', 'Pending', 1),
(3, 2, '24.816612, 46.663539', '789 Oak St, Shelbyville, IL 62565', 2, 2, '2023-10-03 10:00:00', '2023-10-09 10:00:00', 'Please place the dumpster in the backyard.', 'completed', 1),
(4, 2, '24.816612, 46.663539', '123 Main St, Springfield, IL 62701', 2, 2, '2023-10-04 11:00:00', '2023-10-10 11:00:00', 'Please place the dumpster in the driveway.', 'need attention', 1),
(5, 1, '24.816612, 46.663539', '123 Main St, Springfield, IL 62701', 1, 1, '2023-10-01 08:00:00', '2023-10-07 08:00:00', 'Please place the dumpster in the driveway.', 'in-progress', 1),
(6, 1, '24.816612, 46.663539', '456 Elm St, Shelbyville, IL 62565', 1, 1, '2023-10-02 09:00:00', '2023-10-08 09:00:00', 'Leave the dumpster on the street for 2 days.', 'Pending', 1),
(7, 1, '24.816612, 46.663539', '789 Oak St, Shelbyville, IL 62565', 1, 1, '2023-10-03 10:00:00', '2023-10-09 10:00:00', 'Please place the dumpster in the backyard.', 'Pending', 2),
(8, 1, '24.816612, 46.663539', '123 Main St, Springfield, IL 62701', 1, 1, '2023-10-04 11:00:00', '2023-10-10 11:00:00', 'Please place the dumpster in the driveway.', 'Pending', 1);

-- --------------------------------------------------------

--
-- Table structure for table `page_size_options`
--

CREATE TABLE `page_size_options` (
  `id` int(11) NOT NULL,
  `size` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `page_size_options`
--

INSERT INTO `page_size_options` (`id`, `size`) VALUES
(1, 5),
(2, 10),
(3, 25),
(4, 100);

-- --------------------------------------------------------

--
-- Table structure for table `partners`
--

CREATE TABLE `partners` (
  `id` int(11) NOT NULL,
  `companyName` varchar(255) NOT NULL,
  `companyAddress` varchar(255) DEFAULT NULL,
  `companyCity` varchar(255) DEFAULT NULL,
  `companyPhone` varchar(20) DEFAULT NULL,
  `userID` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `partners`
--

INSERT INTO `partners` (`id`, `companyName`, `companyAddress`, `companyCity`, `companyPhone`, `userID`) VALUES
(1, 'Waste Solutions Ltd', '1234 Main St', 'Los Angeles', '************', 3),
(2, 'Clean City Inc.', '1234 Main St', 'Los Angeles', '************', 4);

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`) VALUES
(1, 'Admin'),
(2, 'User'),
(3, 'Partners');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `vat` decimal(5,2) DEFAULT NULL,
  `vatInclusive` tinyint(1) DEFAULT NULL,
  `rentalCharge` decimal(5,2) DEFAULT NULL,
  `currency_en` varchar(10) DEFAULT NULL,
  `currency_ar` varchar(10) DEFAULT NULL,
  `currencySymbolPosition` varchar(10) DEFAULT NULL,
  `currencyDecimalDigits` int(11) DEFAULT NULL,
  `currencyDecimalSeparator` varchar(5) DEFAULT NULL,
  `currencyThousandSeparator` varchar(5) DEFAULT NULL,
  `dateFormat` varchar(20) DEFAULT NULL,
  `timeFormat` varchar(20) DEFAULT NULL,
  `dateTimeFormat` varchar(20) DEFAULT NULL,
  `timeZone` varchar(50) DEFAULT NULL,
  `defaultLanguage` varchar(10) DEFAULT NULL,
  `defaultTheme` varchar(10) DEFAULT NULL,
  `pageSize` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `vat`, `vatInclusive`, `rentalCharge`, `currency_en`, `currency_ar`, `currencySymbolPosition`, `currencyDecimalDigits`, `currencyDecimalSeparator`, `currencyThousandSeparator`, `dateFormat`, `timeFormat`, `dateTimeFormat`, `timeZone`, `defaultLanguage`, `defaultTheme`, `pageSize`) VALUES
(1, '0.15', 1, '0.22', 'SAR', 'ر.س', 'before', 2, '.', ',', 'dd/MM/yyyy', 'HH:mm', 'dd/MM/yyyy HH:mm', 'Asia/Riyadh', 'en', 'light', 10);

-- --------------------------------------------------------

--
-- Table structure for table `tags`
--

CREATE TABLE `tags` (
  `id` int(11) NOT NULL,
  `waste_type_id` int(11) DEFAULT NULL,
  `tag_en` varchar(255) DEFAULT NULL,
  `tag_ar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tags`
--

INSERT INTO `tags` (`id`, `waste_type_id`, `tag_en`, `tag_ar`) VALUES
(1, 1, 'construction', 'بناء'),
(2, 1, 'mixed', 'مختلط'),
(3, 2, 'wood', 'خشب'),
(4, 2, 'recyclable', 'قابل لإعادة التدوير'),
(5, 3, 'general', 'عام'),
(6, 3, 'non-hazardous', 'غير خطرة'),
(7, 4, 'construction', 'بناء'),
(8, 4, 'debris', 'حطام'),
(9, 5, 'soil', 'تربة'),
(10, 5, 'excavation', 'حفر'),
(11, 6, 'organic', 'عضوي'),
(12, 6, 'landscaping', 'تنسيق الحدائق');

-- --------------------------------------------------------

--
-- Table structure for table `themes`
--

CREATE TABLE `themes` (
  `id` int(11) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `code` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `themes`
--

INSERT INTO `themes` (`id`, `name`, `code`) VALUES
(1, 'Light', 'light'),
(2, 'Dark', 'dark');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `phone`, `email`, `password`, `role_id`, `name`) VALUES
(1, '+966523456789', '<EMAIL>', 'mok1109288', 2, 'Mok most'),
(2, '+966512345678', '<EMAIL>', 'mok1109288', 2, 'moosksk kkk'),
(3, '+966512233445', 'mm@c.c', '1234', 1, 'MO CC'),
(4, '+966582083888', '<EMAIL>', '1234', 1, 'Ahmed Saad'),
(14, '+966555666555', 'jsbjdbc@jbjbjbj', 'Mo@999dhd', 2, 'chvchj xjbcsjbc'),
(15, '+966511122233', 'mm@mm', 'Mok@110928', 2, 'Mo Adoss'),
(21, '+966599999999', NULL, NULL, 2, 'jjsnb jkdwnsdkn');

-- --------------------------------------------------------

--
-- Table structure for table `user_info`
--

CREATE TABLE `user_info` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `profileImageURL` varchar(255) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `address` varchar(150) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `user_info`
--

INSERT INTO `user_info` (`id`, `user_id`, `gender`, `dateOfBirth`, `profileImageURL`, `latitude`, `longitude`, `address`) VALUES
(1, 1, 'M', '2025-01-15', 'assets/uploads/users/mk.jpeg', NULL, NULL, NULL),
(2, 2, 'M', '2025-01-15', 'assets/uploads/users/modymok.jpeg', NULL, NULL, NULL),
(3, 14, 'female', '2024-12-20', NULL, NULL, NULL, NULL),
(4, 15, 'male', '2024-12-05', NULL, NULL, NULL, NULL),
(10, 21, NULL, NULL, NULL, NULL, NULL, NULL),
(11, 4, 'M', '1981-12-17', '/assets/uploads/users/IMG_8762.jpg', '24.71360000', '46.67530000', '24.71360000, 46.67530000');

-- --------------------------------------------------------

--
-- Table structure for table `waste_types`
--

CREATE TABLE `waste_types` (
  `id` int(11) NOT NULL,
  `name_en` varchar(255) DEFAULT NULL,
  `name_ar` varchar(255) DEFAULT NULL,
  `description_en` text,
  `description_ar` text,
  `image` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `waste_types`
--

INSERT INTO `waste_types` (`id`, `name_en`, `name_ar`, `description_en`, `description_ar`, `image`) VALUES
(1, 'Mixed construction waste', 'نفايات البناء المختلطة', 'A variety of construction debris mixed together.', 'مجموعة متنوعة من حطام البناء مختلطة معًا.', 'assets/images/waste/teaser-arboriculturalwaste.jpg'),
(2, 'Reclaimed wood', 'الخشب المعاد تدويره', 'Recyclable wood salvaged from construction.', 'الخشب القابل لإعادة التدوير المسترجع من البناء.', 'assets/images/waste/teaser-oldwood.jpg'),
(3, 'Mixed waste', 'النفايات المختلطة', 'A mixture of general and non-hazardous waste.', 'خليط من النفايات العامة وغير الخطرة.', 'assets/images/waste/teaser-azv.jpg'),
(4, 'Construction waste', 'نفايات البناء', 'Debris and materials from construction sites.', 'الحطام والمواد من مواقع البناء.', 'assets/images/waste/teaser-rubble.jpg'),
(5, 'Soil excavation', 'حفر التربة', 'Excavated soil from various construction works.', 'التربة المحفورة من أعمال البناء المختلفة.', 'assets/images/waste/teaser-ground.jpg'),
(6, 'Green cuttings', 'النفايات الخضراء', 'Organic green waste from landscaping.', 'النفايات الخضراء العضوية من تنسيق الحدائق.', 'assets/images/waste/teaser-greencut.jpg');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `dumpsters`
--
ALTER TABLE `dumpsters`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id` (`id`);

--
-- Indexes for table `genders`
--
ALTER TABLE `genders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `installation_locations`
--
ALTER TABLE `installation_locations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `languages`
--
ALTER TABLE `languages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `userId` (`userId`),
  ADD KEY `wasteTypeId` (`wasteTypeId`),
  ADD KEY `dumpsterId` (`dumpsterId`),
  ADD KEY `locationTypeId` (`locationTypeId`);

--
-- Indexes for table `page_size_options`
--
ALTER TABLE `page_size_options`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `partners`
--
ALTER TABLE `partners`
  ADD PRIMARY KEY (`id`),
  ADD KEY `userID` (`userID`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`id`),
  ADD KEY `waste_type_id` (`waste_type_id`);

--
-- Indexes for table `themes`
--
ALTER TABLE `themes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id` (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `fk_role` (`role_id`);

--
-- Indexes for table `user_info`
--
ALTER TABLE `user_info`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `waste_types`
--
ALTER TABLE `waste_types`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `dumpsters`
--
ALTER TABLE `dumpsters`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `genders`
--
ALTER TABLE `genders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `installation_locations`
--
ALTER TABLE `installation_locations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `languages`
--
ALTER TABLE `languages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `page_size_options`
--
ALTER TABLE `page_size_options`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `partners`
--
ALTER TABLE `partners`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tags`
--
ALTER TABLE `tags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `themes`
--
ALTER TABLE `themes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `user_info`
--
ALTER TABLE `user_info`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `waste_types`
--
ALTER TABLE `waste_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`wasteTypeId`) REFERENCES `waste_types` (`id`),
  ADD CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`dumpsterId`) REFERENCES `dumpsters` (`id`),
  ADD CONSTRAINT `orders_ibfk_4` FOREIGN KEY (`locationTypeId`) REFERENCES `installation_locations` (`id`);

--
-- Constraints for table `partners`
--
ALTER TABLE `partners`
  ADD CONSTRAINT `partners_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users` (`id`);

--
-- Constraints for table `tags`
--
ALTER TABLE `tags`
  ADD CONSTRAINT `tags_ibfk_1` FOREIGN KEY (`waste_type_id`) REFERENCES `waste_types` (`id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `fk_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`);

--
-- Constraints for table `user_info`
--
ALTER TABLE `user_info`
  ADD CONSTRAINT `user_info_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
