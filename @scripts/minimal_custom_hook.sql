-- Minimal Custom Access Token Hook
-- This is the simplest possible implementation that should work

-- Drop the existing function
DROP FUNCTION IF EXISTS public.custom_access_token_hook(jsonb);

-- Create a minimal hook function in PUBLIC schema
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    -- Just return a fixed valid claims object
    -- No error handling, no database queries, no logging
    SELECT jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
$$;

-- Grant correct permissions
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
REVOKE EXECUTE ON FUNCTION public.custom_access_token_hook FROM authenticated, anon, public;

-- Test the function directly
SELECT public.custom_access_token_hook('{}'::jsonb); 