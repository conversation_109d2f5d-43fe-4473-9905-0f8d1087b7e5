-- Test SQL script to verify JWT hook is accessible by supabase_auth_admin

-- Set the role to supabase_auth_admin to simulate how Supa<PERSON> Auth will call the function
SET ROLE supabase_auth_admin;

-- Test calling the function as supabase_auth_admin
SELECT current_user, session_user;
SELECT public.custom_access_token_hook('{"test":"true"}'::jsonb) AS auth_admin_result;

-- Reset role for safety
RESET ROLE; 