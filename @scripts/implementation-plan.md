# Implementation Plan: RBAC User Management & SMS Authentication Hook

This document outlines the implementation plan for:
1. Role-Based Access Control (RBAC) with custom JWT claims
2. Integration of Supabase SMS authentication with Authentica SMS provider

## Part 1: RBAC User Management Implementation

### 1.1 Create Custom JWT Claims Function

Run the following SQL in the Supabase SQL Editor:

```sql
-- Create a function to add custom claims to JWT tokens
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := request->>'sub';
  user_role TEXT;
BEGIN
  -- Get the user's role from the profiles table
  SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Return the custom claims to be added to the JWT
  RETURN jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO service_role;
```

### 1.2 Update Profiles Table for User Types

```sql
-- Update the profiles table to ensure user_type is always set
ALTER TABLE public.profiles 
  ALTER COLUMN user_type SET DEFAULT 'customer',
  ADD CONSTRAINT valid_user_type CHECK (user_type IN ('customer', 'partner', 'admin', 'driver'));
```

### 1.3 Create Trigger for JWT Refresh on Role Change

```sql
-- Create a trigger to update JWT claims when user_type changes
CREATE OR REPLACE FUNCTION public.handle_user_type_change()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.user_type IS DISTINCT FROM NEW.user_type THEN
    -- Force refresh of JWT claims by updating auth.users
    UPDATE auth.users SET updated_at = now() WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_user_type_change
  AFTER UPDATE ON public.profiles
  FOR EACH ROW
  WHEN (OLD.user_type IS DISTINCT FROM NEW.user_type)
  EXECUTE FUNCTION public.handle_user_type_change();
```

### 1.4 Update RLS Policies to Use JWT Claims

```sql
-- First, drop existing policies
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;
DROP POLICY IF EXISTS "users_can_view_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_can_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_can_insert_own_profile" ON profiles;

-- Create new policies based on roles
-- All users can view their own profile
CREATE POLICY "users_can_view_own_profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

-- Admin users can view all profiles
CREATE POLICY "admins_can_view_all_profiles"
ON profiles FOR SELECT
USING ((auth.jwt()->>'role')::text = 'admin');

-- Partners can view their own profile and customer profiles
CREATE POLICY "partners_can_view_customer_profiles"
ON profiles FOR SELECT
USING (
  ((auth.jwt()->>'role')::text = 'partner' AND 
   (id = auth.uid() OR (SELECT user_type FROM profiles WHERE id = auth.uid()) = 'customer'))
);

-- Users can update their own profile
CREATE POLICY "users_can_update_own_profile"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- Admin users can update any profile
CREATE POLICY "admins_can_update_any_profile"
ON profiles FOR UPDATE
USING ((auth.jwt()->>'role')::text = 'admin');

-- Users can insert their own profile
CREATE POLICY "users_can_insert_own_profile"
ON profiles FOR INSERT
WITH CHECK (auth.uid() = id);

-- Admin users can create any profile
CREATE POLICY "admins_can_create_any_profile"
ON profiles FOR INSERT
WITH CHECK ((auth.jwt()->>'role')::text = 'admin');
```

### 1.5 Create Helper Functions for Role Checking

```sql
-- Create or replace the is_admin function
CREATE OR REPLACE FUNCTION is_admin(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create or replace the is_partner function
CREATE OR REPLACE FUNCTION is_partner(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'partner'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a new is_driver function
CREATE OR REPLACE FUNCTION is_driver(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'driver'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user role
CREATE OR REPLACE FUNCTION get_user_role(uid UUID)
RETURNS TEXT AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT user_type INTO user_role FROM profiles WHERE id = uid;
  RETURN COALESCE(user_role, 'customer');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 1.6 Apply RLS to Other Tables Based on Roles

```sql
-- Example for orders table
DROP POLICY IF EXISTS "users_can_view_own_orders" ON orders;
DROP POLICY IF EXISTS "admins_can_view_all_orders" ON orders;
DROP POLICY IF EXISTS "partners_can_view_assigned_orders" ON orders;

-- Customers can view their own orders
CREATE POLICY "users_can_view_own_orders"
ON orders FOR SELECT
USING (customer_id = auth.uid());

-- Admin users can view all orders
CREATE POLICY "admins_can_view_all_orders"
ON orders FOR SELECT
USING ((auth.jwt()->>'role')::text = 'admin');

-- Partners can view orders assigned to them
CREATE POLICY "partners_can_view_assigned_orders"
ON orders FOR SELECT
USING (
  ((auth.jwt()->>'role')::text = 'partner' AND partner_id = auth.uid())
);

-- Similar policies for INSERT, UPDATE, DELETE operations
```

## Part 2: SMS Authentication Hook Implementation

### 2.1 Create Authentica API Integration Function

```sql
-- Create a schema for auth hooks if it doesn't exist
CREATE SCHEMA IF NOT EXISTS auth_hooks;

-- Create a function to send SMS via Authentica
CREATE OR REPLACE FUNCTION auth_hooks.send_sms(
  phone TEXT,
  message TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  response JSONB;
  api_key TEXT := 'YOUR_AUTHENTICA_API_KEY'; -- Replace with your actual API key
  sender_name TEXT := 'YOUR_SENDER_NAME'; -- Replace with your registered sender name
BEGIN
  -- Make HTTP request to Authentica API
  SELECT
    content::jsonb INTO response
  FROM
    http((
      'POST',
      'https://api.authentica.sa/api/v1/send-sms',
      ARRAY[
        http_header('X-Authorization', api_key),
        http_header('Accept', 'application/json'),
        http_header('Content-Type', 'application/json')
      ],
      'application/json',
      jsonb_build_object(
        'phone', phone,
        'message', message,
        'sender_name', sender_name
      )::text
    ));
  
  -- Check if the request was successful
  IF response->>'success' = 'true' THEN
    RETURN TRUE;
  ELSE
    RAISE WARNING 'Failed to send SMS: %', response;
    RETURN FALSE;
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error sending SMS: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2.2 Create SMS Auth Hook Function

```sql
-- Create the SMS auth hook function
CREATE OR REPLACE FUNCTION auth_hooks.send_sms_hook(event JSONB)
RETURNS JSONB AS $$
DECLARE
  phone TEXT := event->'user'->>'phone';
  otp TEXT := event->'sms'->>'otp';
  message TEXT;
BEGIN
  -- Create the message with the OTP
  message := 'Your verification code for Dumpster On Demand is: ' || otp;
  
  -- Send the SMS using the Authentica API
  IF auth_hooks.send_sms(phone, message) THEN
    -- Return success response
    RETURN jsonb_build_object(
      'success', true
    );
  ELSE
    -- Return error response
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Failed to send SMS'
    );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2.3 Configure the HTTP Extension (if not already enabled)

```sql
-- Enable the HTTP extension if not already enabled
CREATE EXTENSION IF NOT EXISTS http WITH SCHEMA extensions;
GRANT USAGE ON SCHEMA extensions TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA extensions TO postgres, anon, authenticated, service_role;
```

## Testing and Verification

### Test JWT Claims

1. Create a user with a specific role
2. Sign in with that user
3. Decode the JWT token to verify the custom claims are present

### Test Role-Based Access

1. Create users with different roles (customer, partner, admin, driver)
2. Test access to different resources based on roles
3. Verify that RLS policies are correctly enforced

### Test SMS Authentication

1. Attempt to sign in with a phone number
2. Verify that the SMS is sent via Authentica
3. Complete the verification process with the OTP

## Configuration in Supabase Dashboard

After running the SQL queries, you need to configure the hooks in the Supabase Dashboard:

1. Go to Authentication > Hooks
2. For the "Send SMS" hook, enter: `auth_hooks.send_sms_hook`
3. Save the configuration

## Notes for Implementation

- Replace `'YOUR_AUTHENTICA_API_KEY'` with your actual Authentica API key
- Replace `'YOUR_SENDER_NAME'` with your registered sender name in Authentica
- Customize the SMS message template as needed
- Consider adding error logging for better debugging
- Test thoroughly in a development environment before deploying to production
