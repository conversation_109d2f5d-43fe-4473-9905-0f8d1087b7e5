-- Create custom hook with same pattern as working SMS hook

-- Drop existing function
DROP FUNCTION IF EXISTS public.custom_access_token_hook;

-- Create the function with a simpler implementation
-- Using standard sql language instead of plpgsql
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    -- Return a fixed simple object with just the required fields
    -- No conditionals, no error handling, no logging - pure simplicity
    SELECT jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
$$;

-- Set correct permissions
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;

-- Test the function directly
SELECT jsonb_typeof(public.custom_access_token_hook('{}'::jsonb)) as result_type;
SELECT public.custom_access_token_hook('{}'::jsonb) as hook_result; 