-- Create a custom access token hook function
-- This follows the exact pattern from Supabase documentation
-- https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.custom_access_token_hook(jsonb);

-- Create the function in the public schema as recommended
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id UUID;
  user_role TEXT;
  custom_claims jsonb;
BEGIN
  -- Extract the user_id from the event payload
  user_id := (event->>'sub')::UUID;
  
  -- Log the hook call
  INSERT INTO public.jwt_hook_logs (
    function_name, parameter_type, raw_input
  ) VALUES (
    'public.custom_access_token_hook', 'jsonb', event
  );
  
  -- Get the user's role from the profiles table if available
  BEGIN
    SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  EXCEPTION WHEN OTHERS THEN
    -- Default to 'customer' if any error occurs
    user_role := 'customer';
  END;
  
  -- Default to 'customer' if no role found
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Build custom claims
  custom_claims := jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
  
  -- Log result
  UPDATE public.jwt_hook_logs
  SET result = custom_claims
  WHERE id = currval('jwt_hook_logs_id_seq'::regclass);
  
  -- Return the claims
  RETURN custom_claims;
EXCEPTION WHEN OTHERS THEN
  -- Log error
  UPDATE public.jwt_hook_logs
  SET error = SQLERRM
  WHERE id = currval('jwt_hook_logs_id_seq'::regclass);
  
  -- Always return a valid object even on error
  RETURN jsonb_build_object(
    'role', 'customer',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
END;
$$;

-- Grant permissions as specified in Supabase documentation
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
REVOKE EXECUTE ON FUNCTION public.custom_access_token_hook FROM authenticated, anon, public;

-- Log the fix
INSERT INTO public.logs (event_type, details)
VALUES ('custom_access_token_hook_implemented', jsonb_build_object(
  'timestamp', now(),
  'source', 'supabase_documentation',
  'url', 'https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook'
)); 