#!/usr/bin/env ts-node
/**
 * Account Merging Utility for Dumpster On Demand
 * 
 * This script allows merging multiple Supabase auth accounts that belong to the same user.
 * It can merge accounts by email, phone, or specific user IDs.
 * 
 * IMPORTANT: This is a destructive operation. Always backup your database before running.
 * 
 * Usage:
 *   - By email: npx ts-node merge_user_accounts.ts --email=<EMAIL>
 *   - By phone: npx ts-node merge_user_accounts.ts --phone=+**********
 *   - By IDs: npx ts-node merge_user_accounts.ts --primary-id=uuid1 --secondary-id=uuid2
 * 
 * Options:
 *   --email           Email to find duplicate accounts
 *   --phone           Phone to find duplicate accounts
 *   --primary-id      Primary user ID to keep
 *   --secondary-id    Secondary user ID to merge into primary
 *   --dry-run         Show what would happen without making changes
 *   --help            Show help
 */

import { createClient } from '@supabase/supabase-js';
import { Command } from 'commander';
import readline from 'readline';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define command line options
const program = new Command();
program
  .option('--email <email>', 'Email to find duplicate accounts')
  .option('--phone <phone>', 'Phone to find duplicate accounts')
  .option('--primary-id <id>', 'Primary user ID to keep')
  .option('--secondary-id <id>', 'Secondary user ID to merge into primary')
  .option('--dry-run', 'Show what would happen without making changes', false)
  .helpOption('--help', 'Show help');

program.parse(process.argv);
const options = program.opts();

// Validate options
if (!options.email && !options.phone && !(options.primaryId && options.secondaryId)) {
  console.error('Error: You must provide either an email, phone, or both primary and secondary user IDs');
  program.help();
  process.exit(1);
}

// Set up Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_KEY environment variable is required');
  console.error('Set it with: export SUPABASE_KEY=your_service_role_key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to prompt for confirmation
const confirm = async (message: string): Promise<boolean> => {
  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      resolve(answer.toLowerCase() === 'y');
    });
  });
};

// Define interfaces for our data types
interface AuthUser {
  id: string;
  email: string | null;
  phone: string | null;
  created_at: string;
  provider?: string;
}

interface Profile {
  id: string;
  user_type: string;
  email: string | null;
  full_name: string | null;
  phone: string | null;
  created_at: string;
}

// Update the findAccountsByEmail function
async function findAccountsByEmail(email: string): Promise<AuthUser[]> {
  // First check auth.users for exact match
  const { data: authUsers, error: authError } = await supabase
    .from('auth.users')
    .select('id, email, phone, created_at')
    .eq('email', email);

  if (authError) {
    console.error('Error querying auth.users:', authError);
    return [];
  }

  // Then check auth.identities for the same email
  const { data: authIdentities, error: identitiesError } = await supabase
    .from('auth.identities')
    .select('user_id, provider, email, created_at')
    .eq('email', email);

  if (identitiesError) {
    console.error('Error querying auth.identities:', identitiesError);
    return [];
  }

  // Combine results, removing duplicates by id
  const users = (authUsers || []) as AuthUser[];
  const identityUsers = (authIdentities || []).map(identity => ({
    id: identity.user_id,
    email: identity.email,
    phone: null, // Add phone field with null value for identity users
    provider: identity.provider,
    created_at: identity.created_at
  })) as AuthUser[];
  
  // Combine and remove duplicates
  const allUsers = [...users];
  for (const user of identityUsers) {
    if (!allUsers.some(u => u.id === user.id)) {
      allUsers.push(user);
    }
  }
  
  return allUsers;
}

// Update the findAccountsByPhone function
async function findAccountsByPhone(phone: string): Promise<AuthUser[]> {
  const { data: users, error } = await supabase
    .from('auth.users')
    .select('id, email, phone, created_at')
    .eq('phone', phone);

  if (error) {
    console.error('Error:', error);
    return [];
  }

  return (users || []) as AuthUser[];
}

// Update the getProfile function
async function getProfile(userId: string): Promise<Profile | null> {
  const { data, error } = await supabase
    .from('profiles')
    .select('id, user_type, email, full_name, phone, created_at')
    .eq('id', userId)
    .maybeSingle();

  if (error) {
    console.error('Error getting profile:', error);
    return null;
  }

  return data as Profile | null;
}

// Update the getProfiles function
async function getProfiles(userIds: string[]): Promise<Profile[]> {
  const { data, error } = await supabase
    .from('profiles')
    .select('id, user_type, email, full_name, phone, created_at')
    .in('id', userIds);

  if (error) {
    console.error('Error getting profiles:', error);
    return [];
  }

  return (data || []) as Profile[];
}

// Get user roles
async function getUserRoles(userIds: string[]): Promise<any[]> {
  const { data, error } = await supabase.rpc('admin_get_user_roles', { user_ids: userIds });

  if (error) {
    console.error('Error getting user roles:', error);
    return [];
  }

  return data || [];
}

// Merge user accounts
async function mergeUserAccounts(primaryUserId: string, secondaryUserId: string, dryRun: boolean): Promise<boolean> {
  console.log(`${dryRun ? '[DRY RUN] ' : ''}Merging user ${secondaryUserId} into ${primaryUserId}`);
  
  // Get profiles for both users
  const primaryProfile = await getProfile(primaryUserId);
  const secondaryProfile = await getProfile(secondaryUserId);
  
  if (!primaryProfile) {
    console.error(`Primary user ${primaryUserId} has no profile`);
    return false;
  }
  
  // Get user roles
  const roles = await getUserRoles([primaryUserId, secondaryUserId]);
  const primaryRole = roles.find(r => r.id === primaryUserId)?.role;
  const secondaryRole = roles.find(r => r.id === secondaryUserId)?.role;
  
  console.log(`Primary user: ${primaryUserId}, Role: ${primaryRole || 'none'}, Profile: ${primaryProfile.user_type}`);
  if (secondaryProfile) {
    console.log(`Secondary user: ${secondaryUserId}, Role: ${secondaryRole || 'none'}, Profile: ${secondaryProfile.user_type}`);
  } else {
    console.log(`Secondary user: ${secondaryUserId}, Role: ${secondaryRole || 'none'}, Profile: none`);
  }

  // In dry run mode, just return success
  if (dryRun) {
    console.log('[DRY RUN] Would merge accounts (no changes made)');
    return true;
  }

  try {
    // Handle user role if needed
    if (secondaryRole && !primaryRole) {
      console.log(`Copying role '${secondaryRole}' from secondary to primary user`);
      
      const { error } = await supabase.rpc('admin_copy_user_role', {
        from_user_id: secondaryUserId,
        to_user_id: primaryUserId
      });
      
      if (error) {
        console.error('Error copying user role:', error);
      }
    }
    
    // Update identities to point to the primary user
    const { error: identitiesError } = await supabase
      .from('auth.identities')
      .update({ user_id: primaryUserId })
      .eq('user_id', secondaryUserId);
    
    if (identitiesError) {
      console.error('Error updating identities:', identitiesError);
      return false;
    }
    
    // Update or merge profile if secondary has one but primary doesn't
    if (secondaryProfile && !primaryProfile) {
      console.log(`Updating secondary profile ID to match primary user ID`);
      
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ id: primaryUserId })
        .eq('id', secondaryUserId);
      
      if (profileError) {
        console.error('Error updating profile:', profileError);
      }
    }
    
    // Delete the secondary user (this will cascade deletes for auth.sessions, auth.refresh_tokens, etc.)
    console.log(`Deleting secondary user ${secondaryUserId}`);
    await supabase.rpc('admin_delete_user', { user_id: secondaryUserId });
    
    console.log(`Successfully merged accounts`);
    return true;
  } catch (error) {
    console.error('Error during account merge:', error);
    return false;
  }
}

// Type guard function to assert string is not null
function assertNotNull<T>(value: T | null, message: string): asserts value is T {
  if (value === null) {
    throw new Error(message);
  }
}

// Main function
async function main() {
  try {
    let primaryUserId: string | null = null;
    let secondaryUserIds: string[] = [];
    
    // Find users by email, phone, or direct IDs
    if (options.email) {
      console.log(`Finding accounts with email: ${options.email}`);
      const users = await findAccountsByEmail(options.email);
      
      if (users.length < 2) {
        console.log(`Found ${users.length} accounts with this email. Need at least 2 to merge.`);
        return;
      }
      
      console.log(`Found ${users.length} accounts:`);
      users.forEach((user, index) => {
        console.log(`[${index + 1}] ID: ${user.id}, Email: ${user.email || 'none'}, Phone: ${user.phone || 'none'}, Provider: ${user.provider || 'direct'}, Created: ${user.created_at}`);
      });
      
      // Get associated profiles
      const profiles = await getProfiles(users.map(u => u.id));
      if (profiles.length > 0) {
        console.log('\nAssociated profiles:');
        profiles.forEach(profile => {
          console.log(`Profile for ${profile.id}: Type: ${profile.user_type}, Name: ${profile.full_name || 'none'}, Phone: ${profile.phone || 'none'}`);
        });
      }
      
      // For now, we'll use the oldest account as primary unless specified
      primaryUserId = options.primaryId || users[0].id;
      secondaryUserIds = users.filter(u => u.id !== primaryUserId).map(u => u.id);
      
    } else if (options.phone) {
      console.log(`Finding accounts with phone: ${options.phone}`);
      const users = await findAccountsByPhone(options.phone);
      
      if (users.length < 2) {
        console.log(`Found ${users.length} accounts with this phone. Need at least 2 to merge.`);
        return;
      }
      
      console.log(`Found ${users.length} accounts:`);
      users.forEach((user, index) => {
        console.log(`[${index + 1}] ID: ${user.id}, Email: ${user.email || 'none'}, Phone: ${user.phone}, Created: ${user.created_at}`);
      });
      
      // Get associated profiles
      const profiles = await getProfiles(users.map(u => u.id));
      if (profiles.length > 0) {
        console.log('\nAssociated profiles:');
        profiles.forEach(profile => {
          console.log(`Profile for ${profile.id}: Type: ${profile.user_type}, Name: ${profile.full_name || 'none'}, Phone: ${profile.phone || 'none'}`);
        });
      }
      
      // For now, we'll use the oldest account as primary unless specified
      primaryUserId = options.primaryId || users[0].id;
      secondaryUserIds = users.filter(u => u.id !== primaryUserId).map(u => u.id);
      
    } else if (options.primaryId && options.secondaryId) {
      primaryUserId = options.primaryId;
      secondaryUserIds = [options.secondaryId];
      
      // Validate these users exist
      const primaryProfile = await getProfile(primaryUserId);
      const secondaryProfile = await getProfile(options.secondaryId);
      
      console.log('User information:');
      if (primaryProfile) {
        console.log(`Primary user ${primaryUserId}: Type: ${primaryProfile.user_type}, Name: ${primaryProfile.full_name || 'none'}, Phone: ${primaryProfile.phone || 'none'}`);
      } else {
        console.log(`Primary user ${primaryUserId}: No profile found`);
      }
      
      if (secondaryProfile) {
        console.log(`Secondary user ${options.secondaryId}: Type: ${secondaryProfile.user_type}, Name: ${secondaryProfile.full_name || 'none'}, Phone: ${secondaryProfile.phone || 'none'}`);
      } else {
        console.log(`Secondary user ${options.secondaryId}: No profile found`);
      }
    }
    
    if (!primaryUserId || secondaryUserIds.length === 0) {
      console.error('Failed to identify users to merge');
      return;
    }

    // Assert primaryUserId is not null
    assertNotNull(primaryUserId, 'Primary user ID is null');
    
    // Get profiles for confirmation
    const primaryProfile = await getProfile(primaryUserId);
    console.log('\nPrimary account:');
    if (primaryProfile) {
      console.log(`ID: ${primaryUserId}`);
      console.log(`Type: ${primaryProfile.user_type}`);
      console.log(`Name: ${primaryProfile.full_name || 'none'}`);
      console.log(`Phone: ${primaryProfile.phone || 'none'}`);
      console.log(`Email: ${primaryProfile.email || 'none'}`);
    } else {
      console.log(`ID: ${primaryUserId} (no profile)`);
    }
    
    // Confirm before proceeding
    console.log(`\nWill keep user ${primaryUserId} and merge ${secondaryUserIds.length} account(s) into it`);
    if (options.dryRun) {
      console.log('DRY RUN MODE: No changes will be made');
    } else {
      const confirmed = await confirm('Proceed with account merging?');
      if (!confirmed) {
        console.log('Operation canceled');
        return;
      }
    }
    
    // Perform the merges
    for (const secondaryId of secondaryUserIds) {
      await mergeUserAccounts(primaryUserId, secondaryId, options.dryRun);
    }
    
    console.log('Account merging complete');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    rl.close();
  }
}

// Run the main function
main(); 