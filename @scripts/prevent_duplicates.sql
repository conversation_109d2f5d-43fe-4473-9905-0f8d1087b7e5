-- Add a function and trigger to prevent duplicate phone numbers in profiles
-- This should be applied after cleaning up existing duplicates

-- Function to check for phone number uniqueness across profiles
CREATE OR REPLACE FUNCTION check_phone_uniqueness()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    existing_count INTEGER;
BEGIN
    -- Only check if phone number is provided
    IF NEW.phone IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Check if phone number already exists in another profile
    SELECT COUNT(*)
    INTO existing_count
    FROM public.profiles
    WHERE 
        phone = NEW.phone
        AND id != NEW.id;  -- Exclude the current profile (for updates)
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'Phone number % is already in use by another profile', NEW.phone;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Create trigger to enforce phone uniqueness
CREATE TRIGGER ensure_phone_uniqueness
BEFORE INSERT OR UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION check_phone_uniqueness();

-- Comment on trigger and function
COMMENT ON FUNCTION check_phone_uniqueness() IS 
'Function to ensure phone numbers are unique across all profiles, preventing duplicates';

COMMENT ON TRIGGER ensure_phone_uniqueness ON public.profiles IS
'Trigger to prevent duplicate phone numbers from being inserted or updated in profiles';

-- Add an index to make phone lookups faster
CREATE INDEX IF NOT EXISTS profiles_phone_idx ON public.profiles(phone);

-- Add a note about how to disable the trigger if needed
/*
-- To temporarily disable this trigger (if needed for migration or bulk updates):
ALTER TABLE public.profiles DISABLE TRIGGER ensure_phone_uniqueness;

-- To re-enable the trigger:
ALTER TABLE public.profiles ENABLE TRIGGER ensure_phone_uniqueness;
*/ 