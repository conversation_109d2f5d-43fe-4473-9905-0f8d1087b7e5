# Supabase Auth Hook Configuration

After deploying the SQL function, you need to enable the hook in the Supabase dashboard:

## Custom Access Token Hook Configuration

1. Log in to the [Supabase Dashboard](https://app.supabase.com/)
2. Navigate to your project
3. Go to **Authentication → Hooks**
4. In the **Custom Access Token** section:
   - Enable the hook by clicking the toggle
   - Select "Database Function" as the type
   - Select "auth.jwt_claim" as the function
   - Click "Save"

## Important Notes

- Hooks in Supabase are executed at specific points in the authentication flow
- If you don't enable the hook in the dashboard, the function won't be called
- The configured hook must return a valid JSON object with the required claims
- Make sure your function has proper error handling to avoid auth failures

## Testing the Hook

To test if your hook is working correctly:

1. Try signing in with any method (email, phone, etc.)
2. If authentication succeeds, your hook is working
3. If you get an error about "output claims do not conform to the expected schema", your hook might be returning null or an invalid structure

## Required JWT Claims

According to Supabase documentation, these are the required fields for the JWT:

- `role`: The user's role (e.g., "authenticated", "customer", etc.)

Your hook should always return at least the required fields. 