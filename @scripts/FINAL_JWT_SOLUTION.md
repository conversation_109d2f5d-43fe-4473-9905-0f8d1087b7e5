# Final Solution: <PERSON><PERSON><PERSON> Claims Returning NULL

## Root Cause Identified

Our verification script revealed the core issue:

```
No hooks configured in settings
```

Despite having the SQL function properly defined in the database, the Supabase dashboard is not configured to use this function for JWT claims. This explains why our hook is working when called directly, but auth is failing with:

```
(NOBRIDGE) ERROR Email sign-in error: output claims do not conform to the expected schema: 
- (root): Invalid type. Expected: object, given: null
```

## Complete Solution

### 1. Database Function (Already Working)

Our SQL function is properly defined and returning a valid JWT claims object:

```sql
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
BEGIN
  -- Create a fully compliant return object
  result := jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
  
  -- Log for debugging
  BEGIN
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_call', 
      jsonb_build_object(
        'input', event,
        'output', result,
        'timestamp', now()
      )
    );
  EXCEPTION WHEN OTHERS THEN
    NULL;
  END;
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object('role', 'authenticated');
END;
$$;
```

### 2. Dashboard Configuration (REQUIRED)

You need to configure the hook in the Supabase Dashboard:

1. Log in to the Supabase dashboard at https://app.supabase.com
2. Navigate to your project
3. Go to Authentication → JWT Customization 
4. Toggle "Custom JWT Claims" to ON
5. In the "Custom Token Hook" field, enter exactly: `pg-functions://postgres/public/custom_access_token_hook`
6. Save the changes

### 3. Testing Your Solution

After performing steps 1 and 2:

1. Run the verification script: `npx ts-node verify_jwt_config.ts` 
2. Verify that "Hooks configuration" shows your hook is configured
3. Test authentication with your app or with the test script

## Troubleshooting Guide

If you still experience issues after making these changes:

1. **Dashboard Configuration**: Double-check spelling and ensure the hook URI is correct: `pg-functions://postgres/public/custom_access_token_hook`

2. **Database Permissions**: Ensure permissions are set correctly:
   ```sql
   GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
   GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
   ```

3. **Function Return Value**: The function MUST return a valid JWT claims object with at least the `role` field:
   ```sql
   -- At minimum, return this
   jsonb_build_object('role', 'authenticated')
   ```

4. **Test with Minimal Function**: If problems persist, try the ultra-minimal function:
   ```sql
   CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
   RETURNS jsonb
   LANGUAGE sql
   SECURITY DEFINER
   AS $$
     SELECT '{"role": "authenticated"}'::jsonb;
   $$;
   ```

5. **Force Token Refresh**: After changes, users need to log out and sign in again (or refresh their tokens) to get new claims.

## Confirmation of Fix

Once properly configured, you should see in your monitoring logs that the hook is being called during authentication, and your auth errors should disappear.

Remember that JWT claims are only read when tokens are issued or refreshed, not on every request. 