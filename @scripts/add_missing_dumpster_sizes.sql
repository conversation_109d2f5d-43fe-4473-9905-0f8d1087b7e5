-- Create temporary tables
CREATE TEMP TABLE temp_dumpster_types (
    id uuid,
    capacity numeric
);

CREATE TEMP TABLE temp_dumpster_sizes (
    id uuid,
    dumpster_type_id uuid
);

CREATE TEMP TABLE temp_dumpsters (
    id uuid,
    dumpster_type_id uuid
);

-- Create temporary table for first active partner
CREATE TEMP TABLE temp_first_partner AS
SELECT id FROM public.partners WHERE status = 'active' LIMIT 1;

-- Add missing dumpster types
INSERT INTO public.dumpster_types (
    id, name_en, description_en, capacity, max_weight, 
    suitable_waste_types, image_url
)
SELECT 
    gen_random_uuid(), name_en, description_en, capacity, max_weight, 
    suitable_waste_types, image_url
FROM (VALUES 
    (
        '15 Yard Container', 'Ideal for medium-sized renovation projects',
        15, 3000, ARRAY['construction', 'residential', 'renovation'],
        'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_20YD.png'
    ),
    (
        '20 Yard Container', 'Great for large home renovations and construction',
        20, 4000, ARRAY['construction', 'commercial', 'heavy_debris'],
        'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_20YD.png'
    ),
    (
        '30 Yard Container', 'Suitable for extra large construction projects',
        30, 6000, ARRAY['construction', 'commercial', 'industrial'],
        'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_30YD.png'
    ),
    (
        '40 Yard Container', 'Ideal for very large commercial projects',
        40, 8000, ARRAY['construction', 'commercial', 'industrial'],
        'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_40YD.png'
    )
) AS v(name_en, description_en, capacity, max_weight, suitable_waste_types, image_url)
WHERE NOT EXISTS (
    SELECT 1 FROM public.dumpster_types 
    WHERE capacity = v.capacity
)
RETURNING id, capacity;

-- Store the results in temp table
INSERT INTO temp_dumpster_types 
SELECT id, capacity FROM dumpster_types WHERE id IN (SELECT id FROM dumpster_types ORDER BY created_at DESC LIMIT 4);

-- Add missing dumpster sizes
INSERT INTO public.dumpster_sizes (
    id, dumpster_type_id, name, volume_cubic_yards,
    max_weight_pounds, length, width, height, description
)
SELECT 
    gen_random_uuid(),
    dt.id,
    dt.capacity || ' Yard',
    dt.capacity,
    CASE 
        WHEN dt.capacity = 15 THEN 3000
        WHEN dt.capacity = 20 THEN 4000
        WHEN dt.capacity = 30 THEN 6000
        ELSE 8000
    END,
    CASE 
        WHEN dt.capacity = 15 THEN 15
        WHEN dt.capacity = 20 THEN 16
        WHEN dt.capacity = 30 THEN 20
        ELSE 22
    END,
    8,
    CASE 
        WHEN dt.capacity = 15 THEN 4.5
        WHEN dt.capacity = 20 THEN 5
        WHEN dt.capacity = 30 THEN 6
        ELSE 8
    END,
    'Standard ' || dt.capacity || ' cubic yard container'
FROM temp_dumpster_types dt
WHERE NOT EXISTS (
    SELECT 1 FROM public.dumpster_sizes 
    WHERE name = dt.capacity || ' Yard'
)
RETURNING id, dumpster_type_id;

-- Store the results in temp table
INSERT INTO temp_dumpster_sizes
SELECT id, dumpster_type_id FROM dumpster_sizes WHERE id IN (SELECT id FROM dumpster_sizes ORDER BY created_at DESC LIMIT 4);

-- Add new dumpsters for the new sizes
INSERT INTO public.dumpsters (
    id, partner_id, dumpster_type_id, image_url,
    serial_number, status, price_per_load,
    name_en, description_en, rating, review_count
)
SELECT 
    gen_random_uuid(),
    (SELECT id FROM temp_first_partner),
    dt.id,
    CASE 
        WHEN dt.capacity = 15 THEN 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_20YD.png'
        WHEN dt.capacity = 20 THEN 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_20YD.png'
        WHEN dt.capacity = 30 THEN 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_30YD.png'
        ELSE 'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_40YD.png'
    END,
    'SN-' || SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8),
    'available',
    CASE 
        WHEN dt.capacity = 15 THEN 200.00
        WHEN dt.capacity = 20 THEN 250.00
        WHEN dt.capacity = 30 THEN 350.00
        ELSE 450.00
    END,
    dt.capacity || ' Yard Container',
    'Standard ' || dt.capacity || ' cubic yard container',
    4.5,
    FLOOR(RANDOM() * (100-20+1) + 20)::integer
FROM temp_dumpster_types dt
WHERE NOT EXISTS (
    SELECT 1 FROM public.dumpsters d
    JOIN public.dumpster_types t ON d.dumpster_type_id = t.id
    WHERE t.capacity = dt.capacity
)
RETURNING id, dumpster_type_id;

-- Store the results in temp table
INSERT INTO temp_dumpsters
SELECT id, dumpster_type_id FROM dumpsters WHERE id IN (SELECT id FROM dumpsters ORDER BY created_at DESC LIMIT 4);

-- Create dumpster size options for new dumpsters
INSERT INTO public.dumpster_size_options (
    dumpster_id,
    dumpster_size_id
)
SELECT 
    d.id,
    ds.id
FROM temp_dumpsters d
JOIN temp_dumpster_sizes ds ON d.dumpster_type_id = ds.dumpster_type_id
WHERE NOT EXISTS (
    SELECT 1 FROM public.dumpster_size_options
    WHERE dumpster_id = d.id AND dumpster_size_id = ds.id
);

-- Add pricing plans for new dumpster types
INSERT INTO public.pricing_plans (
    partner_id,
    dumpster_type_id,
    name,
    base_price,
    daily_rate,
    minimum_days,
    maximum_days,
    is_active
)
SELECT 
    (SELECT id FROM temp_first_partner),
    dt.id,
    'Standard ' || dt.capacity || ' Yard Plan',
    CASE 
        WHEN dt.capacity = 15 THEN 200.00
        WHEN dt.capacity = 20 THEN 250.00
        WHEN dt.capacity = 30 THEN 350.00
        ELSE 450.00
    END,
    CASE 
        WHEN dt.capacity = 15 THEN 75.00
        WHEN dt.capacity = 20 THEN 100.00
        WHEN dt.capacity = 30 THEN 150.00
        ELSE 200.00
    END,
    3,
    30,
    true
FROM temp_dumpster_types dt
WHERE NOT EXISTS (
    SELECT 1 FROM public.pricing_plans p
    WHERE p.dumpster_type_id = dt.id
);

-- Clean up temporary tables
DROP TABLE IF EXISTS temp_dumpster_types;
DROP TABLE IF EXISTS temp_dumpster_sizes;
DROP TABLE IF EXISTS temp_dumpsters;
DROP TABLE IF EXISTS temp_first_partner;
