// JWT Claims Test for Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqam5sbndpbnJtbnd5dndsaGoiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTcxMjIxNzQ0MCwiZXhwIjoyMDI3NzkzNDQwfQ.OQxeSP7v7sHVpxnrG_BLK-jEVlRkm86OOqdSHLWRVDI';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'password123';
const TEST_PHONE = process.env.TEST_PHONE || '+966582083888';

// Function to sleep for X milliseconds
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: true,
    detectSessionInUrl: false,
  }
});

// Function to decode a JWT token
function decodeJWT(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(Buffer.from(base64, 'base64').toString()
      .split('')
      .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
      .join(''));
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

// Test JWT claims
async function testJwtClaims() {
  try {
    console.log('\n--- Testing JWT Claims ---');
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('No active session found');
      return false;
    }
    
    console.log('\nCurrent JWT token info:');
    const accessToken = session.access_token;
    const decodedToken = decodeJWT(accessToken);
    console.log('- Token type:', decodedToken?.typ || 'Unknown');
    console.log('- User ID:', decodedToken?.sub || 'Unknown');
    console.log('- Role:', decodedToken?.role || 'Unknown');
    console.log('- App metadata:', JSON.stringify(decodedToken?.app_metadata || {}, null, 2));
    
    console.log('\nRefreshing session to get updated JWT claims...');
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('Error refreshing session:', error.message);
      return false;
    }
    
    if (!data.session) {
      console.log('No session after refresh');
      return false;
    }
    
    console.log('\nRefreshed JWT token info:');
    const newAccessToken = data.session.access_token;
    const newDecodedToken = decodeJWT(newAccessToken);
    console.log('- Token type:', newDecodedToken?.typ || 'Unknown');
    console.log('- User ID:', newDecodedToken?.sub || 'Unknown');
    console.log('- Role:', newDecodedToken?.role || 'Unknown');
    console.log('- App metadata:', JSON.stringify(newDecodedToken?.app_metadata || {}, null, 2));
    
    return true;
  } catch (error) {
    console.error('Error testing JWT claims:', error);
    return false;
return } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://ejjnlnwinrmnwnyvwlhj.supabase.co';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqam5sbndpbnJtbnd5dndsaGoiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTcxMjIxNzQ0MCwiZXhwIjoyMDI3NzkzNDQwfQ.OQxeSP7v7sHVpxnrG_BLK-jEVlRkm86OOqdSHLWRVDI';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'password123';
const TEST_PHONE = process.env.TEST_PHONE || '+966582083888';

// Function to sleep for X milliseconds
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: true,
    detectSessionInUrl: false,
  }
});

// Function to decode a JWT token
function decodeJWT(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(Buffer.from(base64, 'base64').toString()
      .split('')
      .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
      .join(''));
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

// Test JWT claims
async function testJwtClaims() {
  try {
    console.log('\n--- Testing JWT Claims ---');
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('No active session found');
      return false;
    }
    
    console.log('\nCurrent JWT token info:');
    const accessToken = session.access_token;
    const decodedToken = decodeJWT(accessToken);
    console.log('- Token type:', decodedToken?.typ || 'Unknown');
    console.log('- User ID:', decodedToken?.sub || 'Unknown');
    console.log('- Role:', decodedToken?.role || 'Unknown');
    console.log('- App metadata:', JSON.stringify(decodedToken?.app_metadata || {}, null, 2));
    
    console.log('\nRefreshing session to get updated JWT claims...');
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('Error refreshing session:', error.message);
      return false;
    }
    
    if (!data.session) {
      console.log('No session after refresh');
      return false;
    }
    
    console.log('\nRefreshed JWT token info:');
    const newAccessToken = data.session.access_token;
    const newDecodedToken = decodeJWT(newAccessToken);
    console.log('- Token type:', newDecodedToken?.typ || 'Unknown');
    console.log('- User ID:', newDecodedToken?.sub || 'Unknown');
    console.log('- Role:', newDecodedToken?.role || 'Unknown');
    console.log('- App metadata:', JSON.stringify(newDecodedToken?.app_metadata || {}, null, 2));
    
    return true;
  } catch (error) {
    console.error('Error testing JWT claims:', error);
    return false;
  }pa
}

// Try signing in with email
async function tryEmailSignIn() {
  try {
    console.log(`\n--- Signing in with email: ${TEST_EMAIL} ---`);
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
    });
    
    if (error) {
      console.error('Email sign-in error:', error.message);
      return false;
    }
    
    console.log('Email sign-in successful');
    return true;
  } catch (error) {
    console.error('Error during email sign-in:', error);
    return false;
  }
}

// Try signing in with phone OTP
async function tryPhoneSignIn() {
  try {
    console.log(`\n--- Sending OTP to phone: ${TEST_PHONE} ---`);
    const { data, error } = await supabase.auth.signInWithOtp({
      phone: TEST_PHONE,
    });
    
    if (error) {
      console.error('Phone OTP request error:', error.message);
      return false;
    }
    
    console.log('OTP sent successfully');
    console.log('NOTE: This test cannot complete the OTP verification automatically');
    return true;
  } catch (error) {
    console.error('Error sending phone OTP:', error);
    return false;
  }
}

// Main function
async function main() {
  console.log('=== JWT CLAIMS TEST ===');
  console.log('Supabase URL:', SUPABASE_URL);
  
  try {
    // Try email sign-in first
    const emailSignInSuccess = await tryEmailSignIn();
    
    if (emailSignInSuccess) {
      // Wait for JWT to be processed
      await sleep(2000);
      
      // Test JWT claims
      await testJwtClaims();
    } else {
      // Fall back to phone sign-in
      await tryPhoneSignIn();
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  } finally {
    // Sign out to clean up
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Error signing out:', error.message);
    } else {
      console.log('\nSigned out successfully');
    }
  }
}

// Run the main function
main()
  .then(() => console.log('\nTest completed'))
  .catch(err => console.error('Fatal error:', err)); 