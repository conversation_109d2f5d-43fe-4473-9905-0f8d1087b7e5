-- Create a function to add custom claims to JWT tokens
CREATE OR REPLACE FUNCTION auth.jwt_claim(request JSONB) RETURNS JSONB AS $$
DECLARE
  user_id UUID := request->>'sub';
  user_role TEXT;
BEGIN
  -- Get the user's role from the profiles table
  SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Return the custom claims to be added to the JWT
  RETURN jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO service_role;

-- Update the profiles table to ensure user_type is always set
ALTER TABLE public.profiles 
  ALTER COLUMN user_type SET DEFAULT 'customer',
  ADD CONSTRAINT valid_user_type CHECK (user_type IN ('customer', 'partner', 'admin', 'driver'));

-- Create a trigger to update JWT claims when user_type changes
CREATE OR REPLACE FUNCTION public.handle_user_type_change()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.user_type IS DISTINCT FROM NEW.user_type THEN
    -- Force refresh of JWT claims by updating auth.users
    UPDATE auth.users SET updated_at = now() WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_user_type_change
  AFTER UPDATE ON public.profiles
  FOR EACH ROW
  WHEN (OLD.user_type IS DISTINCT FROM NEW.user_type)
  EXECUTE FUNCTION public.handle_user_type_change();

-- Create helper functions for role checking
CREATE OR REPLACE FUNCTION is_admin(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_partner(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'partner'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_driver(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'driver'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_customer(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'customer'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to set default role for new users
CREATE OR REPLACE FUNCTION public.set_default_role()
RETURNS TRIGGER AS $$
BEGIN
  -- Set user_type to 'customer' for new users if not specified
  IF NEW.user_type IS NULL THEN
    NEW.user_type := 'customer';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to set default role for new profiles
CREATE TRIGGER set_default_role_trigger
  BEFORE INSERT ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.set_default_role();

-- Function to ensure profiles exist for all users
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  -- Create a profile entry if it doesn't exist
  INSERT INTO public.profiles (id, user_type, created_at, updated_at)
  VALUES (NEW.id, 'customer', NOW(), NOW())
  ON CONFLICT (id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user(); 