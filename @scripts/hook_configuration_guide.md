# Configuring the Custom Access Token Hook in Supabase

We've created a custom access token hook function that follows the exact specifications in the Supabase documentation. Now we need to configure it in the Supabase Dashboard to make it active.

## Step 1: Access the Supabase Dashboard

1. Go to [https://app.supabase.com/](https://app.supabase.com/)
2. Select your project

## Step 2: Navigate to Authentication Hooks

1. In the left sidebar, click on **Authentication**
2. Click on **Hooks** in the submenu

## Step 3: Configure the Custom Access Token Hook

1. Find the **Custom Access Token** section
2. Toggle the hook to **Enabled**
3. Select **Database Function** from the dropdown
4. Choose function: **public.custom_access_token_hook**
5. Click **Save**

![Custom Access Token Hook Configuration](https://supabase.com/docs/img/auth/hook-custom-access-token-pg.png)

## Step 4: Test the Hook

After configuring the hook, you should test it:

1. Try to sign in using any authentication method (SMS, email, etc.)
2. If the authentication succeeds, the hook is working correctly
3. Check the `public.jwt_hook_logs` table to verify the hook was called:

```sql
SELECT * FROM public.jwt_hook_logs ORDER BY timestamp DESC LIMIT 5;
```

## Troubleshooting

If you encounter issues with authentication after configuring the hook, check the following:

1. **Hook is returning null**: Ensure the hook always returns a valid JSON object with required claims
2. **Permission issues**: Verify the permissions are set correctly:
   ```sql
   GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
   GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
   ```
3. **Function location**: Make sure the function is in the `public` schema and named exactly `custom_access_token_hook`

## Required JWT Claims

According to the Supabase documentation, JWT claims must include at minimum:

- `role`: The user's role in the system

## Additional Information

For more details about auth hooks, refer to the Supabase documentation:
- [Auth Hooks Overview](https://supabase.com/docs/guides/auth/auth-hooks)
- [Custom Access Token Hook](https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook)
- [JWT and RBAC](https://supabase.com/docs/guides/database/postgres/custom-claims-and-role-based-access-control-rbac) 