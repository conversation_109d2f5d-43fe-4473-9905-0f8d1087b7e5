-- Custom Access Token Hook following Supabase Documentation
-- Reference: https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS auth.jwt_claim;

-- The function must be named exactly "jwt_claim" and accept a "request" parameter
CREATE OR REPLACE FUNCTION auth.jwt_claim(request jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id UUID;
  user_role TEXT;
BEGIN
  -- Get the user_id from the request
  user_id := (request->>'sub')::UUID;
  
  -- Log request for debugging
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_claim_request', jsonb_build_object(
    'user_id', user_id,
    'timestamp', now(),
    'request', request
  ));
  
  -- Get the user's role from the profiles table if it exists
  BEGIN
    SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  EXCEPTION WHEN OTHERS THEN
    -- Default to customer if any error occurs
    user_role := 'customer';
  END;
  
  -- If no role is found, default to 'customer'
  IF user_role IS NULL THEN
    user_role := 'customer';
  END IF;
  
  -- Return JWT claims following the exact structure required by Supabase
  -- Required claims must include role at minimum
  RETURN jsonb_build_object(
    'role', user_role,
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
END;
$$;

-- Grant permissions as specified in the documentation
GRANT EXECUTE ON FUNCTION auth.jwt_claim TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
REVOKE EXECUTE ON FUNCTION auth.jwt_claim FROM authenticated, anon, public;

-- Log the fix
INSERT INTO public.logs (event_type, details)
VALUES ('official_jwt_hook_implementation', jsonb_build_object(
  'timestamp', now(),
  'source', 'supabase_documentation',
  'url', 'https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook'
)); 