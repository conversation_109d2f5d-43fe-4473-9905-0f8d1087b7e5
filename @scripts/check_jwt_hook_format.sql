-- Check JWT hook behavior with different input formats

-- First, check function signature
\df public.custom_access_token_hook

-- Create a special test table to track different input formats
CREATE TABLE IF NOT EXISTS public.jwt_hook_tests (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ DEFAULT now(),
    test_name TEXT,
    input_data JSONB,
    result_data JSONB,
    result_type TEXT,
    success BOOLEAN
);

-- Clear previous test results
TRUNCATE public.jwt_hook_tests;

-- Test 1: Empty JSON
INSERT INTO public.jwt_hook_tests (test_name, input_data, result_data, result_type, success)
SELECT 
    'Empty JSON',
    '{}'::jsonb,
    public.custom_access_token_hook('{}'::jsonb),
    jsonb_typeof(public.custom_access_token_hook('{}'::jsonb)),
    public.custom_access_token_hook('{}'::jsonb) IS NOT NULL;

-- Test 2: Null JSON
INSERT INTO public.jwt_hook_tests (test_name, input_data, result_data, result_type, success)
SELECT 
    'Null JSON',
    'null'::jsonb,
    public.custom_access_token_hook('null'::jsonb),
    jsonb_typeof(public.custom_access_token_hook('null'::jsonb)),
    public.custom_access_token_hook('null'::jsonb) IS NOT NULL;

-- Test 3: Actual expected format from Supabase Auth
INSERT INTO public.jwt_hook_tests (test_name, input_data, result_data, result_type, success)
SELECT 
    'Supabase Format',
    '{"sub":"00000000-0000-0000-0000-000000000000","exp":1712217440,"iat":1712217440,"iss":"supabase-demo","aud":"authenticated"}'::jsonb,
    public.custom_access_token_hook('{"sub":"00000000-0000-0000-0000-000000000000","exp":1712217440,"iat":1712217440,"iss":"supabase-demo","aud":"authenticated"}'::jsonb),
    jsonb_typeof(public.custom_access_token_hook('{"sub":"00000000-0000-0000-0000-000000000000","exp":1712217440,"iat":1712217440,"iss":"supabase-demo","aud":"authenticated"}'::jsonb)),
    public.custom_access_token_hook('{"sub":"00000000-0000-0000-0000-000000000000","exp":1712217440,"iat":1712217440,"iss":"supabase-demo","aud":"authenticated"}'::jsonb) IS NOT NULL;

-- Test 4: Minimal but valid JSON
INSERT INTO public.jwt_hook_tests (test_name, input_data, result_data, result_type, success)
SELECT 
    'Minimal JSON',
    '{"test":true}'::jsonb,
    public.custom_access_token_hook('{"test":true}'::jsonb),
    jsonb_typeof(public.custom_access_token_hook('{"test":true}'::jsonb)),
    public.custom_access_token_hook('{"test":true}'::jsonb) IS NOT NULL;

-- View test results
SELECT * FROM public.jwt_hook_tests; 