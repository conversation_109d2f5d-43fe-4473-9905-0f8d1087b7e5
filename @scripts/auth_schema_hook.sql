-- Create JWT hook in the auth schema like the working SMS hook

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS auth.jwt_claims_hook;

-- Create the JWT hook in the auth schema
CREATE OR REPLACE FUNCTION auth.jwt_claims_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id uuid;
  result jsonb;
BEGIN
  -- Get user_id from event
  user_id := (event ->> 'sub')::uuid;
  
  -- Log the hook call
  BEGIN
    INSERT INTO public.logs (event_type, details)
    VALUES ('auth_schema_jwt_hook_call', jsonb_build_object(
      'event', event,
      'timestamp', now(),
      'user_id', user_id
    ));
  EXCEPTION WHEN OTHERS THEN
    -- Silently handle log errors
    NULL;
  END;
  
  -- Create a simple claims object
  result := jsonb_build_object(
    'role', 'authenticated',
    'app_metadata', jsonb_build_object(
      'user_role', 'customer'
    )
  );
  
  -- Log the result
  BEGIN
    INSERT INTO public.logs (event_type, details)
    VALUES ('auth_schema_jwt_hook_result', result);
  EXCEPTION WHEN OTHERS THEN
    NULL;
  END;
  
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  -- Log any errors
  BEGIN
    INSERT INTO public.logs (event_type, details)
    VALUES ('auth_schema_jwt_hook_error', jsonb_build_object(
      'error', SQLERRM,
      'event', event
    ));
  EXCEPTION WHEN OTHERS THEN
    NULL;
  END;
  
  -- Return a minimal valid object even on error
  RETURN jsonb_build_object('role', 'authenticated');
END;
$$;

-- Set required permissions
GRANT EXECUTE ON FUNCTION auth.jwt_claims_hook TO supabase_auth_admin;

-- Test the function
SELECT auth.jwt_claims_hook('{"sub":"00000000-0000-0000-0000-000000000000"}'::jsonb);

-- Instructions:
-- 1. Run this script to create the function in the auth schema
-- 2. In Supabase Dashboard -> Authentication -> Hooks
-- 3. Configure JWT Claims Hook to use:
--    - Schema: auth
--    - Function: jwt_claims_hook 