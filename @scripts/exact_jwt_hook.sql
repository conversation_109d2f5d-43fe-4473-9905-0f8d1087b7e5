-- Creating an exact version of the JWT hook according to Supabase docs
-- https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook

CREATE OR REPLACE FUNCTION public.jwt_claims_hook_v2(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  -- Get the user_id from the event data
  user_id uuid = (event ->> 'sub')::uuid;
  -- Default role
  user_role text = 'customer';
  -- Return value that will be added to the JWT
  result jsonb;
BEGIN
  -- Insert the role information into the JWT
  result := jsonb_build_object(
    'role', 'authenticated', 
    'app_metadata', jsonb_build_object(
      'user_role', user_role
    )
  );
  
  -- Log that the hook was called
  INSERT INTO public.logs (event_type, details)
  VALUES ('jwt_hook_v2_call', jsonb_build_object(
    'input', event,
    'output', result,
    'user_id', user_id,
    'timestamp', now()
  ));
  
  -- Return the jwt
  RETURN result;
EXCEPTION
  WHEN OTHERS THEN
    -- Log any errors
    INSERT INTO public.logs (event_type, details)
    VALUES ('jwt_hook_v2_error', jsonb_build_object(
      'error', SQLERRM,
      'user_id', user_id,
      'timestamp', now()
    ));
    
    -- Always return a valid object even on error
    RETURN jsonb_build_object('role', 'authenticated');
END;
$$;

-- Set permissions
GRANT EXECUTE ON FUNCTION public.jwt_claims_hook_v2 TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
GRANT USAGE ON SEQUENCE public.logs_id_seq TO supabase_auth_admin;
GRANT INSERT ON TABLE public.logs TO supabase_auth_admin;

-- Test call with sample UUID
SELECT public.jwt_claims_hook_v2('{"sub":"00000000-0000-0000-0000-000000000000"}'::jsonb);

-- Instructions:
-- 1. Run this script to create the new hook
-- 2. In Supabase Dashboard -> Authentication -> Hooks
-- 3. Configure JWT Claims Hook to use:
--    - Schema: public
--    - Function: jwt_claims_hook_v2 