--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.12 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA public;


--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON SCHEMA public IS 'standard public schema';


--
-- Name: handle_updated_at(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
    new.updated_at = now();
    return new;
end;
$$;


--
-- Name: is_admin(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_admin(uid uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'admin'
  );
END;
$$;


--
-- Name: is_partner(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_partner(uid uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM partners 
    WHERE profile_id = uid
  );
END;
$$;


--
-- Name: update_order_total(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_order_total() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  UPDATE orders
  SET total_price = (
    SELECT d.price_per_load * COUNT(dl.id)
    FROM dumpsters d
    JOIN dumpster_loads dl ON dl.order_id = NEW.order_id
    WHERE d.id = orders.dumpster_id
    GROUP BY d.price_per_load
  )
  WHERE id = NEW.order_id;
  RETURN NEW;
END;
$$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$;


--
-- Name: update_user_settings_updated_at(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_user_settings_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: addresses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.addresses (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    street_address text,
    city text,
    state text,
    zip_code text,
    latitude double precision,
    longitude double precision,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    type text,
    name text,
    is_default boolean
);


--
-- Name: ai_conversations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ai_conversations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    user_id uuid,
    conversation_data jsonb DEFAULT '{}'::jsonb,
    intent_classification text,
    extracted_requirements jsonb DEFAULT '{}'::jsonb,
    recommended_dumpster_types uuid[],
    conversation_summary text
);


--
-- Name: ai_feedback; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ai_feedback (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    conversation_id uuid,
    user_id uuid,
    feedback_type text,
    feedback_text text,
    is_processed boolean DEFAULT false,
    CONSTRAINT ai_feedback_feedback_type_check CHECK ((feedback_type = ANY (ARRAY['helpful'::text, 'not_helpful'::text, 'suggestion'::text])))
);


--
-- Name: cash_transactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cash_transactions (
    id uuid NOT NULL,
    order_id uuid,
    driver_id uuid,
    amount numeric,
    status text,
    collection_time timestamp without time zone,
    reconciliation_time timestamp without time zone,
    notes text
);


--
-- Name: countries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.countries (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    native_name character varying(100),
    code character varying(2) NOT NULL,
    dial_code character varying(10) NOT NULL,
    flag_url character varying(255) NOT NULL,
    flag_emoji character varying(10) NOT NULL,
    rtl boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: countries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.countries_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: countries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.countries_id_seq OWNED BY public.countries.id;


--
-- Name: discounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.discounts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    partner_id uuid,
    code text NOT NULL,
    description text,
    discount_type text,
    discount_value numeric NOT NULL,
    start_date timestamp with time zone,
    end_date timestamp with time zone,
    max_uses integer,
    current_uses integer DEFAULT 0,
    min_order_value numeric DEFAULT 0,
    applicable_dumpster_types uuid[],
    is_active boolean DEFAULT true,
    CONSTRAINT discounts_discount_type_check CHECK ((discount_type = ANY (ARRAY['percentage'::text, 'fixed_amount'::text])))
);


--
-- Name: driver_assignments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_assignments (
    id uuid NOT NULL,
    driver_id uuid,
    vehicle_id uuid,
    order_id uuid,
    status text,
    assigned_at timestamp without time zone,
    completed_at timestamp without time zone
);


--
-- Name: driver_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_documents (
    id uuid NOT NULL,
    driver_id uuid,
    document_type text,
    document_url text,
    expiry_date date,
    status text,
    created_at timestamp without time zone
);


--
-- Name: driver_incidents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_incidents (
    id uuid NOT NULL,
    driver_id uuid,
    order_id uuid,
    incident_type text,
    description text,
    reported_at timestamp without time zone,
    resolution text,
    resolved_at timestamp without time zone,
    status text,
    created_at timestamp without time zone DEFAULT now()
);


--
-- Name: driver_performance_metrics; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_performance_metrics (
    id uuid NOT NULL,
    driver_id uuid,
    metric_type text,
    metric_name text,
    value numeric,
    target numeric,
    period_start timestamp without time zone,
    period_end timestamp without time zone,
    created_at timestamp without time zone DEFAULT now()
);


--
-- Name: driver_schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_schedules (
    id uuid NOT NULL,
    driver_id uuid,
    date date,
    shift_start time without time zone,
    shift_end time without time zone,
    break_start time without time zone,
    break_end time without time zone,
    mwan_zone text,
    status text,
    created_at timestamp without time zone DEFAULT now()
);


--
-- Name: drivers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.drivers (
    id uuid NOT NULL,
    partner_id uuid,
    profile_id uuid,
    status text,
    license_number text,
    license_expiry date,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


--
-- Name: dumpster_features; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_features (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    feature text NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_images; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_images (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    image_url text NOT NULL,
    sort_order integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_loads; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_loads (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    order_id uuid,
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    scheduled_date timestamp with time zone,
    completed_date timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_size_options; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_size_options (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    dumpster_size_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_sizes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_sizes (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_type_id uuid,
    name text NOT NULL,
    volume_cubic_yards numeric NOT NULL,
    max_weight_pounds numeric NOT NULL,
    length numeric NOT NULL,
    width numeric NOT NULL,
    height numeric NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    dimensions jsonb DEFAULT '{}'::jsonb,
    capacity numeric,
    max_weight numeric,
    image_url text,
    suitable_waste_types text[] DEFAULT '{}'::text[],
    notes text,
    name_ar text,
    description_ar text,
    name_en text NOT NULL,
    description_en text
);


--
-- Name: dumpster_waste_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_waste_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    waste_type_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpsters; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpsters (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    partner_id uuid,
    dumpster_type_id uuid,
    image_url text NOT NULL,
    serial_number text,
    status text DEFAULT 'available'::text,
    current_location jsonb DEFAULT '{"latitude": 0, "longitude": 0}'::jsonb,
    last_maintenance_date timestamp with time zone,
    next_maintenance_date timestamp with time zone,
    purchase_date timestamp with time zone,
    purchase_cost numeric,
    lifetime_revenue numeric DEFAULT 0,
    lifetime_orders integer DEFAULT 0,
    is_available boolean DEFAULT true,
    next_available_date date,
    rating numeric,
    review_count integer DEFAULT 0,
    name_ar text,
    description_ar text,
    description_en text,
    name_en text,
    price_per_load numeric(10,2) DEFAULT 0.00 NOT NULL,
    CONSTRAINT dumpsters_status_check CHECK ((status = ANY (ARRAY['available'::text, 'in_use'::text, 'maintenance'::text, 'decommissioned'::text])))
);


--
-- Name: installation_locations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.installation_locations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name_ar text NOT NULL,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    name_en text NOT NULL
);


--
-- Name: mwan_schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.mwan_schedules (
    id uuid NOT NULL,
    city_zone text,
    operation_type text,
    start_time time without time zone,
    end_time time without time zone,
    days_of_week text[],
    is_active boolean
);


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    user_id uuid,
    type text,
    title text NOT NULL,
    message text NOT NULL,
    related_entity_type text,
    related_entity_id uuid,
    is_read boolean DEFAULT false,
    read_at timestamp with time zone,
    delivery_channels text[],
    delivery_status jsonb DEFAULT '{}'::jsonb,
    CONSTRAINT notifications_type_check CHECK ((type = ANY (ARRAY['order_update'::text, 'payment'::text, 'system'::text, 'promotion'::text])))
);


--
-- Name: order_status_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_status_history (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    order_id uuid,
    status text NOT NULL,
    notes text,
    updated_by uuid
);


--
-- Name: orders; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.orders (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    customer_id uuid DEFAULT auth.uid(),
    partner_id uuid,
    dumpster_id uuid,
    pricing_plan_id uuid,
    discount_id uuid,
    status text DEFAULT 'pending'::text,
    delivery_address text NOT NULL,
    delivery_coordinates jsonb DEFAULT '{"latitude": 0, "longitude": 0}'::jsonb,
    delivery_instructions text,
    delivery_date timestamp with time zone,
    scheduled_pickup_date timestamp with time zone,
    actual_pickup_date timestamp with time zone,
    rental_duration_days integer,
    waste_type text,
    estimated_weight numeric,
    actual_weight numeric,
    base_price numeric,
    discount_amount numeric DEFAULT 0,
    additional_fees jsonb DEFAULT '{}'::jsonb,
    tax_amount numeric DEFAULT 0,
    total_amount numeric,
    payment_status text DEFAULT 'pending'::text,
    payment_method text,
    special_requirements text,
    ai_conversation_id uuid,
    CONSTRAINT orders_payment_method_check CHECK ((payment_method = ANY (ARRAY['credit_card'::text, 'cash'::text, 'bank_transfer'::text]))),
    CONSTRAINT orders_payment_status_check CHECK ((payment_status = ANY (ARRAY['pending'::text, 'partial'::text, 'paid'::text, 'refunded'::text]))),
    CONSTRAINT orders_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'confirmed'::text, 'delivered'::text, 'in_use'::text, 'pickup_scheduled'::text, 'completed'::text, 'cancelled'::text])))
);


--
-- Name: partners; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.partners (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    profile_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    company_name text NOT NULL,
    business_license text,
    tax_id text,
    service_areas jsonb DEFAULT '[]'::jsonb,
    rating numeric DEFAULT 0,
    total_ratings integer DEFAULT 0,
    is_verified boolean DEFAULT false,
    verification_date timestamp with time zone,
    status text DEFAULT 'pending'::text,
    commission_rate numeric DEFAULT 0,
    bank_details jsonb,
    contact_person text,
    contact_email text,
    contact_phone text,
    whatsapp_business_id text,
    CONSTRAINT partners_status_check CHECK ((status = ANY (ARRAY['active'::text, 'pending'::text, 'suspended'::text])))
);


--
-- Name: payments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payments (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    order_id uuid,
    amount numeric NOT NULL,
    payment_method text,
    status text DEFAULT 'pending'::text,
    transaction_id text,
    payment_date timestamp with time zone,
    payment_details jsonb DEFAULT '{}'::jsonb,
    refund_amount numeric DEFAULT 0,
    refund_date timestamp with time zone,
    refund_reason text,
    CONSTRAINT payments_payment_method_check CHECK ((payment_method = ANY (ARRAY['credit_card'::text, 'cash'::text, 'bank_transfer'::text]))),
    CONSTRAINT payments_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'completed'::text, 'failed'::text, 'refunded'::text])))
);


--
-- Name: pricing_plans; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pricing_plans (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    partner_id uuid,
    dumpster_type_id uuid,
    name text NOT NULL,
    base_price numeric NOT NULL,
    daily_rate numeric NOT NULL,
    minimum_days integer DEFAULT 1,
    maximum_days integer,
    overage_fee_per_day numeric,
    weight_limit numeric,
    overage_fee_per_ton numeric,
    is_active boolean DEFAULT true,
    special_instructions text
);


--
-- Name: profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.profiles (
    id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    email text,
    phone text,
    full_name text,
    avatar_url text,
    user_type text DEFAULT ''::text,
    CONSTRAINT profiles_user_type_check CHECK ((user_type = ANY (ARRAY['customer'::text, 'partner'::text, 'admin'::text])))
);


--
-- Name: reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reviews (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    order_id uuid,
    customer_id uuid,
    partner_id uuid,
    rating integer,
    review_text text,
    partner_response text,
    partner_response_date timestamp with time zone,
    is_published boolean DEFAULT true,
    CONSTRAINT reviews_rating_check CHECK (((rating >= 1) AND (rating <= 5)))
);


--
-- Name: user_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_settings (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    settings jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT user_settings_required_fields CHECK (((settings ? 'theme'::text) AND (settings ? 'notifications'::text) AND (settings ? 'location_sharing'::text) AND (settings ? 'privacy'::text))),
    CONSTRAINT user_settings_theme_values CHECK (((settings ->> 'theme'::text) = ANY (ARRAY['light'::text, 'dark'::text, 'system'::text])))
);


--
-- Name: vehicles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.vehicles (
    id uuid NOT NULL,
    partner_id uuid,
    vehicle_number text,
    license_plate text,
    maintenance_due_date date,
    status text,
    created_at timestamp without time zone
);


--
-- Name: waste_tags; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.waste_tags (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    name_en text NOT NULL,
    description_en text,
    name_ar text,
    description_ar text
);


--
-- Name: waste_type_tags; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.waste_type_tags (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    waste_type_id uuid NOT NULL,
    waste_tag_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: waste_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.waste_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    image_url text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    name_ar text,
    description_ar text,
    name_en text NOT NULL,
    description_en text
);


--
-- Name: countries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.countries ALTER COLUMN id SET DEFAULT nextval('public.countries_id_seq'::regclass);


--
-- Name: addresses addresses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_pkey PRIMARY KEY (id);


--
-- Name: ai_conversations ai_conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_conversations
    ADD CONSTRAINT ai_conversations_pkey PRIMARY KEY (id);


--
-- Name: ai_feedback ai_feedback_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_feedback
    ADD CONSTRAINT ai_feedback_pkey PRIMARY KEY (id);


--
-- Name: cash_transactions cash_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cash_transactions
    ADD CONSTRAINT cash_transactions_pkey PRIMARY KEY (id);


--
-- Name: countries countries_code_unique; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.countries
    ADD CONSTRAINT countries_code_unique UNIQUE (code);


--
-- Name: countries countries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.countries
    ADD CONSTRAINT countries_pkey PRIMARY KEY (id);


--
-- Name: discounts discounts_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_code_key UNIQUE (code);


--
-- Name: discounts discounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_pkey PRIMARY KEY (id);


--
-- Name: driver_assignments driver_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_pkey PRIMARY KEY (id);


--
-- Name: driver_documents driver_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_documents
    ADD CONSTRAINT driver_documents_pkey PRIMARY KEY (id);


--
-- Name: driver_incidents driver_incidents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_incidents
    ADD CONSTRAINT driver_incidents_pkey PRIMARY KEY (id);


--
-- Name: driver_performance_metrics driver_performance_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_performance_metrics
    ADD CONSTRAINT driver_performance_metrics_pkey PRIMARY KEY (id);


--
-- Name: driver_schedules driver_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_schedules
    ADD CONSTRAINT driver_schedules_pkey PRIMARY KEY (id);


--
-- Name: drivers drivers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_pkey PRIMARY KEY (id);


--
-- Name: dumpster_features dumpster_features_dumpster_id_feature_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_features
    ADD CONSTRAINT dumpster_features_dumpster_id_feature_key UNIQUE (dumpster_id, feature);


--
-- Name: dumpster_features dumpster_features_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_features
    ADD CONSTRAINT dumpster_features_pkey PRIMARY KEY (id);


--
-- Name: dumpster_images dumpster_images_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_images
    ADD CONSTRAINT dumpster_images_pkey PRIMARY KEY (id);


--
-- Name: dumpster_loads dumpster_loads_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_loads
    ADD CONSTRAINT dumpster_loads_pkey PRIMARY KEY (id);


--
-- Name: dumpster_size_options dumpster_size_options_dumpster_id_dumpster_size_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_size_options
    ADD CONSTRAINT dumpster_size_options_dumpster_id_dumpster_size_id_key UNIQUE (dumpster_id, dumpster_size_id);


--
-- Name: dumpster_size_options dumpster_size_options_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_size_options
    ADD CONSTRAINT dumpster_size_options_pkey PRIMARY KEY (id);


--
-- Name: dumpster_sizes dumpster_sizes_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_sizes
    ADD CONSTRAINT dumpster_sizes_name_key UNIQUE (name);


--
-- Name: dumpster_sizes dumpster_sizes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_sizes
    ADD CONSTRAINT dumpster_sizes_pkey PRIMARY KEY (id);


--
-- Name: dumpster_types dumpster_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_types
    ADD CONSTRAINT dumpster_types_pkey PRIMARY KEY (id);


--
-- Name: dumpster_waste_types dumpster_waste_types_dumpster_id_waste_type_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_dumpster_id_waste_type_id_key UNIQUE (dumpster_id, waste_type_id);


--
-- Name: dumpster_waste_types dumpster_waste_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_pkey PRIMARY KEY (id);


--
-- Name: dumpsters dumpsters_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpsters
    ADD CONSTRAINT dumpsters_pkey PRIMARY KEY (id);


--
-- Name: installation_locations installation_locations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.installation_locations
    ADD CONSTRAINT installation_locations_pkey PRIMARY KEY (id);


--
-- Name: mwan_schedules mwan_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.mwan_schedules
    ADD CONSTRAINT mwan_schedules_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: order_status_history order_status_history_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_pkey PRIMARY KEY (id);


--
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- Name: partners partners_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.partners
    ADD CONSTRAINT partners_pkey PRIMARY KEY (id);


--
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- Name: pricing_plans pricing_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pricing_plans
    ADD CONSTRAINT pricing_plans_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);


--
-- Name: reviews reviews_order_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_order_id_key UNIQUE (order_id);


--
-- Name: reviews reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_pkey PRIMARY KEY (id);


--
-- Name: user_settings user_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_pkey PRIMARY KEY (id);


--
-- Name: vehicles vehicles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vehicles
    ADD CONSTRAINT vehicles_pkey PRIMARY KEY (id);


--
-- Name: waste_tags waste_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_tags
    ADD CONSTRAINT waste_tags_pkey PRIMARY KEY (id);


--
-- Name: waste_type_tags waste_type_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_pkey PRIMARY KEY (id);


--
-- Name: waste_type_tags waste_type_tags_waste_type_id_waste_tag_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_waste_type_id_waste_tag_id_key UNIQUE (waste_type_id, waste_tag_id);


--
-- Name: waste_types waste_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_types
    ADD CONSTRAINT waste_types_pkey PRIMARY KEY (id);


--
-- Name: countries_code_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX countries_code_idx ON public.countries USING btree (code);


--
-- Name: countries_dial_code_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX countries_dial_code_idx ON public.countries USING btree (dial_code);


--
-- Name: idx_addresses_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_addresses_user_id ON public.addresses USING btree (user_id);


--
-- Name: idx_cash_transactions_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cash_transactions_driver_id ON public.cash_transactions USING btree (driver_id);


--
-- Name: idx_cash_transactions_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cash_transactions_order_id ON public.cash_transactions USING btree (order_id);


--
-- Name: idx_driver_assignments_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_assignments_driver_id ON public.driver_assignments USING btree (driver_id);


--
-- Name: idx_driver_assignments_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_assignments_order_id ON public.driver_assignments USING btree (order_id);


--
-- Name: idx_driver_assignments_vehicle_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_assignments_vehicle_id ON public.driver_assignments USING btree (vehicle_id);


--
-- Name: idx_driver_documents_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_documents_driver_id ON public.driver_documents USING btree (driver_id);


--
-- Name: idx_driver_incidents_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_incidents_driver_id ON public.driver_incidents USING btree (driver_id);


--
-- Name: idx_driver_incidents_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_incidents_order_id ON public.driver_incidents USING btree (order_id);


--
-- Name: idx_driver_performance_metrics_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_performance_metrics_driver_id ON public.driver_performance_metrics USING btree (driver_id);


--
-- Name: idx_driver_schedules_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_schedules_driver_id ON public.driver_schedules USING btree (driver_id);


--
-- Name: idx_drivers_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_partner_id ON public.drivers USING btree (partner_id);


--
-- Name: idx_drivers_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_profile_id ON public.drivers USING btree (profile_id);


--
-- Name: idx_dumpster_types_name_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpster_types_name_ar ON public.dumpster_types USING btree (name_ar);


--
-- Name: idx_dumpster_types_name_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpster_types_name_en ON public.dumpster_types USING btree (name_en);


--
-- Name: idx_dumpsters_availability; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_availability ON public.dumpsters USING btree (is_available);


--
-- Name: idx_dumpsters_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_partner_id ON public.dumpsters USING btree (partner_id);


--
-- Name: idx_dumpsters_rating; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_rating ON public.dumpsters USING btree (rating);


--
-- Name: idx_dumpsters_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_status ON public.dumpsters USING btree (status);


--
-- Name: idx_notifications_is_read; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_notifications_is_read ON public.notifications USING btree (is_read);


--
-- Name: idx_notifications_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_notifications_user_id ON public.notifications USING btree (user_id);


--
-- Name: idx_orders_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_customer_id ON public.orders USING btree (customer_id);


--
-- Name: idx_orders_delivery_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_delivery_date ON public.orders USING btree (delivery_date);


--
-- Name: idx_orders_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_partner_id ON public.orders USING btree (partner_id);


--
-- Name: idx_orders_payment_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_payment_status ON public.orders USING btree (payment_status);


--
-- Name: idx_orders_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_status ON public.orders USING btree (status);


--
-- Name: idx_partners_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_partners_profile_id ON public.partners USING btree (profile_id);


--
-- Name: idx_partners_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_partners_status ON public.partners USING btree (status);


--
-- Name: idx_payments_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_payments_order_id ON public.payments USING btree (order_id);


--
-- Name: idx_payments_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_payments_status ON public.payments USING btree (status);


--
-- Name: idx_pricing_plans_dumpster_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_pricing_plans_dumpster_type_id ON public.pricing_plans USING btree (dumpster_type_id);


--
-- Name: idx_pricing_plans_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_pricing_plans_is_active ON public.pricing_plans USING btree (is_active);


--
-- Name: idx_pricing_plans_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_pricing_plans_partner_id ON public.pricing_plans USING btree (partner_id);


--
-- Name: idx_profiles_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_email ON public.profiles USING btree (email);


--
-- Name: idx_profiles_phone; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_phone ON public.profiles USING btree (phone);


--
-- Name: idx_profiles_user_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_user_type ON public.profiles USING btree (user_type);


--
-- Name: idx_reviews_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_reviews_customer_id ON public.reviews USING btree (customer_id);


--
-- Name: idx_reviews_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_reviews_partner_id ON public.reviews USING btree (partner_id);


--
-- Name: idx_reviews_rating; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_reviews_rating ON public.reviews USING btree (rating);


--
-- Name: idx_user_settings_theme; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_settings_theme ON public.user_settings USING btree (((settings ->> 'theme'::text)));


--
-- Name: idx_user_settings_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_settings_user_id ON public.user_settings USING btree (user_id);


--
-- Name: idx_vehicles_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_vehicles_partner_id ON public.vehicles USING btree (partner_id);


--
-- Name: idx_waste_tags_name_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_tags_name_ar ON public.waste_tags USING btree (name_ar);


--
-- Name: idx_waste_tags_name_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_tags_name_en ON public.waste_tags USING btree (name_en);


--
-- Name: idx_waste_types_description_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_description_ar ON public.waste_types USING btree (description_ar);


--
-- Name: idx_waste_types_description_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_description_en ON public.waste_types USING btree (description_en);


--
-- Name: idx_waste_types_name_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_name_ar ON public.waste_types USING btree (name_ar);


--
-- Name: idx_waste_types_name_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_name_en ON public.waste_types USING btree (name_en);


--
-- Name: countries update_countries_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_countries_updated_at BEFORE UPDATE ON public.countries FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: dumpster_loads update_order_total_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_order_total_trigger AFTER INSERT OR DELETE ON public.dumpster_loads FOR EACH ROW EXECUTE FUNCTION public.update_order_total();


--
-- Name: user_settings update_user_settings_updated_at_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_user_settings_updated_at_trigger BEFORE UPDATE ON public.user_settings FOR EACH ROW EXECUTE FUNCTION public.update_user_settings_updated_at();


--
-- Name: addresses addresses_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id);


--
-- Name: ai_conversations ai_conversations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_conversations
    ADD CONSTRAINT ai_conversations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);


--
-- Name: ai_feedback ai_feedback_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_feedback
    ADD CONSTRAINT ai_feedback_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.ai_conversations(id) ON DELETE CASCADE;


--
-- Name: ai_feedback ai_feedback_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_feedback
    ADD CONSTRAINT ai_feedback_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);


--
-- Name: cash_transactions cash_transactions_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cash_transactions
    ADD CONSTRAINT cash_transactions_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: cash_transactions cash_transactions_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cash_transactions
    ADD CONSTRAINT cash_transactions_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: discounts discounts_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: driver_assignments driver_assignments_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_assignments driver_assignments_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: driver_assignments driver_assignments_vehicle_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id);


--
-- Name: driver_documents driver_documents_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_documents
    ADD CONSTRAINT driver_documents_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_incidents driver_incidents_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_incidents
    ADD CONSTRAINT driver_incidents_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_incidents driver_incidents_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_incidents
    ADD CONSTRAINT driver_incidents_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: driver_performance_metrics driver_performance_metrics_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_performance_metrics
    ADD CONSTRAINT driver_performance_metrics_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_schedules driver_schedules_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_schedules
    ADD CONSTRAINT driver_schedules_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: drivers drivers_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: drivers drivers_profile_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_profile_id_fkey FOREIGN KEY (profile_id) REFERENCES public.profiles(id);


--
-- Name: dumpster_features dumpster_features_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_features
    ADD CONSTRAINT dumpster_features_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_images dumpster_images_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_images
    ADD CONSTRAINT dumpster_images_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_loads dumpster_loads_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_loads
    ADD CONSTRAINT dumpster_loads_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: dumpster_size_options dumpster_size_options_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_size_options
    ADD CONSTRAINT dumpster_size_options_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_size_options dumpster_size_options_dumpster_size_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_size_options
    ADD CONSTRAINT dumpster_size_options_dumpster_size_id_fkey FOREIGN KEY (dumpster_size_id) REFERENCES public.dumpster_sizes(id) ON DELETE CASCADE;


--
-- Name: dumpster_sizes dumpster_sizes_dumpster_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_sizes
    ADD CONSTRAINT dumpster_sizes_dumpster_type_id_fkey FOREIGN KEY (dumpster_type_id) REFERENCES public.dumpster_types(id);


--
-- Name: dumpster_waste_types dumpster_waste_types_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_waste_types dumpster_waste_types_waste_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_waste_type_id_fkey FOREIGN KEY (waste_type_id) REFERENCES public.waste_types(id) ON DELETE CASCADE;


--
-- Name: dumpsters dumpsters_dumpster_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpsters
    ADD CONSTRAINT dumpsters_dumpster_type_id_fkey FOREIGN KEY (dumpster_type_id) REFERENCES public.dumpster_types(id);


--
-- Name: dumpsters dumpsters_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpsters
    ADD CONSTRAINT dumpsters_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: notifications notifications_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);


--
-- Name: order_status_history order_status_history_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: order_status_history order_status_history_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.profiles(id);


--
-- Name: orders orders_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.profiles(id);


--
-- Name: orders orders_discount_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_discount_id_fkey FOREIGN KEY (discount_id) REFERENCES public.discounts(id);


--
-- Name: orders orders_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id);


--
-- Name: orders orders_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: orders orders_pricing_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pricing_plan_id_fkey FOREIGN KEY (pricing_plan_id) REFERENCES public.pricing_plans(id);


--
-- Name: partners partners_profile_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.partners
    ADD CONSTRAINT partners_profile_id_fkey FOREIGN KEY (profile_id) REFERENCES public.profiles(id);


--
-- Name: payments payments_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: pricing_plans pricing_plans_dumpster_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pricing_plans
    ADD CONSTRAINT pricing_plans_dumpster_type_id_fkey FOREIGN KEY (dumpster_type_id) REFERENCES public.dumpster_types(id);


--
-- Name: pricing_plans pricing_plans_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pricing_plans
    ADD CONSTRAINT pricing_plans_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: profiles profiles_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id);


--
-- Name: reviews reviews_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.profiles(id);


--
-- Name: reviews reviews_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: reviews reviews_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: user_settings user_settings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id);


--
-- Name: vehicles vehicles_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vehicles
    ADD CONSTRAINT vehicles_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: waste_type_tags waste_type_tags_waste_tag_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_waste_tag_id_fkey FOREIGN KEY (waste_tag_id) REFERENCES public.waste_tags(id) ON DELETE CASCADE;


--
-- Name: waste_type_tags waste_type_tags_waste_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_waste_type_id_fkey FOREIGN KEY (waste_type_id) REFERENCES public.waste_types(id) ON DELETE CASCADE;


--
-- Name: addresses Allow admin access to all addresses; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all addresses" ON public.addresses USING (public.is_admin(auth.uid()));


--
-- Name: ai_conversations Allow admin access to all ai_conversations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all ai_conversations" ON public.ai_conversations USING (public.is_admin(auth.uid()));


--
-- Name: ai_feedback Allow admin access to all ai_feedback; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all ai_feedback" ON public.ai_feedback USING (public.is_admin(auth.uid()));


--
-- Name: discounts Allow admin access to all discounts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all discounts" ON public.discounts USING (public.is_admin(auth.uid()));


--
-- Name: dumpster_features Allow admin access to all dumpster_features; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpster_features" ON public.dumpster_features USING (public.is_admin(auth.uid()));


--
-- Name: dumpster_images Allow admin access to all dumpster_images; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpster_images" ON public.dumpster_images USING (public.is_admin(auth.uid()));


--
-- Name: dumpster_size_options Allow admin access to all dumpster_size_options; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpster_size_options" ON public.dumpster_size_options USING (public.is_admin(auth.uid()));


--
-- Name: dumpster_sizes Allow admin access to all dumpster_sizes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpster_sizes" ON public.dumpster_sizes USING (public.is_admin(auth.uid()));


--
-- Name: dumpster_types Allow admin access to all dumpster_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpster_types" ON public.dumpster_types USING (public.is_admin(auth.uid()));


--
-- Name: dumpster_waste_types Allow admin access to all dumpster_waste_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpster_waste_types" ON public.dumpster_waste_types USING (public.is_admin(auth.uid()));


--
-- Name: dumpsters Allow admin access to all dumpsters; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpsters" ON public.dumpsters USING (public.is_admin(auth.uid()));


--
-- Name: installation_locations Allow admin access to all installation_locations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all installation_locations" ON public.installation_locations USING (public.is_admin(auth.uid()));


--
-- Name: notifications Allow admin access to all notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all notifications" ON public.notifications USING (public.is_admin(auth.uid()));


--
-- Name: order_status_history Allow admin access to all order_status_history; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all order_status_history" ON public.order_status_history USING (public.is_admin(auth.uid()));


--
-- Name: orders Allow admin access to all orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all orders" ON public.orders USING (public.is_admin(auth.uid()));


--
-- Name: partners Allow admin access to all partners; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all partners" ON public.partners USING (public.is_admin(auth.uid()));


--
-- Name: payments Allow admin access to all payments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all payments" ON public.payments USING (public.is_admin(auth.uid()));


--
-- Name: pricing_plans Allow admin access to all pricing_plans; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all pricing_plans" ON public.pricing_plans USING (public.is_admin(auth.uid()));


--
-- Name: profiles Allow admin access to all profiles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all profiles" ON public.profiles USING (public.is_admin(auth.uid()));


--
-- Name: reviews Allow admin access to all reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all reviews" ON public.reviews USING (public.is_admin(auth.uid()));


--
-- Name: user_settings Allow admin access to all user_settings; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all user_settings" ON public.user_settings USING (public.is_admin(auth.uid()));


--
-- Name: waste_tags Allow admin access to all waste_tags; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all waste_tags" ON public.waste_tags USING (public.is_admin(auth.uid()));


--
-- Name: waste_type_tags Allow admin access to all waste_type_tags; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all waste_type_tags" ON public.waste_type_tags USING (public.is_admin(auth.uid()));


--
-- Name: waste_types Allow admin access to all waste_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all waste_types" ON public.waste_types USING (public.is_admin(auth.uid()));


--
-- Name: dumpsters Allow partners to manage their own dumpsters; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to manage their own dumpsters" ON public.dumpsters USING ((public.is_partner(auth.uid()) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: pricing_plans Allow partners to manage their own pricing plans; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to manage their own pricing plans" ON public.pricing_plans USING ((public.is_partner(auth.uid()) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: reviews Allow partners to respond to reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to respond to reviews" ON public.reviews FOR UPDATE USING ((public.is_partner(auth.uid()) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid()))) AND (partner_response IS NULL)));


--
-- Name: partners Allow partners to update their own data; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to update their own data" ON public.partners FOR UPDATE USING ((public.is_partner(auth.uid()) AND (auth.uid() = profile_id)));


--
-- Name: orders Allow partners to update their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to update their own orders" ON public.orders FOR UPDATE USING ((public.is_partner(auth.uid()) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: partners Allow partners to view their own data; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to view their own data" ON public.partners FOR SELECT USING ((public.is_partner(auth.uid()) AND (auth.uid() = profile_id)));


--
-- Name: dumpsters Allow partners to view their own dumpsters; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to view their own dumpsters" ON public.dumpsters FOR SELECT USING ((public.is_partner(auth.uid()) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: orders Allow partners to view their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to view their own orders" ON public.orders FOR SELECT USING ((public.is_partner(auth.uid()) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: payments Allow partners to view their own payments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to view their own payments" ON public.payments FOR SELECT USING ((public.is_partner(auth.uid()) AND (( SELECT orders.partner_id
   FROM public.orders
  WHERE (orders.id = payments.order_id)) = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: discounts Allow public read access for discounts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for discounts" ON public.discounts FOR SELECT USING (true);


--
-- Name: dumpster_features Allow public read access for dumpster_features; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_features" ON public.dumpster_features FOR SELECT USING (true);


--
-- Name: dumpster_images Allow public read access for dumpster_images; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_images" ON public.dumpster_images FOR SELECT USING (true);


--
-- Name: dumpster_size_options Allow public read access for dumpster_size_options; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_size_options" ON public.dumpster_size_options FOR SELECT USING (true);


--
-- Name: dumpster_sizes Allow public read access for dumpster_sizes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_sizes" ON public.dumpster_sizes FOR SELECT USING (true);


--
-- Name: dumpster_types Allow public read access for dumpster_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_types" ON public.dumpster_types FOR SELECT USING (true);


--
-- Name: dumpster_waste_types Allow public read access for dumpster_waste_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_waste_types" ON public.dumpster_waste_types FOR SELECT USING (true);


--
-- Name: dumpsters Allow public read access for dumpsters; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpsters" ON public.dumpsters FOR SELECT USING (true);


--
-- Name: installation_locations Allow public read access for installation_locations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for installation_locations" ON public.installation_locations FOR SELECT USING (true);


--
-- Name: profiles Allow public read access for partners; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for partners" ON public.profiles FOR SELECT USING (true);


--
-- Name: pricing_plans Allow public read access for pricing_plans; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for pricing_plans" ON public.pricing_plans FOR SELECT USING (true);


--
-- Name: profiles Allow public read access for profiles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for profiles" ON public.profiles FOR SELECT USING (true);


--
-- Name: reviews Allow public read access for reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for reviews" ON public.reviews FOR SELECT USING ((is_published = true));


--
-- Name: waste_tags Allow public read access for waste_tags; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for waste_tags" ON public.waste_tags FOR SELECT USING (true);


--
-- Name: waste_type_tags Allow public read access for waste_type_tags; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for waste_type_tags" ON public.waste_type_tags FOR SELECT USING (true);


--
-- Name: waste_types Allow public read access for waste_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for waste_types" ON public.waste_types FOR SELECT USING (true);


--
-- Name: ai_feedback Allow users to create AI feedback; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to create AI feedback" ON public.ai_feedback FOR INSERT WITH CHECK ((auth.uid() = user_id));


--
-- Name: reviews Allow users to create reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to create reviews" ON public.reviews FOR INSERT WITH CHECK ((auth.uid() = customer_id));


--
-- Name: addresses Allow users to manage their own addresses; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to manage their own addresses" ON public.addresses USING ((auth.uid() = user_id));


--
-- Name: profiles Allow users to manage their own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to manage their own profile" ON public.profiles USING ((auth.uid() = id));


--
-- Name: user_settings Allow users to manage their own settings; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to manage their own settings" ON public.user_settings USING ((auth.uid() = user_id));


--
-- Name: notifications Allow users to update their own notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to update their own notifications" ON public.notifications FOR UPDATE USING ((auth.uid() = user_id));


--
-- Name: reviews Allow users to update their own reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to update their own reviews" ON public.reviews FOR UPDATE USING ((auth.uid() = customer_id));


--
-- Name: ai_conversations Allow users to view their own AI conversations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own AI conversations" ON public.ai_conversations FOR SELECT USING ((auth.uid() = user_id));


--
-- Name: notifications Allow users to view their own notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own notifications" ON public.notifications FOR SELECT USING ((auth.uid() = user_id));


--
-- Name: payments Allow users to view their own payments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own payments" ON public.payments FOR SELECT USING ((auth.uid() = ( SELECT orders.customer_id
   FROM public.orders
  WHERE (orders.id = payments.order_id))));


--
-- Name: reviews Allow users to view their own reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own reviews" ON public.reviews FOR SELECT USING ((auth.uid() = customer_id));


--
-- Name: orders Users can create their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can create their own orders" ON public.orders FOR INSERT TO authenticated WITH CHECK (((auth.uid() = customer_id) OR (EXISTS ( SELECT 1
   FROM auth.users
  WHERE (users.id = auth.uid())))));


--
-- Name: profiles Users can delete own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete own profile" ON public.profiles FOR DELETE USING ((auth.uid() = id));


--
-- Name: profiles Users can insert own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK ((auth.uid() = id));


--
-- Name: profiles Users can update own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING ((auth.uid() = id)) WITH CHECK ((auth.uid() = id));


--
-- Name: profiles Users can view own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING ((auth.uid() = id));


--
-- Name: addresses; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.addresses ENABLE ROW LEVEL SECURITY;

--
-- Name: ai_conversations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.ai_conversations ENABLE ROW LEVEL SECURITY;

--
-- Name: ai_feedback; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.ai_feedback ENABLE ROW LEVEL SECURITY;

--
-- Name: cash_transactions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.cash_transactions ENABLE ROW LEVEL SECURITY;

--
-- Name: discounts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.discounts ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_assignments; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_assignments ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_documents; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_documents ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_incidents; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_incidents ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_performance_metrics; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_performance_metrics ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_schedules; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_schedules ENABLE ROW LEVEL SECURITY;

--
-- Name: drivers; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.drivers ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_features; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_features ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_images; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_images ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_size_options; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_size_options ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_sizes; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_sizes ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_types; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_types ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_waste_types; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_waste_types ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpsters; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpsters ENABLE ROW LEVEL SECURITY;

--
-- Name: installation_locations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.installation_locations ENABLE ROW LEVEL SECURITY;

--
-- Name: mwan_schedules; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.mwan_schedules ENABLE ROW LEVEL SECURITY;

--
-- Name: notifications; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

--
-- Name: order_status_history; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.order_status_history ENABLE ROW LEVEL SECURITY;

--
-- Name: orders; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;

--
-- Name: orders orders_insert_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY orders_insert_policy ON public.orders FOR INSERT TO authenticated WITH CHECK (true);


--
-- Name: orders orders_select_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY orders_select_policy ON public.orders FOR SELECT TO authenticated USING ((customer_id = auth.uid()));


--
-- Name: orders orders_update_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY orders_update_policy ON public.orders FOR UPDATE TO authenticated USING (((customer_id = auth.uid()) AND (status = ANY (ARRAY['pending'::text, 'confirmed'::text]))));


--
-- Name: partners; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.partners ENABLE ROW LEVEL SECURITY;

--
-- Name: payments; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

--
-- Name: pricing_plans; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.pricing_plans ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: reviews; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

--
-- Name: user_settings; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

--
-- Name: vehicles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;

--
-- Name: waste_tags; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.waste_tags ENABLE ROW LEVEL SECURITY;

--
-- Name: waste_type_tags; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.waste_type_tags ENABLE ROW LEVEL SECURITY;

--
-- Name: waste_types; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.waste_types ENABLE ROW LEVEL SECURITY;

--
-- PostgreSQL database dump complete
--

