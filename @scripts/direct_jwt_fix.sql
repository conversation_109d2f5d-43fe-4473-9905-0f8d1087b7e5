-- Direct fix for auth.jwt function
-- This creates a very simple implementation

-- Drop the JWT function
DROP FUNCTION IF EXISTS auth.jwt();

-- Create a very simple JWT function
CREATE OR REPLACE FUNCTION auth.jwt()
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
    -- Merge base JWT with fixed customer role
    SELECT COALESCE(
        current_setting('request.jwt.claim', true)::JSONB, 
        '{}'::JSONB
    ) || jsonb_build_object(
        'role', 'customer',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth.jwt TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt TO anon;
GRANT EXECUTE ON FUNCTION auth.jwt TO service_role;

-- Log the change
INSERT INTO public.logs (event_type, details)
VALUES ('direct_jwt_fix', jsonb_build_object('timestamp', now())); 