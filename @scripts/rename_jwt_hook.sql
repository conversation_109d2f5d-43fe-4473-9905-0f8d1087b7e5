-- Rename JWT hook function to a new name to avoid any caching issues

-- Create a new function with a different name but same implementation
CREATE OR REPLACE FUNCTION public.jwt_claims_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    -- Return a fixed simple object with just the required fields
    -- No conditionals, no error handling, just a direct return
    SELECT jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
$$;

-- Set permissions
GRANT EXECUTE ON FUNCTION public.jwt_claims_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;

-- Test calling the new function
SELECT public.jwt_claims_hook('{}'::jsonb) AS new_hook_result; 