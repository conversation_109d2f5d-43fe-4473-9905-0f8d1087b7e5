-- Fix the NULL return value in custom hook function

-- First, drop the existing function
DROP FUNCTION IF EXISTS public.custom_access_token_hook;

-- Create a simpler function using SQL language (not plpgsql) to minimize errors
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    -- No variables, no logging, just return a fixed object directly
    -- This is the most reliable approach
    SELECT jsonb_build_object(
        'role', 'authenticated',
        'app_metadata', jsonb_build_object(
            'user_role', 'customer'
        )
    );
$$;

-- Set proper permissions - only the essentials, avoiding logging for now
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;

-- Test the function with explicit typing to ensure non-null return
SELECT public.custom_access_token_hook('{}'::jsonb) AS hook_result;

-- Test again with pretty printing
SELECT jsonb_pretty(public.custom_access_token_hook('{}'::jsonb)) AS formatted_result; 