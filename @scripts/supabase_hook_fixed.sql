-- Create hook function exactly according to Supabase documentation
-- https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook

-- Drop existing function
DROP FUNCTION IF EXISTS public.custom_access_token_hook;

-- Create function with exact format from documentation
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
/*
Note: This is a security-critical function. 
Any vulnerabilities in this function can lead to compromised user accounts
and unauthorized access to data.
*/
DECLARE
  -- Return value that will be added to the JWT
  result jsonb = jsonb_build_object();
BEGIN
  -- Get the user_id from the event data
  -- This is a guarantee to be set and to be a valid UUID
  -- and is the user_id of the user about to be signed in
  DECLARE
    user_id uuid = (event ->> 'sub')::uuid;
  BEGIN
    -- Log the hook call
    INSERT INTO public.hook_monitoring (
      hook_name, 
      input_data,
      caller_info
    ) VALUES (
      'custom_access_token_hook_new', 
      event,
      format('user:%s', user_id)
    );
  
    -- First create a base result with role set to authenticated (required)
    result = jsonb_build_object('role', 'authenticated');
    
    -- Then add app_metadata with default user_role
    result = result || jsonb_build_object(
      'app_metadata', jsonb_build_object('user_role', 'customer')
    );

    -- Update monitoring record with result
    UPDATE public.hook_monitoring 
    SET output_data = result,
        success = true
    WHERE hook_name = 'custom_access_token_hook_new'
    AND input_data = event
    ORDER BY id DESC
    LIMIT 1;
    
    -- Return the result
    RETURN result;
  END;
EXCEPTION WHEN OTHERS THEN
  -- Log the error
  INSERT INTO public.hook_monitoring (
    hook_name,
    input_data,
    output_data,
    success
  ) VALUES (
    'custom_access_token_hook_error',
    event,
    jsonb_build_object('error', SQLERRM),
    false
  );
  
  -- Always provide a default role value in case of error
  RETURN jsonb_build_object('role', 'authenticated');
END;
$$;

-- Make sure all permissions are set correctly
-- Grant execute permission to auth admin role
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
GRANT USAGE ON SEQUENCE public.hook_monitoring_id_seq TO supabase_auth_admin;
GRANT SELECT, INSERT, UPDATE ON TABLE public.hook_monitoring TO supabase_auth_admin;

-- Test with sample user ID
SELECT public.custom_access_token_hook(
  jsonb_build_object('sub', '00000000-0000-0000-0000-000000000000')
); 