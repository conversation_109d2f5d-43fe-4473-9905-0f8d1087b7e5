-- Diagnostics for hook setup issues

-- Create a diagnostic tool that returns detailed information
CREATE OR REPLACE FUNCTION public.debug_custom_hook()
RETURNS jsonb
LANGUAGE plpgsql
AS $$
DECLARE
  hook_exists boolean;
  hook_grants jsonb;
  user_schema_grants boolean;
  test_hook_result jsonb;
  function_source text;
  diagnostic_result jsonb;
BEGIN
  -- Check if the function exists
  SELECT EXISTS(
    SELECT 1 FROM pg_proc
    WHERE proname = 'custom_access_token_hook'
    AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  ) INTO hook_exists;
  
  -- Test invoke the hook function
  BEGIN
    SELECT public.custom_access_token_hook('{}'::jsonb) INTO test_hook_result;
  EXCEPTION WHEN OTHERS THEN
    test_hook_result := jsonb_build_object('error', SQLERRM);
  END;
  
  -- Get the function source
  SELECT pg_get_functiondef(oid) 
  FROM pg_proc 
  WHERE proname = 'custom_access_token_hook'
  AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  INTO function_source;
  
  -- Check grants for supabase_auth_admin
  SELECT EXISTS(
    SELECT 1 FROM information_schema.usage_privileges
    WHERE object_name = 'public'
    AND object_type = 'SCHEMA'
    AND grantee = 'supabase_auth_admin'
  ) INTO user_schema_grants;
  
  -- Build diagnostic result
  diagnostic_result := jsonb_build_object(
    'timestamp', now(),
    'function_exists', hook_exists,
    'function_source', function_source,
    'test_result', test_hook_result,
    'schema_grants', user_schema_grants
  );
  
  -- Log this for reference
  INSERT INTO public.logs (event_type, details)
  VALUES ('hook_diagnostics', diagnostic_result);
  
  RETURN diagnostic_result;
END;
$$;

-- Run the diagnostic tool
SELECT public.debug_custom_hook(); 