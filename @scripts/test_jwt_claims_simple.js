// Test script to check JW<PERSON> claims and refresh session
// CommonJS version that should work with basic Node

const { createClient } = require('@supabase/supabase-js');

// Create the Supabase client directly in this file
// Replace these with your actual Supabase URL and anon key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing environment variables. Please set:');
  console.error('SUPABASE_URL - Your Supabase project URL');
  console.error('SUPABASE_ANON_KEY - Your Supabase anon key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testJwtClaims() {
  console.log('Testing JWT claims...');
  
  try {
    // First, check current session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return;
    }
    
    if (!sessionData.session) {
      console.log('No active session found. Please sign in first.');
      return;
    }
    
    console.log('Current session:', {
      user_id: sessionData.session.user.id,
      app_metadata: sessionData.session.user.app_metadata,
      role: sessionData.session.user.app_metadata?.role || sessionData.session.user.app_metadata?.user_role
    });
    
    // Refresh the session to get updated JWT claims
    console.log('Refreshing session...');
    const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
    
    if (refreshError) {
      console.error('Error refreshing session:', refreshError);
      return;
    }
    
    if (!refreshData.session) {
      console.log('No session after refresh. Something went wrong.');
      return;
    }
    
    console.log('Refreshed session:', {
      user_id: refreshData.session.user.id,
      app_metadata: refreshData.session.user.app_metadata,
      role: refreshData.session.user.app_metadata?.role || refreshData.session.user.app_metadata?.user_role
    });
    
    // Check if we have the role claim
    if (refreshData.session.user.app_metadata?.role || refreshData.session.user.app_metadata?.user_role) {
      console.log('JWT claims are working correctly!');
    } else {
      console.log('JWT claims are not set correctly. Check your auth.jwt_claim function.');
    }
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the test
testJwtClaims(); 