-- Ultra simple JWT hooks for both schemas with no dependencies

-- Drop existing functions
DROP FUNCTION IF EXISTS public.ultra_simple_hook;
DROP FUNCTION IF EXISTS auth.ultra_simple_hook;

-- Create in public schema with SQL language (no plpgsql)
CREATE OR REPLACE FUNCTION public.ultra_simple_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT '{"role": "authenticated"}'::jsonb;
$$;

-- Set permissions
GRANT EXECUTE ON FUNCTION public.ultra_simple_hook TO supabase_auth_admin;
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;

-- Create in auth schema with SQL language (no plpgsql)
CREATE OR REPLACE FUNCTION auth.ultra_simple_hook(event jsonb)
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT '{"role": "authenticated"}'::jsonb;
$$;

-- Set permissions
GRANT EXECUTE ON FUNCTION auth.ultra_simple_hook TO supabase_auth_admin;

-- Test both functions
SELECT public.ultra_simple_hook('{}'::jsonb) AS public_result;
SELECT auth.ultra_simple_hook('{}'::jsonb) AS auth_result;

-- Instructions:
-- 1. Run this script
-- 2. In the Supabase Dashboard, try both:
--    a) schema: public, function: ultra_simple_hook
--    b) schema: auth, function: ultra_simple_hook
-- 3. Test authentication after each update 