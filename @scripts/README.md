# Authentication and User Management Scripts

This folder contains scripts and SQL files for managing user authentication, roles, and fixing common issues in the Dumpster On Demand application.

## Important Files

- `clean_duplicate_users.ts` - Script to resolve duplicate phone numbers across profiles
- `admin_functions.sql` - SQL functions for user management (requires superuser/service role)
- `prevent_duplicates.sql` - SQL trigger to prevent future duplicate phone numbers

## Common Issues and Solutions

### Duplicate Phone Numbers

If a phone number is associated with multiple accounts (e.g., both customer and partner roles), the user will be unable to log in to the customer app. Here's how to fix it:

1. Install required dependencies:
   ```
   npm install @supabase/supabase-js commander
   ```

2. Run the script with your Supabase service key:
   ```
   SUPABASE_KEY=your_service_key npx ts-node @scripts/clean_duplicate_users.ts --phone=************ --keep-role=partner
   ```

3. Add the phone uniqueness trigger to prevent this issue in the future:
   ```
   psql -h db.ejjnlnwinrmnwnyvwlhj.supabase.co -U postgres -d postgres -f @scripts/prevent_duplicates.sql
   ```

### Authentication State Issues

If users report being stuck during authentication, they can:

1. Tap on the app title 5 times quickly on the login screen
2. Confirm the "Reset Authentication" dialog
3. Try logging in again

### JWT Claims Issues

Instead of relying on JWT claims for role validation, the app now directly checks the `profiles.user_type` field after authentication. This approach is more reliable and avoids issues with custom JWT hooks.

## Database Schema Relationships

- `auth.users` - Core user accounts
- `auth.user_roles` - User role assignments in the auth schema
- `public.profiles` - User profiles with user_type field for role validation
- `public.user_roles` - Additional role information in the public schema

## Admin SQL Tools

To find all duplicate phone numbers in the system:

```sql
SELECT * FROM admin_check_duplicate_phones();
```

To find all profiles associated with a specific phone number:

```sql
SELECT * FROM admin_find_profiles_by_phone('************');
```

## Security Considerations

- The admin functions require superuser or service role permission
- Always back up your database before running cleaning scripts
- Use the `--dry-run` flag first to see what would be deleted

# JWT Troubleshooting Scripts

This directory contains scripts created to troubleshoot and fix JWT token issues with Supabase custom hooks.

## SQL Scripts

### Diagnostic Scripts
- `hook_diagnostics.sql` - Creates a diagnostic function to check hook configuration
- `check_jwt_hook_format.sql` - Tests hook behavior with different input formats
- `test_auth_role_access.sql` - Verifies the function is accessible by supabase_auth_admin role
- `monitor_hook_calls.sql` - Creates monitoring table for hook calls to trace authentication

### Solution Scripts
- `fix_null_hook.sql` - Fixes the NULL return value issue with a simple implementation
- `fix_hook_permissions.sql` - Sets up proper permissions for the hook function
- `match_working_hook_pattern.sql` - Creates a hook matching the working SMS hook pattern
- `minimal_custom_hook.sql` - Creates a minimal custom hook for testing
- `final_hook_fix.sql` - Final fix with robust error handling
- `supabase_hook_fixed.sql` - Version that follows Supabase documentation format
- `rename_jwt_hook.sql` - Renames hook to avoid caching issues
- `exact_jwt_hook.sql` - Creates a version exactly matching Supabase docs

## TypeScript Files
- `hook_test.ts` - Script to test JWT claims with proper error handling
- `verify_jwt_config.ts` - Verifies hook configuration in Supabase settings

## Documentation
- `FINAL_SOLUTION.md` - Complete solution with implementation steps
- `FINAL_JWT_SOLUTION.md` - Detailed explanation of the issue and solution
- `JWT_HOOK_SOLUTION.md` - Initial solution approach

## Usage

The recommended solution is to use one of the new function names in the Supabase Dashboard:

1. Run one of these SQL files to create the function:
   ```
   psql -h YOUR_HOST -U postgres -d postgres -f @scripts/exact_jwt_hook.sql
   ```

2. Update the hook configuration in Supabase Dashboard to use:
   - Schema: `public`
   - Function: `jwt_claims_hook_v2`

3. For debugging, check logs:
   ```sql
   SELECT * FROM public.logs WHERE event_type LIKE 'jwt_hook%' ORDER BY created_at DESC LIMIT 5;
   ```

## Handling Multiple Auth Methods

### Problem: Multiple Auth Records for the Same User

Supabase creates separate records in `auth.users` for each authentication method (email, phone, Google, etc.) by default. This can lead to situations where a single person has multiple user accounts in your system, causing confusion with login flows and profile data.

### Solution: Account Merging Script

We've created a script that can merge multiple authentication accounts into a single user account:

- **merge_user_accounts.ts**: Merges multiple auth accounts that belong to the same user

#### Using the Account Merging Script

1. First, find accounts with the same email or phone:

```bash
# Find by email
SUPABASE_KEY=your_service_key npx ts-node @scripts/merge_user_accounts.ts --email=<EMAIL> --dry-run

# Find by phone number
SUPABASE_KEY=your_service_key npx ts-node @scripts/merge_user_accounts.ts --phone=+********** --dry-run
```

2. After reviewing the results, run without the `--dry-run` flag to perform the merge:

```bash
SUPABASE_KEY=your_service_key npx ts-node @scripts/merge_user_accounts.ts --email=<EMAIL>
```

3. Alternatively, if you already know the account IDs, you can merge directly:

```bash
SUPABASE_KEY=your_service_key npx ts-node @scripts/merge_user_accounts.ts --primary-id=uuid1 --secondary-id=uuid2
```

The script will:
- Keep one user account (the primary)
- Transfer all identities to the primary account
- Ensure the primary account has the correct role
- Delete the secondary accounts

### SQL Functions for Finding Duplicate Accounts

We've also added SQL functions to help identify accounts with multiple auth methods:

```sql
-- Find users with multiple auth methods (across email, phone, oauth)
SELECT * FROM admin_find_users_with_multiple_auth_methods();

-- Find profiles with duplicate phone numbers
SELECT * FROM admin_find_profiles_with_duplicate_phones();
```

### Prevention

To prevent duplicates in the future:
- Use the `prevent_duplicates.sql` trigger to ensure unique phone numbers in profiles
- Consider implementing "Link Account" functionality to allow users to connect multiple authentication methods to the same account
- If possible, use a single auth provider and standardize on that 