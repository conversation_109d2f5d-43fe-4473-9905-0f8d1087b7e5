-- Create temporary table for first active partner
CREATE TEMP TABLE temp_first_partner AS
SELECT id FROM public.partners WHERE status = 'active' LIMIT 1;

-- Add just the 30 yard dumpster type if missing
WITH new_type AS (
    INSERT INTO public.dumpster_types (
        id, name_en, description_en, capacity, max_weight, 
        suitable_waste_types, image_url
    )
    SELECT 
        gen_random_uuid(),
        '30 Yard Container',
        'Suitable for extra large construction projects',
        30,
        6000,
        ARRAY['construction', 'commercial', 'industrial'],
        'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_30YD.png'
    WHERE NOT EXISTS (
        SELECT 1 FROM public.dumpster_types WHERE capacity = 30
    )
    RETURNING id
)
-- Add the dumpster if type was created
INSERT INTO public.dumpsters (
    id, partner_id, dumpster_type_id, image_url,
    serial_number, status, price_per_load,
    name_en, description_en, rating, review_count
)
SELECT 
    gen_random_uuid(),
    (SELECT id FROM temp_first_partner),
    nt.id,
    'https://ejjnlnwinrmnwnyvwlhj.supabase.co/storage/v1/object/public/dumpsters/thumb/dumpters_30YD.png',
    'SN-' || SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8),
    'available',
    350.00,
    '30 Yard Container',
    'Standard 30 cubic yard container',
    4.5,
    FLOOR(RANDOM() * (100-20+1) + 20)::integer
FROM new_type nt
WHERE NOT EXISTS (
    SELECT 1 FROM public.dumpsters d
    JOIN public.dumpster_types t ON d.dumpster_type_id = t.id
    WHERE t.capacity = 30
);

DROP TABLE temp_first_partner;