--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.12 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA public;


--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON SCHEMA public IS 'standard public schema';


--
-- Name: admin_find_profiles_by_phone(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.admin_find_profiles_by_phone(search_phone text) RETURNS TABLE(id uuid, user_type text, email text, phone text, full_name text, created_at timestamp with time zone)
    LANGUAGE sql SECURITY DEFINER
    AS $$ SELECT id, user_type, email, phone, full_name, created_at FROM public.profiles WHERE phone = search_phone ORDER BY created_at DESC; $$;


--
-- Name: admin_find_profiles_with_duplicate_phones(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.admin_find_profiles_with_duplicate_phones() RETURNS TABLE(phone text, profile_ids uuid[], user_types text[], profile_count integer)
    LANGUAGE sql SECURITY DEFINER
    AS $$ SELECT p.phone, array_agg(p.id) as profile_ids, array_agg(p.user_type) as user_types, COUNT(*) as profile_count FROM public.profiles p WHERE p.phone IS NOT NULL GROUP BY p.phone HAVING COUNT(*) > 1 ORDER BY COUNT(*) DESC; $$;


--
-- Name: admin_find_users_with_multiple_auth_methods(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.admin_find_users_with_multiple_auth_methods() RETURNS TABLE(email text, user_ids uuid[], auth_methods text[], provider_count integer)
    LANGUAGE sql SECURITY DEFINER
    AS $$ WITH user_providers AS (SELECT u.id as user_id, u.email, CASE WHEN u.email IS NOT NULL THEN 'email' ELSE NULL END as provider FROM auth.users u WHERE u.email IS NOT NULL UNION ALL SELECT u.id as user_id, u.email, CASE WHEN u.phone IS NOT NULL THEN 'phone' ELSE NULL END as provider FROM auth.users u WHERE u.phone IS NOT NULL UNION ALL SELECT i.user_id, i.email, i.provider FROM auth.identities i WHERE i.email IS NOT NULL) SELECT up.email, array_agg(DISTINCT up.user_id) as user_ids, array_agg(DISTINCT up.provider) as auth_methods, COUNT(DISTINCT up.user_id) as provider_count FROM user_providers up WHERE up.email IS NOT NULL GROUP BY up.email HAVING COUNT(DISTINCT up.user_id) > 1 ORDER BY COUNT(DISTINCT up.user_id) DESC, up.email; $$;


--
-- Name: authorize(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.authorize(required_role text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Get user_role from JWT claims
  RETURN (
    auth.jwt() ->> 'role' = required_role 
    OR auth.jwt() ->> 'role' = 'admin'
    OR EXISTS (
      SELECT 1 
      FROM public.profiles 
      WHERE id = auth.uid()
      AND user_type = required_role
    )
  );
END;
$$;


--
-- Name: can_access_customer_app(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.can_access_customer_app(user_id uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT user_type INTO user_role FROM public.profiles WHERE id = user_id;
  RETURN COALESCE(user_role = 'customer', FALSE);
END;
$$;


--
-- Name: check_auth_id_collision(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.check_auth_id_collision(user_id uuid) RETURNS TABLE(profile_id uuid, user_type text, collision_type text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Check for email auth ID collisions
  RETURN QUERY
  SELECT p.id as profile_id, p.user_type, 'email' as collision_type
  FROM public.profiles p
  WHERE p.email_auth_id = user_id AND p.id <> user_id
  UNION ALL
  -- Check for phone auth ID collisions
  SELECT p.id as profile_id, p.user_type, 'phone' as collision_type
  FROM public.profiles p
  WHERE p.phone_auth_id = user_id AND p.id <> user_id;
END;
$$;


--
-- Name: debug_custom_hook(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.debug_custom_hook() RETURNS jsonb
    LANGUAGE plpgsql
    AS $$
DECLARE
  hook_exists boolean;
  hook_grants jsonb;
  user_schema_grants boolean;
  test_hook_result jsonb;
  function_source text;
  diagnostic_result jsonb;
BEGIN
  -- Check if the function exists
  SELECT EXISTS(
    SELECT 1 FROM pg_proc
    WHERE proname = 'custom_access_token_hook'
    AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  ) INTO hook_exists;
  
  -- Test invoke the hook function
  BEGIN
    SELECT public.custom_access_token_hook('{}'::jsonb) INTO test_hook_result;
  EXCEPTION WHEN OTHERS THEN
    test_hook_result := jsonb_build_object('error', SQLERRM);
  END;
  
  -- Get the function source
  SELECT pg_get_functiondef(oid) 
  FROM pg_proc 
  WHERE proname = 'custom_access_token_hook'
  AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  INTO function_source;
  
  -- Check grants for supabase_auth_admin
  SELECT EXISTS(
    SELECT 1 FROM information_schema.usage_privileges
    WHERE object_name = 'public'
    AND object_type = 'SCHEMA'
    AND grantee = 'supabase_auth_admin'
  ) INTO user_schema_grants;
  
  -- Build diagnostic result
  diagnostic_result := jsonb_build_object(
    'timestamp', now(),
    'function_exists', hook_exists,
    'function_source', function_source,
    'test_result', test_hook_result,
    'schema_grants', user_schema_grants
  );
  
  -- Log this for reference
  INSERT INTO public.logs (event_type, details)
  VALUES ('hook_diagnostics', diagnostic_result);
  
  RETURN diagnostic_result;
END;
$$;


--
-- Name: find_profile_by_phone(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.find_profile_by_phone(phone_in text) RETURNS TABLE(id uuid, user_type text, full_name text, email text, phone text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT p.id, p.user_type, p.full_name, p.email, p.phone
  FROM public.profiles p
  WHERE p.phone = phone_in;
END;
$$;


--
-- Name: get_phone_profile_type(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_phone_profile_type(phone_number text) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  profile_type TEXT;
BEGIN
  SELECT user_type INTO profile_type
  FROM public.profiles
  WHERE phone = phone_number
  LIMIT 1;
  
  RETURN profile_type;
END;
$$;


--
-- Name: get_profile_by_auth_id(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_profile_by_auth_id(auth_id uuid) RETURNS TABLE(id uuid, email text, phone text, full_name text, avatar_url text, user_type text, created_at timestamp with time zone, updated_at timestamp with time zone, oauth_google_id text, email_auth_id uuid, phone_auth_id uuid)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT p.id, p.email, p.phone, p.full_name, p.avatar_url, p.user_type, p.created_at, p.updated_at, p.oauth_google_id, p.email_auth_id, p.phone_auth_id
  FROM public.profiles p
  WHERE p.id = auth_id
     OR p.email_auth_id = auth_id
     OR p.phone_auth_id = auth_id;
END;
$$;


--
-- Name: handle_new_user(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_new_user() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Create a profile entry if it doesn't exist
  INSERT INTO public.profiles (id, user_type, created_at, updated_at)
  VALUES (NEW.id, 'customer', NOW(), NOW())
  ON CONFLICT (id) DO NOTHING;
  
  RETURN NEW;
END;
$$;


--
-- Name: handle_updated_at(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
    new.updated_at = now();
    return new;
end;
$$;


--
-- Name: handle_user_type_change(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_user_type_change() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  IF OLD.user_type IS DISTINCT FROM NEW.user_type THEN
    -- Force refresh of JWT claims by updating auth.users
    UPDATE auth.users SET updated_at = now() WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$;


--
-- Name: is_admin(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_admin(uid uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'admin'
  );
END;
$$;


--
-- Name: is_customer(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_customer(uid uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'customer'
  );
END;
$$;


--
-- Name: is_driver(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_driver(uid uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'driver'
  );
END;
$$;


--
-- Name: is_partner(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_partner(uid uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = uid AND user_type = 'partner'
  );
END;
$$;


--
-- Name: link_accounts_by_email(uuid, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.link_accounts_by_email(user_id uuid, email text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  matching_user_id uuid;
  existing_profile_id uuid;
  current_profile_id uuid;
BEGIN
  -- Find if there's an existing user with the same email
  SELECT id INTO matching_user_id 
  FROM auth.users 
  WHERE email = link_accounts_by_email.email 
    AND id != user_id;
  
  -- If found, check if the existing user has a profile
  IF matching_user_id IS NOT NULL THEN
    -- Get profile ID of existing user
    SELECT id INTO existing_profile_id 
    FROM public.profiles 
    WHERE id = matching_user_id;
    
    -- Get profile ID of current user
    SELECT id INTO current_profile_id 
    FROM public.profiles 
    WHERE id = user_id;
    
    -- If both have profiles, link them by transferring data
    IF existing_profile_id IS NOT NULL AND current_profile_id IS NOT NULL THEN
      -- Update the current profile with any missing data from existing profile
      UPDATE public.profiles 
      SET 
        full_name = COALESCE(profiles.full_name, (SELECT full_name FROM public.profiles WHERE id = existing_profile_id)),
        avatar_url = COALESCE(profiles.avatar_url, (SELECT avatar_url FROM public.profiles WHERE id = existing_profile_id)),
        address = COALESCE(profiles.address, (SELECT address FROM public.profiles WHERE id = existing_profile_id)),
        city = COALESCE(profiles.city, (SELECT city FROM public.profiles WHERE id = existing_profile_id)),
        state = COALESCE(profiles.state, (SELECT state FROM public.profiles WHERE id = existing_profile_id)),
        zip_code = COALESCE(profiles.zip_code, (SELECT zip_code FROM public.profiles WHERE id = existing_profile_id)),
        preferred_language = COALESCE(profiles.preferred_language, (SELECT preferred_language FROM public.profiles WHERE id = existing_profile_id)),
        notification_preferences = COALESCE(profiles.notification_preferences, (SELECT notification_preferences FROM public.profiles WHERE id = existing_profile_id))
      WHERE id = user_id;
      
      -- Delete the existing profile after linking
      DELETE FROM public.profiles WHERE id = existing_profile_id;
      
      -- Return success
      RETURN true;
    END IF;
  END IF;
  
  -- No matching user found or linking not performed
  RETURN false;
END;
$$;


--
-- Name: link_auth_id(uuid, uuid, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.link_auth_id(profile_id uuid, new_auth_id uuid, auth_type text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  success boolean := false;
BEGIN
  -- Determine the auth type if 'unknown' is provided
  IF auth_type = 'unknown' THEN
    -- Check if this auth ID is associated with an email
    IF EXISTS (SELECT 1 FROM auth.users WHERE id = new_auth_id AND email IS NOT NULL) THEN
      auth_type := 'email';
    ELSE
      auth_type := 'phone';
    END IF;
  END IF;
  
  -- Update the appropriate auth ID field
  IF auth_type = 'email' THEN
    UPDATE public.profiles
    SET email_auth_id = new_auth_id
    WHERE id = profile_id
    AND (email_auth_id IS NULL OR email_auth_id <> new_auth_id);
    
    GET DIAGNOSTICS success = ROW_COUNT;
  ELSIF auth_type = 'phone' THEN
    UPDATE public.profiles
    SET phone_auth_id = new_auth_id
    WHERE id = profile_id
    AND (phone_auth_id IS NULL OR phone_auth_id <> new_auth_id);
    
    GET DIAGNOSTICS success = ROW_COUNT;
  END IF;
  
  -- Log the linking attempt
  INSERT INTO public.logs (event_type, details)
  VALUES (
    'auth_id_linking', 
    jsonb_build_object(
      'profile_id', profile_id,
      'auth_id', new_auth_id,
      'auth_type', auth_type,
      'success', success,
      'timestamp', now()
    )
  );
  
  RETURN success;
END;
$$;


--
-- Name: log_debug(text, text, jsonb); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.log_debug(source text, message text, data jsonb DEFAULT NULL::jsonb) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    INSERT INTO public.debug_logs (source, message, data)
    VALUES (source, message, data);
EXCEPTION WHEN OTHERS THEN
    -- Do nothing if logging fails
END;
$$;


--
-- Name: phone_signin(text, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.phone_signin(phone_number text, profile_id uuid) RETURNS json
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
declare
  result json;
begin
  -- Verify the profile exists and matches the phone
  if not exists (
    select 1 from profiles 
    where id = profile_id 
    and phone = phone_number 
    and user_type = 'partner'
  ) then
    return json_build_object(
      'success', false,
      'error', 'Invalid profile or phone number'
    );
  end if;

  -- Generate a new access token
  result := json_build_object(
    'success', true,
    'access_token', sign(
      json_build_object(
        'sub', profile_id::text,
        'role', 'authenticated',
        'exp', extract(epoch from (now() + interval '1 hour'))::integer
      )::json,
      current_setting('app.settings.jwt_secret')
    )
  );

  return result;
end;
$$;


--
-- Name: set_default_role(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.set_default_role() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Set user_type to 'customer' for new users if not specified
  IF NEW.user_type IS NULL THEN
    NEW.user_type := 'customer';
  END IF;
  
  RETURN NEW;
END;
$$;


--
-- Name: sync_user_role(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.sync_user_role() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  IF NEW.user_type IS NOT NULL THEN
    INSERT INTO auth.user_roles (id, role)
    VALUES (NEW.id, NEW.user_type)
    ON CONFLICT (id) DO UPDATE SET role = NEW.user_type, updated_at = now();
  END IF;
  RETURN NEW;
END;
$$;


--
-- Name: test_authentica_api(text, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_authentica_api(phone_number text, test_message text DEFAULT 'This is a test message from Dumpster On Demand'::text) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  response JSONB;
  api_key TEXT := '$2y$10$TK6W./k9tr/UFRQ0ue3igeYyd9UCeLXo0wukQBB2ojOIQ05atC.0i'; -- Replace with your actual API key
  sender_name TEXT := 'DumpsterOD'; -- Replace with your registered sender name
BEGIN
  -- Format the phone number correctly if needed
  IF LEFT(phone_number, 1) != '+' THEN
    phone_number := '+' || phone_number;
  END IF;
  
  -- Log the test attempt
  INSERT INTO public.logs (event_type, details)
  VALUES ('test_authentica_api', jsonb_build_object('phone', phone_number, 'message', test_message));
  
  -- Make HTTP request to Authentica API
  BEGIN
    SELECT
      content::jsonb INTO response
    FROM
      extensions.http((
        'POST',
        'https://api.authentica.sa/api/v1/send-sms',
        ARRAY[
          extensions.http_header('X-Authorization', api_key),
          extensions.http_header('Accept', 'application/json'),
          extensions.http_header('Content-Type', 'application/json')
        ],
        'application/json',
        jsonb_build_object(
          'phone', phone_number,
          'message', test_message,
          'sender_name', sender_name
        )::text
      ));
    
    -- Log the response
    INSERT INTO public.logs (event_type, details)
    VALUES ('test_authentica_response', response);
    
    RETURN response;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error
      INSERT INTO public.logs (event_type, details)
      VALUES ('test_authentica_error', jsonb_build_object('error', SQLERRM));
      
      RETURN jsonb_build_object(
        'error', SQLERRM
      );
  END;
END;
$_$;


--
-- Name: test_authentica_otp_api(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_authentica_otp_api(phone_number text) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  response JSONB;
  api_key TEXT := '$2y$10$TK6W./k9tr/UFRQ0ue3igeYyd9UCeLXo0wukQBB2ojOIQ05atC.0i'; -- Your Authentica API key
BEGIN
  -- Format the phone number correctly if needed
  IF LEFT(phone_number, 1) != '+' THEN
    phone_number := '+' || phone_number;
  END IF;
  
  -- Log the test attempt
  INSERT INTO public.logs (event_type, details)
  VALUES ('test_authentica_otp_api', jsonb_build_object('phone', phone_number));
  
  -- Make HTTP request to Authentica OTP API
  BEGIN
    SELECT
      content::jsonb INTO response
    FROM
      extensions.http((
        'POST',
        'https://api.authentica.sa/api/v1/send-otp',
        ARRAY[
          extensions.http_header('X-Authorization', api_key),
          extensions.http_header('Accept', 'application/json'),
          extensions.http_header('Content-Type', 'application/json')
        ],
        'application/json',
        jsonb_build_object(
          'phone', phone_number,
          'method', 'sms',
          'template_id', 1  -- Using default template ID
        )::text
      ));
    
    -- Log the response
    INSERT INTO public.logs (event_type, details)
    VALUES ('test_authentica_otp_response', response);
    
    RETURN response;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error
      INSERT INTO public.logs (event_type, details)
      VALUES ('test_authentica_otp_error', jsonb_build_object('error', SQLERRM));
      
      RETURN jsonb_build_object(
        'error', SQLERRM
      );
  END;
END;
$_$;


--
-- Name: test_jwt_hook_for_role(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_jwt_hook_for_role(role_to_test text) RETURNS jsonb
    LANGUAGE plpgsql
    AS $$
DECLARE
    test_uid uuid;
    result jsonb;
BEGIN
    -- Find a user with the specified role
    -- Fixed the column reference ambiguity by using different parameter name
    SELECT id INTO test_uid FROM public.profiles WHERE user_type = role_to_test LIMIT 1;
    
    IF test_uid IS NULL THEN
        RETURN jsonb_build_object('error', 'No user found with type: ' || role_to_test);
    END IF;
    
    -- Override the request.jwt.sub setting
    PERFORM set_config('request.jwt.sub', test_uid::text, true);
    
    -- Call the JWT hook function
    SELECT auth.jwt_hook_function() INTO result;
    
    RETURN result;
END;
$$;


--
-- Name: test_updated_sms_api(text, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_updated_sms_api(phone_number text, test_message text DEFAULT 'This is a test message from Dumpster On Demand'::text) RETURNS jsonb
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
  response JSONB;
  api_key TEXT := '$2y$10$TK6W./k9tr/UFRQ0ue3igeYyd9UCeLXo0wukQBB2ojOIQ05atC.0i'; -- Your Authentica API key
BEGIN
  -- Format the phone number correctly if needed
  IF LEFT(phone_number, 1) != '+' THEN
    phone_number := '+' || phone_number;
  END IF;
  
  -- Log the test attempt
  INSERT INTO public.logs (event_type, details)
  VALUES ('test_updated_sms_api', jsonb_build_object('phone', phone_number, 'message', test_message));
  
  -- Make HTTP request to Authentica API WITHOUT sender_name
  BEGIN
    SELECT
      content::jsonb INTO response
    FROM
      extensions.http((
        'POST',
        'https://api.authentica.sa/api/v1/send-sms',
        ARRAY[
          extensions.http_header('X-Authorization', api_key),
          extensions.http_header('Accept', 'application/json'),
          extensions.http_header('Content-Type', 'application/json')
        ],
        'application/json',
        jsonb_build_object(
          'phone', phone_number,
          'message', test_message
          -- Removed sender_name parameter
        )::text
      ));
    
    -- Log the response
    INSERT INTO public.logs (event_type, details)
    VALUES ('test_updated_sms_response', response);
    
    RETURN response;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error
      INSERT INTO public.logs (event_type, details)
      VALUES ('test_updated_sms_error', jsonb_build_object('error', SQLERRM));
      
      RETURN jsonb_build_object(
        'error', SQLERRM
      );
  END;
END;
$_$;


--
-- Name: update_order_total(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_order_total() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  UPDATE orders
  SET total_amount = (
    SELECT d.price_per_load * COUNT(dl.id)
    FROM dumpsters d
    JOIN dumpster_loads dl ON dl.order_id = NEW.order_id
    WHERE d.id = orders.dumpster_id
    GROUP BY d.price_per_load
  )
  WHERE id = NEW.order_id;
  RETURN NEW;
END;
$$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$;


--
-- Name: update_user_settings_updated_at(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_user_settings_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: addresses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.addresses (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    street_address text,
    city text,
    state text,
    zip_code text,
    latitude double precision,
    longitude double precision,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    type text,
    name text,
    is_default boolean
);


--
-- Name: ai_conversations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ai_conversations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    user_id uuid,
    conversation_data jsonb DEFAULT '{}'::jsonb,
    intent_classification text,
    extracted_requirements jsonb DEFAULT '{}'::jsonb,
    recommended_dumpster_types uuid[],
    conversation_summary text
);


--
-- Name: ai_feedback; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ai_feedback (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    conversation_id uuid,
    user_id uuid,
    feedback_type text,
    feedback_text text,
    is_processed boolean DEFAULT false,
    CONSTRAINT ai_feedback_feedback_type_check CHECK ((feedback_type = ANY (ARRAY['helpful'::text, 'not_helpful'::text, 'suggestion'::text])))
);


--
-- Name: ai_recommendations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ai_recommendations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    user_query text NOT NULL,
    recommended_sizes jsonb NOT NULL,
    waste_types jsonb NOT NULL,
    conversation_id uuid,
    selected_dumpster_id uuid,
    resulted_order_id uuid,
    success_rate numeric DEFAULT 0
);


--
-- Name: cash_transactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cash_transactions (
    id uuid NOT NULL,
    order_id uuid,
    driver_id uuid,
    amount numeric,
    status text,
    collection_time timestamp without time zone,
    reconciliation_time timestamp without time zone,
    notes text
);


--
-- Name: countries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.countries (
    id integer NOT NULL,
    name_en character varying(100) NOT NULL,
    native_name character varying(100),
    code character varying(2) NOT NULL,
    dial_code character varying(10) NOT NULL,
    flag_url character varying(255) NOT NULL,
    flag_emoji character varying(10) NOT NULL,
    rtl boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    name_ar text
);


--
-- Name: countries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.countries_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: countries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.countries_id_seq OWNED BY public.countries.id;


--
-- Name: debug_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.debug_logs (
    id integer NOT NULL,
    "timestamp" timestamp with time zone DEFAULT now(),
    source text,
    message text,
    data jsonb
);


--
-- Name: debug_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.debug_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: debug_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.debug_logs_id_seq OWNED BY public.debug_logs.id;


--
-- Name: discounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.discounts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    partner_id uuid,
    code text NOT NULL,
    description text,
    discount_type text,
    discount_value numeric NOT NULL,
    start_date timestamp with time zone,
    end_date timestamp with time zone,
    max_uses integer,
    current_uses integer DEFAULT 0,
    min_order_value numeric DEFAULT 0,
    applicable_dumpster_types uuid[],
    is_active boolean DEFAULT true,
    CONSTRAINT discounts_discount_type_check CHECK ((discount_type = ANY (ARRAY['percentage'::text, 'fixed_amount'::text])))
);


--
-- Name: driver_assignments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_assignments (
    id uuid NOT NULL,
    driver_id uuid,
    vehicle_id uuid,
    order_id uuid,
    status text,
    assigned_at timestamp without time zone,
    completed_at timestamp without time zone
);


--
-- Name: driver_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_documents (
    id uuid NOT NULL,
    driver_id uuid,
    document_type text,
    document_url text,
    expiry_date date,
    status text,
    created_at timestamp without time zone
);


--
-- Name: driver_incidents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_incidents (
    id uuid NOT NULL,
    driver_id uuid,
    order_id uuid,
    incident_type text,
    description text,
    reported_at timestamp without time zone,
    resolution text,
    resolved_at timestamp without time zone,
    status text,
    created_at timestamp without time zone DEFAULT now()
);


--
-- Name: driver_performance_metrics; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_performance_metrics (
    id uuid NOT NULL,
    driver_id uuid,
    metric_type text,
    metric_name text,
    value numeric,
    target numeric,
    period_start timestamp without time zone,
    period_end timestamp without time zone,
    created_at timestamp without time zone DEFAULT now()
);


--
-- Name: driver_schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_schedules (
    id uuid NOT NULL,
    driver_id uuid,
    date date,
    shift_start time without time zone,
    shift_end time without time zone,
    break_start time without time zone,
    break_end time without time zone,
    mwan_zone text,
    status text,
    created_at timestamp without time zone DEFAULT now()
);


--
-- Name: drivers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.drivers (
    id uuid NOT NULL,
    partner_id uuid,
    profile_id uuid,
    status text,
    license_number text,
    license_expiry date,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


--
-- Name: dumpster_feature_links; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_feature_links (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    feature_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_features; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_features (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    feature text NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_images; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_images (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    image_url text NOT NULL,
    sort_order integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_loads; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_loads (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    order_id uuid,
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    scheduled_date timestamp with time zone,
    completed_date timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_sizes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_sizes (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    volume_cubic_yards numeric NOT NULL,
    max_weight_pounds numeric NOT NULL,
    length numeric NOT NULL,
    width numeric NOT NULL,
    height numeric NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpster_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    dimensions jsonb DEFAULT '{}'::jsonb,
    capacity numeric,
    max_weight numeric,
    image_url text,
    suitable_waste_types text[] DEFAULT '{}'::text[],
    notes text,
    name_ar text,
    description_ar text,
    name_en text NOT NULL,
    description_en text
);


--
-- Name: dumpster_waste_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpster_waste_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dumpster_id uuid NOT NULL,
    waste_type_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: dumpsters; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dumpsters (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    partner_id uuid,
    image_url text NOT NULL,
    serial_number text,
    status text DEFAULT 'available'::text,
    current_location jsonb DEFAULT '{"latitude": 0, "longitude": 0}'::jsonb,
    last_maintenance_date timestamp with time zone,
    next_maintenance_date timestamp with time zone,
    purchase_date timestamp with time zone,
    purchase_cost numeric,
    lifetime_revenue numeric DEFAULT 0,
    lifetime_orders integer DEFAULT 0,
    is_available boolean DEFAULT true,
    next_available_date date,
    rating numeric,
    review_count integer DEFAULT 0,
    name_ar text,
    description_ar text,
    description_en text,
    name_en text,
    price_per_load numeric(10,2) DEFAULT 0.00 NOT NULL,
    size_id uuid,
    CONSTRAINT dumpsters_status_check CHECK ((status = ANY (ARRAY['available'::text, 'in_use'::text, 'maintenance'::text, 'decommissioned'::text])))
);


--
-- Name: features; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.features (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name_en text NOT NULL,
    name_ar text,
    icon_name text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: hook_monitoring; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.hook_monitoring (
    id integer NOT NULL,
    "timestamp" timestamp with time zone DEFAULT now(),
    hook_name text,
    input_data jsonb,
    output_data jsonb,
    execution_time_ms double precision,
    caller_info text,
    success boolean
);


--
-- Name: hook_monitoring_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.hook_monitoring_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: hook_monitoring_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.hook_monitoring_id_seq OWNED BY public.hook_monitoring.id;


--
-- Name: installation_locations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.installation_locations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name_ar text NOT NULL,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    name_en text NOT NULL
);


--
-- Name: jwt_hook_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.jwt_hook_logs (
    id integer NOT NULL,
    "timestamp" timestamp with time zone DEFAULT now(),
    function_name text,
    parameter_type text,
    raw_input jsonb,
    error text,
    result jsonb
);


--
-- Name: jwt_hook_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.jwt_hook_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: jwt_hook_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.jwt_hook_logs_id_seq OWNED BY public.jwt_hook_logs.id;


--
-- Name: jwt_hook_tests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.jwt_hook_tests (
    id integer NOT NULL,
    "timestamp" timestamp with time zone DEFAULT now(),
    test_name text,
    input_data jsonb,
    result_data jsonb,
    result_type text,
    success boolean
);


--
-- Name: jwt_hook_tests_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.jwt_hook_tests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: jwt_hook_tests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.jwt_hook_tests_id_seq OWNED BY public.jwt_hook_tests.id;


--
-- Name: logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.logs (
    id integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    event_type text NOT NULL,
    details jsonb DEFAULT '{}'::jsonb
);


--
-- Name: logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.logs_id_seq OWNED BY public.logs.id;


--
-- Name: mwan_schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.mwan_schedules (
    id uuid NOT NULL,
    city_zone text,
    operation_type text,
    start_time time without time zone,
    end_time time without time zone,
    days_of_week text[],
    is_active boolean
);


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    user_id uuid,
    type text,
    title text NOT NULL,
    message text NOT NULL,
    related_entity_type text,
    related_entity_id uuid,
    is_read boolean DEFAULT false,
    read_at timestamp with time zone,
    delivery_channels text[],
    delivery_status jsonb DEFAULT '{}'::jsonb,
    CONSTRAINT notifications_type_check CHECK ((type = ANY (ARRAY['order_update'::text, 'payment'::text, 'system'::text, 'promotion'::text])))
);


--
-- Name: order_status_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_status_history (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    order_id uuid,
    status text NOT NULL,
    notes text,
    updated_by uuid
);


--
-- Name: orders; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.orders (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    customer_id uuid DEFAULT auth.uid(),
    partner_id uuid,
    dumpster_id uuid,
    pricing_plan_id uuid,
    discount_id uuid,
    status text DEFAULT 'pending'::text,
    delivery_address text NOT NULL,
    delivery_coordinates jsonb DEFAULT '{"latitude": 0, "longitude": 0}'::jsonb,
    delivery_instructions text,
    delivery_date timestamp with time zone,
    scheduled_pickup_date timestamp with time zone,
    actual_pickup_date timestamp with time zone,
    rental_duration_days integer,
    waste_type text,
    estimated_weight numeric,
    actual_weight numeric,
    base_price numeric,
    discount_amount numeric DEFAULT 0,
    additional_fees jsonb DEFAULT '{}'::jsonb,
    tax_amount numeric DEFAULT 0,
    total_amount numeric,
    payment_status text DEFAULT 'pending'::text,
    payment_method text,
    special_requirements text,
    ai_conversation_id uuid,
    CONSTRAINT orders_payment_method_check CHECK ((payment_method = ANY (ARRAY['credit_card'::text, 'cash'::text, 'bank_transfer'::text]))),
    CONSTRAINT orders_payment_status_check CHECK ((payment_status = ANY (ARRAY['pending'::text, 'partial'::text, 'paid'::text, 'refunded'::text]))),
    CONSTRAINT orders_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'confirmed'::text, 'delivered'::text, 'in_use'::text, 'pickup_scheduled'::text, 'completed'::text, 'cancelled'::text])))
);


--
-- Name: partners; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.partners (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    profile_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    company_name text NOT NULL,
    business_license text,
    tax_id text,
    service_areas jsonb DEFAULT '[]'::jsonb,
    rating numeric DEFAULT 0,
    total_ratings integer DEFAULT 0,
    is_verified boolean DEFAULT false,
    verification_date timestamp with time zone,
    status text DEFAULT 'pending'::text,
    commission_rate numeric DEFAULT 0,
    bank_details jsonb,
    contact_person text,
    contact_email text,
    contact_phone text,
    whatsapp_business_id text,
    CONSTRAINT partners_status_check CHECK ((status = ANY (ARRAY['active'::text, 'pending'::text, 'suspended'::text])))
);


--
-- Name: payments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payments (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    order_id uuid,
    amount numeric NOT NULL,
    payment_method text,
    status text DEFAULT 'pending'::text,
    transaction_id text,
    payment_date timestamp with time zone,
    payment_details jsonb DEFAULT '{}'::jsonb,
    refund_amount numeric DEFAULT 0,
    refund_date timestamp with time zone,
    refund_reason text,
    CONSTRAINT payments_payment_method_check CHECK ((payment_method = ANY (ARRAY['credit_card'::text, 'cash'::text, 'bank_transfer'::text]))),
    CONSTRAINT payments_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'completed'::text, 'failed'::text, 'refunded'::text])))
);


--
-- Name: pricing_plans; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pricing_plans (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    partner_id uuid,
    dumpster_type_id uuid,
    name text NOT NULL,
    base_price numeric NOT NULL,
    daily_rate numeric NOT NULL,
    minimum_days integer DEFAULT 1,
    maximum_days integer,
    overage_fee_per_day numeric,
    weight_limit numeric,
    overage_fee_per_ton numeric,
    is_active boolean DEFAULT true,
    special_instructions text
);


--
-- Name: profile_conflicts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.profile_conflicts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    auth_user_id uuid NOT NULL,
    conflicting_profile_id uuid NOT NULL,
    conflict_type text NOT NULL,
    resolution_status text NOT NULL,
    resolved_by uuid,
    resolved_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    notes text,
    CONSTRAINT profile_conflicts_conflict_type_check CHECK ((conflict_type = ANY (ARRAY['phone_number'::text, 'email'::text]))),
    CONSTRAINT profile_conflicts_resolution_status_check CHECK ((resolution_status = ANY (ARRAY['pending'::text, 'merged'::text, 'rejected'::text])))
);


--
-- Name: profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.profiles (
    id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    email text,
    phone text,
    full_name text,
    avatar_url text,
    user_type text DEFAULT 'customer'::text,
    phone_auth_id uuid,
    email_auth_id uuid,
    oauth_google_id text,
    CONSTRAINT profiles_user_type_check CHECK ((user_type = ANY (ARRAY['customer'::text, 'partner'::text, 'admin'::text]))),
    CONSTRAINT valid_user_type CHECK ((user_type = ANY (ARRAY['customer'::text, 'partner'::text, 'admin'::text, 'driver'::text])))
);


--
-- Name: reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reviews (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    order_id uuid,
    customer_id uuid,
    partner_id uuid,
    rating integer,
    review_text text,
    partner_response text,
    partner_response_date timestamp with time zone,
    is_published boolean DEFAULT true,
    CONSTRAINT reviews_rating_check CHECK (((rating >= 1) AND (rating <= 5)))
);


--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_roles (
    id uuid NOT NULL,
    role text DEFAULT 'customer'::text NOT NULL,
    CONSTRAINT user_roles_role_check CHECK ((role = ANY (ARRAY['customer'::text, 'partner'::text, 'admin'::text])))
);


--
-- Name: user_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_settings (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    settings jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT user_settings_required_fields CHECK (((settings ? 'theme'::text) AND (settings ? 'notifications'::text) AND (settings ? 'location_sharing'::text) AND (settings ? 'privacy'::text))),
    CONSTRAINT user_settings_theme_values CHECK (((settings ->> 'theme'::text) = ANY (ARRAY['light'::text, 'dark'::text, 'system'::text])))
);


--
-- Name: vehicles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.vehicles (
    id uuid NOT NULL,
    partner_id uuid,
    vehicle_number text,
    license_plate text,
    maintenance_due_date date,
    status text,
    created_at timestamp without time zone
);


--
-- Name: waste_tags; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.waste_tags (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    name_en text NOT NULL,
    description_en text,
    name_ar text,
    description_ar text
);


--
-- Name: waste_type_tags; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.waste_type_tags (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    waste_type_id uuid NOT NULL,
    waste_tag_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: waste_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.waste_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    image_url text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    name_ar text,
    description_ar text,
    name_en text NOT NULL,
    description_en text
);


--
-- Name: countries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.countries ALTER COLUMN id SET DEFAULT nextval('public.countries_id_seq'::regclass);


--
-- Name: debug_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.debug_logs ALTER COLUMN id SET DEFAULT nextval('public.debug_logs_id_seq'::regclass);


--
-- Name: hook_monitoring id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.hook_monitoring ALTER COLUMN id SET DEFAULT nextval('public.hook_monitoring_id_seq'::regclass);


--
-- Name: jwt_hook_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jwt_hook_logs ALTER COLUMN id SET DEFAULT nextval('public.jwt_hook_logs_id_seq'::regclass);


--
-- Name: jwt_hook_tests id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jwt_hook_tests ALTER COLUMN id SET DEFAULT nextval('public.jwt_hook_tests_id_seq'::regclass);


--
-- Name: logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.logs ALTER COLUMN id SET DEFAULT nextval('public.logs_id_seq'::regclass);


--
-- Name: addresses addresses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_pkey PRIMARY KEY (id);


--
-- Name: ai_conversations ai_conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_conversations
    ADD CONSTRAINT ai_conversations_pkey PRIMARY KEY (id);


--
-- Name: ai_feedback ai_feedback_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_feedback
    ADD CONSTRAINT ai_feedback_pkey PRIMARY KEY (id);


--
-- Name: cash_transactions cash_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cash_transactions
    ADD CONSTRAINT cash_transactions_pkey PRIMARY KEY (id);


--
-- Name: countries countries_code_unique; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.countries
    ADD CONSTRAINT countries_code_unique UNIQUE (code);


--
-- Name: countries countries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.countries
    ADD CONSTRAINT countries_pkey PRIMARY KEY (id);


--
-- Name: debug_logs debug_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.debug_logs
    ADD CONSTRAINT debug_logs_pkey PRIMARY KEY (id);


--
-- Name: discounts discounts_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_code_key UNIQUE (code);


--
-- Name: discounts discounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_pkey PRIMARY KEY (id);


--
-- Name: driver_assignments driver_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_pkey PRIMARY KEY (id);


--
-- Name: driver_documents driver_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_documents
    ADD CONSTRAINT driver_documents_pkey PRIMARY KEY (id);


--
-- Name: driver_incidents driver_incidents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_incidents
    ADD CONSTRAINT driver_incidents_pkey PRIMARY KEY (id);


--
-- Name: driver_performance_metrics driver_performance_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_performance_metrics
    ADD CONSTRAINT driver_performance_metrics_pkey PRIMARY KEY (id);


--
-- Name: driver_schedules driver_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_schedules
    ADD CONSTRAINT driver_schedules_pkey PRIMARY KEY (id);


--
-- Name: drivers drivers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_pkey PRIMARY KEY (id);


--
-- Name: dumpster_feature_links dumpster_feature_links_dumpster_id_feature_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_feature_links
    ADD CONSTRAINT dumpster_feature_links_dumpster_id_feature_id_key UNIQUE (dumpster_id, feature_id);


--
-- Name: dumpster_feature_links dumpster_feature_links_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_feature_links
    ADD CONSTRAINT dumpster_feature_links_pkey PRIMARY KEY (id);


--
-- Name: dumpster_features dumpster_features_dumpster_id_feature_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_features
    ADD CONSTRAINT dumpster_features_dumpster_id_feature_key UNIQUE (dumpster_id, feature);


--
-- Name: dumpster_features dumpster_features_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_features
    ADD CONSTRAINT dumpster_features_pkey PRIMARY KEY (id);


--
-- Name: dumpster_images dumpster_images_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_images
    ADD CONSTRAINT dumpster_images_pkey PRIMARY KEY (id);


--
-- Name: dumpster_loads dumpster_loads_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_loads
    ADD CONSTRAINT dumpster_loads_pkey PRIMARY KEY (id);


--
-- Name: dumpster_sizes dumpster_sizes_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_sizes
    ADD CONSTRAINT dumpster_sizes_name_key UNIQUE (name);


--
-- Name: dumpster_sizes dumpster_sizes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_sizes
    ADD CONSTRAINT dumpster_sizes_pkey PRIMARY KEY (id);


--
-- Name: dumpster_types dumpster_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_types
    ADD CONSTRAINT dumpster_types_pkey PRIMARY KEY (id);


--
-- Name: dumpster_waste_types dumpster_waste_types_dumpster_id_waste_type_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_dumpster_id_waste_type_id_key UNIQUE (dumpster_id, waste_type_id);


--
-- Name: dumpster_waste_types dumpster_waste_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_pkey PRIMARY KEY (id);


--
-- Name: dumpsters dumpsters_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpsters
    ADD CONSTRAINT dumpsters_pkey PRIMARY KEY (id);


--
-- Name: features features_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.features
    ADD CONSTRAINT features_pkey PRIMARY KEY (id);


--
-- Name: hook_monitoring hook_monitoring_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.hook_monitoring
    ADD CONSTRAINT hook_monitoring_pkey PRIMARY KEY (id);


--
-- Name: installation_locations installation_locations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.installation_locations
    ADD CONSTRAINT installation_locations_pkey PRIMARY KEY (id);


--
-- Name: jwt_hook_logs jwt_hook_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jwt_hook_logs
    ADD CONSTRAINT jwt_hook_logs_pkey PRIMARY KEY (id);


--
-- Name: jwt_hook_tests jwt_hook_tests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.jwt_hook_tests
    ADD CONSTRAINT jwt_hook_tests_pkey PRIMARY KEY (id);


--
-- Name: logs logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.logs
    ADD CONSTRAINT logs_pkey PRIMARY KEY (id);


--
-- Name: mwan_schedules mwan_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.mwan_schedules
    ADD CONSTRAINT mwan_schedules_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: order_status_history order_status_history_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_pkey PRIMARY KEY (id);


--
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- Name: partners partners_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.partners
    ADD CONSTRAINT partners_pkey PRIMARY KEY (id);


--
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- Name: pricing_plans pricing_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pricing_plans
    ADD CONSTRAINT pricing_plans_pkey PRIMARY KEY (id);


--
-- Name: profile_conflicts profile_conflicts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profile_conflicts
    ADD CONSTRAINT profile_conflicts_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);


--
-- Name: reviews reviews_order_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_order_id_key UNIQUE (order_id);


--
-- Name: reviews reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: user_settings user_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_pkey PRIMARY KEY (id);


--
-- Name: vehicles vehicles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vehicles
    ADD CONSTRAINT vehicles_pkey PRIMARY KEY (id);


--
-- Name: waste_tags waste_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_tags
    ADD CONSTRAINT waste_tags_pkey PRIMARY KEY (id);


--
-- Name: waste_type_tags waste_type_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_pkey PRIMARY KEY (id);


--
-- Name: waste_type_tags waste_type_tags_waste_type_id_waste_tag_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_waste_type_id_waste_tag_id_key UNIQUE (waste_type_id, waste_tag_id);


--
-- Name: waste_types waste_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_types
    ADD CONSTRAINT waste_types_pkey PRIMARY KEY (id);


--
-- Name: countries_code_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX countries_code_idx ON public.countries USING btree (code);


--
-- Name: countries_dial_code_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX countries_dial_code_idx ON public.countries USING btree (dial_code);


--
-- Name: idx_addresses_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_addresses_user_id ON public.addresses USING btree (user_id);


--
-- Name: idx_ai_recommendations_conversation_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_ai_recommendations_conversation_id ON public.ai_recommendations USING btree (conversation_id);


--
-- Name: idx_ai_recommendations_selected_dumpster; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_ai_recommendations_selected_dumpster ON public.ai_recommendations USING btree (selected_dumpster_id);


--
-- Name: idx_cash_transactions_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cash_transactions_driver_id ON public.cash_transactions USING btree (driver_id);


--
-- Name: idx_cash_transactions_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cash_transactions_order_id ON public.cash_transactions USING btree (order_id);


--
-- Name: idx_driver_assignments_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_assignments_driver_id ON public.driver_assignments USING btree (driver_id);


--
-- Name: idx_driver_assignments_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_assignments_order_id ON public.driver_assignments USING btree (order_id);


--
-- Name: idx_driver_assignments_vehicle_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_assignments_vehicle_id ON public.driver_assignments USING btree (vehicle_id);


--
-- Name: idx_driver_documents_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_documents_driver_id ON public.driver_documents USING btree (driver_id);


--
-- Name: idx_driver_incidents_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_incidents_driver_id ON public.driver_incidents USING btree (driver_id);


--
-- Name: idx_driver_incidents_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_incidents_order_id ON public.driver_incidents USING btree (order_id);


--
-- Name: idx_driver_performance_metrics_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_performance_metrics_driver_id ON public.driver_performance_metrics USING btree (driver_id);


--
-- Name: idx_driver_schedules_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_schedules_driver_id ON public.driver_schedules USING btree (driver_id);


--
-- Name: idx_drivers_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_partner_id ON public.drivers USING btree (partner_id);


--
-- Name: idx_drivers_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_profile_id ON public.drivers USING btree (profile_id);


--
-- Name: idx_dumpster_feature_links_dumpster_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpster_feature_links_dumpster_id ON public.dumpster_feature_links USING btree (dumpster_id);


--
-- Name: idx_dumpster_feature_links_feature_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpster_feature_links_feature_id ON public.dumpster_feature_links USING btree (feature_id);


--
-- Name: idx_dumpster_sizes_volume; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpster_sizes_volume ON public.dumpster_sizes USING btree (volume_cubic_yards);


--
-- Name: idx_dumpster_types_name_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpster_types_name_ar ON public.dumpster_types USING btree (name_ar);


--
-- Name: idx_dumpster_types_name_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpster_types_name_en ON public.dumpster_types USING btree (name_en);


--
-- Name: idx_dumpsters_availability; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_availability ON public.dumpsters USING btree (is_available);


--
-- Name: idx_dumpsters_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_partner_id ON public.dumpsters USING btree (partner_id);


--
-- Name: idx_dumpsters_rating; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_rating ON public.dumpsters USING btree (rating);


--
-- Name: idx_dumpsters_size_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_size_id ON public.dumpsters USING btree (size_id);


--
-- Name: idx_dumpsters_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_dumpsters_status ON public.dumpsters USING btree (status);


--
-- Name: idx_notifications_is_read; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_notifications_is_read ON public.notifications USING btree (is_read);


--
-- Name: idx_notifications_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_notifications_user_id ON public.notifications USING btree (user_id);


--
-- Name: idx_orders_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_customer_id ON public.orders USING btree (customer_id);


--
-- Name: idx_orders_delivery_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_delivery_date ON public.orders USING btree (delivery_date);


--
-- Name: idx_orders_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_partner_id ON public.orders USING btree (partner_id);


--
-- Name: idx_orders_payment_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_payment_status ON public.orders USING btree (payment_status);


--
-- Name: idx_orders_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_orders_status ON public.orders USING btree (status);


--
-- Name: idx_partners_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_partners_profile_id ON public.partners USING btree (profile_id);


--
-- Name: idx_partners_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_partners_status ON public.partners USING btree (status);


--
-- Name: idx_payments_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_payments_order_id ON public.payments USING btree (order_id);


--
-- Name: idx_payments_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_payments_status ON public.payments USING btree (status);


--
-- Name: idx_pricing_plans_dumpster_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_pricing_plans_dumpster_type_id ON public.pricing_plans USING btree (dumpster_type_id);


--
-- Name: idx_pricing_plans_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_pricing_plans_is_active ON public.pricing_plans USING btree (is_active);


--
-- Name: idx_pricing_plans_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_pricing_plans_partner_id ON public.pricing_plans USING btree (partner_id);


--
-- Name: idx_profile_conflicts_auth_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profile_conflicts_auth_user_id ON public.profile_conflicts USING btree (auth_user_id);


--
-- Name: idx_profile_conflicts_conflicting_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profile_conflicts_conflicting_profile_id ON public.profile_conflicts USING btree (conflicting_profile_id);


--
-- Name: idx_profiles_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_email ON public.profiles USING btree (email);


--
-- Name: idx_profiles_phone; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_phone ON public.profiles USING btree (phone);


--
-- Name: idx_profiles_user_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_user_type ON public.profiles USING btree (user_type);


--
-- Name: idx_reviews_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_reviews_customer_id ON public.reviews USING btree (customer_id);


--
-- Name: idx_reviews_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_reviews_partner_id ON public.reviews USING btree (partner_id);


--
-- Name: idx_reviews_rating; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_reviews_rating ON public.reviews USING btree (rating);


--
-- Name: idx_user_settings_theme; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_settings_theme ON public.user_settings USING btree (((settings ->> 'theme'::text)));


--
-- Name: idx_user_settings_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_settings_user_id ON public.user_settings USING btree (user_id);


--
-- Name: idx_vehicles_partner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_vehicles_partner_id ON public.vehicles USING btree (partner_id);


--
-- Name: idx_waste_tags_name_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_tags_name_ar ON public.waste_tags USING btree (name_ar);


--
-- Name: idx_waste_tags_name_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_tags_name_en ON public.waste_tags USING btree (name_en);


--
-- Name: idx_waste_types_description_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_description_ar ON public.waste_types USING btree (description_ar);


--
-- Name: idx_waste_types_description_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_description_en ON public.waste_types USING btree (description_en);


--
-- Name: idx_waste_types_name_ar; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_name_ar ON public.waste_types USING btree (name_ar);


--
-- Name: idx_waste_types_name_en; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_waste_types_name_en ON public.waste_types USING btree (name_en);


--
-- Name: jwt_hook_logs_timestamp_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX jwt_hook_logs_timestamp_idx ON public.jwt_hook_logs USING btree ("timestamp" DESC);


--
-- Name: profiles on_user_type_change; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_user_type_change AFTER UPDATE ON public.profiles FOR EACH ROW WHEN ((old.user_type IS DISTINCT FROM new.user_type)) EXECUTE FUNCTION public.handle_user_type_change();


--
-- Name: profiles set_default_role_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER set_default_role_trigger BEFORE INSERT ON public.profiles FOR EACH ROW EXECUTE FUNCTION public.set_default_role();


--
-- Name: profiles sync_user_role_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER sync_user_role_trigger AFTER INSERT OR UPDATE OF user_type ON public.profiles FOR EACH ROW EXECUTE FUNCTION public.sync_user_role();


--
-- Name: countries update_countries_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_countries_updated_at BEFORE UPDATE ON public.countries FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: dumpster_loads update_order_total_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_order_total_trigger AFTER INSERT OR DELETE ON public.dumpster_loads FOR EACH ROW EXECUTE FUNCTION public.update_order_total();


--
-- Name: user_settings update_user_settings_updated_at_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_user_settings_updated_at_trigger BEFORE UPDATE ON public.user_settings FOR EACH ROW EXECUTE FUNCTION public.update_user_settings_updated_at();


--
-- Name: addresses addresses_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id);


--
-- Name: ai_conversations ai_conversations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_conversations
    ADD CONSTRAINT ai_conversations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);


--
-- Name: ai_feedback ai_feedback_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_feedback
    ADD CONSTRAINT ai_feedback_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.ai_conversations(id) ON DELETE CASCADE;


--
-- Name: ai_feedback ai_feedback_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_feedback
    ADD CONSTRAINT ai_feedback_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);


--
-- Name: ai_recommendations ai_recommendations_resulted_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_recommendations
    ADD CONSTRAINT ai_recommendations_resulted_order_id_fkey FOREIGN KEY (resulted_order_id) REFERENCES public.orders(id);


--
-- Name: ai_recommendations ai_recommendations_selected_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_recommendations
    ADD CONSTRAINT ai_recommendations_selected_dumpster_id_fkey FOREIGN KEY (selected_dumpster_id) REFERENCES public.dumpsters(id);


--
-- Name: cash_transactions cash_transactions_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cash_transactions
    ADD CONSTRAINT cash_transactions_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: cash_transactions cash_transactions_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cash_transactions
    ADD CONSTRAINT cash_transactions_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: discounts discounts_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: driver_assignments driver_assignments_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_assignments driver_assignments_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: driver_assignments driver_assignments_vehicle_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_assignments
    ADD CONSTRAINT driver_assignments_vehicle_id_fkey FOREIGN KEY (vehicle_id) REFERENCES public.vehicles(id);


--
-- Name: driver_documents driver_documents_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_documents
    ADD CONSTRAINT driver_documents_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_incidents driver_incidents_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_incidents
    ADD CONSTRAINT driver_incidents_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_incidents driver_incidents_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_incidents
    ADD CONSTRAINT driver_incidents_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: driver_performance_metrics driver_performance_metrics_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_performance_metrics
    ADD CONSTRAINT driver_performance_metrics_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: driver_schedules driver_schedules_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_schedules
    ADD CONSTRAINT driver_schedules_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id);


--
-- Name: drivers drivers_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: drivers drivers_profile_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_profile_id_fkey FOREIGN KEY (profile_id) REFERENCES public.profiles(id);


--
-- Name: dumpster_feature_links dumpster_feature_links_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_feature_links
    ADD CONSTRAINT dumpster_feature_links_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_feature_links dumpster_feature_links_feature_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_feature_links
    ADD CONSTRAINT dumpster_feature_links_feature_id_fkey FOREIGN KEY (feature_id) REFERENCES public.features(id) ON DELETE CASCADE;


--
-- Name: dumpster_features dumpster_features_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_features
    ADD CONSTRAINT dumpster_features_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_images dumpster_images_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_images
    ADD CONSTRAINT dumpster_images_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_loads dumpster_loads_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_loads
    ADD CONSTRAINT dumpster_loads_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: dumpster_waste_types dumpster_waste_types_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id) ON DELETE CASCADE;


--
-- Name: dumpster_waste_types dumpster_waste_types_waste_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpster_waste_types
    ADD CONSTRAINT dumpster_waste_types_waste_type_id_fkey FOREIGN KEY (waste_type_id) REFERENCES public.waste_types(id) ON DELETE CASCADE;


--
-- Name: dumpsters dumpsters_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpsters
    ADD CONSTRAINT dumpsters_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: dumpsters dumpsters_size_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dumpsters
    ADD CONSTRAINT dumpsters_size_id_fkey FOREIGN KEY (size_id) REFERENCES public.dumpster_sizes(id);


--
-- Name: notifications notifications_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);


--
-- Name: order_status_history order_status_history_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: order_status_history order_status_history_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.profiles(id);


--
-- Name: orders orders_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.profiles(id);


--
-- Name: orders orders_discount_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_discount_id_fkey FOREIGN KEY (discount_id) REFERENCES public.discounts(id);


--
-- Name: orders orders_dumpster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_dumpster_id_fkey FOREIGN KEY (dumpster_id) REFERENCES public.dumpsters(id);


--
-- Name: orders orders_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: orders orders_pricing_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pricing_plan_id_fkey FOREIGN KEY (pricing_plan_id) REFERENCES public.pricing_plans(id);


--
-- Name: partners partners_profile_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.partners
    ADD CONSTRAINT partners_profile_id_fkey FOREIGN KEY (profile_id) REFERENCES public.profiles(id);


--
-- Name: payments payments_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: pricing_plans pricing_plans_dumpster_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pricing_plans
    ADD CONSTRAINT pricing_plans_dumpster_type_id_fkey FOREIGN KEY (dumpster_type_id) REFERENCES public.dumpster_types(id);


--
-- Name: pricing_plans pricing_plans_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pricing_plans
    ADD CONSTRAINT pricing_plans_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: profile_conflicts profile_conflicts_auth_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profile_conflicts
    ADD CONSTRAINT profile_conflicts_auth_user_id_fkey FOREIGN KEY (auth_user_id) REFERENCES auth.users(id);


--
-- Name: profile_conflicts profile_conflicts_conflicting_profile_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profile_conflicts
    ADD CONSTRAINT profile_conflicts_conflicting_profile_id_fkey FOREIGN KEY (conflicting_profile_id) REFERENCES public.profiles(id);


--
-- Name: profile_conflicts profile_conflicts_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profile_conflicts
    ADD CONSTRAINT profile_conflicts_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES auth.users(id);


--
-- Name: profiles profiles_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id);


--
-- Name: reviews reviews_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.profiles(id);


--
-- Name: reviews reviews_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- Name: reviews reviews_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: user_roles user_roles_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id);


--
-- Name: user_settings user_settings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id);


--
-- Name: vehicles vehicles_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vehicles
    ADD CONSTRAINT vehicles_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partners(id);


--
-- Name: waste_type_tags waste_type_tags_waste_tag_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_waste_tag_id_fkey FOREIGN KEY (waste_tag_id) REFERENCES public.waste_tags(id) ON DELETE CASCADE;


--
-- Name: waste_type_tags waste_type_tags_waste_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.waste_type_tags
    ADD CONSTRAINT waste_type_tags_waste_type_id_fkey FOREIGN KEY (waste_type_id) REFERENCES public.waste_types(id) ON DELETE CASCADE;


--
-- Name: user_roles Admin users can update roles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admin users can update roles" ON public.user_roles USING ((EXISTS ( SELECT 1
   FROM public.user_roles user_roles_1
  WHERE ((user_roles_1.id = auth.uid()) AND (user_roles_1.role = 'admin'::text))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM public.user_roles user_roles_1
  WHERE ((user_roles_1.id = auth.uid()) AND (user_roles_1.role = 'admin'::text)))));


--
-- Name: user_roles Admin users can view all roles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admin users can view all roles" ON public.user_roles FOR SELECT USING ((EXISTS ( SELECT 1
   FROM public.user_roles user_roles_1
  WHERE ((user_roles_1.id = auth.uid()) AND (user_roles_1.role = 'admin'::text)))));


--
-- Name: addresses Allow admin access to all addresses; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all addresses" ON public.addresses USING (public.authorize('admin'::text));


--
-- Name: discounts Allow admin access to all discounts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all discounts" ON public.discounts USING (public.authorize('admin'::text));


--
-- Name: dumpster_feature_links Allow admin access to all dumpster_feature_links; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpster_feature_links" ON public.dumpster_feature_links USING (public.authorize('admin'::text));


--
-- Name: dumpsters Allow admin access to all dumpsters; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all dumpsters" ON public.dumpsters USING (public.authorize('admin'::text));


--
-- Name: features Allow admin access to all features; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all features" ON public.features USING (public.authorize('admin'::text));


--
-- Name: notifications Allow admin access to all notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all notifications" ON public.notifications USING (public.authorize('admin'::text));


--
-- Name: orders Allow admin access to all orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all orders" ON public.orders USING (public.authorize('admin'::text));


--
-- Name: partners Allow admin access to all partners; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all partners" ON public.partners USING (public.authorize('admin'::text));


--
-- Name: payments Allow admin access to all payments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all payments" ON public.payments USING (public.authorize('admin'::text));


--
-- Name: profiles Allow admin access to all profiles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all profiles" ON public.profiles USING (public.authorize('admin'::text));


--
-- Name: reviews Allow admin access to all reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow admin access to all reviews" ON public.reviews USING (public.authorize('admin'::text));


--
-- Name: orders Allow customers to create orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow customers to create orders" ON public.orders FOR INSERT WITH CHECK ((auth.uid() = customer_id));


--
-- Name: reviews Allow customers to create reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow customers to create reviews" ON public.reviews FOR INSERT WITH CHECK ((auth.uid() = customer_id));


--
-- Name: addresses Allow customers to manage their own addresses; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow customers to manage their own addresses" ON public.addresses USING ((auth.uid() = user_id));


--
-- Name: orders Allow customers to update their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow customers to update their own orders" ON public.orders FOR UPDATE USING (((auth.uid() = customer_id) AND (status = ANY (ARRAY['pending'::text, 'confirmed'::text]))));


--
-- Name: reviews Allow customers to update their own reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow customers to update their own reviews" ON public.reviews FOR UPDATE USING ((auth.uid() = customer_id));


--
-- Name: orders Allow customers to view their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow customers to view their own orders" ON public.orders FOR SELECT USING ((auth.uid() = customer_id));


--
-- Name: partners Allow partners to manage their own data; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to manage their own data" ON public.partners USING (((auth.uid() = profile_id) OR (EXISTS ( SELECT 1
   FROM public.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.user_type = 'admin'::text))))));


--
-- Name: dumpsters Allow partners to manage their own dumpsters; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to manage their own dumpsters" ON public.dumpsters USING ((public.authorize('partner'::text) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: pricing_plans Allow partners to manage their own pricing plans; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to manage their own pricing plans" ON public.pricing_plans USING ((public.authorize('partner'::text) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: reviews Allow partners to respond to reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to respond to reviews" ON public.reviews FOR UPDATE USING ((public.authorize('partner'::text) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid()))) AND (partner_response IS NULL)));


--
-- Name: partners Allow partners to update their own data; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to update their own data" ON public.partners FOR UPDATE USING ((public.authorize('partner'::text) AND (auth.uid() = profile_id)));


--
-- Name: orders Allow partners to update their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to update their own orders" ON public.orders FOR UPDATE USING ((public.authorize('partner'::text) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: partners Allow partners to view their own data; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to view their own data" ON public.partners FOR SELECT USING ((public.authorize('partner'::text) AND (auth.uid() = profile_id)));


--
-- Name: orders Allow partners to view their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow partners to view their own orders" ON public.orders FOR SELECT USING ((public.authorize('partner'::text) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: discounts Allow public read access for discounts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for discounts" ON public.discounts FOR SELECT USING (true);


--
-- Name: dumpster_feature_links Allow public read access for dumpster_feature_links; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_feature_links" ON public.dumpster_feature_links FOR SELECT USING (true);


--
-- Name: dumpster_features Allow public read access for dumpster_features; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_features" ON public.dumpster_features FOR SELECT USING (true);


--
-- Name: dumpster_images Allow public read access for dumpster_images; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_images" ON public.dumpster_images FOR SELECT USING (true);


--
-- Name: dumpster_sizes Allow public read access for dumpster_sizes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_sizes" ON public.dumpster_sizes FOR SELECT USING (true);


--
-- Name: dumpster_types Allow public read access for dumpster_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_types" ON public.dumpster_types FOR SELECT USING (true);


--
-- Name: dumpster_waste_types Allow public read access for dumpster_waste_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpster_waste_types" ON public.dumpster_waste_types FOR SELECT USING (true);


--
-- Name: dumpsters Allow public read access for dumpsters; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for dumpsters" ON public.dumpsters FOR SELECT USING (true);


--
-- Name: features Allow public read access for features; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for features" ON public.features FOR SELECT USING (true);


--
-- Name: installation_locations Allow public read access for installation_locations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for installation_locations" ON public.installation_locations FOR SELECT USING (true);


--
-- Name: pricing_plans Allow public read access for pricing_plans; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for pricing_plans" ON public.pricing_plans FOR SELECT USING (true);


--
-- Name: reviews Allow public read access for reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for reviews" ON public.reviews FOR SELECT USING ((is_published = true));


--
-- Name: waste_tags Allow public read access for waste_tags; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for waste_tags" ON public.waste_tags FOR SELECT USING (true);


--
-- Name: waste_type_tags Allow public read access for waste_type_tags; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for waste_type_tags" ON public.waste_type_tags FOR SELECT USING (true);


--
-- Name: waste_types Allow public read access for waste_types; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access for waste_types" ON public.waste_types FOR SELECT USING (true);


--
-- Name: partners Allow public read access to partners; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access to partners" ON public.partners FOR SELECT USING (true);


--
-- Name: profiles Allow public read access to profiles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow public read access to profiles" ON public.profiles FOR SELECT USING (true);


--
-- Name: ai_feedback Allow users to create AI feedback; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to create AI feedback" ON public.ai_feedback FOR INSERT WITH CHECK ((auth.uid() = user_id));


--
-- Name: reviews Allow users to create reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to create reviews" ON public.reviews FOR INSERT WITH CHECK ((auth.uid() = customer_id));


--
-- Name: addresses Allow users to manage their own addresses; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to manage their own addresses" ON public.addresses USING ((auth.uid() = user_id));


--
-- Name: profiles Allow users to manage their own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to manage their own profile" ON public.profiles USING ((auth.uid() = id));


--
-- Name: user_settings Allow users to manage their own settings; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to manage their own settings" ON public.user_settings USING ((auth.uid() = user_id));


--
-- Name: notifications Allow users to update their own notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to update their own notifications" ON public.notifications FOR UPDATE USING ((auth.uid() = user_id));


--
-- Name: reviews Allow users to update their own reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to update their own reviews" ON public.reviews FOR UPDATE USING ((auth.uid() = customer_id));


--
-- Name: ai_conversations Allow users to view their own AI conversations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own AI conversations" ON public.ai_conversations FOR SELECT USING ((auth.uid() = user_id));


--
-- Name: notifications Allow users to view their own notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own notifications" ON public.notifications FOR SELECT USING ((auth.uid() = user_id));


--
-- Name: payments Allow users to view their own payments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own payments" ON public.payments FOR SELECT USING ((auth.uid() = ( SELECT orders.customer_id
   FROM public.orders
  WHERE (orders.id = payments.order_id))));


--
-- Name: reviews Allow users to view their own reviews; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow users to view their own reviews" ON public.reviews FOR SELECT USING ((auth.uid() = customer_id));


--
-- Name: orders Customers can update own orders via authorize; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Customers can update own orders via authorize" ON public.orders FOR UPDATE USING (((auth.uid() = customer_id) AND (status = ANY (ARRAY['pending'::text, 'confirmed'::text]))));


--
-- Name: orders Customers can view own orders via authorize; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Customers can view own orders via authorize" ON public.orders FOR SELECT USING ((auth.uid() = customer_id));


--
-- Name: orders Partners can access orders via authorize; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Partners can access orders via authorize" ON public.orders USING ((public.authorize('partner'::text) AND (partner_id = ( SELECT partners.id
   FROM public.partners
  WHERE (partners.profile_id = auth.uid())))));


--
-- Name: orders Users can create their own orders; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can create their own orders" ON public.orders FOR INSERT TO authenticated WITH CHECK (((auth.uid() = customer_id) OR (EXISTS ( SELECT 1
   FROM auth.users
  WHERE (users.id = auth.uid())))));


--
-- Name: profiles Users can delete own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete own profile" ON public.profiles FOR DELETE USING ((auth.uid() = id));


--
-- Name: profiles Users can insert own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK ((auth.uid() = id));


--
-- Name: profiles Users can update own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING ((auth.uid() = id)) WITH CHECK ((auth.uid() = id));


--
-- Name: profiles Users can view own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING ((auth.uid() = id));


--
-- Name: user_roles Users can view their own role; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view their own role" ON public.user_roles FOR SELECT USING ((auth.uid() = id));


--
-- Name: addresses; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.addresses ENABLE ROW LEVEL SECURITY;

--
-- Name: logs admins_can_view_logs; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY admins_can_view_logs ON public.logs FOR SELECT USING ((auth.uid() IN ( SELECT profiles.id
   FROM public.profiles
  WHERE (profiles.user_type = 'admin'::text))));


--
-- Name: ai_conversations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.ai_conversations ENABLE ROW LEVEL SECURITY;

--
-- Name: ai_feedback; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.ai_feedback ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles authenticated_access; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY authenticated_access ON public.profiles TO authenticated USING ((auth.uid() = id)) WITH CHECK ((auth.uid() = id));


--
-- Name: cash_transactions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.cash_transactions ENABLE ROW LEVEL SECURITY;

--
-- Name: discounts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.discounts ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_assignments; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_assignments ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_documents; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_documents ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_incidents; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_incidents ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_performance_metrics; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_performance_metrics ENABLE ROW LEVEL SECURITY;

--
-- Name: driver_schedules; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.driver_schedules ENABLE ROW LEVEL SECURITY;

--
-- Name: drivers; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.drivers ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_feature_links; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_feature_links ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_features; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_features ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_images; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_images ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_sizes; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_sizes ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_types; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_types ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpster_waste_types; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpster_waste_types ENABLE ROW LEVEL SECURITY;

--
-- Name: dumpsters; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.dumpsters ENABLE ROW LEVEL SECURITY;

--
-- Name: features; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.features ENABLE ROW LEVEL SECURITY;

--
-- Name: installation_locations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.installation_locations ENABLE ROW LEVEL SECURITY;

--
-- Name: jwt_hook_logs; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.jwt_hook_logs ENABLE ROW LEVEL SECURITY;

--
-- Name: jwt_hook_logs jwt_hook_logs_insert_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY jwt_hook_logs_insert_policy ON public.jwt_hook_logs FOR INSERT TO authenticated, anon, service_role, postgres WITH CHECK (true);


--
-- Name: jwt_hook_logs jwt_hook_logs_select_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY jwt_hook_logs_select_policy ON public.jwt_hook_logs FOR SELECT TO authenticated, anon, service_role, postgres USING (true);


--
-- Name: jwt_hook_logs jwt_hook_logs_update_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY jwt_hook_logs_update_policy ON public.jwt_hook_logs FOR UPDATE TO authenticated, anon, service_role, postgres USING (true);


--
-- Name: logs; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.logs ENABLE ROW LEVEL SECURITY;

--
-- Name: mwan_schedules; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.mwan_schedules ENABLE ROW LEVEL SECURITY;

--
-- Name: notifications; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

--
-- Name: order_status_history; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.order_status_history ENABLE ROW LEVEL SECURITY;

--
-- Name: orders; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;

--
-- Name: orders orders_insert_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY orders_insert_policy ON public.orders FOR INSERT TO authenticated WITH CHECK (true);


--
-- Name: orders orders_select_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY orders_select_policy ON public.orders FOR SELECT TO authenticated USING ((customer_id = auth.uid()));


--
-- Name: orders orders_update_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY orders_update_policy ON public.orders FOR UPDATE TO authenticated USING (((customer_id = auth.uid()) AND (status = ANY (ARRAY['pending'::text, 'confirmed'::text]))));


--
-- Name: partners; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.partners ENABLE ROW LEVEL SECURITY;

--
-- Name: payments; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles phone_lookup_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY phone_lookup_policy ON public.profiles FOR SELECT USING (true);


--
-- Name: pricing_plans; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.pricing_plans ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: reviews; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

--
-- Name: user_roles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

--
-- Name: user_settings; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

--
-- Name: vehicles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;

--
-- Name: waste_tags; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.waste_tags ENABLE ROW LEVEL SECURITY;

--
-- Name: waste_type_tags; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.waste_type_tags ENABLE ROW LEVEL SECURITY;

--
-- Name: waste_types; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.waste_types ENABLE ROW LEVEL SECURITY;

--
-- PostgreSQL database dump complete
--

