# Test with your current configuration
curl -X POST https://api.openai.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ********************************************************************************************************************************************************************" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful dumpster rental assistant."
      },
      {
        "role": "user",
        "content": "I need a dumpster for a kitchen renovation"
      }
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'

# Or using environment variables (safer)
# export OPENAI_API_KEY='your-api-key'
# curl -X POST https://api.openai.com/v1/chat/completions \
#   -H "Content-Type: application/json" \
#   -H "Authorization: Bearer $OPENAI_API_KEY" \
#   -d '{
#     "model": "gpt-3.5-turbo",
#     "messages": [
#       {
#         "role": "system",
#         "content": "You are a helpful dumpster rental assistant."
#       },
#       {
#         "role": "user",
#         "content": "I need a dumpster for a kitchen renovation"
#       }
#     ],
#     "temperature": 0.7,
#     "max_tokens": 150
#   }'