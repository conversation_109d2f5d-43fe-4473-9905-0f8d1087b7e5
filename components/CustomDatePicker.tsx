import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { styled } from 'nativewind';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

interface CustomDatePickerProps {
  date: Date;
  onDateChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  placeholder?: string;
  formatDate?: (date: Date) => string;
}

export default function CustomDatePicker({
  date,
  onDateChange,
  minimumDate,
  maximumDate,
  placeholder = 'Select Date',
  formatDate = (date: Date) => date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}: CustomDatePickerProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      onDateChange(selectedDate);
    }
  };

  return (
    <>
      <StyledTouchableOpacity 
        className="flex-row items-center justify-between p-4 border border-gray-200 rounded-xl"
        onPress={() => setShowDatePicker(true)}
        style={styles.container}
      >
        <StyledView className="flex-row items-center">
          <StyledView 
            className="w-10 h-10 rounded-full mr-3 items-center justify-center"
            style={{ backgroundColor: '#3b82f6' }}
          >
            <MaterialIcons name="calendar-today" size={20} color="#ffffff" />
          </StyledView>
          <StyledText className="text-base text-gray-700 font-medium">
            {formatDate(date)}
          </StyledText>
        </StyledView>
        <MaterialIcons name="keyboard-arrow-down" size={24} color="#3b82f6" />
      </StyledTouchableOpacity>
      
      {showDatePicker && (
        <DateTimePicker
          value={date}
          mode="date"
          display="default"
          minimumDate={minimumDate}
          maximumDate={maximumDate}
          onChange={handleDateChange}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f9fafb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  }
}); 