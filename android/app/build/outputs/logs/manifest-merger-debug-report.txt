-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:1:1-45:12
MERGED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:1:1-45:12
INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:expo] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_datetimepicker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-community_slider] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/slider/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-get-random-values] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-get-random-values/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-linear-gradient] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-localization] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-localization/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-restart] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-restart/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-application] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-application/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-asset] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-asset/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-background-fetch] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:expo-blur] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-blur/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-crypto] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-crypto/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:expo-eas-client] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-eas-client/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-font/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-haptics] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-19:12
MERGED from [:expo-image-loader] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-loader/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-57:12
MERGED from [:expo-manifests] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-keep-awake] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-keep-awake/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linear-gradient] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linking/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-17:12
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-43:12
MERGED from [:expo-splash-screen] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-splash-screen/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-structured-headers] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-structured-headers/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-29:12
MERGED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [:unimodules-app-loader] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f7e0f6ce9084451fa15b37e0e69c94e8/transformed/play-services-location-21.0.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.material:material:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd840b7336fae8c068846fca431063fc/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:2:1-16:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae922c10d4c361b23fc1eafd8b6b470f/transformed/glide-plugin-3.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73407362229b62c2c82f0aaf9d923d0c/transformed/avif-integration-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ec6387658310c87087d29e8de610082c/transformed/glide-transformations-4.3.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e38f0af3446908f03866031a3d815bd/transformed/glide-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:2:1-38:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78365384a40b1f46f521dcd0ed6827b8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/96701bd0e62441f1ee4a8284e2b9ab00/transformed/awebp-3.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/f276553bc42918ed41210b70f4f11290/transformed/apng-3.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/600d0424b6f5c1d836ea23db9b2d05d1/transformed/gif-3.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/732aa76c2ebe8b13a7363cc4cd7fca95/transformed/avif-3.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.10.2/transforms/04775ad1cee85aa75275c67658594e11/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b13fa7f6e3a908ed2b62743ed291296e/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6ca38eae6048548125d4d188667dff8e/transformed/frameanimation-3.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aa88c846e864571f4558ffd55398a2ef/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ee7d2922514238145de538091a344095/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbaa152913f58306ffe0234d21614abc/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/656bad39a570112f127c49fdd084518f/transformed/play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/624566eb4cbc2de9ede471aee14daa2b/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/b287b31968ab21b18927dd3ba3fa3d60/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/7d18829510fdac1c32a3242148401aa9/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/55a8346d94054c62e1fe614bbe71aa8e/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8da1700ff38e9c23d62e4c02533156d/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/fab3dc437d1e3d8d2425324185a45391/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/a515e20e08bed2c8e738c639f1a0faee/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/2f1c30e91b6d2e6fd2456b3e6143dadd/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/883ab54f112a35bd4b277555c75f0182/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/44d01de45ea2b50d367d43535f14f8fb/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb211ff01ae410ebbe920d3bfa3171b7/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5e8a2ae799851ff2f57aa00e4f31bf06/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/78f57a7bb86353430c8945a583799761/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.7.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6f98901d6bb8c5633e09e5b0cbd6227a/transformed/activity-ktx-1.7.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/a05f65f98a3f711ca5f04aa76a885af9/transformed/activity-1.7.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/469a3b08c3dd992816911ec425a5114a/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/945cbe2625153f550a7a92a5f9980ee9/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01170bcef019d1ab93baf86184e97fdf/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a62157a70bd7a9bad19ed0561155fa45/transformed/animated-gif-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4055fb508865ca850b1065916834ca36/transformed/webpsupport-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0abadb77610fe19558ec9d5cf546f6a0/transformed/fresco-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f3d468eda90f1c940a7444355dea7c5d/transformed/imagepipeline-okhttp3-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed3f0395a3925029af8b1b55e696908/transformed/animated-base-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1563aff4217c4422705d2f6187d242f3/transformed/animated-drawable-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f5a1bdfd74586d7ae6e8b994c434b1da/transformed/vito-options-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3eb77f99a0a4c0f434aa141e0e47671a/transformed/drawee-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/87bce6bf8ee76cfec5a871ad554e848e/transformed/nativeimagefilters-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6707c325cc743e33edcb95f888fbbd1a/transformed/memory-type-native-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/745d36945e860747d86472fdbcd8c9dc/transformed/memory-type-java-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d956af59bd023c99c8fa68daa94a6dfd/transformed/imagepipeline-native-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ef7f9eaaf5ff59029b8903b5a1f0679d/transformed/memory-type-ashmem-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7a2339201461aa9a87cb2889f13df5d/transformed/imagepipeline-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cc8723afb482eade78bc6e1e9ece7c98/transformed/nativeimagetranscoder-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd25b26d30803f75123dcad040add85e/transformed/imagepipeline-base-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3be0548b4515129a0402ac677c6d8b38/transformed/middleware-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1ccc5258618ec7c85c5e130c6e67b93/transformed/ui-common-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/46c3ea75bb2e0ad04597b021895937d1/transformed/soloader-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/75a7388f1cdd115d015adc34a83e91c4/transformed/fbcore-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0abb540ffb27e310e3f408230b89d02/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c43952f8f59a22fb6d26fd05e79f07e1/transformed/core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/04b22cf1ef75a66ebc79a74febee8798/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c0176138d5a50e6382eba7391d62d81f/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f23ba8be396710e62310be87c194db08/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b0d83f5f37dfe24d221a192beefa02f8/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/83bd2c07f36535642edd28fef9d42bfa/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7a8e1525ff3ba9c5ea5b1ebde0949214/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/888c2e815693a7375208a6c1b5842b51/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae42b5ae8cfbb0073e969b3673e1b0b3/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5c4e920fc5b2796dac71013ed405eb7/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8ea5f0f61a91e8d03204398d9dc59341/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bea76edc7a5fb54404bf485c9952c6d2/transformed/graphics-path-1.0.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5372576be014a8057131e4e22f5e29a4/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9107eb85ecda943c752360494b57cd83/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f62ec52335bea96696167e60137cb3fb/transformed/lifecycle-viewmodel-2.8.3/AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7707b8eb7714dfdee38c21efae6f064/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/fa088aeec92d791888c412e6a6612471/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/3df36557eb0fd39a3943f295f628c4fc/transformed/lifecycle-runtime-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/e511d7d13457d4fd3c8cfc088eee17cf/transformed/lifecycle-viewmodel-ktx-2.8.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f8f4737cd213c1facf2bd8e94c837cd/transformed/lifecycle-viewmodel-savedstate-2.8.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b95749c06b646fd1c3a9ac856155914c/transformed/lifecycle-service-2.8.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/68e22083c84f8288a1dae579511d9ada/transformed/lifecycle-livedata-core-ktx-2.8.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b67cad0d8cbb2a0a9cebef1ca032d5b8/transformed/lifecycle-livedata-core-2.8.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/3cee9d7547171d812c60cb52c92f8ded/transformed/lifecycle-livedata-2.8.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/6bbc45b49780d0f1dcdfe2154b677c34/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/781afb608f1e9306868332440704a483/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/53d3d1d5c4745a7cdf3627473ba36bdd/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6bf8e080a1512b40ba60657a9a1ec9df/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10e3cd6e9b5af14de89de85f76f6d43f/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/22888200b96b2c4879b3c2fd2f38418c/transformed/fragment-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f95c7cbe19ff935c584b6b83cf955e34/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c93a1ed3adb70b128ee8f1adc4dba2c/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0e59ab2bf98c1885e22cd08fc5913475/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/de67d545462883a39b61f9fa66497fbf/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:17:1-31:12
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/630888bd9b86257d447d62cf332f68a5/transformed/vito-renderer-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c98921e59ac747fcf347ad028582653e/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/478fbcbff80134e348112c6b25f2d8b7/transformed/sqlite-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.react:hermes-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/00539a65bc277a5b7e243e70ff19cd3c/transformed/hermes-android-0.76.9-debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d33c3415ae1fa51d53afb00f59c70d54/transformed/BlurView-version-2.0.6/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c385b0eb0a143d5e88d1e23ecece994f/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0ddb78eef896d32fcbde11e3ecd5ad8d/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/c7088d96ee3e93bb0908d1378a1c5001/transformed/exifinterface-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5b1f8286688359a3fea1d8e4ae39a492/transformed/gifdecoder-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.databinding:viewbinding:7.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a6d8c4cdb736b5f8573172457fc3f70/transformed/viewbinding-7.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5139976b9c95b58007b43e96f930b302/transformed/firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c18c4c4a3d1d82f2fb3eea9a81faf51f/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ad8d5d694b4a772c799c82f6815f4d14/transformed/transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae5f120f2548049627e5cc8757b59548/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/20460333bb10dab577147e27e90d797c/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71081a360238d190898fc111501bb847/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a6a830e8a4a8f3f3fdcb6c10667962fe/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6673db4abdb30abedf56845ff85db743/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f87530d05483e6ae8181cde56795ecae/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0be21279d28c15c6efbf3823d88f33fc/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:2:1-15:12
MERGED from [com.facebook.fbjni:fbjni:0.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/77970482e3b9c1aed01991343fbf672a/transformed/fbjni-0.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:2:1-13:12
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/d724e9c4260ee79d124aa1a14072324d/transformed/androidsvg-aar-1.4/AndroidManifest.xml:2:1-11:12
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:2:1-30:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:2:1-52:12
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.10.2/transforms/eca0361f6791774932d8e61f95cdbba8/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:2:3-78
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-81
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-81
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:12:5-81
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:12:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:3:3-76
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-79
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:14:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:14:5-79
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:3:20-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:4:3-76
MERGED from [:expo-updates] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [:expo-updates] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-79
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:13:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:13:5-79
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:4:20-74
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:5:3-64
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-67
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:5:20-62
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:6:3-74
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:23:5-77
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:6:20-72
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:7:3-77
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-80
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:5-17:38
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:5-17:38
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-80
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-80
	android:maxSdkVersion
		ADDED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:9-35
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:7:20-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:8:3-78
MERGED from [:expo-background-fetch] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-81
MERGED from [:expo-background-fetch] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-81
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-81
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:27:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:8:20-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:9:3-68
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:9:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:10:3-75
MERGED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:10:3-75
MERGED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:10:3-75
MERGED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:10:20-73
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:11:3-63
MERGED from [:expo-haptics] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
MERGED from [:expo-haptics] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:11:20-61
uses-permission#android.permission.WAKE_LOCK
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:12:3-65
MERGED from [:expo-background-fetch] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
MERGED from [:expo-background-fetch] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:25:5-68
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:12:20-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:13:3-78
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-81
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-81
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:13:20-76
queries
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:14:3-20:13
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-18:15
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:5-25:15
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:5-25:15
MERGED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:15
MERGED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:30:5-34:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:9:5-20:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:9:5-20:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:15:5-19:14
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:16:7-58
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:16:15-56
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:17:7-67
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:17:17-65
data
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:7-37
	android:scheme
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:13-35
application
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:3-44:17
MERGED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:3-44:17
MERGED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:3-44:17
INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml:6:5-162
MERGED from [:react-native-community_datetimepicker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-20
MERGED from [:react-native-community_datetimepicker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-20
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:5-31:19
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:5-55:19
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:5-55:19
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-15:19
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-15:19
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-41:19
MERGED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-27:19
MERGED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-27:19
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f7e0f6ce9084451fa15b37e0e69c94e8/transformed/play-services-location-21.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f7e0f6ce9084451fa15b37e0e69c94e8/transformed/play-services-location-21.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.material:material:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/AndroidManifest.xml:24:5-20
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:22:5-36:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78365384a40b1f46f521dcd0ed6827b8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78365384a40b1f46f521dcd0ed6827b8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ee7d2922514238145de538091a344095/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ee7d2922514238145de538091a344095/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbaa152913f58306ffe0234d21614abc/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbaa152913f58306ffe0234d21614abc/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/656bad39a570112f127c49fdd084518f/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/656bad39a570112f127c49fdd084518f/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10e3cd6e9b5af14de89de85f76f6d43f/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10e3cd6e9b5af14de89de85f76f6d43f/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c93a1ed3adb70b128ee8f1adc4dba2c/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c93a1ed3adb70b128ee8f1adc4dba2c/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d33c3415ae1fa51d53afb00f59c70d54/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d33c3415ae1fa51d53afb00f59c70d54/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71081a360238d190898fc111501bb847/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71081a360238d190898fc111501bb847/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:7:5-13:19
MERGED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:7:5-13:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:11:5-20
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:18:5-28:19
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:18:5-28:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.10.2/transforms/eca0361f6791774932d8e61f95cdbba8/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.10.2/transforms/eca0361f6791774932d8e61f95cdbba8/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:5:5-6:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:222-248
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:222-248
	android:label
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:48-80
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:48-80
	tools:ignore
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:116-161
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:116-161
	tools:targetApi
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml:6:54-74
	android:icon
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:81-115
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:81-115
	android:allowBackup
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:162-189
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:162-189
	android:theme
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:190-221
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:190-221
	tools:replace
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:249-284
		REJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:249-284
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:16-47
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:16-47
meta-data#com.google.android.geo.API_KEY
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:22:5-119
	android:value
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:22:62-117
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:22:16-61
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:23:5-139
	android:resource
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:23:88-137
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:23:16-87
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:24:5-135
	android:resource
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:24:87-133
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:24:16-86
meta-data#expo.modules.notifications.default_notification_color
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:25:5-136
	android:resource
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:25:85-134
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:25:16-84
meta-data#expo.modules.notifications.default_notification_icon
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:26:5-132
	android:resource
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:26:84-130
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:26:16-83
meta-data#expo.modules.updates.ENABLED
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:27:5-83
	android:value
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:27:60-81
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:27:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:28:5-105
	android:value
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:28:81-103
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:28:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:29:5-99
	android:value
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:29:80-97
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:29:16-79
activity#com.dumpster.userapp.MainActivity
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:5-42:16
	android:screenOrientation
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:280-316
	android:launchMode
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:135-166
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:167-209
	android:exported
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:256-279
	android:configChanges
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:44-134
	android:theme
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:210-255
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:31:7-34:23
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:32:9-60
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:32:17-58
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:33:9-68
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:33:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.dumpster.userapp+data:scheme:dumpster-user-app
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:35:7-41:23
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:37:9-67
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:37:19-65
uses-library#org.apache.http.legacy
ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:43:5-83
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:39:9-41:40
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:43:57-81
	android:name
		ADDED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:43:19-56
uses-sdk
INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
MERGED from [:expo] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/slider/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/slider/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-get-random-values/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-get-random-values/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-localization] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-localization/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-localization] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-localization/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-restart] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-restart/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-restart] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-restart/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-application] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-application/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-application] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-application/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-asset/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-asset/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-background-fetch] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-background-fetch] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-blur/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-blur/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-crypto] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-crypto/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-crypto] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-crypto/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-eas-client/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-eas-client/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-font] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-font/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-font] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-font/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:4:5-44
MERGED from [:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:4:5-44
MERGED from [:expo-image-loader] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-loader/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-loader/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-manifests] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-keep-awake/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-keep-awake/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linking/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linking/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-splash-screen/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-splash-screen/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-structured-headers/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-structured-headers/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:unimodules-app-loader] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:unimodules-app-loader] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:10:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f7e0f6ce9084451fa15b37e0e69c94e8/transformed/play-services-location-21.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f7e0f6ce9084451fa15b37e0e69c94e8/transformed/play-services-location-21.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.material:material:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd840b7336fae8c068846fca431063fc/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd840b7336fae8c068846fca431063fc/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae922c10d4c361b23fc1eafd8b6b470f/transformed/glide-plugin-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae922c10d4c361b23fc1eafd8b6b470f/transformed/glide-plugin-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73407362229b62c2c82f0aaf9d923d0c/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/73407362229b62c2c82f0aaf9d923d0c/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ec6387658310c87087d29e8de610082c/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ec6387658310c87087d29e8de610082c/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e38f0af3446908f03866031a3d815bd/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e38f0af3446908f03866031a3d815bd/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78365384a40b1f46f521dcd0ed6827b8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78365384a40b1f46f521dcd0ed6827b8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/96701bd0e62441f1ee4a8284e2b9ab00/transformed/awebp-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/96701bd0e62441f1ee4a8284e2b9ab00/transformed/awebp-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/f276553bc42918ed41210b70f4f11290/transformed/apng-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/f276553bc42918ed41210b70f4f11290/transformed/apng-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/600d0424b6f5c1d836ea23db9b2d05d1/transformed/gif-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/600d0424b6f5c1d836ea23db9b2d05d1/transformed/gif-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/732aa76c2ebe8b13a7363cc4cd7fca95/transformed/avif-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/732aa76c2ebe8b13a7363cc4cd7fca95/transformed/avif-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.10.2/transforms/04775ad1cee85aa75275c67658594e11/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.10.2/transforms/04775ad1cee85aa75275c67658594e11/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b13fa7f6e3a908ed2b62743ed291296e/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b13fa7f6e3a908ed2b62743ed291296e/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6ca38eae6048548125d4d188667dff8e/transformed/frameanimation-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6ca38eae6048548125d4d188667dff8e/transformed/frameanimation-3.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aa88c846e864571f4558ffd55398a2ef/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aa88c846e864571f4558ffd55398a2ef/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ee7d2922514238145de538091a344095/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ee7d2922514238145de538091a344095/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbaa152913f58306ffe0234d21614abc/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bbaa152913f58306ffe0234d21614abc/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/656bad39a570112f127c49fdd084518f/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/656bad39a570112f127c49fdd084518f/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/624566eb4cbc2de9ede471aee14daa2b/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/624566eb4cbc2de9ede471aee14daa2b/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/b287b31968ab21b18927dd3ba3fa3d60/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/b287b31968ab21b18927dd3ba3fa3d60/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/7d18829510fdac1c32a3242148401aa9/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/7d18829510fdac1c32a3242148401aa9/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/55a8346d94054c62e1fe614bbe71aa8e/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/55a8346d94054c62e1fe614bbe71aa8e/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8da1700ff38e9c23d62e4c02533156d/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8da1700ff38e9c23d62e4c02533156d/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/fab3dc437d1e3d8d2425324185a45391/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/fab3dc437d1e3d8d2425324185a45391/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/a515e20e08bed2c8e738c639f1a0faee/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/a515e20e08bed2c8e738c639f1a0faee/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/2f1c30e91b6d2e6fd2456b3e6143dadd/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/2f1c30e91b6d2e6fd2456b3e6143dadd/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/883ab54f112a35bd4b277555c75f0182/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/883ab54f112a35bd4b277555c75f0182/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/44d01de45ea2b50d367d43535f14f8fb/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/44d01de45ea2b50d367d43535f14f8fb/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb211ff01ae410ebbe920d3bfa3171b7/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb211ff01ae410ebbe920d3bfa3171b7/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5e8a2ae799851ff2f57aa00e4f31bf06/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5e8a2ae799851ff2f57aa00e4f31bf06/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/78f57a7bb86353430c8945a583799761/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/78f57a7bb86353430c8945a583799761/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6f98901d6bb8c5633e09e5b0cbd6227a/transformed/activity-ktx-1.7.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6f98901d6bb8c5633e09e5b0cbd6227a/transformed/activity-ktx-1.7.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/a05f65f98a3f711ca5f04aa76a885af9/transformed/activity-1.7.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/a05f65f98a3f711ca5f04aa76a885af9/transformed/activity-1.7.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/469a3b08c3dd992816911ec425a5114a/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/469a3b08c3dd992816911ec425a5114a/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/945cbe2625153f550a7a92a5f9980ee9/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/945cbe2625153f550a7a92a5f9980ee9/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01170bcef019d1ab93baf86184e97fdf/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/01170bcef019d1ab93baf86184e97fdf/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a62157a70bd7a9bad19ed0561155fa45/transformed/animated-gif-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a62157a70bd7a9bad19ed0561155fa45/transformed/animated-gif-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4055fb508865ca850b1065916834ca36/transformed/webpsupport-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4055fb508865ca850b1065916834ca36/transformed/webpsupport-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0abadb77610fe19558ec9d5cf546f6a0/transformed/fresco-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0abadb77610fe19558ec9d5cf546f6a0/transformed/fresco-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f3d468eda90f1c940a7444355dea7c5d/transformed/imagepipeline-okhttp3-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f3d468eda90f1c940a7444355dea7c5d/transformed/imagepipeline-okhttp3-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed3f0395a3925029af8b1b55e696908/transformed/animated-base-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed3f0395a3925029af8b1b55e696908/transformed/animated-base-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1563aff4217c4422705d2f6187d242f3/transformed/animated-drawable-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1563aff4217c4422705d2f6187d242f3/transformed/animated-drawable-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f5a1bdfd74586d7ae6e8b994c434b1da/transformed/vito-options-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f5a1bdfd74586d7ae6e8b994c434b1da/transformed/vito-options-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3eb77f99a0a4c0f434aa141e0e47671a/transformed/drawee-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3eb77f99a0a4c0f434aa141e0e47671a/transformed/drawee-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/87bce6bf8ee76cfec5a871ad554e848e/transformed/nativeimagefilters-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/87bce6bf8ee76cfec5a871ad554e848e/transformed/nativeimagefilters-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6707c325cc743e33edcb95f888fbbd1a/transformed/memory-type-native-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6707c325cc743e33edcb95f888fbbd1a/transformed/memory-type-native-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/745d36945e860747d86472fdbcd8c9dc/transformed/memory-type-java-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/745d36945e860747d86472fdbcd8c9dc/transformed/memory-type-java-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d956af59bd023c99c8fa68daa94a6dfd/transformed/imagepipeline-native-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d956af59bd023c99c8fa68daa94a6dfd/transformed/imagepipeline-native-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ef7f9eaaf5ff59029b8903b5a1f0679d/transformed/memory-type-ashmem-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ef7f9eaaf5ff59029b8903b5a1f0679d/transformed/memory-type-ashmem-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7a2339201461aa9a87cb2889f13df5d/transformed/imagepipeline-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7a2339201461aa9a87cb2889f13df5d/transformed/imagepipeline-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cc8723afb482eade78bc6e1e9ece7c98/transformed/nativeimagetranscoder-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cc8723afb482eade78bc6e1e9ece7c98/transformed/nativeimagetranscoder-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd25b26d30803f75123dcad040add85e/transformed/imagepipeline-base-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd25b26d30803f75123dcad040add85e/transformed/imagepipeline-base-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3be0548b4515129a0402ac677c6d8b38/transformed/middleware-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3be0548b4515129a0402ac677c6d8b38/transformed/middleware-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1ccc5258618ec7c85c5e130c6e67b93/transformed/ui-common-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1ccc5258618ec7c85c5e130c6e67b93/transformed/ui-common-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/46c3ea75bb2e0ad04597b021895937d1/transformed/soloader-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/46c3ea75bb2e0ad04597b021895937d1/transformed/soloader-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/75a7388f1cdd115d015adc34a83e91c4/transformed/fbcore-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/75a7388f1cdd115d015adc34a83e91c4/transformed/fbcore-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0abb540ffb27e310e3f408230b89d02/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0abb540ffb27e310e3f408230b89d02/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c43952f8f59a22fb6d26fd05e79f07e1/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c43952f8f59a22fb6d26fd05e79f07e1/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/04b22cf1ef75a66ebc79a74febee8798/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/04b22cf1ef75a66ebc79a74febee8798/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c0176138d5a50e6382eba7391d62d81f/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c0176138d5a50e6382eba7391d62d81f/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f23ba8be396710e62310be87c194db08/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f23ba8be396710e62310be87c194db08/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b0d83f5f37dfe24d221a192beefa02f8/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b0d83f5f37dfe24d221a192beefa02f8/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/83bd2c07f36535642edd28fef9d42bfa/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/83bd2c07f36535642edd28fef9d42bfa/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7a8e1525ff3ba9c5ea5b1ebde0949214/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7a8e1525ff3ba9c5ea5b1ebde0949214/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/888c2e815693a7375208a6c1b5842b51/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/888c2e815693a7375208a6c1b5842b51/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae42b5ae8cfbb0073e969b3673e1b0b3/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae42b5ae8cfbb0073e969b3673e1b0b3/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5c4e920fc5b2796dac71013ed405eb7/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5c4e920fc5b2796dac71013ed405eb7/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8ea5f0f61a91e8d03204398d9dc59341/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8ea5f0f61a91e8d03204398d9dc59341/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bea76edc7a5fb54404bf485c9952c6d2/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bea76edc7a5fb54404bf485c9952c6d2/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5372576be014a8057131e4e22f5e29a4/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5372576be014a8057131e4e22f5e29a4/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9107eb85ecda943c752360494b57cd83/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9107eb85ecda943c752360494b57cd83/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f62ec52335bea96696167e60137cb3fb/transformed/lifecycle-viewmodel-2.8.3/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/f62ec52335bea96696167e60137cb3fb/transformed/lifecycle-viewmodel-2.8.3/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7707b8eb7714dfdee38c21efae6f064/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7707b8eb7714dfdee38c21efae6f064/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/fa088aeec92d791888c412e6a6612471/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/fa088aeec92d791888c412e6a6612471/transformed/lifecycle-runtime-ktx-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/3df36557eb0fd39a3943f295f628c4fc/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/3df36557eb0fd39a3943f295f628c4fc/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/e511d7d13457d4fd3c8cfc088eee17cf/transformed/lifecycle-viewmodel-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/e511d7d13457d4fd3c8cfc088eee17cf/transformed/lifecycle-viewmodel-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f8f4737cd213c1facf2bd8e94c837cd/transformed/lifecycle-viewmodel-savedstate-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/9f8f4737cd213c1facf2bd8e94c837cd/transformed/lifecycle-viewmodel-savedstate-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b95749c06b646fd1c3a9ac856155914c/transformed/lifecycle-service-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b95749c06b646fd1c3a9ac856155914c/transformed/lifecycle-service-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/68e22083c84f8288a1dae579511d9ada/transformed/lifecycle-livedata-core-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/68e22083c84f8288a1dae579511d9ada/transformed/lifecycle-livedata-core-ktx-2.8.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b67cad0d8cbb2a0a9cebef1ca032d5b8/transformed/lifecycle-livedata-core-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/b67cad0d8cbb2a0a9cebef1ca032d5b8/transformed/lifecycle-livedata-core-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/3cee9d7547171d812c60cb52c92f8ded/transformed/lifecycle-livedata-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/3cee9d7547171d812c60cb52c92f8ded/transformed/lifecycle-livedata-2.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/6bbc45b49780d0f1dcdfe2154b677c34/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/6bbc45b49780d0f1dcdfe2154b677c34/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/781afb608f1e9306868332440704a483/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/781afb608f1e9306868332440704a483/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/53d3d1d5c4745a7cdf3627473ba36bdd/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/53d3d1d5c4745a7cdf3627473ba36bdd/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6bf8e080a1512b40ba60657a9a1ec9df/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6bf8e080a1512b40ba60657a9a1ec9df/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10e3cd6e9b5af14de89de85f76f6d43f/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/10e3cd6e9b5af14de89de85f76f6d43f/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/22888200b96b2c4879b3c2fd2f38418c/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/22888200b96b2c4879b3c2fd2f38418c/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f95c7cbe19ff935c584b6b83cf955e34/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/f95c7cbe19ff935c584b6b83cf955e34/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c93a1ed3adb70b128ee8f1adc4dba2c/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c93a1ed3adb70b128ee8f1adc4dba2c/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0e59ab2bf98c1885e22cd08fc5913475/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0e59ab2bf98c1885e22cd08fc5913475/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/de67d545462883a39b61f9fa66497fbf/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/de67d545462883a39b61f9fa66497fbf/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/630888bd9b86257d447d62cf332f68a5/transformed/vito-renderer-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/630888bd9b86257d447d62cf332f68a5/transformed/vito-renderer-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c98921e59ac747fcf347ad028582653e/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c98921e59ac747fcf347ad028582653e/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/478fbcbff80134e348112c6b25f2d8b7/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/478fbcbff80134e348112c6b25f2d8b7/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/00539a65bc277a5b7e243e70ff19cd3c/transformed/hermes-android-0.76.9-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/00539a65bc277a5b7e243e70ff19cd3c/transformed/hermes-android-0.76.9-debug/AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d33c3415ae1fa51d53afb00f59c70d54/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/d33c3415ae1fa51d53afb00f59c70d54/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c385b0eb0a143d5e88d1e23ecece994f/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c385b0eb0a143d5e88d1e23ecece994f/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0ddb78eef896d32fcbde11e3ecd5ad8d/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/0ddb78eef896d32fcbde11e3ecd5ad8d/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/c7088d96ee3e93bb0908d1378a1c5001/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/c7088d96ee3e93bb0908d1378a1c5001/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5b1f8286688359a3fea1d8e4ae39a492/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5b1f8286688359a3fea1d8e4ae39a492/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.databinding:viewbinding:7.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a6d8c4cdb736b5f8573172457fc3f70/transformed/viewbinding-7.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.databinding:viewbinding:7.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5a6d8c4cdb736b5f8573172457fc3f70/transformed/viewbinding-7.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5139976b9c95b58007b43e96f930b302/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5139976b9c95b58007b43e96f930b302/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c18c4c4a3d1d82f2fb3eea9a81faf51f/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c18c4c4a3d1d82f2fb3eea9a81faf51f/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ad8d5d694b4a772c799c82f6815f4d14/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ad8d5d694b4a772c799c82f6815f4d14/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae5f120f2548049627e5cc8757b59548/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ae5f120f2548049627e5cc8757b59548/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/20460333bb10dab577147e27e90d797c/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/20460333bb10dab577147e27e90d797c/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71081a360238d190898fc111501bb847/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71081a360238d190898fc111501bb847/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a6a830e8a4a8f3f3fdcb6c10667962fe/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a6a830e8a4a8f3f3fdcb6c10667962fe/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6673db4abdb30abedf56845ff85db743/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6673db4abdb30abedf56845ff85db743/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f87530d05483e6ae8181cde56795ecae/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f87530d05483e6ae8181cde56795ecae/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0be21279d28c15c6efbf3823d88f33fc/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0be21279d28c15c6efbf3823d88f33fc/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/77970482e3b9c1aed01991343fbf672a/transformed/fbjni-0.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/77970482e3b9c1aed01991343fbf672a/transformed/fbjni-0.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:5:5-7:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/d724e9c4260ee79d124aa1a14072324d/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/d724e9c4260ee79d124aa1a14072324d/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:8:5-10:41
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:8:5-10:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.10.2/transforms/eca0361f6791774932d8e61f95cdbba8/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.10.2/transforms/eca0361f6791774932d8e61f95cdbba8/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:4:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/AndroidManifest.xml
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-29:70
	android:resource
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-67
	android:name
		ADDED from [:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:17-67
uses-permission#android.permission.CAMERA
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-65
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-62
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-73
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-80
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:32:13-40
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:17-94
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:39:17-33
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:9-44:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-56
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:49:13-37
	android:name
		ADDED from [:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:47:13-89
service#expo.modules.location.services.LocationTaskService
ADDED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-53
	android:name
		ADDED from [:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-78
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-78
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:13-92
receiver#expo.modules.taskManager.TaskBroadcastReceiver
ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
	android:exported
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:name
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:expo.modules.taskManager.TaskBroadcastReceiver.INTENT_ACTION
ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-15:29
action#expo.modules.taskManager.TaskBroadcastReceiver.INTENT_ACTION
ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:17-103
	android:name
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:25-100
service#expo.modules.taskManager.TaskJobService
ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-22:72
	android:enabled
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-35
	android:exported
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-37
	android:permission
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-69
	android:name
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-67
meta-data#expo.modules.taskManager.oneAppId
ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:9-26:36
	android:value
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:13-33
	android:name
		ADDED from [:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-61
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-90
	android:name
		ADDED from [:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:21-87
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a8f1726a39734b4438280848e71298a/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:20:13-77
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:33:18-61
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cbf4f822d30bcdd0e2c57ced82b2f61c/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:20:19-85
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:11:21-69
category#android.intent.category.OPENABLE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:13:13-73
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:13:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:24:13-63
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c93a1ed3adb70b128ee8f1adc4dba2c/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c93a1ed3adb70b128ee8f1adc4dba2c/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.dumpster.userapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.dumpster.userapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
activity#com.jakewharton.processphoenix.ProcessPhoenix
ADDED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:8:9-12:75
	android:process
		ADDED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:11:13-39
	android:exported
		ADDED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:12:13-72
	android:name
		ADDED from [com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:9:13-73
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:15:5-98
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:15:22-95
uses-permission#com.google.android.gms.permission.ACTIVITY_RECOGNITION
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:16:5-94
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:16:22-91
service#io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:20:13-132
service#io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:22:9-24:40
	android:exported
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:24:13-37
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:23:13-127
service#io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:25:9-27:40
	android:exported
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:26:13-120
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
