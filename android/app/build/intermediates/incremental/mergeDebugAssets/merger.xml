<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates-interface/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":unimodules-app-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/unimodules-app-loader/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-web-browser" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-task-manager" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-structured-headers" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-structured-headers/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-splash-screen/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-location" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linking/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-linear-gradient/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-keep-awake/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-json-utils/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-manifests/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-image-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-loader/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-image" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-haptics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-haptics/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-font/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-eas-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-eas-client/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-updates" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="expo-root.pem" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-updates/android/build/intermediates/library_assets/debug/packageDebugAssets/out/expo-root.pem"/></source></dataSet><dataSet config=":expo-crypto" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-crypto/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-constants/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="app.config" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-constants/android/build/intermediates/library_assets/debug/packageDebugAssets/out/app.config"/></source></dataSet><dataSet config=":expo-blur" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-blur/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-background-fetch" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-background-fetch/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-asset" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-asset/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-application" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-application/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-vector-icons/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-svg/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-restart" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-restart/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-reanimated/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-maps" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-maps/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-localization" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-localization/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-linear-gradient/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-get-random-values" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-get-random-values/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/slider/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-community_datetimepicker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-screens/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-safe-area-context/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-gesture-handler/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/assets"/></dataSet><dataSet config="assets-createDebugUpdatesResources" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/build/generated/assets/createDebugUpdatesResources"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>