<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res"><file name="ic_launcher" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-mdpi/ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-mdpi/ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-mdpi/ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-hdpi/ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-hdpi/ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-hdpi/ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="rn_edit_text_material" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable/rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable/ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xxhdpi/ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/values-night/colors.xml" qualifiers="night-v8"/><file path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/values/colors.xml" qualifiers=""><color name="splashscreen_background">#00BFA5</color><color name="iconBackground">#226d7a</color><color name="colorPrimary">#023c69</color><color name="colorPrimaryDark">#00BFA5</color><color name="notification_icon_color">#226d7a</color></file><file path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/values/styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#00BFA5</item>
  </style><style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style><style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style></file><file path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">dumpster-user-app</string><string name="expo_splash_screen_resize_mode" translatable="false">contain</string><string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string></file><file name="splashscreen_logo" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-xhdpi/splashscreen_logo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="notification_icon" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-xhdpi/notification_icon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-xxhdpi/splashscreen_logo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="notification_icon" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-xxhdpi/notification_icon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-hdpi/splashscreen_logo.png" qualifiers="hdpi-v4" type="drawable"/><file name="notification_icon" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-hdpi/notification_icon.png" qualifiers="hdpi-v4" type="drawable"/><file name="splashscreen_logo" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-mdpi/splashscreen_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="notification_icon" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-mdpi/notification_icon.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xhdpi/ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-xhdpi/ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="splashscreen_logo" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-xxxhdpi/splashscreen_logo.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="notification_icon" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/drawable-xxxhdpi/notification_icon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="notification_sound" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/raw/notification_sound.wav" qualifiers="" type="raw"/><file name="ic_launcher" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/build/generated/res/resValues/debug"><file path="/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/build/generated/res/resValues/debug/values/gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer></file></source></dataSet><mergedItems/></merger>