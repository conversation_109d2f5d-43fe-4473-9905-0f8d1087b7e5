{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-si/values-si.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-si/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5402", "endColumns": "138", "endOffsets": "5536"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,279,347,430,499,567,643,722,805,891,960,1040,1129,1209,1292,1377,1456,1533,1613,1705,1778,1857,1929", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "119,201,274,342,425,494,562,638,717,800,886,955,1035,1124,1204,1287,1372,1451,1528,1608,1700,1773,1852,1924,2002"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2990,4341,7482,7622,7690,8009,8972,9040,9116,12068,12151,12398,12467,12702,12791,12871,13029,13114,13277,13435,13515,13708,13929,14126,14198", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "3054,4418,7550,7685,7768,8073,9035,9111,9190,12146,12232,12462,12542,12786,12866,12949,13109,13188,13349,13510,13602,13776,14003,14193,14271"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,978,1060,1133,1208,1292,1373,1454,1521", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,973,1055,1128,1203,1287,1368,1449,1516,1634"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4169,4258,6721,6820,6919,7773,7858,11822,11908,12237,12316,12629,12954,13193,13354,13781,13862,14008", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "4253,4336,6815,6914,6996,7853,7944,11903,11983,12311,12393,12697,13024,13272,13430,13857,13924,14121"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,390,497,604,687,792,908,998,1084,1175,1268,1362,1456,1556,1649,1744,1838,1929,2020,2104,2213,2317,2415,2525,2625,2732,2891,12547", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "385,492,599,682,787,903,993,1079,1170,1263,1357,1451,1551,1644,1739,1833,1924,2015,2099,2208,2312,2410,2520,2620,2727,2886,2985,12624"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3137,3239,3342,3447,3552,3651,3755,13607", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3234,3337,3442,3547,3646,3750,3864,13703"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-si/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4423,4531,4685,4809,4922,5064,5188,5304,5541,5692,5807,5963,6094,6238,6399,6472,6533", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "4526,4680,4804,4917,5059,5183,5299,5397,5687,5802,5958,6089,6233,6394,6467,6528,6608"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6613,7154,7261,7377", "endColumns": "107,106,115,104", "endOffsets": "6716,7256,7372,7477"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-si/values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,92", "endOffsets": "140,233"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14276,14366", "endColumns": "89,92", "endOffsets": "14361,14454"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-si/values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,302,404,519,602,666,755,822,882,976,1039,1095,1165,1232,1287,1406,1463,1527,1581,1654,1776,1859,1944,2046,2124,2204,2290,2357,2423,2493,2566,2648,2720,2797,2869,2939,3032,3105,3195,3288,3362,3434,3525,3579,3645,3729,3814,3876,3940,4003,4108,4208,4303,4403", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "219,297,399,514,597,661,750,817,877,971,1034,1090,1160,1227,1282,1401,1458,1522,1576,1649,1771,1854,1939,2041,2119,2199,2285,2352,2418,2488,2561,2643,2715,2792,2864,2934,3027,3100,3190,3283,3357,3429,3520,3574,3640,3724,3809,3871,3935,3998,4103,4203,4298,4398,4478"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3059,3869,3971,4086,7001,7065,7555,7949,8078,8172,8235,8291,8361,8428,8483,8602,8659,8723,8777,8850,9195,9278,9363,9465,9543,9623,9709,9776,9842,9912,9985,10067,10139,10216,10288,10358,10451,10524,10614,10707,10781,10853,10944,10998,11064,11148,11233,11295,11359,11422,11527,11627,11722,11988", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "269,3132,3966,4081,4164,7060,7149,7617,8004,8167,8230,8286,8356,8423,8478,8597,8654,8718,8772,8845,8967,9273,9358,9460,9538,9618,9704,9771,9837,9907,9980,10062,10134,10211,10283,10353,10446,10519,10609,10702,10776,10848,10939,10993,11059,11143,11228,11290,11354,11417,11522,11622,11717,11817,12063"}}]}]}