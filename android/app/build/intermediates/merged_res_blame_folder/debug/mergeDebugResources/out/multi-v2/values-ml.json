{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-ml/values-ml.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1028,1115,1193,1270,1344,1417,1493,1560", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1023,1110,1188,1265,1339,1412,1488,1555,1674"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4217,4312,6949,7048,7152,8018,8104,12312,12399,12736,12822,13141,13463,13704,13851,14266,14342,14489", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "4307,4394,7043,7147,7237,8099,8200,12394,12482,12817,12904,13214,13535,13773,13919,14337,14404,14603"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-ml/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5537", "endColumns": "159", "endOffsets": "5692"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6840,7406,7509,7620", "endColumns": "108,102,110,103", "endOffsets": "6944,7504,7615,7719"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,383,489,600,691,796,918,996,1071,1162,1255,1356,1450,1550,1644,1739,1838,1929,2020,2102,2211,2315,2414,2526,2638,2759,2924,13058", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "378,484,595,686,791,913,991,1066,1157,1250,1351,1445,1545,1639,1734,1833,1924,2015,2097,2206,2310,2409,2521,2633,2754,2919,3020,13136"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3184,3286,3389,3491,3595,3698,3799,14092", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3281,3384,3486,3590,3693,3794,3916,14188"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,207,282,352,435,504,571,645,720,801,885,954,1034,1116,1196,1278,1364,1442,1515,1587,1683,1756,1836,1904", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "122,202,277,347,430,499,566,640,715,796,880,949,1029,1111,1191,1273,1359,1437,1510,1582,1678,1751,1831,1899,1972"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3025,4399,7724,7865,7935,8265,9280,9347,9421,12571,12652,12909,12978,13219,13301,13381,13540,13626,13778,13924,13996,14193,14409,14608,14676", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "3092,4474,7794,7930,8013,8329,9342,9416,9491,12647,12731,12973,13053,13296,13376,13458,13621,13699,13846,13991,14087,14261,14484,14671,14744"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-ml/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4479,4598,4777,4916,5037,5201,5326,5431,5697,5879,5995,6169,6305,6454,6615,6679,6748", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "4593,4772,4911,5032,5196,5321,5426,5532,5874,5990,6164,6300,6449,6610,6674,6743,6835"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-ml/values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,313,407,517,609,674,773,839,899,1001,1077,1135,1213,1278,1332,1449,1513,1577,1631,1711,1845,1931,2020,2126,2211,2299,2394,2461,2527,2606,2688,2779,2855,2932,3009,3080,3187,3267,3364,3464,3538,3619,3724,3782,3849,3940,4032,4094,4158,4221,4324,4440,4545,4661", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,93,109,91,64,98,65,59,101,75,57,77,64,53,116,63,63,53,79,133,85,88,105,84,87,94,66,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,83", "endOffsets": "221,308,402,512,604,669,768,834,894,996,1072,1130,1208,1273,1327,1444,1508,1572,1626,1706,1840,1926,2015,2121,2206,2294,2389,2456,2522,2601,2683,2774,2850,2927,3004,3075,3182,3262,3359,3459,3533,3614,3719,3777,3844,3935,4027,4089,4153,4216,4319,4435,4540,4656,4740"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3097,3921,4015,4125,7242,7307,7799,8205,8334,8436,8512,8570,8648,8713,8767,8884,8948,9012,9066,9146,9496,9582,9671,9777,9862,9950,10045,10112,10178,10257,10339,10430,10506,10583,10660,10731,10838,10918,11015,11115,11189,11270,11375,11433,11500,11591,11683,11745,11809,11872,11975,12091,12196,12487", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,86,93,109,91,64,98,65,59,101,75,57,77,64,53,116,63,63,53,79,133,85,88,105,84,87,94,66,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,83", "endOffsets": "271,3179,4010,4120,4212,7302,7401,7860,8260,8431,8507,8565,8643,8708,8762,8879,8943,9007,9061,9141,9275,9577,9666,9772,9857,9945,10040,10107,10173,10252,10334,10425,10501,10578,10655,10726,10833,10913,11010,11110,11184,11265,11370,11428,11495,11586,11678,11740,11804,11867,11970,12086,12191,12307,12566"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-ml/values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14749,14837", "endColumns": "87,91", "endOffsets": "14832,14924"}}]}]}