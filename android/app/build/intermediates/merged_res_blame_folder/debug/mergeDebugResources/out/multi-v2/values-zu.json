{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-zu/values-zu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-zu/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5322", "endColumns": "131", "endOffsets": "5449"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-zu/values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,303,415,541,622,689,792,867,930,1022,1087,1154,1226,1298,1352,1473,1532,1596,1650,1727,1859,1944,2025,2144,2231,2314,2406,2473,2539,2611,2688,2779,2859,2938,3013,3092,3182,3255,3349,3446,3520,3593,3692,3747,3815,3903,3992,4054,4118,4181,4290,4395,4498,4607", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "221,298,410,536,617,684,787,862,925,1017,1082,1149,1221,1293,1347,1468,1527,1591,1645,1722,1854,1939,2020,2139,2226,2309,2401,2468,2534,2606,2683,2774,2854,2933,3008,3087,3177,3250,3344,3441,3515,3588,3687,3742,3810,3898,3987,4049,4113,4176,4285,4390,4493,4602,4684"}, "to": {"startLines": "2,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2962,3769,3881,4007,6962,7029,7464,7710,7773,7865,7930,7997,8069,8141,8195,8316,8375,8439,8493,8570,8702,8787,8868,8987,9074,9157,9249,9316,9382,9454,9531,9622,9702,9781,9856,9935,10025,10098,10192,10289,10363,10436,10535,10590,10658,10746,10835,10897,10961,11024,11133,11238,11341,11626", "endLines": "5,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,124", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "271,3034,3876,4002,4083,7024,7127,7534,7768,7860,7925,7992,8064,8136,8190,8311,8370,8434,8488,8565,8697,8782,8863,8982,9069,9152,9244,9311,9377,9449,9526,9617,9697,9776,9851,9930,10020,10093,10187,10284,10358,10431,10530,10585,10653,10741,10830,10892,10956,11019,11128,11233,11336,11445,11703"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "34,35,36,37,38,39,40,132", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3039,3137,3241,3340,3443,3549,3656,12274", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "3132,3236,3335,3438,3544,3651,3764,12370"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-zu/values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "12648,12739", "endColumns": "90,91", "endOffsets": "12734,12826"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,1017,1105,1181,1262,1338,1413,1492,1562", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,80,75,74,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,1012,1100,1176,1257,1333,1408,1487,1557,1681"}, "to": {"startLines": "44,45,65,66,67,74,75,122,123,125,126,128,129,130,131,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4088,4183,6662,6767,6872,7539,7621,11450,11543,11708,11796,11966,12042,12123,12199,12375,12454,12524", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,80,75,74,78,69,123", "endOffsets": "4178,4260,6762,6867,6957,7616,7705,11538,11621,11791,11879,12037,12118,12194,12269,12449,12519,12643"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-zu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4265,4376,4552,4687,4791,4959,5088,5212,5454,5614,5724,5889,6020,6178,6335,6396,6465", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "4371,4547,4682,4786,4954,5083,5207,5317,5609,5719,5884,6015,6173,6330,6391,6460,6545"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6550,7132,7240,7352", "endColumns": "111,107,111,111", "endOffsets": "6657,7235,7347,7459"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,384,491,603,691,794,909,988,1065,1156,1249,1344,1438,1538,1631,1726,1820,1911,2004,2085,2189,2292,2390,2497,2604,2709,2866,11884", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "379,486,598,686,789,904,983,1060,1151,1244,1339,1433,1533,1626,1721,1815,1906,1999,2080,2184,2287,2385,2492,2599,2704,2861,2957,11961"}}]}]}