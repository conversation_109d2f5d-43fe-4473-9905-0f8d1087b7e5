{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-ms/values-ms.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-ms/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5480", "endColumns": "145", "endOffsets": "5621"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,997,1083,1155,1232,1305,1378,1454,1520", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,992,1078,1150,1227,1300,1373,1449,1515,1635"}, "to": {"startLines": "45,46,70,71,72,87,88,138,139,144,145,148,152,155,157,162,163,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4182,4272,7112,7208,7310,8458,8541,12466,12553,12955,13040,13284,13598,13838,13988,14400,14476,14619", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "4267,4351,7203,7305,7390,8536,8631,12548,12633,13035,13121,13351,13670,13906,14056,14471,14537,14734"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-ms/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4434,4540,4713,4843,4949,5121,5253,5373,5626,5811,5920,6095,6230,6401,6576,6643,6707", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "4535,4708,4838,4944,5116,5248,5368,5475,5806,5915,6090,6225,6396,6571,6638,6702,6785"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "35,36,37,38,39,40,41,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3137,3232,3334,3431,3541,3647,3765,14224", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3227,3329,3426,3536,3642,3760,3875,14320"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-ms/values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,321,424,540,623,688,781,846,905,992,1054,1114,1180,1242,1296,1404,1461,1522,1577,1648,1768,1859,1945,2063,2149,2235,2323,2390,2456,2527,2605,2688,2761,2837,2910,2981,3073,3146,3236,3329,3403,3474,3565,3617,3685,3769,3854,3916,3980,4043,4147,4253,4349,4457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,102,115,82,64,92,64,58,86,61,59,65,61,53,107,56,60,54,70,119,90,85,117,85,85,87,66,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,85", "endOffsets": "229,316,419,535,618,683,776,841,900,987,1049,1109,1175,1237,1291,1399,1456,1517,1572,1643,1763,1854,1940,2058,2144,2230,2318,2385,2451,2522,2600,2683,2756,2832,2905,2976,3068,3141,3231,3324,3398,3469,3560,3612,3680,3764,3849,3911,3975,4038,4142,4248,4344,4452,4538"}, "to": {"startLines": "2,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3050,3880,3983,4099,7395,7460,8243,8636,8764,8851,8913,8973,9039,9101,9155,9263,9320,9381,9436,9507,9777,9868,9954,10072,10158,10244,10332,10399,10465,10536,10614,10697,10770,10846,10919,10990,11082,11155,11245,11338,11412,11483,11574,11626,11694,11778,11863,11925,11989,12052,12156,12262,12358,12638", "endLines": "5,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "endColumns": "12,86,102,115,82,64,92,64,58,86,61,59,65,61,53,107,56,60,54,70,119,90,85,117,85,85,87,66,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,85", "endOffsets": "279,3132,3978,4094,4177,7455,7548,8303,8690,8846,8908,8968,9034,9096,9150,9258,9315,9376,9431,9502,9622,9863,9949,10067,10153,10239,10327,10394,10460,10531,10609,10692,10765,10841,10914,10985,11077,11150,11240,11333,11407,11478,11569,11621,11689,11773,11858,11920,11984,12047,12151,12257,12353,12461,12719"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "284,395,500,608,695,799,910,989,1067,1158,1251,1346,1440,1538,1631,1726,1820,1911,2002,2082,2194,2302,2399,2508,2612,2719,2878,13203", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "390,495,603,690,794,905,984,1062,1153,1246,1341,1435,1533,1626,1721,1815,1906,1997,2077,2189,2297,2394,2503,2607,2714,2873,2974,13279"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,204,274,342,424,493,567,643,725,808,885,968,1042,1127,1212,1290,1367,1444,1530,1605,1682,1752", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "121,199,269,337,419,488,562,638,720,803,880,963,1037,1122,1207,1285,1362,1439,1525,1600,1677,1747,1821"}, "to": {"startLines": "33,47,78,85,86,90,103,104,142,143,146,149,150,151,153,154,156,158,159,161,164,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2979,4356,7874,8308,8376,8695,9627,9701,12790,12872,13126,13356,13439,13513,13675,13760,13911,14061,14138,14325,14542,14739,14809", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "3045,4429,7939,8371,8453,8759,9696,9772,12867,12950,13198,13434,13508,13593,13755,13833,13983,14133,14219,14395,14614,14804,14878"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "66,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6790,7553,7653,7772", "endColumns": "104,99,118,101", "endOffsets": "6890,7648,7767,7869"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,216,272,316,383,446,518,571", "endColumns": "113,46,55,43,66,62,71,52,65", "endOffsets": "164,211,267,311,378,441,513,566,632"}, "to": {"startLines": "67,68,69,79,80,81,82,83,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6895,7009,7056,7944,7988,8055,8118,8190,12724", "endColumns": "113,46,55,43,66,62,71,52,65", "endOffsets": "7004,7051,7107,7983,8050,8113,8185,8238,12785"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-ms/values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,90", "endOffsets": "137,228"}, "to": {"startLines": "168,169", "startColumns": "4,4", "startOffsets": "14883,14970", "endColumns": "86,90", "endOffsets": "14965,15056"}}]}]}