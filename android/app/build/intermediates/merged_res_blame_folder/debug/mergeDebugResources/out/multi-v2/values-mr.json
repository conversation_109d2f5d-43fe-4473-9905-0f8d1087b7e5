{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-mr/values-mr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,375,481,588,678,779,891,969,1046,1137,1230,1323,1420,1520,1613,1708,1802,1893,1984,2064,2171,2272,2369,2478,2580,2694,2851,12465", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "370,476,583,673,774,886,964,1041,1132,1225,1318,1415,1515,1608,1703,1797,1888,1979,2059,2166,2267,2364,2473,2575,2689,2846,2949,12540"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3106,3206,3310,3411,3514,3616,3721,13482", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3201,3305,3406,3509,3611,3716,3833,13578"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14132,14217", "endColumns": "84,84", "endOffsets": "14212,14297"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-mr/values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,297,397,513,595,658,749,814,873,961,1023,1083,1150,1213,1267,1381,1438,1499,1553,1623,1742,1823,1908,2013,2090,2167,2253,2320,2386,2456,2534,2621,2691,2767,2838,2907,3003,3077,3175,3271,3345,3415,3517,3572,3639,3726,3819,3882,3946,4009,4109,4212,4306,4410", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "209,292,392,508,590,653,744,809,868,956,1018,1078,1145,1208,1262,1376,1433,1494,1548,1618,1737,1818,1903,2008,2085,2162,2248,2315,2381,2451,2529,2616,2686,2762,2833,2902,2998,3072,3170,3266,3340,3410,3512,3567,3634,3721,3814,3877,3941,4004,4104,4207,4301,4405,4483"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3023,3838,3938,4054,6933,6996,7470,7859,7985,8073,8135,8195,8262,8325,8379,8493,8550,8611,8665,8735,9071,9152,9237,9342,9419,9496,9582,9649,9715,9785,9863,9950,10020,10096,10167,10236,10332,10406,10504,10600,10674,10744,10846,10901,10968,11055,11148,11211,11275,11338,11438,11541,11635,11911", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "259,3101,3933,4049,4131,6991,7082,7530,7913,8068,8130,8190,8257,8320,8374,8488,8545,8606,8660,8730,8849,9147,9232,9337,9414,9491,9577,9644,9710,9780,9858,9945,10015,10091,10162,10231,10327,10401,10499,10595,10669,10739,10841,10896,10963,11050,11143,11206,11270,11333,11433,11536,11630,11734,11984"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,272,343,425,492,559,633,709,789,869,937,1020,1102,1177,1263,1350,1425,1496,1567,1658,1730,1805,1874", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "119,198,267,338,420,487,554,628,704,784,864,932,1015,1097,1172,1258,1345,1420,1491,1562,1653,1725,1800,1869,1942"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2954,4312,7401,7535,7606,7918,8854,8921,8995,11989,12069,12314,12382,12616,12698,12773,12935,13022,13173,13320,13391,13583,13797,13990,14059", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "3018,4386,7465,7601,7683,7980,8916,8990,9066,12064,12144,12377,12460,12693,12768,12854,13017,13092,13239,13386,13477,13650,13867,14054,14127"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-mr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4391,4498,4666,4789,4901,5046,5167,5275,5515,5665,5773,5927,6051,6190,6343,6403,6469", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "4493,4661,4784,4896,5041,5162,5270,5367,5660,5768,5922,6046,6185,6338,6398,6464,6545"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6550,7087,7188,7299", "endColumns": "100,100,110,101", "endOffsets": "6646,7183,7294,7396"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-mr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5372", "endColumns": "142", "endOffsets": "5510"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,986,1071,1142,1218,1294,1370,1446,1512", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,75,75,75,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,981,1066,1137,1213,1289,1365,1441,1507,1625"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4136,4230,6651,6748,6846,7688,7774,11739,11828,12149,12229,12545,12859,13097,13244,13655,13731,13872", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,75,75,75,75,65,117", "endOffsets": "4225,4307,6743,6841,6928,7769,7854,11823,11906,12224,12309,12611,12930,13168,13315,13726,13792,13985"}}]}]}