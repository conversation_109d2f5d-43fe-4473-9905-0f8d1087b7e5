{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-hi/values-hi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,208,262,307,373,440,524,578", "endColumns": "105,46,53,44,65,66,83,53,64", "endOffsets": "156,203,257,302,368,435,519,573,638"}, "to": {"startLines": "67,68,69,79,80,81,82,83,142", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6765,6871,6918,7799,7844,7910,7977,8061,12678", "endColumns": "105,46,53,44,65,66,83,53,64", "endOffsets": "6866,6913,6967,7839,7905,7972,8056,8110,12738"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "45,46,70,71,72,87,88,139,140,145,146,150,154,157,159,164,165,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4171,4266,6972,7065,7163,8333,8411,12425,12514,12907,12988,13302,13615,13869,14018,14427,14508,14649", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "4261,4344,7060,7158,7247,8406,8503,12509,12594,12983,13068,13370,13703,13939,14088,14503,14569,14764"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "66,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6659,7411,7513,7625", "endColumns": "105,101,111,102", "endOffsets": "6760,7508,7620,7723"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-hi/values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,295,402,534,617,682,776,845,904,989,1052,1110,1175,1236,1297,1403,1461,1521,1580,1650,1766,1845,1925,2029,2104,2180,2277,2344,2410,2480,2557,2643,2711,2787,2868,2946,3032,3119,3216,3315,3389,3459,3563,3617,3684,3774,3866,3928,3992,4055,4160,4268,4369,4478", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,103,74,75,96,66,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,78", "endOffsets": "209,290,397,529,612,677,771,840,899,984,1047,1105,1170,1231,1292,1398,1456,1516,1575,1645,1761,1840,1920,2024,2099,2175,2272,2339,2405,2475,2552,2638,2706,2782,2863,2941,3027,3114,3211,3310,3384,3454,3558,3612,3679,3769,3861,3923,3987,4050,4155,4263,4364,4473,4552"}, "to": {"startLines": "2,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3015,3849,3956,4088,7252,7317,8115,8508,8634,8719,8782,8840,8905,8966,9027,9133,9191,9251,9310,9380,9713,9792,9872,9976,10051,10127,10224,10291,10357,10427,10504,10590,10658,10734,10815,10893,10979,11066,11163,11262,11336,11406,11510,11564,11631,11721,11813,11875,11939,12002,12107,12215,12316,12599", "endLines": "5,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,141", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,103,74,75,96,66,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,78", "endOffsets": "259,3091,3951,4083,4166,7312,7406,8179,8562,8714,8777,8835,8900,8961,9022,9128,9186,9246,9305,9375,9491,9787,9867,9971,10046,10122,10219,10286,10352,10422,10499,10585,10653,10729,10810,10888,10974,11061,11158,11257,11331,11401,11505,11559,11626,11716,11808,11870,11934,11997,12102,12210,12311,12420,12673"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-hi/values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "170,171", "startColumns": "4,4", "startOffsets": "14911,14996", "endColumns": "84,85", "endOffsets": "14991,15077"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-hi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4428,4536,4696,4822,4934,5085,5215,5327,5585,5742,5851,6017,6147,6288,6441,6504,6571", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "4531,4691,4817,4929,5080,5210,5322,5434,5737,5846,6012,6142,6283,6436,6499,6566,6654"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,343,423,490,557,631,707,790,871,939,1018,1096,1171,1258,1344,1419,1493,1567,1654,1726,1801,1870", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "119,198,269,338,418,485,552,626,702,785,866,934,1013,1091,1166,1253,1339,1414,1488,1562,1649,1721,1796,1865,1938"}, "to": {"startLines": "33,47,78,85,86,90,103,104,105,143,144,147,148,151,152,153,155,156,158,160,161,163,166,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2946,4349,7728,8184,8253,8567,9496,9563,9637,12743,12826,13073,13141,13375,13453,13528,13708,13794,13944,14093,14167,14355,14574,14769,14838", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "3010,4423,7794,8248,8328,8629,9558,9632,9708,12821,12902,13136,13215,13448,13523,13610,13789,13864,14013,14162,14249,14422,14644,14833,14906"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,370,468,578,664,766,887,965,1042,1133,1226,1321,1415,1515,1608,1703,1797,1888,1979,2060,2165,2267,2365,2475,2578,2687,2845,13220", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "365,463,573,659,761,882,960,1037,1128,1221,1316,1410,1510,1603,1698,1792,1883,1974,2055,2160,2262,2360,2470,2573,2682,2840,2941,13297"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "35,36,37,38,39,40,41,162", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3096,3194,3297,3402,3503,3616,3722,14254", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3189,3292,3397,3498,3611,3717,3844,14350"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-hi/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5439", "endColumns": "145", "endOffsets": "5580"}}]}]}