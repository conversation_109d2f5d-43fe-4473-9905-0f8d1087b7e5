{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-pl/values-pl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,220,278,323,389,453,512,565", "endColumns": "117,46,57,44,65,63,58,52,67", "endOffsets": "168,215,273,318,384,448,507,560,628"}, "to": {"startLines": "68,69,70,80,81,82,83,84,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6812,6930,6977,7862,7907,7973,8037,8096,12598", "endColumns": "117,46,57,44,65,63,58,52,67", "endOffsets": "6925,6972,7030,7902,7968,8032,8091,8144,12661"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,1002,1089,1161,1237,1315,1391,1473,1541", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,75,77,75,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,997,1084,1156,1232,1310,1386,1468,1536,1656"}, "to": {"startLines": "46,47,71,72,73,88,89,138,139,144,145,149,153,156,158,163,164,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4203,4298,7035,7144,7249,8367,8444,12349,12439,12844,12927,13252,13567,13804,13954,14365,14447,14591", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,75,77,75,81,67,119", "endOffsets": "4293,4378,7139,7244,7321,8439,8532,12434,12517,12922,13009,13319,13638,13877,14025,14442,14510,14706"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "36,37,38,39,40,41,42,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3182,3279,3381,3479,3578,3692,3797,14186", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3274,3376,3474,3573,3687,3792,3914,14282"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "67,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "6712,7471,7570,7685", "endColumns": "99,98,114,103", "endOffsets": "6807,7565,7680,7784"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-pl/values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "169,170", "startColumns": "4,4", "startOffsets": "14865,14953", "endColumns": "87,87", "endOffsets": "14948,15036"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,341,420,505,622,704,768,849,913,974,1085,1153,1207,1276,1338,1392,1503,1564,1626,1680,1752,1881,1970,2052,2171,2253,2336,2423,2490,2556,2627,2703,2792,2869,2947,3025,3101,3191,3264,3359,3456,3528,3602,3702,3754,3820,3908,3998,4060,4124,4187,4294,4383,4482,4570", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "336,415,500,617,699,763,844,908,969,1080,1148,1202,1271,1333,1387,1498,1559,1621,1675,1747,1876,1965,2047,2166,2248,2331,2418,2485,2551,2622,2698,2787,2864,2942,3020,3096,3186,3259,3354,3451,3523,3597,3697,3749,3815,3903,3993,4055,4119,4182,4289,4378,4477,4565,4641"}, "to": {"startLines": "2,35,43,44,45,74,75,85,90,91,92,93,94,95,96,97,98,99,100,101,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3103,3919,4004,4121,7326,7390,8149,8537,8598,8709,8777,8831,8900,8962,9016,9127,9188,9250,9304,9376,9660,9749,9831,9950,10032,10115,10202,10269,10335,10406,10482,10571,10648,10726,10804,10880,10970,11043,11138,11235,11307,11381,11481,11533,11599,11687,11777,11839,11903,11966,12073,12162,12261,12522", "endLines": "7,35,43,44,45,74,75,85,90,91,92,93,94,95,96,97,98,99,100,101,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "386,3177,3999,4116,4198,7385,7466,8208,8593,8704,8772,8826,8895,8957,9011,9122,9183,9245,9299,9371,9500,9744,9826,9945,10027,10110,10197,10264,10330,10401,10477,10566,10643,10721,10799,10875,10965,11038,11133,11230,11302,11376,11476,11528,11594,11682,11772,11834,11898,11961,12068,12157,12256,12344,12593"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-pl/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4460,4564,4732,4854,4964,5115,5240,5351,5590,5761,5870,6045,6173,6332,6493,6562,6628", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "4559,4727,4849,4959,5110,5235,5346,5445,5756,5865,6040,6168,6327,6488,6557,6623,6707"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "391,506,608,716,802,909,1028,1107,1183,1274,1367,1462,1556,1657,1750,1845,1940,2031,2122,2204,2313,2413,2512,2621,2733,2844,3007,13169", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "501,603,711,797,904,1023,1102,1178,1269,1362,1457,1551,1652,1745,1840,1935,2026,2117,2199,2308,2408,2507,2616,2728,2839,3002,3098,13247"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,205,274,359,435,514,597,692,762,847,933,1008,1090,1173,1251,1323,1393,1479,1557,1633,1707", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "127,200,269,354,430,509,592,687,757,842,928,1003,1085,1168,1246,1318,1388,1474,1552,1628,1702,1782"}, "to": {"startLines": "48,79,86,87,103,104,142,143,146,147,150,151,152,154,155,157,159,160,162,165,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4383,7789,8213,8282,9505,9581,12666,12749,13014,13084,13324,13410,13485,13643,13726,13882,14030,14100,14287,14515,14711,14785", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "4455,7857,8277,8362,9576,9655,12744,12839,13079,13164,13405,13480,13562,13721,13799,13949,14095,14181,14360,14586,14780,14860"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-pl/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5450", "endColumns": "139", "endOffsets": "5585"}}]}]}