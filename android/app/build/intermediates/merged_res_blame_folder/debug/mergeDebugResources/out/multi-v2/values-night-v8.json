{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-night-v8/values-night-v8.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/04775ad1cee85aa75275c67658594e11/transformed/core-splashscreen-1.2.0-alpha02/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "195"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "11,12,13,14,15,16,17,42", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "490,560,644,728,824,926,1028,3776", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "555,639,723,819,921,1023,1117,3860"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,847,935,1059,1161,1263,1379,1481,1595,1723,1839,1961,2097,2217,2351,2471,2583,2709,2826,2950,3080,3202,3340,3474,3590", "endColumns": "74,110,88,100,106,106,98,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,842,930,1054,1156,1258,1374,1476,1590,1718,1834,1956,2092,2212,2346,2466,2578,2704,2821,2945,3075,3197,3335,3469,3585,3705"}, "to": {"startLines": "18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,43,44,45,46,47,48,49,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1122,1197,1308,1397,1498,1605,1712,1811,1914,2002,2126,2228,2330,2446,2548,2662,2790,2906,3028,3164,3284,3418,3538,3650,3865,3982,4106,4236,4358,4496,4630,4746", "endColumns": "74,110,88,100,106,106,98,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1192,1303,1392,1493,1600,1707,1806,1909,1997,2121,2223,2325,2441,2543,2657,2785,2901,3023,3159,3279,3413,3533,3645,3771,3977,4101,4231,4353,4491,4625,4741,4861"}}, {"source": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/intermediates/packaged_res/debug/packageDebugResources/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,5", "startColumns": "4,4", "startOffsets": "55,200", "endLines": "4,7", "endColumns": "9,9", "endOffsets": "195,340"}, "to": {"startLines": "5,8", "startColumns": "4,4", "startOffsets": "200,345", "endLines": "7,10", "endColumns": "9,9", "endOffsets": "340,485"}}]}]}