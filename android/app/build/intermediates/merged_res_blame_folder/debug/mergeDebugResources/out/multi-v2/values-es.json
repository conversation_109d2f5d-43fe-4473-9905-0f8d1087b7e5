{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-es/values-es.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-es/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4471,4579,4742,4873,4981,5142,5275,5397,5667,5859,5968,6133,6265,6430,6587,6654,6723", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4574,4737,4868,4976,5137,5270,5392,5502,5854,5963,6128,6260,6425,6582,6649,6718,6803"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,283,353,436,505,572,652,735,822,917,989,1080,1164,1240,1323,1405,1480,1559,1634,1724,1797,1880,1956", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "120,203,278,348,431,500,567,647,730,817,912,984,1075,1159,1235,1318,1400,1475,1554,1629,1719,1792,1875,1951,2038"}, "to": {"startLines": "33,47,78,85,86,90,103,104,105,145,146,149,150,153,154,155,157,158,160,162,163,165,168,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3014,4388,7910,8396,8466,8784,9781,9848,9928,13180,13267,13539,13611,13862,13946,14022,14181,14263,14418,14573,14648,14839,15064,15268,15344", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "3079,4466,7980,8461,8544,8848,9843,9923,10006,13262,13357,13606,13697,13941,14017,14100,14258,14333,14492,14643,14733,14907,15142,15339,15426"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "283,385,498,606,691,792,920,1006,1087,1179,1273,1370,1464,1564,1658,1754,1850,1942,2034,2116,2223,2334,2433,2541,2649,2756,2915,13702", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "380,493,601,686,787,915,1001,1082,1174,1268,1365,1459,1559,1653,1749,1845,1937,2029,2111,2218,2329,2428,2536,2644,2751,2910,3009,13780"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "35,36,37,38,39,40,41,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3171,3270,3372,3472,3570,3677,3783,14738", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3265,3367,3467,3565,3672,3778,3898,14834"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-es/values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "172,173", "startColumns": "4,4", "startOffsets": "15431,15531", "endColumns": "99,101", "endOffsets": "15526,15628"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-es/values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,320,424,546,627,692,787,868,931,1020,1089,1152,1226,1290,1346,1464,1522,1584,1640,1720,1859,1948,2030,2141,2222,2302,2392,2459,2525,2604,2686,2774,2848,2925,2995,3074,3158,3242,3334,3434,3508,3589,3691,3744,3811,3904,3993,4055,4119,4182,4295,4388,4492,4586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "228,315,419,541,622,687,782,863,926,1015,1084,1147,1221,1285,1341,1459,1517,1579,1635,1715,1854,1943,2025,2136,2217,2297,2387,2454,2520,2599,2681,2769,2843,2920,2990,3069,3153,3237,3329,3429,3503,3584,3686,3739,3806,3899,3988,4050,4114,4177,4290,4383,4487,4581,4664"}, "to": {"startLines": "2,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3084,3903,4007,4129,7429,7494,8315,8721,8853,8942,9011,9074,9148,9212,9268,9386,9444,9506,9562,9642,10011,10100,10182,10293,10374,10454,10544,10611,10677,10756,10838,10926,11000,11077,11147,11226,11310,11394,11486,11586,11660,11741,11843,11896,11963,12056,12145,12207,12271,12334,12447,12540,12644,12917", "endLines": "5,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,141", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "278,3166,4002,4124,4205,7489,7584,8391,8779,8937,9006,9069,9143,9207,9263,9381,9439,9501,9557,9637,9776,10095,10177,10288,10369,10449,10539,10606,10672,10751,10833,10921,10995,11072,11142,11221,11305,11389,11481,11581,11655,11736,11838,11891,11958,12051,12140,12202,12266,12329,12442,12535,12639,12733,12995"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,1010,1101,1178,1254,1334,1410,1492,1562", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,1005,1096,1173,1249,1329,1405,1487,1557,1678"}, "to": {"startLines": "45,46,70,71,72,87,88,139,140,147,148,152,156,159,161,166,167,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4210,4306,7139,7237,7340,8549,8628,12738,12830,13362,13448,13785,14105,14338,14497,14912,14994,15147", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "4301,4383,7232,7335,7424,8623,8716,12825,12912,13443,13534,13857,14176,14413,14568,14989,15059,15263"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "66,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6808,7589,7690,7805", "endColumns": "106,100,114,104", "endOffsets": "6910,7685,7800,7905"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-es/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5507", "endColumns": "159", "endOffsets": "5662"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,223,279,332,406,476,543,609,662,734", "endColumns": "120,46,55,52,73,69,66,65,52,71,54", "endOffsets": "171,218,274,327,401,471,538,604,657,729,784"}, "to": {"startLines": "67,68,69,79,80,81,82,83,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6915,7036,7083,7985,8038,8112,8182,8249,13000,13053,13125", "endColumns": "120,46,55,52,73,69,66,65,52,71,54", "endOffsets": "7031,7078,7134,8033,8107,8177,8244,8310,13048,13120,13175"}}]}]}