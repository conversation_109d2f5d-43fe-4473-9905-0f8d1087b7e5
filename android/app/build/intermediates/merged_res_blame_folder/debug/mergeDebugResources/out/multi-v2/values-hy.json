{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-hy/values-hy.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,993,1081,1155,1232,1311,1388,1469,1538", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,988,1076,1150,1227,1306,1383,1464,1533,1651"}, "to": {"startLines": "44,45,66,67,68,77,78,127,128,130,131,133,134,135,137,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4085,4184,6762,6856,6956,7751,7833,11727,11822,11989,12074,12245,12319,12396,12550,12802,12883,12952", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "4179,4261,6851,6951,7034,7828,7914,11817,11899,12069,12157,12314,12391,12470,12622,12878,12947,13065"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-hy/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4343,4449,4614,4748,4856,5010,5146,5273,5537,5704,5812,5980,6116,6278,6444,6509,6576", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "4444,4609,4743,4851,5005,5141,5268,5381,5699,5807,5975,6111,6273,6439,6504,6571,6653"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,378,478,588,677,783,900,982,1062,1153,1246,1341,1435,1535,1628,1723,1817,1908,1999,2082,2188,2294,2393,2503,2611,2712,2882,12162", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "373,473,583,672,778,895,977,1057,1148,1241,1336,1430,1530,1623,1718,1812,1903,1994,2077,2183,2289,2388,2498,2606,2707,2877,2974,12240"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "34,35,36,37,38,39,40,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3059,3159,3264,3362,3461,3566,3668,12701", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3154,3259,3357,3456,3561,3663,3774,12797"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-hy/values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,88", "endOffsets": "135,224"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "13070,13155", "endColumns": "84,88", "endOffsets": "13150,13239"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,281,349,421,496", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "127,195,276,344,416,491,565"}, "to": {"startLines": "46,75,76,80,93,136,138", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4266,7602,7670,7981,8952,12475,12627", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "4338,7665,7746,8044,9019,12545,12696"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-hy/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5386", "endColumns": "150", "endOffsets": "5532"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "65,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6658,7200,7303,7414", "endColumns": "103,102,110,102", "endOffsets": "6757,7298,7409,7512"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-hy/values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,395,525,606,670,767,852,914,1001,1065,1126,1193,1254,1308,1430,1487,1547,1601,1682,1817,1901,1986,2092,2167,2242,2337,2404,2470,2544,2624,2710,2781,2857,2933,3010,3098,3178,3274,3370,3444,3522,3622,3673,3742,3829,3920,3982,4046,4109,4214,4315,4415,4520", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "215,295,390,520,601,665,762,847,909,996,1060,1121,1188,1249,1303,1425,1482,1542,1596,1677,1812,1896,1981,2087,2162,2237,2332,2399,2465,2539,2619,2705,2776,2852,2928,3005,3093,3173,3269,3365,3439,3517,3617,3668,3737,3824,3915,3977,4041,4104,4209,4310,4410,4515,4600"}, "to": {"startLines": "2,33,41,42,43,69,70,74,79,81,82,83,84,85,86,87,88,89,90,91,92,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2979,3779,3874,4004,7039,7103,7517,7919,8049,8136,8200,8261,8328,8389,8443,8565,8622,8682,8736,8817,9024,9108,9193,9299,9374,9449,9544,9611,9677,9751,9831,9917,9988,10064,10140,10217,10305,10385,10481,10577,10651,10729,10829,10880,10949,11036,11127,11189,11253,11316,11421,11522,11622,11904", "endLines": "5,33,41,42,43,69,70,74,79,81,82,83,84,85,86,87,88,89,90,91,92,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "265,3054,3869,3999,4080,7098,7195,7597,7976,8131,8195,8256,8323,8384,8438,8560,8617,8677,8731,8812,8947,9103,9188,9294,9369,9444,9539,9606,9672,9746,9826,9912,9983,10059,10135,10212,10300,10380,10476,10572,10646,10724,10824,10875,10944,11031,11122,11184,11248,11311,11416,11517,11617,11722,11984"}}]}]}