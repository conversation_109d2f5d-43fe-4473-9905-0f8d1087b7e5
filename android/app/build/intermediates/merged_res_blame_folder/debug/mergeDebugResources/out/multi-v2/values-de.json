{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-de/values-de.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,393,491,603,689,795,910,988,1063,1155,1249,1345,1446,1553,1653,1757,1855,1953,2050,2132,2243,2345,2443,2550,2653,2757,2913,13508", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "388,486,598,684,790,905,983,1058,1150,1244,1340,1441,1548,1648,1752,1850,1948,2045,2127,2238,2340,2438,2545,2648,2752,2908,3010,13585"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "35,36,37,38,39,40,41,162", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3174,3272,3374,3474,3574,3682,3787,14526", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3267,3369,3469,3569,3677,3782,3900,14622"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-de/values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "169,170", "startColumns": "4,4", "startOffsets": "15120,15207", "endColumns": "86,89", "endOffsets": "15202,15292"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-de/values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,322,423,545,626,692,786,856,915,1023,1092,1150,1222,1286,1340,1468,1528,1590,1644,1722,1859,1951,2035,2150,2234,2320,2410,2477,2543,2617,2699,2792,2866,2944,3016,3090,3182,3264,3353,3442,3516,3594,3680,3735,3802,3882,3966,4028,4092,4155,4262,4366,4465,4571", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,100,121,80,65,93,69,58,107,68,57,71,63,53,127,59,61,53,77,136,91,83,114,83,85,89,66,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,81", "endOffsets": "233,317,418,540,621,687,781,851,910,1018,1087,1145,1217,1281,1335,1463,1523,1585,1639,1717,1854,1946,2030,2145,2229,2315,2405,2472,2538,2612,2694,2787,2861,2939,3011,3085,3177,3259,3348,3437,3511,3589,3675,3730,3797,3877,3961,4023,4087,4150,4257,4361,4460,4566,4648"}, "to": {"startLines": "2,34,42,43,44,73,74,84,89,90,91,92,93,94,95,96,97,98,99,100,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3090,3905,4006,4128,7460,7526,8327,8724,8783,8891,8960,9018,9090,9154,9208,9336,9396,9458,9512,9590,9946,10038,10122,10237,10321,10407,10497,10564,10630,10704,10786,10879,10953,11031,11103,11177,11269,11351,11440,11529,11603,11681,11767,11822,11889,11969,12053,12115,12179,12242,12349,12453,12552,12835", "endLines": "5,34,42,43,44,73,74,84,89,90,91,92,93,94,95,96,97,98,99,100,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "endColumns": "12,83,100,121,80,65,93,69,58,107,68,57,71,63,53,127,59,61,53,77,136,91,83,114,83,85,89,66,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,81", "endOffsets": "283,3169,4001,4123,4204,7521,7615,8392,8778,8886,8955,9013,9085,9149,9203,9331,9391,9453,9507,9585,9722,10033,10117,10232,10316,10402,10492,10559,10625,10699,10781,10874,10948,11026,11098,11172,11264,11346,11435,11524,11598,11676,11762,11817,11884,11964,12048,12110,12174,12237,12344,12448,12547,12653,12912"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,289,357,439,506,582,658,741,828,909,992,1072,1158,1243,1321,1392,1462,1553,1628,1703", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "125,208,284,352,434,501,577,653,736,823,904,987,1067,1153,1238,1316,1387,1457,1548,1623,1698,1776"}, "to": {"startLines": "33,47,78,85,86,102,103,104,144,145,148,151,152,153,155,156,158,160,161,163,166,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3015,4393,7932,8397,8465,9727,9794,9870,13092,13175,13427,13665,13748,13828,13989,14074,14224,14365,14435,14627,14847,15042", "endColumns": "74,82,75,67,81,66,75,75,82,86,80,82,79,85,84,77,70,69,90,74,74,77", "endOffsets": "3085,4471,8003,8460,8542,9789,9865,9941,13170,13257,13503,13743,13823,13909,14069,14147,14290,14430,14521,14697,14917,15115"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "66,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6827,7620,7721,7832", "endColumns": "103,100,110,99", "endOffsets": "6926,7716,7827,7927"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-de/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5538", "endColumns": "144", "endOffsets": "5678"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,191,238,299,347,419,487,564,618,671,738", "endColumns": "135,46,60,47,71,67,76,53,52,66,54", "endOffsets": "186,233,294,342,414,482,559,613,666,733,788"}, "to": {"startLines": "67,68,69,79,80,81,82,83,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6931,7067,7114,8008,8056,8128,8196,8273,12917,12970,13037", "endColumns": "135,46,60,47,71,67,76,53,52,66,54", "endOffsets": "7062,7109,7170,8051,8123,8191,8268,8322,12965,13032,13087"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-de/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4476,4585,4749,4877,4989,5167,5298,5419,5683,5863,5975,6144,6275,6437,6613,6684,6747", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "4580,4744,4872,4984,5162,5293,5414,5533,5858,5970,6139,6270,6432,6608,6679,6742,6822"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,1009,1093,1168,1243,1315,1385,1464,1530", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,1004,1088,1163,1238,1310,1380,1459,1525,1645"}, "to": {"startLines": "45,46,70,71,72,87,88,138,139,146,147,150,154,157,159,164,165,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4209,4305,7175,7273,7373,8547,8632,12658,12747,13262,13343,13590,13914,14152,14295,14702,14781,14922", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "4300,4388,7268,7368,7455,8627,8719,12742,12830,13338,13422,13660,13984,14219,14360,14776,14842,15037"}}]}]}