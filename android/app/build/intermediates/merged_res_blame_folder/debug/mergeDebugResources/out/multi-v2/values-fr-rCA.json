{"logs": [{"outputFile": "com.dumpster.userapp-mergeDebugResources-80:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/41ea898dccaf6691ae5b67b43d1d3225/transformed/play-services-basement-18.3.0/res/values-fr-rCA/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5550", "endColumns": "160", "endOffsets": "5706"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "35,36,37,38,39,40,41,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3190,3288,3390,3489,3591,3695,3799,13990", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3283,3385,3484,3586,3690,3794,3908,14086"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/res/values-fr-rCA/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4487,4593,4773,4903,5012,5183,5316,5437,5711,5906,6018,6203,6339,6499,6678,6751,6815", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "4588,4768,4898,5007,5178,5311,5432,5545,5901,6013,6198,6334,6494,6673,6746,6810,6894"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/687dac141c1ab44f5b341b0756319e1c/transformed/material-1.6.1/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,320,420,542,627,693,790,870,932,1024,1098,1159,1238,1302,1356,1472,1531,1593,1647,1729,1858,1950,2034,2148,2227,2308,2401,2468,2534,2613,2694,2785,2857,2935,3010,3082,3179,3256,3354,3452,3530,3611,3711,3768,3834,3917,4004,4066,4130,4193,4295,4402,4499,4608", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "233,315,415,537,622,688,785,865,927,1019,1093,1154,1233,1297,1351,1467,1526,1588,1642,1724,1853,1945,2029,2143,2222,2303,2396,2463,2529,2608,2689,2780,2852,2930,3005,3077,3174,3251,3349,3447,3525,3606,3706,3763,3829,3912,3999,4061,4125,4188,4290,4397,4494,4603,4692"}, "to": {"startLines": "2,34,42,43,44,70,71,76,80,82,83,84,85,86,87,88,89,90,91,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3108,3913,4013,4135,7297,7363,7856,8193,8322,8414,8488,8549,8628,8692,8746,8862,8921,8983,9037,9119,9408,9500,9584,9698,9777,9858,9951,10018,10084,10163,10244,10335,10407,10485,10560,10632,10729,10806,10904,11002,11080,11161,11261,11318,11384,11467,11554,11616,11680,11743,11845,11952,12049,12337", "endLines": "5,34,42,43,44,70,71,76,80,82,83,84,85,86,87,88,89,90,91,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,131", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "283,3185,4008,4130,4215,7358,7455,7931,8250,8409,8483,8544,8623,8687,8741,8857,8916,8978,9032,9114,9243,9495,9579,9693,9772,9853,9946,10013,10079,10158,10239,10330,10402,10480,10555,10627,10724,10801,10899,10997,11075,11156,11256,11313,11379,11462,11549,11611,11675,11738,11840,11947,12044,12153,12421"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/31ed8c8f7f7a6c921c2183281685bba5/transformed/foundation-release/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "159,160", "startColumns": "4,4", "startOffsets": "14669,14757", "endColumns": "87,94", "endOffsets": "14752,14847"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c19fc97a693c5dadf5e76e2e9911d55a/transformed/ui-release/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1017,1102,1178,1253,1331,1405,1484,1553", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1012,1097,1173,1248,1326,1400,1479,1548,1670"}, "to": {"startLines": "45,46,67,68,69,78,79,129,130,134,135,139,143,146,148,153,154,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4220,4319,7006,7104,7210,8019,8099,12158,12250,12608,12689,13020,13362,13600,13755,14163,14242,14394", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "4314,4402,7099,7205,7292,8094,8188,12245,12332,12684,12769,13091,13432,13673,13824,14237,14306,14511"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e67a8c0e6576dfe554b0db993f9b0490/transformed/browser-1.6.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6899,7460,7562,7681", "endColumns": "106,101,118,104", "endOffsets": "7001,7557,7676,7781"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,205,275,358,425,504,585,675,767,838,926,1021,1112,1192,1272,1355,1432,1505,1593,1665,1748,1821", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "120,200,270,353,420,499,580,670,762,833,921,1016,1107,1187,1267,1350,1427,1500,1588,1660,1743,1816,1896"}, "to": {"startLines": "33,47,75,77,81,94,95,132,133,136,137,140,141,142,144,145,147,149,150,152,155,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3038,4407,7786,7936,8255,9248,9327,12426,12516,12774,12845,13096,13191,13282,13437,13517,13678,13829,13902,14091,14311,14516,14589", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "3103,4482,7851,8014,8317,9322,9403,12511,12603,12840,12928,13186,13277,13357,13512,13595,13750,13897,13985,14158,14389,14584,14664"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/843ebde063b58c1dda2d842a44366349/transformed/appcompat-1.7.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,399,506,616,703,809,939,1024,1104,1195,1288,1386,1481,1581,1674,1767,1862,1953,2044,2130,2240,2351,2454,2565,2673,2780,2939,12933", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "394,501,611,698,804,934,1019,1099,1190,1283,1381,1476,1576,1669,1762,1857,1948,2039,2125,2235,2346,2449,2560,2668,2775,2934,3033,13015"}}]}]}