1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dumpster.userapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:10:3-75
11-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:10:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:2:3-78
12-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:3:3-76
13-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:4:3-76
14-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:5:3-64
15-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:5:20-62
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:6:3-74
16-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:6:20-72
17    <uses-permission
17-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:7:3-77
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:7:20-75
19        android:maxSdkVersion="32" />
19-->[:expo-image] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:9-35
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:8:3-78
20-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:8:20-76
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:9:3-68
21-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:9:20-66
22    <uses-permission android:name="android.permission.VIBRATE" />
22-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:11:3-63
22-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:11:20-61
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:12:3-65
23-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:12:20-63
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:13:3-78
24-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:13:20-76
25
26    <queries>
26-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:14:3-20:13
27        <intent>
27-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:15:5-19:14
28            <action android:name="android.intent.action.VIEW" />
28-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:16:7-58
28-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:16:15-56
29
30            <category android:name="android.intent.category.BROWSABLE" />
30-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:17:7-67
30-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:17:17-65
31
32            <data android:scheme="https" />
32-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:7-37
32-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:13-35
33        </intent>
34        <!-- Query open documents -->
35        <intent>
35-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-17:18
36            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
36-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-79
36-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:21-76
37        </intent>
38        <intent>
38-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-19:18
39
40            <!-- Required for picking images from the camera roll if targeting API 30 -->
41            <action android:name="android.media.action.IMAGE_CAPTURE" />
41-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-73
41-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:21-70
42        </intent>
43        <intent>
43-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:9-24:18
44
45            <!-- Required for picking images from the camera if targeting API 30 -->
46            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
46-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-80
46-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:21-77
47        </intent>
48        <intent>
48-->[:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:18
49
50            <!-- Required for opening tabs if targeting API 30 -->
51            <action android:name="android.support.customtabs.action.CustomTabsService" />
51-->[:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-90
51-->[:expo-web-browser] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:21-87
52        </intent> <!-- Needs to be explicitly declared on Android R+ -->
53        <package android:name="com.google.android.apps.maps" />
53-->[com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:33:9-64
53-->[com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:33:18-61
54
55        <intent>
55-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:10:9-16:18
56            <action android:name="android.intent.action.GET_CONTENT" />
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:11:13-72
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:11:21-69
57
58            <category android:name="android.intent.category.OPENABLE" />
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:13:13-73
58-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:13:23-70
59
60            <data android:mimeType="*/*" />
60-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:7-37
61        </intent>
62    </queries> <!-- Required for picking images from camera directly -->
63    <uses-permission android:name="android.permission.CAMERA" />
63-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-65
63-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-62
64
65    <uses-feature
65-->[com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:26:5-28:35
66        android:glEsVersion="0x00020000"
66-->[com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:27:9-41
67        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
67-->[com.google.android.gms:play-services-maps:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a1121609079440b5f889d97c97d7d6bc/transformed/play-services-maps-18.2.0/AndroidManifest.xml:28:9-32
68    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
68-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
68-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
69    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
69-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:28:5-77
69-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:28:22-74
70
71    <permission
71-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
72        android:name="com.dumpster.userapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
72-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
73        android:protectionLevel="signature" />
73-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
74
75    <uses-permission android:name="com.dumpster.userapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
75-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
75-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
76    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
76-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
76-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/720c2fea68fe51b4a1839e088d19c2b0/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
77    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
77-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:15:5-98
77-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:15:22-95
78    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" /> <!-- for android -->
78-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:16:5-94
78-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:16:22-91
79    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
80    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
81    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
82    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
83    <!-- for Samsung -->
84    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
84-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
84-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
85    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
85-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
85-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
86    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
86-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
86-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
87    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
87-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
87-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
88    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
88-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
88-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
89    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
89-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
89-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
90    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
90-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
90-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
91    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
91-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
91-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
92    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
92-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
92-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
93    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
93-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
93-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
94    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
94-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
94-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
95    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
95-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
95-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
96    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
96-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
96-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
97    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
97-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
97-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
98    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
98-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
98-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
99    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
99-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
99-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.10.2/transforms/802ad3e4b4ba9d24e9c1304110b218f7/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
100
101    <application
101-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:3-44:17
102        android:name="com.dumpster.userapp.MainApplication"
102-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:16-47
103        android:allowBackup="false"
103-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:162-189
104        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
104-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d886fda41c1eb4d8ee50a0d17dc2ab23/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
105        android:debuggable="true"
106        android:extractNativeLibs="false"
107        android:icon="@mipmap/ic_launcher"
107-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:81-115
108        android:label="@string/app_name"
108-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:48-80
109        android:roundIcon="@mipmap/ic_launcher_round"
109-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:116-161
110        android:supportsRtl="true"
110-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:222-248
111        android:theme="@style/AppTheme"
111-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:190-221
112        android:usesCleartextTraffic="true" >
112-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:21:249-284
113        <meta-data
113-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:22:5-119
114            android:name="com.google.android.geo.API_KEY"
114-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:22:16-61
115            android:value="AIzaSyBMIHTUcYMqJDVWpa4VXJNB_t84BfwK1hY" />
115-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:22:62-117
116        <meta-data
116-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:23:5-139
117            android:name="com.google.firebase.messaging.default_notification_color"
117-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:23:16-87
118            android:resource="@color/notification_icon_color" />
118-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:23:88-137
119        <meta-data
119-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:24:5-135
120            android:name="com.google.firebase.messaging.default_notification_icon"
120-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:24:16-86
121            android:resource="@drawable/notification_icon" />
121-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:24:87-133
122        <meta-data
122-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:25:5-136
123            android:name="expo.modules.notifications.default_notification_color"
123-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:25:16-84
124            android:resource="@color/notification_icon_color" />
124-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:25:85-134
125        <meta-data
125-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:26:5-132
126            android:name="expo.modules.notifications.default_notification_icon"
126-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:26:16-83
127            android:resource="@drawable/notification_icon" />
127-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:26:84-130
128        <meta-data
128-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:27:5-83
129            android:name="expo.modules.updates.ENABLED"
129-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:27:16-59
130            android:value="false" />
130-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:27:60-81
131        <meta-data
131-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:28:5-105
132            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
132-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:28:16-80
133            android:value="ALWAYS" />
133-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:28:81-103
134        <meta-data
134-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:29:5-99
135            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
135-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:29:16-79
136            android:value="0" />
136-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:29:80-97
137
138        <activity
138-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:5-42:16
139            android:name="com.dumpster.userapp.MainActivity"
139-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:15-43
140            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
140-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:44-134
141            android:exported="true"
141-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:256-279
142            android:launchMode="singleTask"
142-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:135-166
143            android:screenOrientation="portrait"
143-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:280-316
144            android:theme="@style/Theme.App.SplashScreen"
144-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:210-255
145            android:windowSoftInputMode="adjustResize" >
145-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:30:167-209
146            <intent-filter>
146-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:31:7-34:23
147                <action android:name="android.intent.action.MAIN" />
147-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:32:9-60
147-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:32:17-58
148
149                <category android:name="android.intent.category.LAUNCHER" />
149-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:33:9-68
149-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:33:19-66
150            </intent-filter>
151            <intent-filter>
151-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:35:7-41:23
152                <action android:name="android.intent.action.VIEW" />
152-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:16:7-58
152-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:16:15-56
153
154                <category android:name="android.intent.category.DEFAULT" />
154-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:37:9-67
154-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:37:19-65
155                <category android:name="android.intent.category.BROWSABLE" />
155-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:17:7-67
155-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:17:17-65
156
157                <data android:scheme="dumpster-user-app" />
157-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:7-37
157-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:13-35
158                <data android:scheme="com.dumpster.userapp" />
158-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:7-37
158-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:18:13-35
159            </intent-filter>
160        </activity>
161
162        <uses-library
162-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:43:5-83
163            android:name="org.apache.http.legacy"
163-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:43:19-56
164            android:required="false" />
164-->/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android/app/src/main/AndroidManifest.xml:43:57-81
165
166        <provider
166-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:9-30:20
167            android:name="expo.modules.filesystem.FileSystemFileProvider"
167-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-74
168            android:authorities="com.dumpster.userapp.FileSystemFileProvider"
168-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-74
169            android:exported="false"
169-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-37
170            android:grantUriPermissions="true" >
170-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-47
171            <meta-data
171-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-29:70
172                android:name="android.support.FILE_PROVIDER_PATHS"
172-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:17-67
173                android:resource="@xml/file_system_provider_paths" />
173-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-67
174        </provider>
175
176        <service
176-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:9-40:19
177            android:name="com.google.android.gms.metadata.ModuleDependencies"
177-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-78
178            android:enabled="false"
178-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-36
179            android:exported="false" >
179-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:13-37
180            <intent-filter>
180-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:33:13-35:29
181                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
181-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:17-94
181-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:25-91
182            </intent-filter>
183
184            <meta-data
184-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:13-39:36
185                android:name="photopicker_activity:0:required"
185-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-63
186                android:value="" />
186-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:39:17-33
187        </service>
188
189        <activity
189-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:9-44:59
190            android:name="com.canhub.cropper.CropImageActivity"
190-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-64
191            android:exported="true"
191-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:35:13-36
192            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
192-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-56
193        <provider
193-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:46:9-54:20
194            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
194-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:47:13-89
195            android:authorities="com.dumpster.userapp.ImagePickerFileProvider"
195-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:48:13-75
196            android:exported="false"
196-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:49:13-37
197            android:grantUriPermissions="true" >
197-->[:expo-image-picker] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-image-picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:50:13-47
198            <meta-data
198-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-29:70
199                android:name="android.support.FILE_PROVIDER_PATHS"
199-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:17-67
200                android:resource="@xml/image_picker_provider_paths" />
200-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-67
201        </provider>
202
203        <service
203-->[:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-14:56
204            android:name="expo.modules.location.services.LocationTaskService"
204-->[:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-78
205            android:exported="false"
205-->[:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-37
206            android:foregroundServiceType="location" />
206-->[:expo-location] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-53
207        <service
207-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-17:19
208            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
208-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-91
209            android:exported="false" >
209-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-37
210            <intent-filter android:priority="-1" >
210-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:29
210-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:28-49
211                <action android:name="com.google.firebase.MESSAGING_EVENT" />
211-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-78
211-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:25-75
212            </intent-filter>
213        </service>
214
215        <receiver
215-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:20
216            android:name="expo.modules.notifications.service.NotificationsService"
216-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-83
217            android:enabled="true"
217-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-35
218            android:exported="false" >
218-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
219            <intent-filter android:priority="-1" >
219-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-30:29
219-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:28-49
220                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
220-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:17-88
220-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:25-85
221                <action android:name="android.intent.action.BOOT_COMPLETED" />
221-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-79
221-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-76
222                <action android:name="android.intent.action.REBOOT" />
222-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:17-71
222-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:25-68
223                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
223-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:17-82
223-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:25-79
224                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
224-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:17-82
224-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:25-79
225                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
225-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-84
225-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:25-81
226            </intent-filter>
227        </receiver>
228
229        <activity
229-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:33:9-40:75
230            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
230-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:13-92
231            android:excludeFromRecents="true"
231-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:13-46
232            android:exported="false"
232-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-37
233            android:launchMode="standard"
233-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:13-42
234            android:noHistory="true"
234-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:13-37
235            android:taskAffinity=""
235-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:39:13-36
236            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
236-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:40:13-72
237
238        <receiver
238-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
239            android:name="expo.modules.taskManager.TaskBroadcastReceiver"
239-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
240            android:exported="false" >
240-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
241            <intent-filter>
241-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-15:29
242                <action android:name="expo.modules.taskManager.TaskBroadcastReceiver.INTENT_ACTION" />
242-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:17-103
242-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:25-100
243                <action android:name="android.intent.action.BOOT_COMPLETED" />
243-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-79
243-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-76
244                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
244-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-84
244-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:25-81
245            </intent-filter>
246        </receiver>
247
248        <service
248-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-22:72
249            android:name="expo.modules.taskManager.TaskJobService"
249-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-67
250            android:enabled="true"
250-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-35
251            android:exported="false"
251-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-37
252            android:permission="android.permission.BIND_JOB_SERVICE" />
252-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-69
253
254        <meta-data
254-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:9-26:36
255            android:name="expo.modules.taskManager.oneAppId"
255-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-61
256            android:value="true" />
256-->[:expo-task-manager] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-task-manager/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:13-33
257        <meta-data
257-->[:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
258            android:name="org.unimodules.core.AppLoader#react-native-headless"
258-->[:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
259            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
259-->[:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
260        <meta-data
260-->[:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
261            android:name="com.facebook.soloader.enabled"
261-->[:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
262            android:value="true" />
262-->[:expo-modules-core] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
263
264        <activity
264-->[com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:19:9-21:40
265            android:name="com.facebook.react.devsupport.DevSettingsActivity"
265-->[com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:20:13-77
266            android:exported="false" />
266-->[com.facebook.react:react-android:0.76.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7e0afc5a9d350f1578485b1656f5bf9a/transformed/react-android-0.76.9-debug/AndroidManifest.xml:21:13-37
267
268        <meta-data
268-->[com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:8:9-10:69
269            android:name="com.google.android.gms.version"
269-->[com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:9:13-58
270            android:value="@integer/google_play_services_version" />
270-->[com.google.maps.android:android-maps-utils:3.8.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6a8246aecd137f204bd55fbfadebf9cb/transformed/android-maps-utils-3.8.2/AndroidManifest.xml:10:13-66
271
272        <receiver
272-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
273            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
273-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
274            android:exported="true"
274-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
275            android:permission="com.google.android.c2dm.permission.SEND" >
275-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
276            <intent-filter>
276-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
277                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
277-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
277-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
278            </intent-filter>
279
280            <meta-data
280-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
281                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
281-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
282                android:value="true" />
282-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
283        </receiver>
284        <!--
285             FirebaseMessagingService performs security checks at runtime,
286             but set to not exported to explicitly avoid allowing another app to call it.
287        -->
288        <service
288-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
289            android:name="com.google.firebase.messaging.FirebaseMessagingService"
289-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
290            android:directBootAware="true"
290-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
291            android:exported="false" >
291-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
292            <intent-filter android:priority="-500" >
292-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:29
292-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:28-49
293                <action android:name="com.google.firebase.MESSAGING_EVENT" />
293-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-78
293-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:25-75
294            </intent-filter>
295        </service>
296        <service
296-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
297            android:name="com.google.firebase.components.ComponentDiscoveryService"
297-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
298            android:directBootAware="true"
298-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
299            android:exported="false" >
299-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
300            <meta-data
300-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
301                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
301-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
302                android:value="com.google.firebase.components.ComponentRegistrar" />
302-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
303            <meta-data
303-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
304                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
304-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
305                android:value="com.google.firebase.components.ComponentRegistrar" />
305-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e68ef85a16a9701cc4d8cc9885c48b3e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
306            <meta-data
306-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
307                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
307-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
308                android:value="com.google.firebase.components.ComponentRegistrar" />
308-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
309            <meta-data
309-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
310                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
310-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
311                android:value="com.google.firebase.components.ComponentRegistrar" />
311-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/219cd3266752a9860a4f31004882c693/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
312            <meta-data
312-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
313                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
313-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
314                android:value="com.google.firebase.components.ComponentRegistrar" />
314-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/410cec3fcaee33a136a9efd5932ce49f/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
315            <meta-data
315-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
316                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
316-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
317                android:value="com.google.firebase.components.ComponentRegistrar" />
317-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
318            <meta-data
318-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
319                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
319-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
320                android:value="com.google.firebase.components.ComponentRegistrar" />
320-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d2f43212f7e04e8e68a35e47a67241fe/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
321        </service>
322
323        <activity
323-->[com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:20:9-22:45
324            android:name="com.google.android.gms.common.api.GoogleApiActivity"
324-->[com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:20:19-85
325            android:exported="false"
325-->[com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:22:19-43
326            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
326-->[com.google.android.gms:play-services-base:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8863d1fe00a46f3177ab27282faf82fa/transformed/play-services-base-18.2.0/AndroidManifest.xml:21:19-78
327
328        <meta-data
328-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
329            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
329-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
330            android:value="GlideModule" />
330-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/33caf4dcd8cb47319c3e1370f49f84eb/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
331
332        <provider
332-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:23:9-31:20
333            android:name="com.canhub.cropper.CropFileProvider"
333-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:24:13-63
334            android:authorities="com.dumpster.userapp.cropper.fileprovider"
334-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:25:13-72
335            android:exported="false"
335-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:26:13-37
336            android:grantUriPermissions="true" >
336-->[com.github.CanHub:Android-Image-Cropper:4.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/439ee6bb6984ecdcb3c06406dc097da8/transformed/Android-Image-Cropper-4.3.1/AndroidManifest.xml:27:13-47
337            <meta-data
337-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-29:70
338                android:name="android.support.FILE_PROVIDER_PATHS"
338-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:17-67
339                android:resource="@xml/library_file_paths" />
339-->[:expo-file-system] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-67
340        </provider>
341        <provider
341-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
342            android:name="com.google.firebase.provider.FirebaseInitProvider"
342-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
343            android:authorities="com.dumpster.userapp.firebaseinitprovider"
343-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
344            android:directBootAware="true"
344-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
345            android:exported="false"
345-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
346            android:initOrder="100" />
346-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/19462e69c2c79d9f89bda128ac35969b/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
347        <provider
347-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:31:9-39:20
348            android:name="androidx.startup.InitializationProvider"
348-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:32:13-67
349            android:authorities="com.dumpster.userapp.androidx-startup"
349-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:33:13-68
350            android:exported="false" >
350-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:34:13-37
351            <meta-data
351-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:36:13-38:52
352                android:name="androidx.work.WorkManagerInitializer"
352-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:37:17-68
353                android:value="androidx.startup" />
353-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:38:17-49
354            <meta-data
354-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
355                android:name="androidx.emoji2.text.EmojiCompatInitializer"
355-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
356                android:value="androidx.startup" />
356-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b9c5d7ed494bb64d08d6f44028dde550/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
357            <meta-data
357-->[androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:29:13-31:52
358                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
358-->[androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:30:17-78
359                android:value="androidx.startup" />
359-->[androidx.lifecycle:lifecycle-process:2.8.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/758a114cb0573a0058a6cd55def5dcda/transformed/lifecycle-process-2.8.3/AndroidManifest.xml:31:17-49
360            <meta-data
360-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
361                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
361-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
362                android:value="androidx.startup" />
362-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
363        </provider>
364
365        <service
365-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:41:9-46:35
366            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
366-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:42:13-88
367            android:directBootAware="false"
367-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:43:13-44
368            android:enabled="@bool/enable_system_alarm_service_default"
368-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:44:13-72
369            android:exported="false" />
369-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:45:13-37
370        <service
370-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:47:9-53:35
371            android:name="androidx.work.impl.background.systemjob.SystemJobService"
371-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:48:13-84
372            android:directBootAware="false"
372-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:49:13-44
373            android:enabled="@bool/enable_system_job_service_default"
373-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:50:13-70
374            android:exported="true"
374-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:51:13-36
375            android:permission="android.permission.BIND_JOB_SERVICE" />
375-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:52:13-69
376        <service
376-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:54:9-59:35
377            android:name="androidx.work.impl.foreground.SystemForegroundService"
377-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:55:13-81
378            android:directBootAware="false"
378-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:56:13-44
379            android:enabled="@bool/enable_system_foreground_service_default"
379-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:57:13-77
380            android:exported="false" />
380-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:58:13-37
381
382        <receiver
382-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:61:9-66:35
383            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
383-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:62:13-88
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:63:13-44
385            android:enabled="true"
385-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:64:13-35
386            android:exported="false" />
386-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:65:13-37
387        <receiver
387-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:67:9-77:20
388            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
388-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:68:13-106
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:69:13-44
390            android:enabled="false"
390-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:70:13-36
391            android:exported="false" >
391-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:71:13-37
392            <intent-filter>
392-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:73:13-76:29
393                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
393-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:74:17-87
393-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:74:25-84
394                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
394-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:75:17-90
394-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:75:25-87
395            </intent-filter>
396        </receiver>
397        <receiver
397-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:78:9-88:20
398            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
398-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:79:13-104
399            android:directBootAware="false"
399-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:80:13-44
400            android:enabled="false"
400-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:81:13-36
401            android:exported="false" >
401-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:82:13-37
402            <intent-filter>
402-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:84:13-87:29
403                <action android:name="android.intent.action.BATTERY_OKAY" />
403-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:85:17-77
403-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:85:25-74
404                <action android:name="android.intent.action.BATTERY_LOW" />
404-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:86:17-76
404-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:86:25-73
405            </intent-filter>
406        </receiver>
407        <receiver
407-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:89:9-99:20
408            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
408-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:90:13-104
409            android:directBootAware="false"
409-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:91:13-44
410            android:enabled="false"
410-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:92:13-36
411            android:exported="false" >
411-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:93:13-37
412            <intent-filter>
412-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:95:13-98:29
413                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
413-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:96:17-83
413-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:96:25-80
414                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
414-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:97:17-82
414-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:97:25-79
415            </intent-filter>
416        </receiver>
417        <receiver
417-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:100:9-109:20
418            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
418-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:101:13-103
419            android:directBootAware="false"
419-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:102:13-44
420            android:enabled="false"
420-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:103:13-36
421            android:exported="false" >
421-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:104:13-37
422            <intent-filter>
422-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:106:13-108:29
423                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
423-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:107:17-79
423-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:107:25-76
424            </intent-filter>
425        </receiver>
426        <receiver
426-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:110:9-121:20
427            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
427-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:111:13-88
428            android:directBootAware="false"
428-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:112:13-44
429            android:enabled="false"
429-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:113:13-36
430            android:exported="false" >
430-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:114:13-37
431            <intent-filter>
431-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:116:13-120:29
432                <action android:name="android.intent.action.BOOT_COMPLETED" />
432-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-79
432-->[:expo-notifications] /Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-76
433                <action android:name="android.intent.action.TIME_SET" />
433-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:118:17-73
433-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:118:25-70
434                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
434-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:119:17-81
434-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:119:25-78
435            </intent-filter>
436        </receiver>
437        <receiver
437-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:122:9-131:20
438            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
438-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:123:13-99
439            android:directBootAware="false"
439-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:124:13-44
440            android:enabled="@bool/enable_system_alarm_service_default"
440-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:125:13-72
441            android:exported="false" >
441-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:126:13-37
442            <intent-filter>
442-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:128:13-130:29
443                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
443-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:129:17-98
443-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:129:25-95
444            </intent-filter>
445        </receiver>
446        <receiver
446-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:132:9-142:20
447            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
447-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:133:13-78
448            android:directBootAware="false"
448-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:134:13-44
449            android:enabled="true"
449-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:135:13-35
450            android:exported="true"
450-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:136:13-36
451            android:permission="android.permission.DUMP" >
451-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:137:13-57
452            <intent-filter>
452-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:139:13-141:29
453                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
453-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:140:17-88
453-->[androidx.work:work-runtime:2.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3cd8c089743f599a711e5b4d2bb367c/transformed/work-runtime-2.7.1/AndroidManifest.xml:140:25-85
454            </intent-filter>
455        </receiver>
456        <receiver
456-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
457            android:name="androidx.profileinstaller.ProfileInstallReceiver"
457-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
458            android:directBootAware="false"
458-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
459            android:enabled="true"
459-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
460            android:exported="true"
460-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
461            android:permission="android.permission.DUMP" >
461-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
462            <intent-filter>
462-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
463                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
463-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
463-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
464            </intent-filter>
465            <intent-filter>
465-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
466                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
466-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
466-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
467            </intent-filter>
468            <intent-filter>
468-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
469                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
469-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
469-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
470            </intent-filter>
471            <intent-filter>
471-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
472                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
472-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
472-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/05f1d091dfb915c2a09183a7ca9487cb/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
473            </intent-filter>
474        </receiver>
475
476        <service
476-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
477            android:name="androidx.room.MultiInstanceInvalidationService"
477-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
478            android:directBootAware="true"
478-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
479            android:exported="false" />
479-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/48ae025a27e4789600d9b37af5a172a7/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
480        <service
480-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
481            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
481-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
482            android:exported="false" >
482-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
483            <meta-data
483-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
484                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
484-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
485                android:value="cct" />
485-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/7c2dd8c038c93de4425b00160d265817/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
486        </service>
487        <service
487-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
488            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
488-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
489            android:exported="false"
489-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
490            android:permission="android.permission.BIND_JOB_SERVICE" >
490-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
491        </service>
492
493        <receiver
493-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
494            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
494-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
495            android:exported="false" />
495-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.10.2/transforms/a005ff5b037b724c2cfa929f3844ce4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
496
497        <activity
497-->[com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:8:9-12:75
498            android:name="com.jakewharton.processphoenix.ProcessPhoenix"
498-->[com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:9:13-73
499            android:exported="false"
499-->[com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:10:13-37
500            android:process=":phoenix"
500-->[com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:11:13-39
501            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
501-->[com.jakewharton:process-phoenix:2.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/1193c04103e7d5df180fc9909c798645/transformed/process-phoenix-2.1.2/AndroidManifest.xml:12:13-72
502
503        <service
503-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:19:9-21:40
504            android:name="io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService"
504-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:20:13-132
505            android:exported="false" />
505-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:21:13-37
506        <service
506-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:22:9-24:40
507            android:name="io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService"
507-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:23:13-127
508            android:exported="false" />
508-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:24:13-37
509        <service
509-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:25:9-27:40
510            android:name="io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService"
510-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:26:13-120
511            android:exported="false" />
511-->[:expo-location$io.nlopez.smartlocation-jetified-aar] /Users/<USER>/.gradle/caches/8.10.2/transforms/369c9e376bf2bc1de734a95add6b913b/transformed/io.nlopez.smartlocation-3.3.3-jetified/AndroidManifest.xml:27:13-37
512    </application>
513
514</manifest>
