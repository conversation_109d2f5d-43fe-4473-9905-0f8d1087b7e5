{"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app", "reactNativePath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native", "dependencies": {"@react-native-community/datetimepicker": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker", "name": "@react-native-community/datetimepicker", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android", "packageImportPath": "import com.reactcommunity.rndatetimepicker.RNDateTimePickerPackage;", "packageInstance": "new RNDateTimePickerPackage()", "buildTypes": [], "libraryName": "RNDateTimePickerCGen", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-community/slider": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/slider", "name": "@react-native-community/slider", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/slider/android", "packageImportPath": "import com.reactnativecommunity.slider.ReactSliderPackage;", "packageInstance": "new ReactSliderPackage()", "buildTypes": [], "libraryName": "RNCSlider", "componentDescriptors": ["RNCSliderComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo", "name": "expo", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo/android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-gesture-handler/android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerButtonComponentDescriptor", "RNGestureHandlerRootViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-get-random-values": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-get-random-values", "name": "react-native-get-random-values", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-get-random-values/android", "packageImportPath": "import org.linusu.RNGetRandomValuesPackage;", "packageInstance": "new RNGetRandomValuesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-get-random-values/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-linear-gradient": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-linear-gradient", "name": "react-native-linear-gradient", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-linear-gradient/android", "packageImportPath": "import com.BV.LinearGradient.LinearGradientPackage;", "packageInstance": "new LinearGradientPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-linear-gradient/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-localization": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-localization", "name": "react-native-localization", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-localization/android", "packageImportPath": "import com.babisoft.ReactNativeLocalization.ReactNativeLocalizationPackage;", "packageInstance": "new ReactNativeLocalizationPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-localization/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-maps": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-maps", "name": "react-native-maps", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-maps/android", "packageImportPath": "import com.rnmaps.maps.MapsPackage;", "packageInstance": "new MapsPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-maps/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-reanimated/android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-restart": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-restart", "name": "react-native-restart", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-restart/android", "packageImportPath": "import com.reactnativerestart.RestartPackage;", "packageInstance": "new RestartPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-restart/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-safe-area-context/android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-screens/android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-svg": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-svg", "name": "react-native-svg", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-svg/android", "packageImportPath": "import com.horcrux.svg.SvgPackage;", "packageInstance": "new SvgPackage()", "buildTypes": [], "libraryName": "rnsvg", "componentDescriptors": ["RNSVGCircleComponentDescriptor", "RNSVGClipPathComponentDescriptor", "RNSVGDefsComponentDescriptor", "RNSVGFeBlendComponentDescriptor", "RNSVGFeColorMatrixComponentDescriptor", "RNSVGFeCompositeComponentDescriptor", "RNSVGFeFloodComponentDescriptor", "RNSVGFeGaussianBlurComponentDescriptor", "RNSVGFeMergeComponentDescriptor", "RNSVGFeOffsetComponentDescriptor", "RNSVGFilterComponentDescriptor", "RNSVGEllipseComponentDescriptor", "RNSVGForeignObjectComponentDescriptor", "RNSVGGroupComponentDescriptor", "RNSVGImageComponentDescriptor", "RNSVGLinearGradientComponentDescriptor", "RNSVGLineComponentDescriptor", "RNSVGMarkerComponentDescriptor", "RNSVGMaskComponentDescriptor", "RNSVGPathComponentDescriptor", "RNSVGPatternComponentDescriptor", "RNSVGRadialGradientComponentDescriptor", "RNSVGRectComponentDescriptor", "RNSVGSvgViewAndroidComponentDescriptor", "RNSVGSymbolComponentDescriptor", "RNSVGTextComponentDescriptor", "RNSVGTextPathComponentDescriptor", "RNSVGTSpanComponentDescriptor", "RNSVGUseComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-vector-icons": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-vector-icons/android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-async-storage/async-storage": {"root": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-async-storage/async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-async-storage/async-storage/android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.dumpster.userapp", "sourceDir": "/Users/<USER>/Desktop/GitHub/dumpster-on-demamd/dumpster-user-app/android"}}}